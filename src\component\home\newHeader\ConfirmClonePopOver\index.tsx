import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './ConfirmClonePopOver.module.scss'
import Button from '@/src/component/shared/button'

interface ModalProps {
  open: boolean
  onClose: () => void
  handleConfirm: () => void
}

const ConfirmClonePopOver: React.FC<ModalProps> = ({ open, onClose, handleConfirm }) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '440px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    // boxShadow: 24,
    pt: '20px',
    px: '20px',
    pb: '20px',
    zIndex: 1,
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.confirmButtonContent}>
          <div className={styles.confirmContent}>Are you sure want to proceed?</div>
          {/* <TypographyField sx="bodyBold" text={date ? date : ""} /> */}

          <div className={styles.reassignButtons}>
            <Button onClick={() => handleConfirm()}>Yes</Button>
            <Button color="secondary" onClick={() => onClose()}>
              No
            </Button>
          </div>
        </div>
      </Box>
    </Modal>
  )
}

export default ConfirmClonePopOver

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createSvp, deleteSvp, getSvps, updateSvp } from '../services/svp'
import { ISvpResponse, IUpdateSvp } from '../services/svp/interface'

export const QUERY_SVP = 'svp'

/**
 * Hook to fetch svp
 * @param enabled - Enables or disables the query
 */
export const useGetSvp = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<ISvpResponse>({
    queryKey: [QUERY_SVP],
    queryFn: getSvps,
    enabled,
  })

  return {
    svps: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a svp
 */
export const useCreateSvp = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createSvp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SVP] })
    },
  })
}

/**
 * Hook to delete a svp
 */
export const useDeleteSvp = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteSvp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SVP] })
    },
  })
}

/**
 * Hook to update a svp
 */
export const useUpdateSvp = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateSvp,
    onMutate: async (updatedData: IUpdateSvp) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_SVP] })
      const previousData = queryClient.getQueryData<ISvpResponse>([QUERY_SVP])
      // Optimistically update the cache
      queryClient.setQueryData<ISvpResponse>([QUERY_SVP], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, delivery_project_manager: updatedData.delivery_project_manager }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_SVP], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SVP] })
    },
  })
}

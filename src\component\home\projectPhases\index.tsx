import React from 'react'
import AddMilestoneNumber from './milestoneNumber'
import MitigationRecoveryPlan from './mitigationRecoveryPlan'
import NextStepsToAdvancedProgress from './nextStepsToAdvancedProgress'
import NonRecoverableDelayJustification from './nonRecoverableDelayJustification'
import ProjectPhaseCategory from './projectPhaseCategory'
import styles from './ProjectPhases.module.scss'
import AddProjectStageStatus from './projectStageStatus'
import AddProjectStatus from './projectStatus'
import AddSubStatuses from './projectSubStage'
import ReasonForDelay from './reasonForDelay'
import Drawer from '../../shared/drawer'
import TypographyField from '../../shared/typography'
import ImageCard from '../imageCard'
import { DrawerStates } from '../interface'
import AddProjectPhases from '../masterCards/projectPhases'
import { LAPTOP, MO<PERSON><PERSON> } from '@/src/constant/breakpoint'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'
import useModuleCount from '@/src/redux/moduleCount/useModuleCount'

interface IProjectPhasesProps {
  drawerStates: DrawerStates
  toggleDrawer?: (drawer: keyof DrawerStates) => (open: boolean) => void
}

type ImageCardItem = {
  label: string
  value: any
  drawer: keyof DrawerStates
  img: string
}

const drawerComponentsMap = {
  nonRecoverableDelayJustification: NonRecoverableDelayJustification,
  nextStepsToAdvancedProgress: NextStepsToAdvancedProgress,
  mitigationRecoveryPlan: MitigationRecoveryPlan,
  reasonForDelay: ReasonForDelay,
  addProjectPhases: AddProjectPhases,
  addProjectStatus: AddProjectStatus,
  addSubStatuses: AddSubStatuses,
  addProjectStageStatus: AddProjectStageStatus,
  projectPhaseCategory: ProjectPhaseCategory,
  milestoneNumber: AddMilestoneNumber,
}

const ProjectPhases: React.FC<IProjectPhasesProps> = ({ toggleDrawer, drawerStates }) => {
  const { moduleCounts } = useModuleCount()
  const breakpoint = useBreakpoint()

  const imageCards: ImageCardItem[] = [
    {
      label: 'Project Status',
      value: moduleCounts.project_phases?.project_status,
      drawer: 'addProjectStatus',
      img: 'status',
    },
    {
      label: 'Stage Status',
      value: moduleCounts.project_phases?.project_stage_status,
      drawer: 'addProjectStageStatus',
      img: 'stageStatus',
    },
    { label: 'Sub Stage', value: moduleCounts.project_phases?.sub_stage, drawer: 'addSubStatuses', img: 'subStatues' },
    {
      label: 'Reason For Delay',
      value: moduleCounts.project_phases?.reason_for_delay,
      drawer: 'reasonForDelay',
      img: 'subStatues',
    },
    {
      label: 'Mitigation Recovery Plan',
      value: moduleCounts.project_phases?.mitigation_recovery_plan,
      drawer: 'mitigationRecoveryPlan',
      img: 'subStatues',
    },
    {
      label: 'Next Steps To Advanced Progress',
      value: moduleCounts.project_phases?.next_steps_to_advance_progress,
      drawer: 'nextStepsToAdvancedProgress',
      img: 'subStatues',
    },
    {
      label: 'Non Recoverable Delay Justification',
      value: moduleCounts.project_phases?.non_recoverable_delay_justification,
      drawer: 'nonRecoverableDelayJustification',
      img: 'subStatues',
    },
    {
      label: 'Project Phase Category',
      value: moduleCounts.project_phases?.project_phase_category,
      drawer: 'projectPhaseCategory',
      img: 'subStatues',
    },
    {
      label: 'Milestone Number',
      value: moduleCounts.project_phases?.milestone_number,
      drawer: 'milestoneNumber',
      img: 'subStatues',
    },
  ]

  return (
    <div className={styles.cardContainer}>
      {/* Test below code */}
      <TypographyField variant="subheading" text="Project Phases" />
      <div className={styles.cards}>
        {imageCards
          .reduce((rows, item, index) => {
            const totalCards = breakpoint === LAPTOP ? 4 : breakpoint === MOBILE ? 1 : 3
            if (index % totalCards === 0) rows.push([]) // Start a new row every 3 items
            rows[rows.length - 1].push(item) // Push item correctly
            return rows
          }, [] as ImageCardItem[][])
          .map((row, rowIndex) => (
            <div key={rowIndex} className={styles.card}>
              {row.map(({ label, value, drawer, img }) => (
                <ImageCard
                  key={drawer} // Ensure a unique key
                  label={label}
                  value={value}
                  imageSrc={`/svg/${img}.svg`}
                  imageWidth={43}
                  imageHeight={50}
                  onAddButtonClick={() => toggleDrawer && toggleDrawer(drawer)(true)}
                />
              ))}
            </div>
          ))}
      </div>

      {Object.entries(drawerComponentsMap).map(([key, Component]) => (
        <Drawer
          key={key}
          anchor="right"
          open={drawerStates[key as keyof DrawerStates]}
          onClose={() => toggleDrawer && toggleDrawer(key as keyof DrawerStates)(false)}
        >
          <Component
            drawerStates={drawerStates}
            onClose={() => toggleDrawer && toggleDrawer(key as keyof DrawerStates)(false)}
          />
        </Drawer>
      ))}
    </div>
  )
}

export default ProjectPhases

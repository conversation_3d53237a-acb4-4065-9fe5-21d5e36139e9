import { IEntityCategoryPayload, IUpdateEntityCategory } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_ENTITY_CATEGORY_URL = `/master-entity-category`

// Get Master Entity Category data
export const getEntityCategory = async () => {
  const response = await api.get(`${MASTER_ENTITY_CATEGORY_URL}`)
  return response?.data
}

export const deleteEntityCategory = async (data: number) => {
  const response = await api.delete(`${MASTER_ENTITY_CATEGORY_URL}/${data}`)
  return response?.data
}

// Create Master Entity Category entry
export const createEntityCategory = async (data: IEntityCategoryPayload) => {
  const response = await api.post(`${MASTER_ENTITY_CATEGORY_URL}`, data)
  return response
}

// Update Master Entity Category entry
export const updateEntityCategory = async (data: IUpdateEntityCategory): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_ENTITY_CATEGORY_URL}/${data.id}`, { ...rest })
  return response
}

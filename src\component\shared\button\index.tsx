import React, { ReactElement } from 'react'
import { Button as ButtonMui, ButtonProps } from '@mui/material'
import styles from './Button.module.scss'

const Button: React.FC<ButtonProps> = ({ children, className, startIcon, endIcon, variant, ...rest }): ReactElement => {
  const getClass = () => {
    if (startIcon) {
      return variant === 'outlined' ? styles.outlineStart : styles.startIcon
    }
    if (endIcon) {
      return variant === 'outlined' ? styles.outlineEnd : styles.endIcon
    }
  }

  return (
    <ButtonMui
      className={`${styles.root} ${className} ${getClass()}`}
      startIcon={startIcon}
      endIcon={endIcon}
      variant={variant}
      {...rest}
    >
      {children}
    </ButtonMui>
  )
}

export default Button

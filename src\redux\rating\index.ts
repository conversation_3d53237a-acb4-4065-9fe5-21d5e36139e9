import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterRatingResponse, IRatingState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IRatingState = {
  getMasterRatingStatus: StatusEnum.Idle,
  addMasterRatingStatus: StatusEnum.Idle,
  deleteMasterRatingStatus: StatusEnum.Idle,
  updateMasterRatingStatus: StatusEnum.Idle,
  ratings: [],
  rating: {
    id: 0,
    rating: '',
  },
}

export const getMasterRatings = createAsyncThunk('/get-master-rating', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-rating`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterRatings = createAsyncThunk('/add-master-rating', async (data: { rating: string }, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/master-rating`, {
      rating: data.rating,
    })
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateMasterRatings = createAsyncThunk(
  '/update-master-rating',
  async (data: { id: number; rating: string }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/master-rating/${data.id}`, {
        rating: data.rating,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteMasterRatings = createAsyncThunk('/delete-master-rating', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/master-rating/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const ratingSlice = createSlice({
  name: 'rating',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterRatings
    builder.addCase(getMasterRatings.pending, (state) => {
      state.getMasterRatingStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterRatings.fulfilled, (state, action) => {
      state.getMasterRatingStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterRatingResponse
      state.ratings = actionPayload.data
    })
    builder.addCase(getMasterRatings.rejected, (state) => {
      state.getMasterRatingStatus = StatusEnum.Failed
    })
    //addMasterRatings
    builder.addCase(addMasterRatings.pending, (state) => {
      state.addMasterRatingStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterRatings.fulfilled, (state, action) => {
      state.addMasterRatingStatus = StatusEnum.Success
    })
    builder.addCase(addMasterRatings.rejected, (state) => {
      state.addMasterRatingStatus = StatusEnum.Failed
    })
    //updateMasterRatings
    builder.addCase(updateMasterRatings.pending, (state) => {
      state.updateMasterRatingStatus = StatusEnum.Pending
    })
    builder.addCase(updateMasterRatings.fulfilled, (state, action) => {
      state.updateMasterRatingStatus = StatusEnum.Success
    })
    builder.addCase(updateMasterRatings.rejected, (state) => {
      state.updateMasterRatingStatus = StatusEnum.Failed
    })
    //deleteMasterRatings
    builder.addCase(deleteMasterRatings.pending, (state) => {
      state.deleteMasterRatingStatus = StatusEnum.Pending
    })
    builder.addCase(deleteMasterRatings.fulfilled, (state, action) => {
      state.deleteMasterRatingStatus = StatusEnum.Success
    })
    builder.addCase(deleteMasterRatings.rejected, (state) => {
      state.deleteMasterRatingStatus = StatusEnum.Failed
    })
  },
})

export const {} = ratingSlice.actions

// Export the reducer
export default ratingSlice.reducer

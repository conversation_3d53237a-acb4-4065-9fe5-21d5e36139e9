.summaryTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 8px 20px;
  background: #eeeeec;
  border-radius: 0 8px 8px 0;
  font-size: 16px;
  font-weight: 700;
  line-height: 18px;
  color: #444444;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  padding-right: 8px;
  min-width: 212px;
}

.buttonPlus {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  border: 1px solid #444444;
  background: #444444;
  width: 20px;
  height: 20px;
}

.expandCollapseGroup {
  gap: 6px;
  width: fit-content !important;
}

.expandedCollapse {
  width: 120px !important;
  margin-right: 12px !important;
}
.content {
  padding-left: 0px !important;
}

.overallPhasereplacebutton {
  width: 250px;
  margin-right: 12px !important;
}

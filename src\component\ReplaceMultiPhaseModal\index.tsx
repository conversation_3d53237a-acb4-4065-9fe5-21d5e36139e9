import React from 'react'
import styles from './replaceMultiphaseModal.module.scss'
import Loader from '../shared/loader'
import PulseModel from '../shared/pulseModel'
import { sortData } from '../shared/tanStackTable/helper'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import { convertValuesToCommaSeparated, getUniquePhases } from '@/src/helpers/helpers'
import usePhase from '@/src/redux/phase/usePhase'

interface IConfirmDeleteModal {
  open: boolean
  onClose: () => void
  handleConfirm: () => void
  selectedPhase: any
  isSubmittingPhase: boolean
  setSelectedPhase: (data: any) => void
}

const ReplaceMultiPhaseModal: React.FC<IConfirmDeleteModal> = ({
  open,
  onClose,
  handleConfirm,
  selectedPhase,
  setSelectedPhase,
  isSubmittingPhase,
}) => {
  const { uniquePhaseCategories } = usePhase()

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    minWidth: '500px',
    maxWidth: '500px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    // boxShadow: 24,
    pt: '20px',
    px: '20px',
    pb: '10px',
    zIndex: 1,
  }

  const comboBoxOptions = Array.from(
    new Map(
      sortData(uniquePhaseCategories, 'category').map((item) => [
        `${item.category} / ${item.phase}`,
        {
          label: `${item.category} / ${item.phase}`,
          value: item.phase,
          id: item.id,
        },
      ]),
    ).values(),
  )

  return (
    <PulseModel
      style={style}
      open={open}
      closable={false}
      onClose={onClose}
      content={
        <div className={styles.confirmButtonContent}>
          <div className={styles.title}>Select phase</div>
          <div className={styles.selectFieldWrapper}>
            <ComboBox
              className={styles.selectField}
              focusCustomClass={styles.focusClass}
              options={comboBoxOptions}
              labelText="Select the phase that required to replace with multiple phases *"
              placeholder="Select phase..."
              value={selectedPhase}
              clearIcon={true}
              onChange={(val) => {
                setSelectedPhase(val || null)
              }}
            />

            <div className={styles.actionButtons}>
              <Button color="secondary" onClick={handleConfirm} disabled={!selectedPhase?.id || isSubmittingPhase}>
                <span className={styles.actionButtonMultiphase}>
                  {isSubmittingPhase ? <Loader smallLoader={true} /> : 'Submit'}
                </span>
              </Button>
              <Button color="secondary" onClick={onClose}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      }
    />
  )
}

export default ReplaceMultiPhaseModal

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './PricingType.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreatePricingType,
  useDeletePricingType,
  useGetPricingType,
  useUpdatePricingType,
} from '@/src/hooks/usePricingType'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddPricingType: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { pricingType } = drawerStates
  //REACT-QUERY
  const { pricingTypes } = useGetPricingType(pricingType)
  // MUTATIONS
  const { mutate: addPricingType } = useCreatePricingType()
  const { mutate: deletePricingType } = useDeletePricingType()
  const { mutate: updatePricingType } = useUpdatePricingType()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { pricingType: '' },
    onSubmit: async (values) => {
      const payload = { pricing_type: values.pricingType }
      if (editIndex !== null) {
        updatePricingType(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addPricingType(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deletePricingType(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPricingType: any = pricingTypes.find((pricingType) => pricingType.id === id)
    formik.setValues({ pricingType: editPricingType?.pricing_type })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'pricing_type',
      header: 'Pricing Type',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Pricing Type'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="pricingType"
              labelText={'Pricing Type'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.pricingType}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.pricingType?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.pricingType?.trim()}
              >
                {'+ Add Pricing Type'}
              </Button>
            )}
          </div>
        </form>
        {pricingTypes?.length >= 1 && (
          // <Table data={pricingTypes} columns={columns} />
          <TanStackTable rows={sortData(pricingTypes, 'pricing_type') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddPricingType

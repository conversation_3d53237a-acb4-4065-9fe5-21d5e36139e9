import React, { ReactElement, useEffect, useRef, useState } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { LocalizationProvider, PickersActionBarProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
// import styles from './DatePicker.module.scss'
import Button from '../../../button'
import TypographyField from '../../../typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import { DARK_200 } from '@/src/constant/color'
import { parseDateFromString } from '@/src/utils/dateUtils'

interface MuiDatePickerProps {
  labelText?: string
  value: string | number | Date | null
  onChange?: (date: any) => void
  className?: any
  minDate?: string | number | Date | null
  maxDate?: string | number | Date
  error?: boolean
  helperText?: string
  disableEditable?: boolean
  onAccept?: (value: any) => void
  placeholder?: string
  onError?: (reason?: string | null) => void
  shouldDisableDate?: any
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: any
  onBlur?: any
  actionButtons?: any
  CustomDay?: any
  shouldCloseOnSelect?: boolean
}

const CellDatePicker: React.FC<MuiDatePickerProps> = ({
  labelText,
  value,
  onChange = () => null,
  className,
  minDate,
  maxDate,
  error = false,
  helperText,
  disableEditable = false,
  onAccept,
  onBlur,
  placeholder = '',
  onError = () => null,
  shouldDisableDate,
  actionButtons = false,
  disabled = false,
  required = false,
  name = '',
  sx,
  CustomDay,
  ...props
}): ReactElement => {
  const dateValue = value ? parseDateFromString(value as string) : '-'
  const pickerRef = useRef<HTMLDivElement>(null)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        // Close the date picker
        !isOpen && onAccept && onAccept(dateValue)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [dateValue, onAccept])

  return (
    <div style={{ width: '100%' }} className={className} ref={pickerRef}>
      {labelText && (
        <div>
          <TypographyField style={{ color: DARK_200 }} variant="caption" text={labelText} />
        </div>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          {...props}
          open={isOpen}
          onClose={() => setIsOpen(false)}
          format="dd-MM-yyyy"
          closeOnSelect
          value={dateValue || null}
          disabled={disabled}
          onChange={(val) => {
            onChange(val ? val : null)
          }}
          onAccept={(value) => onAccept && onAccept(value)}
          slots={{
            openPickerIcon: () => <CalendarIcon />,
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay && CustomDay,
          }}
          slotProps={{
            layout: {
              sx: {
                display: 'block',
                textAlign: 'end',
              },
            },
            textField: {
              placeholder: placeholder || 'dd/MM/yyyy',
              sx: {
                '& .Mui-error': {
                  borderBottom: '1px solid #ffffff',
                },
                ...sx,
              },
            },
            openPickerButton: {
              onClick: () => {
                setIsOpen(!isOpen)
              },
              onMouseDown: (e) => {
                e.preventDefault()
              },
            },
          }}
        />
      </LocalizationProvider>
    </div>
  )
}

export default CellDatePicker

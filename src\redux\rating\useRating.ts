import { useDispatch, useSelector } from 'react-redux'
import { getMasterRatings, addMasterRatings, updateMasterRatings, deleteMasterRatings } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useRating = () => {
  const dispatch: AppDispatch = useDispatch()

  const getMasterRatingStatus = useSelector((state: RootState) => state.rating.getMasterRatingStatus)
  const ratings = useSelector((state: RootState) => state.rating.ratings)

  const getMasterRatingsApi = () => dispatch(getMasterRatings())
  const addMasterRatingsApi = (data: { rating: string }) => dispatch(addMasterRatings(data))
  const updateMasterRatingsApi = (data: { id: number; rating: string }) => dispatch(updateMasterRatings(data))
  const deleteMasterRatingsApi = (data: number) => dispatch(deleteMasterRatings(data))

  return {
    getMasterRatingStatus,
    ratings,
    getMasterRatingsApi,
    addMasterRatings<PERSON><PERSON>,
    updateMasterRatings<PERSON><PERSON>,
    deleteMasterRatings<PERSON><PERSON>,
  }
}
export default useRating

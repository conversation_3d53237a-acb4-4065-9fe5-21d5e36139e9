import { useDispatch, useSelector } from 'react-redux'
import {
  addProjectToBeCompletedIN,
  deleteProjectToBeCompletedIN,
  getProjectToBeCompletedIN,
  updateProjectToBeCompletedIN,
} from '.'
import { IProjectToBeCompletedIn } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useProjectToBeComplatedIn = () => {
  const dispatch: AppDispatch = useDispatch()

  const getProjectToBeCompletedInStatus = useSelector(
    (state: RootState) => state.projectToBeComplatedIn.getProjectToBeCompletedInStatus,
  )

  const projectToBeCompletedIns = useSelector(
    (state: RootState) => state.projectToBeComplatedIn.projectToBeCompletedIns,
  )

  const getProjectToBeCompletedInApi = (data: { period: string }) => dispatch(getProjectToBeCompletedIN(data))
  const addProjectToBeCompletedInApi = (data: IProjectToBeCompletedIn) => dispatch(addProjectToBeCompletedIN(data))
  const updateProjectToBeCompletedInApi = (data: { id: number; data: IProjectToBeCompletedIn }) =>
    dispatch(updateProjectToBeCompletedIN(data))
  const deleteProjectToBeCompletedInApi = (data: number) => dispatch(deleteProjectToBeCompletedIN(data))

  return {
    getProjectToBeCompletedInStatus,
    projectToBeCompletedIns,
    getProjectToBeCompletedInApi,
    addProjectToBeCompletedInApi,
    updateProjectToBeCompletedInApi,
    deleteProjectToBeCompletedInApi,
  }
}
export default useProjectToBeComplatedIn

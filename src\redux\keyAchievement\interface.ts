import { StatusEnum } from '../types'

export interface IKeyAchievementsStatus {
  getKeyAchievementStatus: StatusEnum
  addKeyAchievementStatus: StatusEnum
  deleteKeyAchievementStatus: StatusEnum
  updateKeyAchievementStatus: StatusEnum
  keyAchievementSortStatus: StatusEnum
  keyAchievements: IKeyAchievement[]
  keyAchievement: IKeyAchievement
  formData: any
}

export interface IKeyAchievement {
  id?: number
  period?: string
  project_name?: string
  phase?: string
  start_date?: string
  end_date?: string
  key_achievments_delim?: string
  next_plan_delim?: string
  last_updated?: Date
}

export interface IGetKeyAchievementResponse {
  data: IKeyAchievement[]
  message: string
  success: boolean
}

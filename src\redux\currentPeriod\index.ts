import { createSlice } from '@reduxjs/toolkit'

export interface CurrentPeriodState {
  currentPeriod: string
  isPeriodChangeFromProjectList: boolean
}

const initialState: CurrentPeriodState = {
  currentPeriod: '',
  isPeriodChangeFromProjectList: false,
}

const currentPeriodReducer = createSlice({
  name: 'currentPeriodData',
  initialState,
  reducers: {
    setPeriod(state, action) {
      state.currentPeriod = action.payload
    },
    setIsPeriodChangeFromProjectList(state, action) {
      state.isPeriodChangeFromProjectList = action.payload
    },
  },
})

export const { setPeriod, setIsPeriodChangeFromProjectList } = currentPeriodReducer.actions

// Export the reducer
export default currentPeriodReducer.reducer

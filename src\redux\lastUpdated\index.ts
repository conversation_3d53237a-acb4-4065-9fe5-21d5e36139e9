import { createSlice } from '@reduxjs/toolkit'
import { ILastUpdatedState } from './interface'

const initialState: ILastUpdatedState = {
  lastUpdateOfProgress: [],
}

export const lastUpdatedSlice = createSlice({
  name: 'lastUpdated',
  initialState,
  reducers: {
    // add last update data of status to indicate to ribbon
    addLastUpdateOfProgress(state, action) {
      state.lastUpdateOfProgress = action.payload
    },
  },
})

export const { addLastUpdateOfProgress } = lastUpdatedSlice.actions

// Export the reducer
export default lastUpdatedSlice.reducer

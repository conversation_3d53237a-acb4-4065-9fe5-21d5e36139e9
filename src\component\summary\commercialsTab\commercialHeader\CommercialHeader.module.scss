// _CommercialAccordionHeader.module.scss

@import '/styles/color.scss';

.summaryTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.accordianHeader {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 8px 20px;
  background: #eeeeec;
  border-radius: 0 8px 8px 0;
  font-size: 16px;
  font-weight: 700;
  line-height: 18px;
  color: #444444;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  padding-right: 8px;
  min-width: 212px;
}

.detailsAction {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  position: relative;
  margin-right: 8px;

  .action {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .lastUpdatedSection {
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-end;
  }
}

.timerIcon {
  display: flex;
  align-items: center;
  gap: 4px;
}

.deleteIcon {
  cursor: pointer;
}

@import '/styles/color.scss';

.avatarPicker {
  display: flex;
  // align-items: center;
  // justify-content: center;
  .avatarButton {
    border: none;
    background: transparent;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.avatarStack {
  position: relative;
  display: flex;
  width: 50px;
  cursor: pointer;
}

.activeAvatar {
  > div {
    border: 1px solid #706d6d;
  }
}

.stackedAvatar:hover {
  transform: scale(1.1);
}

.avatarPopup {
  padding: 13px 15px 15px 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  min-width: 300px;
  max-width: 350px;
  min-height: 295px;
  box-sizing: border-box;
}

.avatarSearch {
  margin-bottom: 10px;
}

.avatarCategories {
  display: grid;
  grid-template-columns: repeat(5, 1fr); // 5 columns
  gap: 10px;
  margin-top: 10px;
  align-content: start; // Ensures it aligns properly

  .childAvatar {
    margin-top: 5px;
    margin-left: 5px;
    position: relative;
    width: 45px;
    height: 45px;
    > div {
      width: 37px;
      height: 37px;
      > img {
        color: transparent;
        position: absolute;
        top: -7.8px;
        left: -4.1px;
      }
    }
  }
}

.categoryAvatar {
  border-radius: 25px;
  border: 3.5px solid transparent;
  cursor: pointer;
  transition: transform 0.2s;
  // &:hover {
  //   transform: scale(1.1);
  // }
}

.avatarBorder {
  border-radius: 25px;
  border: 3.5px solid $PULSE_TURQUOISE !important;
}

.avatarItem {
  border-radius: 25px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(0.8);
  }
}

.avatarGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  width: 100%;
  height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  align-content: start;
  padding-right: 10px;
  .childAvatar {
    width: 35px;
    height: 35px;
    margin-top: 5px;
    margin-left: 5px;
    // > div {
    //   width: 30px;
    //   height: 30px;
    // }
  }
  &::-webkit-scrollbar {
    width: 3px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 5px;
  }
}

.avatarItem {
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.3);
  }
}

.childAvatar::after {
  content: '';
  position: absolute;
  right: -10px;
  top: 4px;
  height: 80%;
  width: 1px;
  background-color: #ccc;
}

.childAvatar:nth-child(5n)::after {
  display: none;
}

.avatarLength {
  position: absolute;
  right: -49px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgb(151 151 151 / 70%);
  color: white;
  font-size: 14px;
  font-weight: 300;
  padding: 9px 5px;
  border-radius: 50px;
  min-width: 28px;
  text-align: center;
  z-index: 4;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.imgContainer {
  border-radius: 25px;
  border: 3px solid transparent;
  width: 34px;
  height: 34px;
  margin-left: 5px;
  border-radius: 50%;
  position: relative;
  > img {
    color: transparent;
    position: absolute;
    top: -7px;
    left: -3.5px;
  }
}

// ---------- Animated Tooltip ------------

.childAvatarContainer {
  position: relative;
  margin-right: -1rem;
}

.avatarContainer {
  position: relative;
  margin-right: -1rem;
}

.avatarContainer::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 8px;
  height: 65%;
  width: 1px;
  background-color: #ccc;
}

.avatarContainer:nth-child(5n)::after {
  display: none;
}

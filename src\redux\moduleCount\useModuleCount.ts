import { useDispatch, useSelector } from 'react-redux'
import { getModuleCount } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useModuleCount = () => {
  const dispatch: AppDispatch = useDispatch()

  const getModuleCountStatus = useSelector((state: RootState) => state.moduleCount.getModuleCountStatus)
  const moduleCounts = useSelector((state: RootState) => state.moduleCount.moduleCounts)

  const getModuleCountApi = (data: { period: string }) => dispatch(getModuleCount(data))

  return {
    getModuleCountStatus,
    moduleCounts,
    getModuleCountApi,
  }
}
export default useModuleCount

import { StatusEnum } from '../types'

export interface IConsultantStatus {
  getCommercialStatus: StatusEnum
  addCommercialStatus: StatusEnum
  deleteCommercialStatus: StatusEnum
  updateCommercialStatus: StatusEnum
  commercials: ICommercial[]
  commercial: ICommercial
}

export interface ICommercial {
  id?: number
  period?: string
  project_name: string
  description: string
  budget: number
  committed_cost: number
  construction_budget: number
  supervision_budget: number
  management_fees: number
  contingency: number
  year_planned_vowd: number
  year_forecast_vowd: number
  vowd: number
  paid_amount: number
  last_updated: Date
}

export interface IGetCommercialsResponse {
  data: ICommercial[]
  message: string
  success: true
}

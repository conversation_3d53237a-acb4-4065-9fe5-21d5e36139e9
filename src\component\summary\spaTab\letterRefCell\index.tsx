import { useRef, useState } from 'react'
import { PictureAsPdfOutlined, AddCircleOutline, WarningAmberOutlined } from '@mui/icons-material'
import { CircularProgress, IconButton, Tooltip, Typography } from '@mui/material'
import { toast } from 'sonner'
import styles from './LetterRefCell.module.scss'
import { ILetterReferenceCell } from '../interface'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import { FILE_SIZE_ERROR_MESSAGE, isFileSizeValid } from '@/src/utils/fileUtils'
import { errorToast } from '@/src/utils/toastUtils'

const LetterReferenceCell: React.FC<ILetterReferenceCell> = ({
  value,
  attachments,
  onUpload,
  onDownload,
  isEditForUser = true,
}) => {
  const [fileLoader, setFileLoader] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  return (
    <div className={styles.letterReference} style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
      {/* Hidden File Input */}
      <input
        type="file"
        accept="application/pdf"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file && file.type === 'application/pdf') {
            const isValidFileSize = isFileSizeValid(file)
            if (!isValidFileSize) {
              errorToast(FILE_SIZE_ERROR_MESSAGE)
              return
            }

            setFileLoader(true)
            onUpload(
              e,
              () => setFileLoader(true),
              () => setFileLoader(false),
            )
          } else {
            if (fileInputRef.current) {
              fileInputRef.current.value = ''
            }
            toast('Please upload only PDF file')
          }
        }}
      />

      {/* Download Icon with Tooltip */}
      {attachments?.length > 0 && (
        <Tooltip
          title={
            <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column' }}>
              <span style={{ fontFamily: 'Poppins' }}>Click to download</span>
              <span style={{ fontFamily: 'Poppins', fontSize: '12px' }}>{attachments[0]?.name}</span>
            </div>
          }
          arrow
        >
          <IconButton
            onClick={(e) => {
              e.stopPropagation()
              onDownload()
            }}
            sx={{
              color: '#ea3323',
              transition: 'transform 0.2s ease-in-out',
              '&:hover': { transform: 'scale(1.2)' },
            }}
          >
            <PictureAsPdfOutlined />
          </IconButton>
        </Tooltip>
      )}

      {/* Upload Icon with Tooltip */}
      <Tooltip title="Upload a PDF file" arrow>
        <IconButton
          onClick={() => {
            if (!isEditForUser) {
              toast(`The current reporting period is locked`, {
                icon: <WarningAmberOutlined />,
              })
              return
            }
            fileInputRef.current?.click()
          }}
          sx={{
            fontFamily: 'Poppins',
            color: 'black',
            transition: 'transform 0.2s ease-in-out',
            '&:hover': { transform: 'scale(1.2)', color: '#007bff' },
          }}
        >
          {fileLoader ? <CircularProgress size={20} /> : <AddCircleOutline />}
        </IconButton>
      </Tooltip>

      {/* File Information */}
      <Typography sx={{ fontSize: '12px' }}>
        {' '}
        {/* {value || ''} */}
        <LineClampWithTooltip lineNumber={2}>{value || ''}</LineClampWithTooltip>
      </Typography>
    </div>
  )
}

export default LetterReferenceCell

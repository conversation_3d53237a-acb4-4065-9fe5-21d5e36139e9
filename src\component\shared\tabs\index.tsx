import React, { ReactElement, useEffect, useState } from 'react'
import { Tabs as TabsMui, Tab, TabProps } from '@mui/material'
import styles from './Tabs.module.scss'

export interface ITabPanels {
  label: string
  icon?: JSX.Element
  position?: TabProps['iconPosition']
  disabled?: boolean
}

interface ITabsProps {
  tabPanels: Array<ITabPanels>
  selectedTab?: (tab: number) => void
  children: React.ReactNode[]
  className?: string
  endContent?: JSX.Element | null
  activeTab?: number
}

const Tabs: React.FC<ITabsProps> = ({
  tabPanels = [],
  children = [],
  className,
  selectedTab,
  endContent,
  activeTab,
}): ReactElement => {
  const [value, setValue] = useState(activeTab ?? 0)

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
    selectedTab && selectedTab(newValue)
  }

  useEffect(() => {
    if (activeTab !== undefined) {
      setValue(activeTab)
    }
  }, [activeTab])

  return (
    <>
      <TabsMui
        value={value}
        onChange={handleChange}
        aria-label="basic tabs example"
        classes={{
          root: `${className} ${styles.root}`,
          flexContainer: styles.flexContainer,
          indicator: styles.indicator,
        }}
      >
        {tabPanels.map(({ label, icon, position = 'start', disabled }, index) => (
          <Tab
            icon={icon}
            iconPosition={position}
            key={label}
            label={<p className={styles.label}>{label}</p>}
            disabled={disabled}
            classes={{ root: styles.tabRoot, selected: styles.selected }}
            id={`simple-tab-${index}`}
            aria-controls={`simple-tabpanel-${index}`}
          />
        ))}
        {endContent && endContent}
      </TabsMui>
      {children.map((child: React.ReactNode, index: number) => {
        return (
          <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            key={index}
          >
            {value === index && <div>{child}</div>}
          </div>
        )
      })}
    </>
  )
}

export default Tabs

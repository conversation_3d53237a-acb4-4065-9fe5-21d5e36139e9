import * as React from 'react'
import Slider from '@mui/material/Slider'
import { styled } from '@mui/material/styles'
import Tooltip from '@mui/material/Tooltip'

interface CustomSliderProps {
  value: number[]
  onChange: (event: Event, newValue: number | number[], activeThumb: number) => void
  minDistance: number
  ariaLabel: string
  min: number
  max: number
}

const RangeTooltip = styled(Tooltip)(({ theme }) => ({
  fontSize: theme.typography.pxToRem(12),
}))

const RangeSelector: React.FC<CustomSliderProps> = ({ value, onChange, minDistance, ariaLabel, min, max }) => {
  const handleSliderChange = (event: Event, newValue: number | number[], activeThumb: number) => {
    onChange(event, newValue, activeThumb)
  }

  const handleValueText = (value: number) => {
    return `${value}`
  }

  return (
    <Slider
      value={value}
      onChange={handleSliderChange}
      valueLabelDisplay="auto"
      getAriaLabel={() => ariaLabel}
      getAriaValueText={handleValueText}
      min={min}
      max={max}
      disableSwap
      step={1}
      valueLabelFormat={handleValueText}
      sx={{
        height: 2,
        '& .MuiSlider-thumb': {
          width: 24,
          height: 24,
          backgroundColor: '#fff',
          border: '2px solid currentColor',
          '&:focus, &:hover, &.Mui-active': {
            boxShadow: 'inherit',
          },
        },
        '& .MuiSlider-track': {
          height: 4,
        },
        '& .MuiSlider-rail': {
          height: 4,
        },
      }}
    />
  )
}

export default RangeSelector

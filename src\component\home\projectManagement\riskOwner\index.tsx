import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './RiskOwner.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useCreateRiskOwner, useDeleteRiskOwner, useGetRiskOwner, useUpdateRiskOwner } from '@/src/hooks/useRiskOwner'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddRiskOwner: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { riskOwner } = drawerStates
  //REACT-QUERY
  const { riskOwners } = useGetRiskOwner(riskOwner)
  // MUTATIONS
  const { mutate: addRiskOwner } = useCreateRiskOwner()
  const { mutate: deleteRiskOwner } = useDeleteRiskOwner()
  const { mutate: updateRiskOwner } = useUpdateRiskOwner()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { riskOwner: '' },
    onSubmit: async (values) => {
      const payload = { risk_owner: values.riskOwner }
      if (editIndex !== null) {
        updateRiskOwner(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addRiskOwner(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteRiskOwner = async (id: number) => {
    deleteRiskOwner(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editRiskOwner = riskOwners.find((riskOwner) => riskOwner.id === id)
    if (editRiskOwner) {
      formik.setValues({ riskOwner: editRiskOwner.risk_owner })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'risk_owner',
      header: 'Risk Owner',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: any) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.row.id)} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Risk Owner'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="riskOwner"
              labelText={'Risk Owner'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.riskOwner}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskOwner?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskOwner?.trim()}
              >
                {'+ Add Risk Owner'}
              </Button>
            )}
          </div>
        </form>
        {riskOwners.length >= 1 ? (
          // <Table data={riskOwners} columns={columns} />
          <TanStackTable rows={sortData(riskOwners, 'risk_owner') as any} columns={columns} isOverflow={true} />
        ) : (
          ''
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteRiskOwner(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddRiskOwner

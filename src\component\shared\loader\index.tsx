import React, { CSSProperties } from 'react'
import styles from './Loader.module.scss'

interface LoaderProps {
  smallLoader?: boolean
  width?: string
  style?: CSSProperties
}

const Loader: React.FC<LoaderProps> = ({ smallLoader = false, width, style }) => {
  return (
    <div className={styles.container} style={{ width: width, ...style }}>
      <div className={smallLoader ? styles.smallLoader : styles.loader}></div>
    </div>
  )
}

export default Loader

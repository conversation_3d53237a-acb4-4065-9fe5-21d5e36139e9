import { createTheme } from '@mui/material/styles'
import {
  ERROR,
  GRAY_500,
  DARK,
  LIGHT,
  LIGHT_200,
  WHITE,
  PULSE_GRAY,
  PULSE_LIGHT_GRAY,
  PULSE_TURQUOISE,
} from '@/src/constant/color'

const fontFamily = 'Poppins'

export const theme = createTheme({
  palette: {
    primary: {
      main: DARK,
    },
    secondary: {
      main: WHITE,
    },
  },
  components: {
    //customize theme for button
    MuiButton: {
      defaultProps: { size: 'large', color: 'primary', variant: 'text' },
      styleOverrides: {
        root: {
          fontFamily: fontFamily,
          textTransform: 'initial',
          boxShadow: 'none',
          padding: '7px 9px ',
          '&:hover': { boxShadow: 'none' },
          fontSize: '14px',
          fontWeight: 400,
          lineHeight: '20px',
          '& .MuiTouchRipple-root': {
            display: 'none',
          },
        },
        endIcon: {
          marginLeft: '4px',
          marginRight: 0,
          '& svg': { fontSize: '20px !important' },
        },
        startIcon: {
          marginRight: '6px',
          marginLeft: '1px',
          '& svg': { fontSize: '20px !important' },
        },
      },
      variants: [
        {
          props: { color: 'primary' },
          style: {
            width: 'fit-content',
            border: 'none',
            backgroundColor: DARK,
            color: LIGHT_200,
            '&:hover': {
              backgroundColor: DARK,
            },
            '&.Mui-disabled': {
              opacity: '30%',
              color: LIGHT_200,
            },
          },
        },
        {
          props: { color: 'secondary' },
          style: {
            width: 'fit-content',
            backgroundColor: WHITE,
            border: `1px solid ${LIGHT_200}`,
            color: DARK,
            // "&:hover": {
            //   backgroundColor: WHITE,
            //   border: `1px solid ${LIGHT_200}`,
            //   color: DARK,
            // },
            '&.Mui-disabled': {
              opacity: '30%',
            },
          },
        },
        {
          props: { variant: 'outlined' },
          style: {
            width: 'fit-content',
            backgroundColor: `${WHITE}`,
            border: `1px solid ${PULSE_GRAY}`,
            color: DARK,
            '&:hover': {
              backgroundColor: `${WHITE}`,
              border: `1px solid ${PULSE_GRAY}`,
              color: DARK,
            },
            '&.Mui-disabled': {
              opacity: '50%',
              width: 'fit-content',
              backgroundColor: `${WHITE}`,
              border: `1px solid ${PULSE_GRAY}`,
              color: DARK,
            },
            endIcon: {
              marginLeft: '4px',
              marginRight: 0,
              color: DARK,

              '& svg': { fontSize: '20px !important' },
            },
            startIcon: {
              marginRight: '6px',
              marginLeft: '1px',
              color: DARK,
              '& svg': { fontSize: '20px !important' },
            },
          },
        },
        {
          props: { variant: 'contained' },
          style: {
            width: 'fit-content',
            backgroundColor: `${PULSE_TURQUOISE}`,
            // border: `1px solid ${PULSE_GRAY}`,
            color: WHITE,
            '&:hover': {
              backgroundColor: `${PULSE_TURQUOISE}`,
              // border: `1px solid ${PULSE_GRAY}`,
              color: WHITE,
            },
            '&.Mui-disabled': {
              opacity: '50%',
              width: 'fit-content',
              backgroundColor: `${PULSE_TURQUOISE}`,
              // border: `1px solid ${PULSE_GRAY}`,
              color: WHITE,
            },
            endIcon: {
              marginLeft: '4px',
              marginRight: 0,
              color: WHITE,

              '& svg': { fontSize: '20px !important' },
            },
            startIcon: {
              marginRight: '6px',
              marginLeft: '1px',
              color: WHITE,
              '& svg': { fontSize: '20px !important' },
            },
          },
        },
        {
          props: { variant: 'outlined', color: 'secondary' },
          style: {
            width: 'fit-content',
            border: 'none',
            backgroundColor: WHITE,
            color: DARK,
            // "&:hover": {
            //   backgroundColor: WHITE,
            //   color: DARK,
            //   border: "none",
            // },
            '&.Mui-disabled': {
              opacity: '30%',
            },
          },
        },
      ],
    },
    // customize theme for input field
    MuiTextField: {
      defaultProps: { variant: 'outlined' },
      styleOverrides: {
        root: {
          '& .MuiInputBase-input': {
            fontFamily: fontFamily,
            fontSize: '14px',
            lineHeight: '20px',
            color: DARK,
            fontWeight: 400,
            padding: '10px 12px',
            height: 'unset',
            textOverflow: 'ellipsis',
            '&::placeholder': {
              color: GRAY_500,
              opacity: '50%',
              fontWeight: 400,
              fontSize: '14px',
              lineHeight: '20px',
            },
          },
        },
      },
      variants: [
        {
          props: { variant: 'outlined' },
          style: {
            width: '100%',
            borderRadius: '4px',
            '& .MuiInputBase-root': {
              backgroundColor: LIGHT,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 0,
              borderWidth: '0',
            },
            // '& .MuiInputBase-root:hover': {
            //   '& .MuiOutlinedInput-notchedOutline': { borderColor: GREY_500 },
            // },
            // '& .Mui-focused': { '& .MuiOutlinedInput-notchedOutline': { border: `1px solid ${BLUE_500} !important` } },
            '& .Mui-disabled': {
              // background: BLUE_50,
              '-webkit-text-fill-color': `${DARK} !important`,
              // '& .MuiOutlinedInput-notchedOutline': { border: `1px solid ${GREY_100}` },
            },
            // '& .MuiOutlinedInput-root': {
            //   '&.Mui-error': { '& .MuiOutlinedInput-notchedOutline': { border: `1px solid ${ERROR_500}` } },
            // },
            '& .Mui-error': {
              '&.MuiFormHelperText-root': {
                color: ERROR,
              },
            },
            '& .MuiFormHelperText-root': {
              color: ERROR,
              margin: '0',
              fontFamily: 'Poppins',
              textAlign: 'left',
              fontSize: '12px',
              lineHeight: '18px',
              fontWeight: 400,
              background: 'transparent',
              display: '-webkit-box',
              '-webkit-line-clamp': '1',
              '-webkit-box-orient': 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          },
        },
        {
          props: { variant: 'outlined', error: true }, // Define styles for error variant
          style: {
            width: '100%',
            borderRadius: '4px',
            padding: 'opx',
            '& .MuiInputBase-root': {
              backgroundColor: '#FAFAFA',
              border: `1px solid ${ERROR}`,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 0,
              borderWidth: '0',
            },
            '& .Mui-error': {
              '&.MuiFormHelperText-root': {
                color: ERROR,
              },
            },
            '& .MuiFormHelperText-root': {
              color: DARK,
              margin: '0',
              fontSize: '12px',
              lineHeight: '18px',
              fontWeight: 400,
              background: 'transparent',
              display: '-webkit-box',
              '-webkit-line-clamp': '1',
              '-webkit-box-orient': 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          },
        },
      ],
    },
    // customize theme for tabs
    MuiTabs: {
      styleOverrides: { root: { minHeight: '32px', fontFamily: fontFamily } },
    },
    MuiTypography: {
      // Corrected property name
      defaultProps: {
        fontFamily: [fontFamily].join(','),
      },
      variants: [
        {
          props: { variant: 'h2' }, //SubHeading in figma
          style: {
            fontSize: '16px',
            fontWeight: 700,
            lineHeight: '24px',
            color: DARK,
          },
        },

        {
          props: { variant: 'body1' }, //body in figma
          style: {
            fontSize: '14px',
            fontWeight: 400,
            lineHeight: '20px',
            letterSpacing: '0px',

            color: DARK,
          },
        },

        {
          props: { variant: 'h5' }, //body in heading5
          style: {
            fontSize: '18px',
            fontWeight: 600,
            lineHeight: '23px',
            letterSpacing: '0px',

            color: DARK,
          },
        },

        {
          props: { variant: 'caption' }, //body in heading5
          style: {
            fontSize: '12px',
            fontWeight: 400,
            lineHeight: '18px',
            letterSpacing: '0px',
            color: DARK,
          },
        },
      ],
    },
  },
})

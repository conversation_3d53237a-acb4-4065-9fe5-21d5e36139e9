import { StatusEnum } from '../types'

export interface IProjectKpiStates {
  getProjectKpiStates: StatusEnum
  addProjectKpiStates: StatusEnum
  deleteProjectKpiStates: StatusEnum
  updateProjectKpiStates: StatusEnum
  projectKpis: IProjectKpi[]
  projectKpi: IProjectKpi
}

export interface IProjectKpi {
  id?: any
  period?: string
  project_name?: string
  is_executive_kpi?: Boolean
  project_type?: string
  kpi?: string
  kpi_value?: number
  last_updated?: Date
}

export interface IGetProjectKpisResponse {
  data: IProjectKpi[]
  message: string
  success: true
}

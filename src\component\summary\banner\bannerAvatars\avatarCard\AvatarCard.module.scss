@import '/styles/color.scss';

.avatarCard {
  text-wrap: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 7px;

  .avatarDetails {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .avatarRole {
      font-size: 12px;
      font-weight: 600;
      line-height: 18px;
      color: $BLACK;
      opacity: 100%;
      width: fit-content;
      // white-space: break-spaces;
    }

    .avatarOccupation {
      font-size: 10px;
      font-weight: 400;
      line-height: 15px;
      color: $NEUTRAL_400;
      width: fit-content;
    }
  }
}

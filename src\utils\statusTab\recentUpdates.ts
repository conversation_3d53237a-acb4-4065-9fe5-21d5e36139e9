import { IRecentStatusUpdate } from './interface'
import { isWithinLast7DaysInArray } from '../../component/summary/statusTab/statusService'
import { findProjectManagementData } from '../../component/updateProgress/helper'
import { IProjectManagement } from '../../redux/projectManagement/interface'
import { IStatus } from '../../redux/status/interface'
import { getLastUpdate as extractLastUpdateDate } from '../dateUtils'

// Generic filtering function
const filterByProject = <T extends { project_name?: string }>(data: T[], projectName: string): T[] =>
  data.filter((item) => item.project_name === projectName)

// Function to check if the last update was within the last 7 days
const wasUpdatedWithinLast7Days = (status: IStatus, projectManagements: IProjectManagement[]): boolean => {
  const projectManagement = filterByProject(projectManagements, status.project_name as string)
  const projectManagementData: IProjectManagement = findProjectManagementData(
    projectManagement,
    status.LookupProjectToPhase,
    status?.MasterProjectStageStatus,
    status?.MasterProjectSubStage,
  )

  const lastUpdatedDates = [
    extractLastUpdateDate(projectManagementData?.last_updated?.toString() as string),
    extractLastUpdateDate(status.last_updated?.toString() as string),
  ]

  return isWithinLast7DaysInArray(lastUpdatedDates)
}

// Main function to generate processed statuses
export const isRecentStatusUpdate = (
  projectName: string,
  statuses: IStatus[],
  projectManagements: IProjectManagement[],
): IRecentStatusUpdate[] =>
  statuses
    .filter((status) => status.project_name === projectName)
    .map((status) => ({
      projectName,
      statusId: status.id ?? null,
      is7Days: wasUpdatedWithinLast7Days(status, projectManagements),
    }))

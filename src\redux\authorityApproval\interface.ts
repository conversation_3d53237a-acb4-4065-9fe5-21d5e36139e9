import { StatusEnum } from '../types'

export interface IAuthorityApprovalState {
  getAuthorityApprovalStatus: StatusEnum
  addAuthorityApprovalStatus: StatusEnum
  deleteAuthorityApprovalStatus: StatusEnum
  updateAuthorityApprovalStatus: StatusEnum
  authorities: IAuthorityApproval[]
  authority: IAuthorityApproval
}
export interface IAuthorityApproval {
  id?: number
  period?: string
  project_name?: string
  phase?: string
  design_enoc_forecast_date?: string
  design_enoc_status?: string
  tpd_forecast_date?: string
  enoc_status?: string
  enoc_forecast_date?: string
  last_updated?: string
  tpd_status?: string
  description?: string
}

export interface IGetMasterConsultantsResponse {
  data: IAuthorityApproval[]
  message: string
  success: true
}

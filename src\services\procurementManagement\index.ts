import { IProcureManagementPayload, IUpdateProcureManagement } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROCUREMENT_MANAGER_URL = `/master-procurement-manager`

// Get Master Procurement Manager data
export const getMasterProcurementManager = async () => {
  const response = await api.get(`${MASTER_PROCUREMENT_MANAGER_URL}`)
  return response?.data
}
export const deleteProcurementManager = async (data: number) => {
  const response = await api.delete(`${MASTER_PROCUREMENT_MANAGER_URL}/${data}`)
  return response?.data
}

// Create Master Procurement Manager entry
export const createMasterProcurementManager = async (data: IProcureManagementPayload) => {
  const response = await api.post(`${MASTER_PROCUREMENT_MANAGER_URL}`, data)
  return response
}

// Update Master Procurement Manager entry
export const updateMasterProcurementManager = async (data: IUpdateProcureManagement): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROCUREMENT_MANAGER_URL}/${data.id}`, { ...rest })
  return response
}

import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'

interface IStatuses {
  id: string | number
  stageStatus: string
  phaseWeightage: string
  [key: string]: string | number
}

//* Check total phase weightage of exiting record
// export const checkPhaseWeighage = (statuses: IStatuses[], currentStage: IStatuses) => {
//   const cloneStatuses = [...statuses]
//   const currentStageStatus = currentStage.stageStatus
//   const sameStageStatusDate = cloneStatuses.filter((item) => item.stageStatus === currentStage.stageStatus)
//   if (currentStageStatus === 'Design') {
//   } else {
//     const totalValue = phaseWeighageTotalBySameStageStatus(sameStageStatusDate)
//     return totalValue
//   }
// }

// //* Calculate the total phase weighage for all which have sam
// const phaseWeighageTotalBySameStageStatus = (data: any, multiplier: number = 100) => {
//   const totalPhaseWeighageSum = data.reduce((sum: any, item: any) => sum + Number(item.phaseWeightage) * multiplier, 0)
//   const totalValue = totalPhaseWeighageSum?.toFixed(weightageDecimal)
//   return totalValue
// }

// //TODO : Manage multi phase order
// //TODO : GET DATA Which Have Same Category and Same Phase
// const checkPhaseWeighageByDesignStageStatus = () => {}

// //TODO :
// const combinationExist = () => {

// }

//TODO : Manage for no data
export function isSameCombination(
  a: string | undefined,
  b: string | undefined,
  separator = MULTI_SELECT_SEPARATOR,
): boolean {
  // if (!a || !b) return false
  const arrA =
    a ||
    ''
      .split(separator)
      .map((s) => s.trim())
      .filter(Boolean)
  const arrB =
    b ||
    ''
      .split(separator)
      .map((s) => s.trim())
      .filter(Boolean)
  if (arrA.length !== arrB.length) return false
  const setA = new Set(arrA)
  const setB = new Set(arrB)
  if (setA.size !== setB.size) return false
  for (const item of setA) {
    if (!setB.has(item)) return false
  }
  return true
}

//TODO : Manage for no data With string and array.
export function isSamearrayCombination(
  a: number[] | undefined,
  b: number[] | number | undefined,
  separator = MULTI_SELECT_SEPARATOR,
): boolean {
  // if (!a || !b) return false
  const arrA = a?.map((s) => s)
  const arrB = (b && Array.isArray(b) ? b : [b])?.map((s) => s)
  if (arrA?.length !== arrB?.length) return false
  const setA = new Set(arrA)
  const setB = new Set(arrB)
  if (setA.size !== setB.size) return false
  for (const item of setA) {
    if (!setB.has(item)) return false
  }
  return true
}

export const weightageDecimal = 2

export function findMatchingRecordByKey<T extends Record<string, any>>(
  array: T[],
  values: Record<string, string>,
  separator: string = MULTI_SELECT_SEPARATOR,
): T | undefined {
  const keys = Object.keys(values)
  return array.find((item) => keys.every((key) => isSameCombination(item[key], values[key], separator)))
}

/**
 * Returns matching or non-matching records from array based on isSameCombination for all keys in values.
 * @param array The array to filter
 * @param values The key-value pairs to match
 * @param separator The separator for multi-select
 * @param match If true, returns matching records; if false, returns non-matching records
 */
export function filterMatchingRecordByKey<T extends Record<string, any>>(
  array: T[],
  values: Record<string, string>,
  separator: string = MULTI_SELECT_SEPARATOR,
  match: boolean = true,
): T[] {
  const keys = Object.keys(values)
  return array.filter((item) => {
    const isMatch = keys.every((key) => isSameCombination(item[key], values[key], separator))
    return match ? isMatch : !isMatch
  })
}

export function cleanNumber(input: string | number, decimal: number = weightageDecimal): number {
  const num = Number(input)
  if (isNaN(num)) {
    return 0
  }

  const formattedNum = parseFloat(num.toString()).toFixed(decimal)
  return Number(formattedNum)
}

/**
 * @description Converts a number to a string with the specified number of decimal places.
 * @param num - 100, 80, 0.25
 * @param decimal - The number of decimal places to keep (default is 4).
 * @returns 1, 0.8, 0.0025
 */
export function numberWithPrecision(num: number, decimal: number = 4): string {
  return cleanNumber(Number(cleanNumber(num)) / 100, decimal).toString()
}

/**
 * Calculates the total and required phase weightage for a given stage, deduplicating by (phase, category) combination.
 * @param records - All records (statuses)
 * @param current - The current record (form values)
 * @param options - Optional: separator and keys
 * @returns { total: number, required: number, uniqueRecords: T[] }
 */
export function getPhaseWeightageTotalAndRequired<T extends Record<string, any>>(
  records: T[],
  current: T,
  options?: {
    separator?: string
    phaseKey?: string
    categoryKey?: string
    stageStatusKey?: string
    weightageKey?: string
  },
  multiplier: number = 100,
): { total: number; required: number; uniqueRecords: T[] } {
  const {
    separator = MULTI_SELECT_SEPARATOR,
    phaseKey = 'phase',
    categoryKey = 'project_phase_category',
    stageStatusKey = 'stage_status',
    weightageKey = 'phase_weightage',
  } = options || {}
  // 1. Filter out the current record (by id if present)
  const filtered = records.filter((item) => !current.id || String(item.id) !== String(current.id))

  // 2. Filter for same stage_status
  const sameStage = filtered.filter((item) => item[stageStatusKey] === current[stageStatusKey])
  const uniqueRecords: T[] = []
  for (const item of sameStage) {
    const findItem = uniqueRecords.find(
      (i) =>
        isSameCombination(i[phaseKey], item[phaseKey], separator) &&
        isSameCombination(i[categoryKey], item[categoryKey], separator),
    )
    if (!findItem) {
      uniqueRecords.push(item)
    }
  }

  // 3. Now filter uniqueRecords for same stage_status, but both phase and category should be different
  const finalRecords = uniqueRecords.filter(
    (item) =>
      item[stageStatusKey] === current[stageStatusKey] &&
      !(
        isSameCombination(item[phaseKey], current[phaseKey], separator) &&
        isSameCombination(item[categoryKey], current[categoryKey], separator)
      ),
  )
  // 4. Calculate total and required
  const total = cleanNumber(finalRecords.reduce((sum, item) => sum + (item[weightageKey] || 0) * multiplier, 0))
  // Required is 100 minus total
  const required = cleanNumber(Math.max(0, 100 - total))
  return { total, required, uniqueRecords }
}

/**
 * Calculates the total and required design stage weightage for a given phase/category/stage combination.
 * Deduplicates using isSameCombination for phase and category, and matches stage.
 * @param records - All records (statuses)
 * @param current - The current record (form values)
 * @param options - Optional: separator and keys
 * @returns { total: number, required: number, uniqueRecords: T[] }
 */
export function getDesignStageWeightageTotalAndRequired<T extends Record<string, any>>(
  records: T[],
  current: T,
  options?: {
    separator?: string
    phaseKey?: string
    categoryKey?: string
    stageStatusKey?: string
    weightageKey?: string
  },
  multiplier: number = 100,
): { total: number; required: number; matching: T[] } {
  const {
    separator = MULTI_SELECT_SEPARATOR,
    phaseKey = 'project_to_project_phase_ids',
    categoryKey = 'project_to_project_phase_category_ids',
    stageStatusKey = 'master_project_stage_status_id',
    weightageKey = 'design_stage_weightage',
  } = options || {}
  // 1. Filter out the current record (by id if present)
  const filtered = records.filter((item) => !current.id || String(item.id) !== String(current.id))

  // 2. Filter for same phase, category, and stage (using isSameCombination)
  const matching = filtered.filter(
    (item) =>
      isSameCombination(item[phaseKey], current[phaseKey], separator) &&
      isSameCombination(item[categoryKey], current[categoryKey], separator) &&
      item[stageStatusKey] === current[stageStatusKey],
  )

  // 3. Deduplicate by (phase, category) combination (should be unique after above filter, but keep for safety)
  // const uniqueRecords: T[] = []
  // for (const item of matching) {
  //   const findItem = uniqueRecords.find(
  //     (i) =>
  //       isSameCombination(i[phaseKey], item[phaseKey], separator) &&
  //       isSameCombination(i[categoryKey], item[categoryKey], separator),
  //   )
  //   if (!findItem) {
  //     uniqueRecords.push(item)
  //   }
  // }

  // 4. Calculate total and required
  const total = cleanNumber(matching.reduce((sum, item) => sum + (item[weightageKey] || 0) * multiplier, 0))
  const required = cleanNumber(Math.max(0, 100 - total))
  return { total, required, matching }
}

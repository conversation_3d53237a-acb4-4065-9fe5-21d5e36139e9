@import '/styles/color.scss';

.endAdornment {
  margin-right: 0px !important;
  .endAdornmentIcon {
    text-align: center;
    color: $BLACK;
  }
}

.inputFields {
  > div > div {
    border: 1px solid #f2f2f2 !important;
  }
  margin-bottom: 10px;
  input {
    border-radius: 4px !important;
    background-color: #f2f2f2 !important;
  }
  div {
    background-color: #f2f2f2 !important;
  }
}

.highlightField {
  input {
    background-color: #f0f8ff !important;
    border-radius: 4px !important;
  }
  div > div {
    border: 1px solid rgb(40 101 220) !important;
    background-color: #f0f8ff !important;

    > div {
      border: none !important;
    }
  }
}

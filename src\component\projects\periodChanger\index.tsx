import React, { useState } from 'react'
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers'
import { format, isValid } from 'date-fns'
import { useRouter } from 'next/router'
import DatePeriodSelector from './DatePeriodSelector'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'

const CustomDay: React.FC<PickersDayProps<Date>> = (props) => {
  const { allPeriods: allowedDates } = useMasterPeriod() // Get the current period date
  const date = new Date(props.day)

  const formattedDate: any = format(date, 'yyyy-MM-dd')
  const isAllowed = allowedDates.includes(formattedDate)
  // const isCurrent = period && formattedDate === period

  return (
    <PickersDay
      {...props}
      disabled={!isAllowed}
      sx={{
        backgroundColor: isAllowed ? '#c5c5c5' : undefined, // Highlight allowed/current dates
        color: isAllowed ? '#000' : undefined,
        borderRadius: '50%',
        '&:hover': {
          backgroundColor: isAllowed ? '#808080' : undefined, // Prevent hover color change
        },
      }}
    />
  )
}

const PeriodChanger: React.FC = () => {
  const {
    currentPeriod,
    setPeriodApi,
    allPeriods: allowedDates,
    setIsPeriodChangeFromProjectListApi,
  } = useMasterPeriod()
  const router = useRouter()

  const shouldDisableDate = (date: Date) => {
    const dateStr: any = date.toISOString().split('T')[0]
    return !allowedDates?.includes(dateStr) // Disable dates not in allowedDates
  }

  return (
    <div>
      <DatePeriodSelector
        value={currentPeriod}
        onChange={(val: any) => {
          if (!val || isNaN(new Date(val).getTime())) {
            return
          }
          const date = new Date(val)
          const formattedDate = format(date, 'yyyy-MM-dd')
          setPeriodApi(formattedDate)
          if (router.route === '/projects') {
            setIsPeriodChangeFromProjectListApi(true)
          }
        }}
        onClose={() => setPeriodApi(currentPeriod)}
        showActionButtons={true}
        shouldDisableDate={shouldDisableDate}
        CustomDay={CustomDay} // Pass the custom day component
        sx={{
          display: 'content',
          '& .MuiOutlinedInput-root': {
            backgroundColor: 'transparent',
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            borderRadius: 0,
          },
          '& .MuiOutlinedInput-input': {
            width: '120px',
            padding: '6px 0px 6px 8px',
            fontWeight: '400',
            fontSize: '12px',
            lineHeight: '18px',
            color: 'black',
            display: 'none',
          },
          '& .MuiButtonBase-root': {
            top: '13px',
            left: '-17px',
            width: 'unset',
          },
          '& .Mui-disabled': {
            fontWeight: '400',
            fontSize: '12px',
            lineHeight: '18px',
            color: 'black !important',
            '& .MuiInputBase-input': {
              '-webkit-text-fill-color': 'black',
            },
          },
          '& .Mui-focused': {
            '& .MuiOutlinedInput-root': {
              backgroundColor: '#f4f4fc',
            },
          },
        }}
      />
    </div>
  )
}

export default PeriodChanger

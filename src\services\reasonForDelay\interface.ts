export interface IReasonForDelayPayload {
  reason_for_delay: string
  reason_for_delay_code: string
}

export interface IUpdateReasonForDelay extends IReasonForDelayPayload {
  id: number
}

export interface IReasonForDelay {
  id: number
  reason_for_delay: string
  reason_for_delay_code: string
}

export interface IGetReasonForDelayResponse {
  data: IReasonForDelay[]
  message: string
  success: true
}

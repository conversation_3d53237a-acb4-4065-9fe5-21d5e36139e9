// SearchDrawer.module.scss
@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.mainContainer {
  width: 0;
  position: fixed;
  background-color: white;
  box-shadow: 0px 4px 200px 0px #00000026;
  height: calc(100vh - 53px);
  z-index: 100;
  left: 50px; // Default for mobile
  top: 30px;
  opacity: 0;
  transition: 0.5s ease;
  border-radius: 0px 15px 15px 0px;

  @include respond-to('tablet') {
    left: 60px;
    border-radius: 0px 20px 20px 0px;
  }

  @include respond-to('laptop') {
    left: 71px;
    border-radius: 0px 25px 25px 0px;
  }

  @include respond-to('mobile') {
    left: 75px;
    border-radius: 0px 25px 25px 0px;
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .searchComponent {
    display: flex;
    align-items: center;
    margin: 10px;
    padding: 0 5px;

    @include respond-to('tablet') {
      margin: 10px;
      padding: 0 10px;
    }
    @include respond-to('laptop') {
      margin: 10px;
      padding: 0 5px;
    }
  }
}

.searchField {
  width: 100%;

  // Adjust input size for different screens
  :global(.MuiInputBase-root) {
    height: 40px;

    @include respond-to('tablet') {
      height: 45px;
    }

    @include respond-to('laptop') {
      height: 50px;
    }
  }
}

.listContainer {
  cursor: pointer;
  gap: 8px;
  display: flex;
  align-items: center;
  transition:
    background-color 0.2s,
    transform 0.2s;
  border-radius: 5px;
  padding: 5px;
  z-index: 100;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
  }
  // @include respond-to('tablet') {
  //   margin: 10px;
  //   padding: 8px;
  // }

  .selectList {
    width: 1px;
    height: 40px;

    @include respond-to('tablet') {
      height: 45px;
    }

    @include respond-to('laptop') {
      height: 50px;
    }

    background: $PULSE_TURQUOISE;
  }

  .listItem {
    width: 100%;
    word-break: break-word;
    font-size: 14px;

    @include respond-to('tablet') {
      font-size: 16px;
    }
  }
}

.searchList {
  margin-right: 5px;
  margin-left: 5px;
  margin-bottom: 10px;
  overflow-y: auto;
  height: calc(100vh - 150px);

  @include respond-to('tablet') {
    margin-right: 10px;
    margin-left: 10px;
    height: calc(100vh - 160px);
  }

  @include respond-to('laptop') {
    height: calc(100vh - 171px);
  }

  .cursorDefault {
    cursor: default;
  }

  &::-webkit-scrollbar {
    width: 3px;

    @include respond-to('tablet') {
      width: 4px;
    }
  }

  &::-webkit-scrollbar-track {
    border-radius: 5px;
    width: 3px;

    @include respond-to('tablet') {
      width: 4px;
    }
  }

  &::-webkit-scrollbar-thumb {
    background: #dddddd;
    border-radius: 5px;
    width: 3px;

    @include respond-to('tablet') {
      width: 4px;
    }
  }
}

.popup {
  position: absolute;
  z-index: 1299;

  .title {
    font-size: 12px;

    @include respond-to('tablet') {
      font-size: 14px;
    }
  }

  .multiAutoSelect > div > div {
    flex-wrap: nowrap !important;
  }
}

.filterBox {
  display: flex;
  align-items: center;
}

.applyFilter {
  justify-content: space-between;
  display: flex;
  margin-top: 15px;
  padding-right: 9px;

  @include respond-to('tablet') {
    margin-top: 20px;
  }

  button {
    font-size: 12px;
    padding: 6px 12px;

    @include respond-to('tablet') {
      font-size: 14px;
      padding: 8px 16px;
    }
  }
}

.count {
  display: flex;
  font-size: 12px;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 5px 10px 0 0;

  @include respond-to('tablet') {
    font-size: 14px;
    padding: 10px 20px 0 0;
  }
}

.filterIcon {
  cursor: pointer;
  height: 24px !important;
  width: 24px !important;
  color: $PULSE_TURQUOISE;
  transition: all 0.2s ease-in-out;
  margin-left: 5px;

  @include respond-to('tablet') {
    height: 26px !important;
    width: 26px !important;
    margin-left: 8px;
  }

  @include respond-to('laptop') {
    height: 28px !important;
    width: 28px !important;
  }

  &:hover {
    transform: scale(1.1);
    color: darken($PULSE_TURQUOISE, 10%);
  }
}

// Add responsive styles for PopupBody
:global(.MuiPopup-root) {
  .PopupBody {
    width: 200px;
    padding: 10px 12px;

    @include respond-to('tablet') {
      width: 220px;
      padding: 12px 14px;
    }

    @include respond-to('laptop') {
      width: 250px;
      padding: 12px 16px;
    }
  }
}

// Add mobile styles for opened drawer
.drawerOpen {
  width: calc(100vw - 100px);
  opacity: 1;

  @include respond-to('tablet') {
    width: 320px;
  }

  @include respond-to('laptop') {
    width: 350px;
  }

  @include respond-to('desktop') {
    width: 400px;
  }
}

.endClearAdornmentIcon {
  cursor: pointer;
  &:hover {
    transform: scale(1.05);
  }
}

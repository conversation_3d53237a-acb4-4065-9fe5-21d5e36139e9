import React, { useState } from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import LockIcon from '@mui/icons-material/Lock'
import Head from 'next/head'
import Image from 'next/image'
import { useRouter } from 'next/router'
import styles from './login.module.scss'
import aldarLogo from '../../public/svg/aldarLogo.svg'
import Button from '@/src/component/shared/button'
import { StorageKeys } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import { removeLocalStorageItem } from '@/src/utils/storageUtils'

const mailSubject = 'Request for Access to Pulse'
const mailBody = `Hi Team,

I would like to request access to the pulse platform. Please let me know if any additional information is required.

You can also find the service portal here:
https://sd.aldar.com/app/itdesk/ui/ssp/pages/home

Thanks`

const Login = () => {
  const router = useRouter()
  const { authUrlApi } = useAuthorization()
  const { setRecentProject } = useProjectSummary()
  const serviceEmailAddress = '<EMAIL>'

  const [toastDisplay, setToastDisplay] = useState(false)

  const mailtoLink = `mailto:${serviceEmailAddress}?subject=${encodeURIComponent(
    mailSubject,
  )}&body=${encodeURIComponent(mailBody)}`

  const handleLogin = async () => {
    removeLocalStorageItem(StorageKeys.IS_LOGIN)
    setRecentProject({ projectName: '' })
    const redirectUrl = `${window.location.origin}/redirect`
    const res: Record<string, any> = await authUrlApi(redirectUrl)
    if (res?.payload.exitCode === 138) {
      setToastDisplay(true)
    } else {
      if (!res.payload.success) return
      window.location.href = res.payload.data
      // router.replace(res.payload.data);`
    }
  }

  return (
    <div className={styles.container}>
      <Head>
        <title>Login</title>
        <meta name="description" content="Login" />
      </Head>
      <div className={`${styles.card} ${styles.overlay}`}>
        <div className={styles.contain}>
          <div className={styles.fields}>
            <Image src={aldarLogo} height={100} width={100} alt="aldar" />
          </div>
          <h1>Welcome to PULSE</h1>
          <Button color="secondary" onClick={() => handleLogin()} className={styles.button}>
            Login
          </Button>

          {toastDisplay && (
            <div className={styles.requestAccessSection}>
              <div className={styles.requestAccessContent}>
                <div className={styles.requestAccessLinks}>
                  <a
                    href={mailtoLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`${styles.requestAccessLink} ${styles.portalLink}`}
                    aria-label="Visit ALDAR IT Service Desk Portal"
                  >
                    Request to Access
                    <ArrowForwardIcon />
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {toastDisplay && (
        <div className={styles.popupOverlay}>
          <div className={styles.popupContainer} onClick={(e) => e.stopPropagation()}>
            <div className={styles.popupHeader}>
              <div className={styles.popupIcon}>
                <LockIcon className={styles.lockIconLarge} />
              </div>
            </div>

            <div className={styles.popupContent}>
              <h2 className={styles.popupTitle}>Access Denied</h2>
              <p className={styles.popupMessage}>
                Access to this application is not authorized for you. Please request access to this application.
              </p>
            </div>
            <div className={styles.requestAccessSectionpopup}>
              <div className={styles.requestAccessContent}>
                <div className={styles.requestAccessLinks}>
                  <a
                    href={mailtoLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`${styles.requestAccessLink} ${styles.portalLink}`}
                    aria-label="Visit ALDAR IT Service Desk Portal"
                  >
                    Request to Access
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Login

import React from 'react'
import { Modal, Box, SxProps, Theme } from '@mui/material'
import styles from './PulseModel.module.scss'

interface IPulseModel {
  open: boolean
  closable?: boolean
  onClose: () => void
  content: React.ReactNode
  style?: SxProps<Theme>
}

const PulseModel: React.FC<IPulseModel> = ({ open, closable = true, onClose, content, style }) => {
  const defaultStyle = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    padding: '20px',
    zIndex: 1,
    ...style,
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      onClose={() => closable && onClose()}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <Box sx={defaultStyle}>{content}</Box>
    </Modal>
  )
}

export default PulseModel

import React, { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import styles from './projectPhases.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import Table from '@/src/component/shared/table'
import { ITableColumn } from '@/src/component/shared/table/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { IPhase } from '@/src/redux/phase/interface'
import usePhase from '@/src/redux/phase/usePhase'

const AddProjectPhases: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)

  const [editIndex, setEditIndex] = useState<number | null>(null)
  const {
    getMasterPhaseStatus,
    phases,
    getMasterPhaseApi,
    addMasterPhaseApi,
    updateMasterPhaseApi,
    deleteMasterPhaseApi,
  } = usePhase()

  const { addProjectPhases } = drawerStates

  useEffect(() => {
    addProjectPhases && getMasterPhaseApi()
  }, [addProjectPhases])

  const formik = useFormik({
    initialValues: { phase: '' },
    onSubmit: async (values) => {
      if (editIndex !== null) {
        const response: Record<string, any> = await updateMasterPhaseApi({
          id: editIndex,
          project_phase: values.phase,
        })
        if (response.payload.success === true) getMasterPhaseApi()
        setEditIndex(null)
      } else {
        const response: Record<string, any> = await addMasterPhaseApi({ project_phase: values.phase })
        if (response.payload.success === true) getMasterPhaseApi()
      }
      formik.resetForm()
    },
  })

  const handleDeleteStatus = async (id: number) => {
    const response: Record<string, any> = await deleteMasterPhaseApi(id)
    if (response.payload.success === true) {
      getMasterPhaseApi()
      setDeleteModel(null)
    }
  }

  const handleEditButtonClick = (id: number) => {
    const selectedProjectStatus: IPhase | null = phases?.find((item: any) => item.id === id) || null
    if (selectedProjectStatus) {
      formik.setValues({ phase: selectedProjectStatus?.project_phase })
      setEditIndex(id)
    }
  }

  const columns: ITableColumn[] = [
    {
      key: 'project_phase',
      label: 'phase',
    },
    {
      key: 'action',
      label: 'Action',
      cellStyle: {
        width: '40px',
      },
      renderCell: (value: unknown, row: Record<string, any>) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add phase'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} color="secondary" onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {phases?.length >= 1 && <Table data={phases} columns={columns} />}
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.statusField}
              name="phase"
              labelText={'Phase'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.phase}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.phase?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.phase?.trim()}
              >
                {'+ Add Phase'}
              </Button>
            )}
          </div>
        </form>
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteStatus(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddProjectPhases

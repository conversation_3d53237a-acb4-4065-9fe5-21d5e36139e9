.portalTooltip {
  /* Fixed position ensures it's rendered at the exact coordinates we specify */
  position: fixed;
  /* High z-index to ensure it appears above everything */
  z-index: 9999;
  /* Other styling from your existing tooltip */

  .tooltip {
    position: relative;
    z-index: 50;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    background-color: white;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    pointer-events: none; /* Ensures the tooltip doesn't interfere with mouse events */

    .tooltipGradient1 {
      position: absolute;
      inset-inline: 2.5rem;
      bottom: -1px;
      z-index: 30;
      height: 1px;
      width: 20%;
    }

    .tooltipGradient2 {
      position: absolute;
      bottom: -1px;
      left: 2.5rem;
      z-index: 30;
      height: 1px;
      width: 40%;
    }

    .tooltipText {
      position: relative;
      z-index: 9999;
      font-size: 12px;
      font-weight: bold;
      color: black;
    }
  }
}

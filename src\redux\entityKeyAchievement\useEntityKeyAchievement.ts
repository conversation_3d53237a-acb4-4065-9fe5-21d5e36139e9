import { useDispatch, useSelector } from 'react-redux'
import {
  addEntityKeyAchievement,
  deleteEntityKeyAchievement,
  getEntityKeyAchievement,
  updateEntityKeyAchievement,
} from '.'
import { IEntityKeyAchievement } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useEntityKeyAchievement = () => {
  const dispatch: AppDispatch = useDispatch()

  const getEntityKeyAchievementStatus = useSelector(
    (state: RootState) => state.entityKeyAchievement.getEntityKeyAchievement,
  )

  const entityKeyAchievements = useSelector((state: RootState) => state.entityKeyAchievement.entityKeyAchievements)

  const getEntityKeyAchievementApi = (data: { period: string }) => dispatch(getEntityKeyAchievement(data))
  const addEntityKeyAchievementApi = (data: IEntityKeyAchievement) => dispatch(addEntityKeyAchievement(data))
  const updateEntityKeyAchievementApi = (data: { id: number; data: IEntityKeyAchievement }) =>
    dispatch(updateEntityKeyAchievement(data))
  const deleteEntityKeyAchievementApi = (data: number) => dispatch(deleteEntityKeyAchievement(data))

  return {
    getEntityKeyAchievementStatus,
    entityKeyAchievements,
    getEntityKeyAchievementApi,
    addEntityKeyAchievementApi,
    updateEntityKeyAchievementApi,
    deleteEntityKeyAchievementApi,
  }
}
export default useEntityKeyAchievement

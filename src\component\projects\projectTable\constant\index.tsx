export const globalSearchArray = [
  { accessorKey: 'sortNumber', header: 'Entity Sorting', isSelect: false },
  { accessorKey: 'project', header: 'Project Name', isSelect: false },
  { accessorKey: 'owningEntity', header: 'Owning Entity', isSelect: false },
  { accessorKey: 'project_id', header: 'ERP Project ID', isSelect: false },
  { accessorKey: 'cpds_project_id', header: 'CPDS ID', isSelect: false },
  { accessorKey: 'dof_number', header: 'DOF No', isSelect: false },
  { accessorKey: 'project_classification', header: 'Classification', isSelect: false },
  { accessorKey: 'project_status', header: 'Project Status', isSelect: false },
  { accessorKey: 'project_type', header: 'Project Type ', isSelect: false },
  { accessorKey: 'location', header: 'Location' },
  { accessorKey: 'portfolio_manager', header: 'Portfolio Manager', isSelect: false },
  { accessorKey: 'design_project_manager', header: 'Design Manager', isSelect: false },
  { accessorKey: 'controls_manager', header: 'Controls Manager', isSelect: false },
  { accessorKey: 'procurement_manager', header: 'Procurement Manager', isSelect: false },
  { accessorKey: 'design_executive_director', header: 'Design Executive Director', isSelect: false },
  { accessorKey: 'delivery_manager', header: 'Delivery Executive Director', isSelect: false },
  { accessorKey: 'startYear', header: 'Start Year', isSelect: false },
  { accessorKey: 'director', header: 'Delivery Director', isSelect: false },
  { accessorKey: 'delivery_project_manager', header: 'Delivery PM', isSelect: false },
]

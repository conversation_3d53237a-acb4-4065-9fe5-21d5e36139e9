import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterNstAdvancedProgress,
  deleteMasterNstAdvancedProgress,
  getMasterNstAdvancedProgresses,
  updateMasterNstAdvancedProgress,
} from '../services/nextStepsToAdvancedProgress'
import {
  IGetMasterNstAdvancedProgressRes,
  IUpdateNstAdvancedProgress,
} from '../services/nextStepsToAdvancedProgress/interface'

export const QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS = 'next-steps-to-advance-progress'

/**
 * Hook to fetch NextAdvancedProgresses
 * @param enabled - Enables or disables the query
 */
export const useGetNstAdvancedProgresses = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterNstAdvancedProgressRes>({
    queryKey: [QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS],
    queryFn: getMasterNstAdvancedProgresses,
    enabled,
  })

  return {
    nextStepsToAdvancedProgresses: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a NextAdvancedProgresses
 */
export const useCreateNstAdvancedProgresses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterNstAdvancedProgress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS] })
    },
  })
}

/**
 * Hook to delete a NextAdvancedProgresses
 */
export const useDeleteNstAdvancedProgresses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterNstAdvancedProgress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS] })
    },
  })
}

/**
 * Hook to update a NextAdvancedProgresses
 */
export const useUpdateNstAdvancedProgresses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterNstAdvancedProgress,
    onMutate: async (updatedData: IUpdateNstAdvancedProgress) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS] })
      const previousData = queryClient.getQueryData<IGetMasterNstAdvancedProgressRes>([
        QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS,
      ])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterNstAdvancedProgressRes>([QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? {
                  ...manager,
                  next_steps_to_advance_progress: updatedData.next_steps_to_advance_progress,
                  next_steps_to_advance_progress_code: updatedData.next_steps_to_advance_progress_code,
                }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEXT_STEPS_TO_ADVANCE_PROGRESS] })
    },
  })
}

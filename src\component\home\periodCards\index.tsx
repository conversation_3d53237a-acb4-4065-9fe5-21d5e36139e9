import React, { useState, useMemo, useEffect } from 'react'
import Lock<PERSON>ersonIcon from '@mui/icons-material/LockPerson'
import { Drawer } from '@mui/material'
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers'
import { isSameDay } from 'date-fns'
import { io, Socket } from 'socket.io-client'
import { toast } from 'sonner'
import FinalFreezePeriodModel from './finalFreezePeriodModel'
import NewsFeedsDrawer from './newsFeedsDrawer'
import PeriodConfirm from './periodConfirm'
import PeriodDatePicker from './periodDatepicker'
import styles from './Periods.module.scss'
import Scoring from './scoring'
import Button from '../../shared/button'
import LockableSwitch from '../../shared/switchToggle'
import TypographyField from '../../shared/typography'
import ImageCard from '../imageCard'
import { DrawerStates } from '../interface'
import { BaseURL } from '@/src/api'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

let socket: Socket
let freezPerminantId: string | number | null = null
const PERMANENT_FREEZE_SYNC_KEY = 'permanent_freeze_in_progress'

interface FreezeStatusProps {
  freezeType: string
  currentPeriod: string
  handleSwitchChange: () => void
}

interface PermanentFreezeButtonProps {
  setIsPermanentModel: (value: boolean) => void
  setSelectedAction: (value: string) => void
  freezeType: string
}

interface AddPeriodCardProps {
  currentPeriod: string
}

const CustomDay = ({ day, ...props }: PickersDayProps<Date>) => {
  const highlightedDays = useMemo(() => [new Date('2024-04-10')], [])
  const matchedStyles = highlightedDays.reduce((a, v) => {
    return isSameDay(new Date(day), v) ? { backgroundColor: '#999999' } : a
  }, {})

  return <PickersDay {...props} day={day} sx={{ ...matchedStyles }} />
}

const FreezeStatus: React.FC<FreezeStatusProps> = ({ freezeType, currentPeriod, handleSwitchChange }) => (
  <div className={styles.freezeContainer}>
    <div className={styles.freezeType}>
      <div>Status:</div>
      <div className={styles.boldText}>
        {currentPeriod ? (freezeType === 'userFreeze' ? 'Frozen' : 'Active') : 'No Period'}
      </div>
    </div>
    {currentPeriod && <LockableSwitch checked={freezeType === 'userFreeze'} onChange={handleSwitchChange} />}
  </div>
)

const PermanentFreezeButton: React.FC<PermanentFreezeButtonProps> = ({
  setIsPermanentModel,
  setSelectedAction,
  freezeType,
}) => (
  <div className={styles.buttons}>
    {freezeType === 'userFreeze' && (
      <Button
        style={{ background: '#CCCECC', color: '#000000' }}
        endIcon={<LockPersonIcon />}
        onClick={() => {
          setIsPermanentModel(true)
          setSelectedAction('Permanent Freeze')
        }}
      >
        Permanent Freeze
      </Button>
    )}
  </div>
)

const AddPeriodCard: React.FC<AddPeriodCardProps> = ({ currentPeriod }) =>
  !currentPeriod && (
    <div className={styles.card}>
      <TypographyField
        className={styles.value}
        variant="caption"
        style={{ color: '#000000', fontSize: '14px' }}
        text="Add New Reporting Period"
      />
      <div className={styles.dateContainer}>
        <PeriodDatePicker
          disabled={!!currentPeriod}
          actionButtons
          CustomDay={CustomDay}
          sx={{
            display: 'content',
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'transparent',
              borderTop: 0,
              borderRight: 0,
              borderLeft: 0,
              borderRadius: 0,
            },
            '& .MuiOutlinedInput-input': {
              width: '120px',
              padding: '6px 0px 6px 8px',
              fontWeight: '400',
              fontSize: '12px',
              lineHeight: '18px',
              color: 'black',
              display: 'none',
            },
            '& .MuiButtonBase-root': {
              top: '14px',
              left: '-17px',
            },
            '& .Mui-disabled': {
              fontWeight: '400',
              fontSize: '12px',
              lineHeight: '18px',
              color: 'black !important',
              '& .MuiInputBase-input': {
                '-webkit-text-fill-color': 'black',
              },
            },
            '& .Mui-focused': {
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#f4f4fc',
              },
            },
          }}
        />
      </div>
    </div>
  )

const drawerComponentsMap = {
  newsFeeds: NewsFeedsDrawer,
  scoring: Scoring,
}

interface IProjectsAndEntitiesProps {
  drawerStates: DrawerStates
  toggleDrawer: (drawer: keyof DrawerStates) => (open: boolean) => void
}

const PeriodCards: React.FC<IProjectsAndEntitiesProps> = ({ drawerStates, toggleDrawer }) => {
  const { freezeType, currentPeriod, mainPeriod } = useMasterPeriod()
  const [selectedAction, setSelectedAction] = useState<string>('')
  const [isModel, setIsModel] = useState<boolean>(false)
  const [isPermanentModel, setIsPermanentModel] = useState<boolean>(false)
  // const [permanentFreezeDisabled, setPermanentFreezeDisabled] = useState(false)

  const { currentUser } = useAuthorization()
  // const socketURL = BaseURL?.replace(/\/api\/?$/, '')

  // const showPermanentFreezeToast = () => {
  //   if (!freezPerminantId) {
  //     const id = toast('🔒 Permanent Freeze in progress...', {
  //       id: 'permanent-freeze-toast',
  //       position: 'top-center',
  //       dismissible: true,
  //       duration: Infinity,
  //       description: 'This will close automatically when freeze is completed.',
  //       action: {
  //         label: '✕',
  //         onClick: () => {
  //           toast.dismiss('permanent-freeze-toast')
  //           freezPerminantId = null
  //         },
  //       },
  //     })
  //     freezPerminantId = id
  //   }
  // }

  // const handleSocketEvents = () => {
  //   try {
  //     socket = io(socketURL, {
  //       transports: ['websocket'],
  //       reconnection: true,
  //       auth: { email: currentUser?.email },
  //     })

  //     socket.on('connect', () => console.log('✅ Socket connected:', socket.id))

  //     socket.on('freezeDb_completed', (message) => {
  //       successToast(message?.data?.message)
  //       toast.dismiss('permanent-freeze-toast')
  //       freezPerminantId = null
  //       localStorage.removeItem(PERMANENT_FREEZE_SYNC_KEY)
  //       setPermanentFreezeDisabled(false)
  //     })

  //     socket.on('freezeDb_error', (message) => {
  //       errorToast(message?.data?.message)
  //       toast.dismiss('permanent-freeze-toast')
  //       freezPerminantId = null
  //       localStorage.removeItem(PERMANENT_FREEZE_SYNC_KEY)
  //       setPermanentFreezeDisabled(false)
  //     })
  //   } catch (e) {
  //     console.error('❗ Socket setup failed:', e)
  //   }
  // }

  // useEffect(() => {
  //   const inProgress = localStorage.getItem(PERMANENT_FREEZE_SYNC_KEY) === 'true'
  //   if (inProgress) {
  //     setPermanentFreezeDisabled(true)
  //     showPermanentFreezeToast()
  //   }
  //   handleSocketEvents()

  //   return () => {
  //     if (socket?.connected) socket.disconnect()
  //   }
  // }, [])

  // useEffect(() => {
  //   if (permanentFreezeDisabled) showPermanentFreezeToast()
  // }, [permanentFreezeDisabled])

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleSwitchChange = () => {
    setIsModel(true)
    setSelectedAction(freezeType === 'userFreeze' ? 'Unfreeze' : 'Freeze')
  }

  const onClickFreezePeriod = () => {
    // setPermanentFreezeDisabled(true)
    // localStorage.setItem(PERMANENT_FREEZE_SYNC_KEY, 'true')
  }

  return (
    <div className={styles.cardContainer}>
      <TypographyField variant="subheading" text="Reporting Period" />
      {
        // !permanentFreezeDisabled &&
        isEditForUser && (
          <>
            <div className={styles.cards}>
              <FreezeStatus
                freezeType={freezeType}
                currentPeriod={currentPeriod}
                handleSwitchChange={handleSwitchChange}
              />
              <PermanentFreezeButton
                setIsPermanentModel={setIsPermanentModel}
                setSelectedAction={setSelectedAction}
                freezeType={freezeType}
              />
              <AddPeriodCard currentPeriod={currentPeriod} />
            </div>
            <PeriodConfirm open={isModel} onClose={() => setIsModel(false)} selectedAction={selectedAction} />
            <FinalFreezePeriodModel
              open={isPermanentModel}
              onClose={() => setIsPermanentModel(false)}
              selectedAction={selectedAction}
              onClickFreezePeriod={onClickFreezePeriod}
            />
          </>
        )
      }

      <div className={styles.cards}>
        <ImageCard
          label="News Feed"
          value={''}
          imageSrc="/svg/status.svg"
          imageWidth={43}
          imageHeight={50}
          // onCardClick={() => toggleDrawer("viewEntity")(true)}
          onAddButtonClick={() => toggleDrawer('newsFeeds')(true)}
        />
      </div>

      <div className={styles.cards}>
        <ImageCard
          label="Scoring"
          value={''}
          imageSrc="/svg/subStatues.svg"
          imageWidth={43}
          imageHeight={50}
          // onCardClick={() => toggleDrawer("viewEntity")(true)}
          onAddButtonClick={() => toggleDrawer('scoring')(true)}
        />
      </div>

      {/* Add Entity */}
      {/* <Drawer anchor="right" open={drawerStates?.newsFeeds} onClose={() => toggleDrawer('newsFeeds')(false)}>
        <NewsFeedsDrawer drawerStates={drawerStates} onClose={() => toggleDrawer('newsFeeds')(false)} />
      </Drawer> */}
      {Object.entries(drawerComponentsMap).map(([key, Component]) => (
        <Drawer
          key={key}
          anchor="right"
          open={drawerStates[key as keyof DrawerStates]}
          onClose={() => toggleDrawer && toggleDrawer(key as keyof DrawerStates)(false)}
        >
          <Component
            drawerStates={drawerStates}
            onClose={() => toggleDrawer && toggleDrawer(key as keyof DrawerStates)(false)}
          />
        </Drawer>
      ))}
    </div>
  )
}

export default PeriodCards

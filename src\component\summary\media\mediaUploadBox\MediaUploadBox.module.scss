@import '/styles/color.scss';

.categoryBox {
  flex: 1;
  border: 1px solid #ccc;
  padding: 20px;
  border-radius: 5px;
  background-color: #f9f9f9;
  text-align: center;

  .dropZone {
    margin: 10px 0;
    padding: 20px;
    border: 2px dashed #ccc;
    text-align: center;
    background-color: #fafafa;
    border-radius: 5px;
    position: relative;

    &.dragOver {
      background: #e3f2fd;
      border-color: #3874cb;
      transform: scale(1.02);
    }

    .customFileInput {
      display: block;
      margin: 10px 0;
      position: relative;

      .fileInput {
        display: none;
      }

      span {
        display: block;
        cursor: pointer;
        background-color: #444444;
        color: white;
        padding: 10px;
        text-align: center;
        border-radius: 5px;
        opacity: 1;
        &:hover {
          background-color: #777777;
        }
      }
      .isEditForUser {
        opacity: 30%;
        cursor: default;
      }
    }
  }

  .error {
    color: red;
    margin-top: 10px;
    font-size: 12px;
  }

  .previews {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
    position: relative;

    .preview {
      width: 265px;
      height: 175px;
      overflow: hidden;
      border: 1px solid #ccc;
      border-radius: 5px;
      background-color: #fff;
      position: relative;

      .previewImage {
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .uploadButton {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #444444;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;

    &:hover {
      background-color: #777777;
    }
  }
}

.title {
  font-size: 20px;
  font-weight: bold;
  border-bottom: 3px solid red;
  display: inline-block;
  margin-top: 40px;
}

.media {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  .imageWrapper {
    flex: 1;
    max-width: 400px;
    width: 100%;
    background-color: white;
  }
  .imageName {
    padding: 0 20px;
    word-wrap: break-word;
  }
}

.deleteButton {
  position: absolute;
  display: inline-block;
  right: 20px;
  top: 10px;
  background: white;
  display: flex;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.content {
  display: flex;
  position: relative;
  .deleteIcon {
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 20px;
  }
}
.pdfPreview {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .deletePreviewButton {
    cursor: pointer;
  }
}

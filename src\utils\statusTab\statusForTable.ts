import { differenceInDays, parseISO } from 'date-fns'
import { IRecentStatusUpdate } from './interface'
import { getStageStatusByPermission } from './stageStatusByPermission'
import { sortArrayByKeyWithTypeConversion } from '../arrayUtils'
import { DDMMYYYYformate } from '../dateUtils'
import { convertToPercentage } from '../numberUtils'
import { IStatusForTable } from '@/src/component/summary/statusTab/interface'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'
import { ICurrentUser } from '@/src/redux/authorization/interface'
import {
  ILookupProjectStatusPredecessor,
  ILookupProjectStatusSuccessor,
  ILookupProjectToProjectPhaseCategory,
  IStatus,
} from '@/src/redux/status/interface'

// Constants for status categories
const STATUS_CATEGORIES = {
  DESIGN_PHASE: [LDC_PROCUREMENT, DESIGN, INITIATION],
  CONSTRUCTION_PHASE: [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT],
}

// Get the next row's forecast and baseline dates based on stage status
const getNextRowDates = (nextRow: IStatus) => {
  if (STATUS_CATEGORIES.DESIGN_PHASE.includes(nextRow?.MasterProjectStageStatus?.project_stage_status as string)) {
    return {
      forecastDate: nextRow?.forecast_finish ? String(nextRow.forecast_finish) : '',
      baseLineDate: nextRow?.baseline_plan_finish ? String(nextRow.baseline_plan_finish) : '',
    }
  }
  if (
    STATUS_CATEGORIES.CONSTRUCTION_PHASE.includes(nextRow?.MasterProjectStageStatus?.project_stage_status as string)
  ) {
    return {
      forecastDate: nextRow?.forecasted_end_date ? String(nextRow.forecasted_end_date) : '',
      baseLineDate: nextRow?.plan_end_date ? String(nextRow.plan_end_date) : '',
    }
  }
  return { forecastDate: '', baseLineDate: '' }
}

// Calculate the difference between two dates
const getDifferenceBetweenDates = (date1?: string | Date, date2?: string | Date) => {
  if (!date1 || !date2) return null
  const firstDate = parseISO(String(date1))
  const secondDate = parseISO(String(date2))
  return isNaN(firstDate.getTime()) || isNaN(secondDate.getTime()) ? null : differenceInDays(secondDate, firstDate)
}

// Create a status object to be displayed in the table
const createFormattedStatus = (item: IStatus, lastUpdateOfProgress: IRecentStatusUpdate[], filterStatus: IStatus[]) => {
  const ribbonData = lastUpdateOfProgress.find((progress) => progress.statusId === item.id)

  let status: IStatusForTable = {
    id: item?.id as number,
    sortingOrder: Number(item.project_status_sorting_order),
    actualLWeek: convertToPercentage(item.actual_progress_percentage_of_last_week) || '-',
    contractor: item?.MasterContractor?.contractor || '-',
    pmcConsultant: item?.MasterPMCConsultant?.pmc_consultant || '-',
    planLWeek: convertToPercentage(item.actual_progress_percentage_for_last_week) || '-',
    // phase: item?.LookupProjectToPhase.map((item) => item?.phase)?.join(', ') || '-',
    // phase: item?.LookupProjectToPhase?.map((item) => item?.phase).join('$@'),
    // project_phase_category: item?.LookupProjectToProjectPhaseCategory?.map(
    //   (item: ILookupProjectToProjectPhaseCategory) => item?.MasterProjectPhaseCategory?.project_phase_category,
    // ).join('$@'),
    LookupProjectToPhase: item?.LookupProjectToPhase,
    LookupProjectToProjectPhaseCategory: item?.LookupProjectToProjectPhaseCategory,
    plan_start_date: item.plan_start_date ? DDMMYYYYformate(item.plan_start_date) : '',
    forecast_start_date: item.forecast_start_date ? DDMMYYYYformate(item.forecast_start_date) : '',
    variance: item.variance_in_days || '',
    stageStatus: item?.MasterProjectStageStatus?.project_stage_status as string,
    subStatus: item?.MasterProjectSubStage?.project_sub_stage || '',
    designStageWeightage: item.design_stage_weightage ? convertToPercentage(item.design_stage_weightage) : '-',
    phaseWeightage: item.phase_weightage ? convertToPercentage(item.phase_weightage) : '-',
    isRibbon: ribbonData?.is7Days,
    design_manager:
      item?.DesignManagers?.map((manager) => manager?.design_manager?.split(';')?.join(', ')).join(', ') || '-',
    procurement_manager: item?.MasterProcurementManager?.procurement_manager?.split(';')?.join(', ') || '-',
    delivery_project_manager:
      item.DeliveryProjectManagers?.map((manager) => manager?.delivery_project_manager).join(', ') ||
      '-'?.split(';')?.join(', '),
    baseLineDiff: null,
    forecastDiff: null,
    forecast_duration: item.forecast_duration as number | null,
    plan_duration: item.plan_duration as number | null,
    calculated_plan_progress: item.calculated_plan_progress
      ? (() => {
          const value = Math.min(item.calculated_plan_progress * 100, 100)
          return value === 100 ? '100' : value.toFixed(2)
        })()
      : null,
    forecastCompletionLastWeek: item.forecast_completion_last_week
      ? DDMMYYYYformate(item.forecast_completion_last_week)
      : '-',
    predecessor:
      item.LookupProjectStatusPredecessor?.map(
        (item: ILookupProjectStatusPredecessor) => item?.DestinationProjectStatus?.destination_project_status_id,
      ).filter((id): id is number => typeof id === 'number') || [],
    successor:
      item.LookupProjectStatusSuccessor?.map(
        (item: ILookupProjectStatusSuccessor) => item?.DestinationProjectStatus?.destination_project_status_id,
      ).filter((id): id is number => typeof id === 'number') || [],
  }

  // Update status fields for DESIGN_PHASE
  if (STATUS_CATEGORIES.DESIGN_PHASE.includes(item?.MasterProjectStageStatus?.project_stage_status as string)) {
    Object.assign(status, {
      actualPlanPercentage: convertToPercentage(item.actual_percentage),
      revPlanPercentage: convertToPercentage(item.rev_plan_percentage),
      baselinePlanFinish: item.baseline_plan_finish ? DDMMYYYYformate(String(item.baseline_plan_finish)) : '',
      revisedBaselineFinish: item.revised_baseline_finish ? DDMMYYYYformate(String(item.revised_baseline_finish)) : '',
      forecastFinish: item.forecast_finish ? DDMMYYYYformate(String(item.forecast_finish)) : '',
    })
  }
  // Update status fields for CONSTRUCTION_PHASE
  else if (
    STATUS_CATEGORIES.CONSTRUCTION_PHASE.includes(item?.MasterProjectStageStatus?.project_stage_status as string)
  ) {
    Object.assign(status, {
      actualPlanPercentage: convertToPercentage(item.actual_plan_percentage),
      revPlanPercentage: convertToPercentage(item.revised_plan_percentage),
      baselinePlanFinish: item.plan_end_date ? DDMMYYYYformate(String(item.plan_end_date)) : '-',
      revisedBaselineFinish: item.revised_plan_end_date ? DDMMYYYYformate(String(item.revised_plan_end_date)) : '-',
      forecastFinish: item.forecasted_end_date ? DDMMYYYYformate(String(item.forecasted_end_date)) : '-',
    })
  }
  // Find the next row based on sorting order
  const nextRow = filterStatus.find(
    (nextItem: IStatus) =>
      nextItem.project_status_sorting_order !== undefined &&
      item?.project_status_sorting_order !== undefined &&
      nextItem.project_status_sorting_order?.toString() === (Number(item.project_status_sorting_order) + 1)?.toString(),
  )

  // Helper function to update forecast and baseline date differences
  const updateStatusWithDates = (dates: { forecastDate?: string; baseLineDate?: string }, nextRow?: IStatus) => {
    if (!nextRow) return
    const forecastedDate: any = STATUS_CATEGORIES.CONSTRUCTION_PHASE.includes(
      item?.MasterProjectStageStatus?.project_stage_status as string,
    )
      ? item.forecasted_end_date
      : item.forecast_finish
    const planDate: any = STATUS_CATEGORIES.CONSTRUCTION_PHASE.includes(
      item?.MasterProjectStageStatus?.project_stage_status as string,
    )
      ? item.plan_end_date
      : item.baseline_plan_finish
    status.forecastDiff = getDifferenceBetweenDates(forecastedDate, dates.forecastDate)
    status.baseLineDiff = getDifferenceBetweenDates(planDate, dates.baseLineDate)
  }

  if (nextRow) {
    updateStatusWithDates(getNextRowDates(nextRow), nextRow)
  }
  return status
}

// Main function to format the statuses for the table
export const generateStatusTableData = (
  statuses: IStatus[],
  currentUser: ICurrentUser,
  projectName: string,
  lastUpdateOfProgress: IRecentStatusUpdate[],
): IStatusForTable[] => {
  const formattedStatuses = statuses.map((item) => createFormattedStatus(item, lastUpdateOfProgress, statuses))

  // Filter statuses based on user permissions
  const allowedStatuses = formattedStatuses.filter((item) =>
    getStageStatusByPermission(currentUser.role).includes(item.stageStatus),
  )

  // Sort and return the formatted statuses
  return sortArrayByKeyWithTypeConversion(allowedStatuses, 'sortingOrder', true)
}

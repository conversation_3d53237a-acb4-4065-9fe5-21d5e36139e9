import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  IGetMasterCategoryResponse,
  ICategoryState,
  IRenameCategoryPayload,
  IDeleteCategoryPayload,
  IAddCategoryPayload,
} from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ICategoryState = {
  getMasterCategoryStatus: StatusEnum.Idle,
  addCategoryStatus: StatusEnum.Idle,
  renameCategoryStatus: StatusEnum.Idle,
  deleteCategoryStatus: StatusEnum.Idle,
  categories: [],
  category: {
    id: 0,
    category: '', // replace with the actual field name with backend
  },
  localCategory: [],
}

// TODO : change endpoint
export const addCategory = createAsyncThunk(
  '/project-status/add-phase-category',
  async (data: IAddCategoryPayload, thunkAPI) => {
    try {
      const response = await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(`/master-project-to-project-phase-categories`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

// TODO : change endpoint
export const renameCategory = createAsyncThunk(
  '/project-status/rename-phase-category',
  async (data: IRenameCategoryPayload, thunkAPI) => {
    const { old_master_project_phase_category_id, ...rest } = data
    try {
      const response = await ApiPutNoAuth(
        `/master-project-to-project-phase-categories/${old_master_project_phase_category_id}`,
        rest,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteCategory = createAsyncThunk(
  '/project-status/delete-phase-category',
  async (data: IDeleteCategoryPayload, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(
        `/master-project-to-project-phase-categories/${data.master_project_phase_category_id}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const categorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    //addCategoryProcess
    builder.addCase(addCategory.pending, (state) => {
      state.addCategoryStatus = StatusEnum.Pending
    })
    builder.addCase(addCategory.fulfilled, (state, action) => {
      state.addCategoryStatus = StatusEnum.Success
    })
    builder.addCase(addCategory.rejected, (state) => {
      state.addCategoryStatus = StatusEnum.Failed
    })
    //renameCategoryProcess
    builder.addCase(renameCategory.pending, (state) => {
      state.renameCategoryStatus = StatusEnum.Pending
    })
    builder.addCase(renameCategory.fulfilled, (state, action) => {
      state.renameCategoryStatus = StatusEnum.Success
    })
    builder.addCase(renameCategory.rejected, (state) => {
      state.renameCategoryStatus = StatusEnum.Failed
    })
    //deleteCategory
    builder.addCase(deleteCategory.pending, (state) => {
      state.deleteCategoryStatus = StatusEnum.Pending
    })
    builder.addCase(deleteCategory.fulfilled, (state, action) => {
      state.deleteCategoryStatus = StatusEnum.Success
    })
    builder.addCase(deleteCategory.rejected, (state) => {
      state.deleteCategoryStatus = StatusEnum.Failed
    })
  },
})

export const {} = categorySlice.actions

// Export the reducer
export default categorySlice.reducer

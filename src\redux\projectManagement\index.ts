import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  IDeleteDocProjectManagementPayload,
  IGetProjectManagementsResponse,
  IProjectManagement,
  IProjectManagementStatus,
} from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IProjectManagementStatus = {
  getProjectManagementStatus: StatusEnum.Idle,
  addProjectManagementStatus: StatusEnum.Idle,
  deleteProjectManagementStatus: StatusEnum.Idle,
  updateProjectManagementStatus: StatusEnum.Idle,
  projectManagements: [],
  projectManagement: {},
}

export const getProjectManagements = createAsyncThunk(
  '/get-project-management',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const { period, project_name } = data

    try {
      const query = `/project-management?period=${period}${project_name ? `&project_name=${encodeURIComponent(project_name)}` : ''}`
      const response = await ApiGetNoAuth(query)

      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addProjectManagements = createAsyncThunk(
  '/add-project-management',
  async (data: IProjectManagement, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/project-management`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateProjectManagements = createAsyncThunk(
  '/update-project-management',
  async (data: { id: number; data: IProjectManagement }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-management/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteProjectManagements = createAsyncThunk(
  '/delete-project-management',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/project-management/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const uploadDocProjectManagements = createAsyncThunk(
  '/upload-project-management',
  async ({ id, data }: { id: number; data: FormData }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/files/upload-project-management`, data, {
        isFormData: true,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteDocProjectManagements = createAsyncThunk(
  '/delete-project-management',
  async (data: IDeleteDocProjectManagementPayload, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/files/delete-project-management`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const fetchDocument = createAsyncThunk('/files/fetch', async (data: any, thunkAPI) => {
  try {
    const fileName = encodeURIComponent(data.name)
    const projectName = encodeURIComponent(data.projectName)
    const response = await ApiGetNoAuth(
      `/files/fetch?name=${fileName}&projectName=${projectName}&period=${data.period}`,
    )
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const projectManagementSlice = createSlice({
  name: 'projectManagement',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getProjectManagements
    builder.addCase(getProjectManagements.pending, (state) => {
      state.getProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(getProjectManagements.fulfilled, (state, action) => {
      state.getProjectManagementStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetProjectManagementsResponse
      state.projectManagements = actionPayload.data
    })
    builder.addCase(getProjectManagements.rejected, (state) => {
      state.getProjectManagementStatus = StatusEnum.Failed
    })
    //addProjectManagements
    builder.addCase(addProjectManagements.pending, (state) => {
      state.addProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(addProjectManagements.fulfilled, (state, action) => {
      state.addProjectManagementStatus = StatusEnum.Success
    })
    builder.addCase(addProjectManagements.rejected, (state) => {
      state.addProjectManagementStatus = StatusEnum.Failed
    })
    //updateProjectManagements
    builder.addCase(updateProjectManagements.pending, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(updateProjectManagements.fulfilled, (state, action) => {
      state.updateProjectManagementStatus = StatusEnum.Success
    })
    builder.addCase(updateProjectManagements.rejected, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Failed
    })
    //deleteProjectManagements
    builder.addCase(deleteProjectManagements.pending, (state) => {
      state.deleteProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(deleteProjectManagements.fulfilled, (state, action) => {
      state.deleteProjectManagementStatus = StatusEnum.Success
    })
    builder.addCase(deleteProjectManagements.rejected, (state) => {
      state.deleteProjectManagementStatus = StatusEnum.Failed
    })
    //uploadprojectManagements
    builder.addCase(uploadDocProjectManagements.pending, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(uploadDocProjectManagements.fulfilled, (state, action) => {
      state.updateProjectManagementStatus = StatusEnum.Success
    })
    builder.addCase(uploadDocProjectManagements.rejected, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Failed
    })
    // Reducers for fetchDocument
    builder.addCase(fetchDocument.pending, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Pending
    })
    builder.addCase(fetchDocument.fulfilled, (state, action) => {
      state.updateProjectManagementStatus = StatusEnum.Success
    })
    builder.addCase(fetchDocument.rejected, (state) => {
      state.updateProjectManagementStatus = StatusEnum.Failed
    })
  },
})

export const {} = projectManagementSlice.actions

// Export the reducer
export default projectManagementSlice.reducer

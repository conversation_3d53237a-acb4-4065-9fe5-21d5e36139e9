import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createPotentialBudgetUpliftScope,
  deletePotentialBudgetUpliftScope,
  getPotentialBudgetUpliftScope,
  updatePotentialBudgetUpliftScope,
} from '../services/potentialBudgetUpliftScope'
import {
  IGetPotentialBudgetUpliftScope,
  IUpdatePotentialBudgetUpliftScope,
} from '../services/potentialBudgetUpliftScope/interface'
import { errorToast, successToast } from '../utils/toastUtils'

export const QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE = 'potential-budget-uplift-scope'

/**
 * Hook to fetch Potential Budget Uplift Scope
 * @param enabled - Enables or disables the query
 */
export const useGetPotentialBudgetUpliftScope = (
  enabled: boolean = true,
  currentPeriod: string,
  projectName: string,
) => {
  const { data, isError, ...rest } = useQuery<IGetPotentialBudgetUpliftScope>({
    queryKey: [QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE, projectName, currentPeriod],
    queryFn: () => getPotentialBudgetUpliftScope({ period: currentPeriod, project_name: projectName }),
    enabled,
  })

  return {
    potentialBudgetUpliftScopes: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Potential Budget Uplift Scope */
export const useCreatePotentialBudgetUpliftScope = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createPotentialBudgetUpliftScope,
    onSuccess: (response) => {
      successToast(response.data.message)
      queryClient.invalidateQueries({ queryKey: [QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE] })
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.data?.message)
    },
  })
}

/**
 * Hook to delete a Potential Budget Uplift Scope */
export const useDeletePotentialBudgetUpliftScope = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deletePotentialBudgetUpliftScope,
    onSuccess: (response) => {
      successToast(response.message)
      queryClient.invalidateQueries({ queryKey: [QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE] })
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.message)
    },
  })
}

/**
 * Hook to update a Potential Budget Uplift Scope */
export const useUpdatePotentialBudgetUpliftScope = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updatePotentialBudgetUpliftScope,
    onMutate: async (updatedData: IUpdatePotentialBudgetUpliftScope) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE] })
      const previousData = queryClient.getQueryData<IGetPotentialBudgetUpliftScope>([
        QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE,
      ])
      // Optimistically update the cache
      queryClient.setQueryData<IGetPotentialBudgetUpliftScope>([QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((res) =>
            res.id === updatedData.id
              ? {
                  ...res,
                  current_scope: updatedData?.current_scope,
                  amended_scope: updatedData?.amended_scope,
                  cost_impact: updatedData?.cost_impact,
                }
              : res,
          ),
        }
      })

      return { previousData }
    },
    onSuccess: (response) => {
      // response will contain { data, message, status }
      successToast(response.data.message)
      queryClient.invalidateQueries({ queryKey: ['potentialBudgetUpliftScope'] })
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.data?.message)
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_POTENTIAL_BUDGET_UPLIFT_SCOPE] })
    },
  })
}

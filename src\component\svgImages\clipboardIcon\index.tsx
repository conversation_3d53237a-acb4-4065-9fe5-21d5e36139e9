import React, { ReactElement, forwardRef } from 'react'

interface ClipboardIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const ClipboardIcon: React.FC<ClipboardIconProps> = forwardRef<SVGSVGElement, ClipboardIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="16"
        height="17"
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.84186 2.67239C4.92212 1.91961 5.55925 1.33325 6.33333 1.33325H9.66667C10.4408 1.33325 11.0779 1.91961 11.1581 2.67239C11.6669 2.68275 12.1068 2.70906 12.4824 2.77806C12.9878 2.87088 13.4178 3.04771 13.7678 3.39766C14.169 3.79894 14.3416 4.30419 14.4223 4.90426C14.5 5.48224 14.5 6.21712 14.5 7.12885V11.202C14.5 12.1137 14.5 12.8486 14.4223 13.4266C14.3416 14.0267 14.169 14.5319 13.7678 14.9332C13.3665 15.3345 12.8612 15.5071 12.2612 15.5877C11.6832 15.6655 10.9483 15.6654 10.0366 15.6654H5.96342C5.05169 15.6654 4.31681 15.6655 3.73883 15.5877C3.13876 15.5071 2.63351 15.3345 2.23223 14.9332C1.83096 14.5319 1.65836 14.0267 1.57768 13.4266C1.49998 12.8486 1.49999 12.1137 1.5 11.202V7.12884C1.49999 6.21712 1.49998 5.48224 1.57768 4.90426C1.65836 4.30419 1.83096 3.79894 2.23223 3.39766C2.58219 3.04771 3.01224 2.87088 3.51757 2.77806C3.89318 2.70906 4.33309 2.68275 4.84186 2.67239ZM4.84317 3.67267C4.36445 3.6829 3.99555 3.70699 3.69823 3.7616C3.32036 3.83101 3.10164 3.94246 2.93934 4.10477C2.75483 4.28928 2.63453 4.54832 2.56877 5.03751C2.50106 5.54107 2.5 6.20848 2.5 7.16543V11.1654C2.5 12.1224 2.50106 12.7898 2.56877 13.2933C2.63453 13.7825 2.75483 14.0416 2.93934 14.2261C3.12385 14.4106 3.3829 14.5309 3.87208 14.5967C4.37565 14.6644 5.04306 14.6654 6 14.6654H10C10.9569 14.6654 11.6244 14.6644 12.1279 14.5967C12.6171 14.5309 12.8762 14.4106 13.0607 14.2261C13.2452 14.0416 13.3655 13.7825 13.4312 13.2933C13.4989 12.7898 13.5 12.1224 13.5 11.1654V7.16543C13.5 6.20848 13.4989 5.54107 13.4312 5.03751C13.3655 4.54832 13.2452 4.28928 13.0607 4.10477C12.8984 3.94246 12.6796 3.83101 12.3018 3.7616C12.0045 3.70699 11.6355 3.6829 11.1568 3.67267C11.0712 4.41973 10.4367 4.99992 9.66667 4.99992H6.33333C5.56333 4.99992 4.92884 4.41973 4.84317 3.67267ZM6.33333 2.33325C6.05719 2.33325 5.83333 2.55711 5.83333 2.83325V3.49992C5.83333 3.77606 6.05719 3.99992 6.33333 3.99992H9.66667C9.94281 3.99992 10.1667 3.77606 10.1667 3.49992V2.83325C10.1667 2.55711 9.94281 2.33325 9.66667 2.33325H6.33333ZM10.3655 7.49209C10.5539 7.69397 10.543 8.01036 10.3412 8.19878L7.48402 10.8654C7.29191 11.0447 6.9938 11.0447 6.8017 10.8654L5.65884 9.79878C5.45697 9.61036 5.44606 9.29397 5.63447 9.09209C5.82289 8.89022 6.13928 8.87931 6.34116 9.06772L7.14286 9.81598L9.65884 7.46772C9.86072 7.27931 10.1771 7.29022 10.3655 7.49209Z"
          fill={props.color ? props.color : '#808080'}
        />
      </svg>
    )
  },
)
ClipboardIcon.displayName = 'AccedingIcon'

export default ClipboardIcon

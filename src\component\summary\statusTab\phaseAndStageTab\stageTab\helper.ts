import { IStageFormTable, IStageFormTablePayloadForValidation } from '../interface'
import {
  getPhaseWeightageTotalAndRequired,
  getDesignStageWeightageTotalAndRequired,
  cleanNumber,
} from '../validationError/weightageValidation'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { IStatus } from '@/src/redux/status/interface'

export class CategoryValidator {
  static async isCategoryAvailable(
    values: Partial<IStageFormTablePayloadForValidation>,
    currentStatus: IStatus,
    statuses: IStageFormTable[],
  ): Promise<boolean> {
    if ('project_phase_category' in values) {
      return !!values?.project_phase_category
    }
    return false
  }
}

export const checkValidationStatus = async (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStatus: IStatus,
  statuses: IStageFormTable[],
) => {
  const messages = []

  const isCategoryPass = await CategoryValidator.isCategoryAvailable(values, currentStatus, statuses)
  const isPhaseWeighagePass = checkPhaseWeighage(values, currentStatus, statuses)
  const isDesignStageWeighagePass = checkDesignStageWeighage(values, currentStatus, statuses)

  if (!isCategoryPass) {
    messages.push('At least one Category is required')
  }

  if (isPhaseWeighagePass) {
    messages.push(isPhaseWeighagePass)
  }

  if (isDesignStageWeighagePass) {
    messages.push(isDesignStageWeighagePass)
  }

  return messages
}

const checkPhaseWeighage = (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStage: IStatus,
  statuses: IStageFormTable[],
) => {
  console.log('values: ', values)
  console.log('currentStage: ', currentStage)

  // const modifiedCurrentRecord = {
  //   id: currentStage.id ? String(currentStage.id) : '',
  //   phase: (values?.phase ? values.phase : currentStage.phase) || '',
  //   projectPhaseCategory:
  //     (values?.project_phase_category ? values.project_phase_category : currentStage.project_phase_category) || '',
  //   stageStatus: (values?.stage_status ? values.stage_status : currentStage.stage_status) || '',
  //   sortingOrder: currentStage.project_status_sorting_order || 0,
  //   subStage: currentStage.sub_stage || '',
  //   designStageWeightage: values?.design_stage_weightage
  //     ? cleanNumber(Number(values.design_stage_weightage))?.toString()
  //     : currentStage.design_stage_weightage
  //       ? cleanNumber(Number(currentStage.design_stage_weightage) * 100)?.toString()
  //       : '',
  //   phaseWeightage: values?.phase_weightage
  //     ? cleanNumber(Number(values.phase_weightage))?.toString()
  //     : currentStage.phase_weightage
  //       ? cleanNumber(Number(currentStage.phase_weightage) * 100)?.toString()
  //       : '',
  //   predecessor: values?.predecessor ? values.predecessor : currentStage.predecessor || [],
  //   successor: values?.successor ? values.successor : currentStage.successor || [],
  // }

  // const { total, required } = getPhaseWeightageTotalAndRequired<IStageFormTable>(
  //   statuses,
  //   modifiedCurrentRecord,
  //   keys,
  //   1,
  // )
  // const newValue = Number(values?.phase_weightage || 0)
  // if (Number(newValue) === 0) return false
  // if (Number(total) + Number(newValue) > 100) {
  //   const errorMessage =
  //     Number(required) > 0
  //       ? `Phase weightage across stages must not exceed 100 (Remaining value is: ${Number(required)})`
  //       : `Phase weightage across stages must not exceed 100 (Remaining value is: 0)`
  //   return errorMessage
  // }
  return false
}

const keys = {
  phaseKey: 'phase',
  categoryKey: 'projectPhaseCategory',
  stageStatusKey: 'stageStatus',
  weightageKey: 'phaseWeightage',
}

const designKeys = {
  phaseKey: 'phase',
  categoryKey: 'projectPhaseCategory',
  stageStatusKey: 'stageStatus',
  weightageKey: 'designStageWeightage',
}

const checkDesignStageWeighage = (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStage: IStatus,
  statuses: IStageFormTable[],
) => {
  const modifiedCurrentRecord = {
    id: currentStage.id ? String(currentStage.id) : '',
    phase: (values?.phase ? values.phase : currentStage.phase) || '',
    projectPhaseCategory:
      (values?.project_phase_category ? values.project_phase_category : currentStage.project_phase_category) || '',
    stageStatus: (values?.stage_status ? values.stage_status : currentStage.stage_status) || '',
    sortingOrder: currentStage.project_status_sorting_order || 0,
    subStage: currentStage.sub_stage || '',
    designStageWeightage: values?.design_stage_weightage
      ? cleanNumber(Number(values.design_stage_weightage))?.toString()
      : currentStage.design_stage_weightage
        ? cleanNumber(Number(currentStage.design_stage_weightage) * 100)?.toString()
        : '',
    phaseWeightage: values?.phase_weightage
      ? cleanNumber(Number(values.phase_weightage))?.toString()
      : currentStage.phase_weightage
        ? cleanNumber(Number(currentStage.phase_weightage) * 100)?.toString()
        : '',
    predecessor: values?.predecessor ? values.predecessor : currentStage.predecessor || [],
    successor: values?.successor ? values.successor : currentStage.successor || [],
  }

  const { total, required } = getDesignStageWeightageTotalAndRequired(statuses, modifiedCurrentRecord, designKeys, 1)
  const category = modifiedCurrentRecord.projectPhaseCategory?.split(MULTI_SELECT_SEPARATOR)?.join(', ') || ''
  const phase = modifiedCurrentRecord.phase
    ? modifiedCurrentRecord.phase?.split(MULTI_SELECT_SEPARATOR)?.join(', ')
    : '-'
  const stage = modifiedCurrentRecord.stageStatus ? modifiedCurrentRecord.stageStatus : ''

  const newValue = Number(values?.design_stage_weightage || 0)
  const combination = `(${category} / ${phase} / ${stage})`
  if (Number(newValue) === 0) return false
  if (Number(total) + Number(newValue) > 100) {
    const errorMessage =
      Number(required) > 0
        ? `Design Stage weightage for ${combination} must not exceed 100 (Remaining value is: ${required})`
        : `Design Stage weightage for ${combination} must not exceed 100 (Remaining value is: 0)`
    return errorMessage
  }
  return false
}

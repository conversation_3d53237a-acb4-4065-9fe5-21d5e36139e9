export interface IProjectTableRecord {
  project: string
  project_name: string
  owningEntity: string
  dof_number: string
  is_refurbishment_project: boolean
  budget_uplift_submitted: boolean
  scope_change: boolean
  is_musanada_project: boolean
  is_subject_to_rebaseline: boolean
  tip_submitted: boolean
  eot_submitted: boolean
  potential_budget_uplift_submitted: boolean
  project_type: string
  project_status: string
  project_classification: string
  location: string
  Sub_location: string
  controls_manager: string
  delivery_manager: string
  procurement_manager: string
  design_project_manager: string
  design_executive_director: string
  portfolio_manager: string
  delivery_project_manager: string
  director: string
  cpds_project_id: any
  project_id: any
  // designMangerByStatus: any
  id: number
  sortNumber: number
  last_updated: string
  startYear: string
  executive_sorting_order?: number | null
  isRibbon: boolean
  isExecutiveProject: boolean
}

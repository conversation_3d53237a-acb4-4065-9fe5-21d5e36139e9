export interface DrawerStates {
  viewEntity: boolean
  addEntity: boolean
  // viewProject: boolean; // Assuming you uncomment this line
  addProject: boolean
  viewTypologies: boolean
  addTypologies: boolean
  viewControlManagers: boolean
  addControlManagers: boolean
  viewExecutiveDirectors: boolean
  addExecutiveDirectors: boolean
  addDesignManagers: boolean
  viewPortFolioManagers: boolean
  addPortFolioManagers: boolean
  viewProjectStatus: boolean
  addProjectStatus: boolean
  viewLocation: boolean
  addLocation: boolean
  viewSubLocation: boolean
  addSubLocation: boolean
  viewDevelopers: boolean
  addDevelopers: boolean
  viewContractors: boolean
  addContractors: boolean
  viewConsultants: boolean
  addConsultants: boolean
  viewRating: boolean
  addRating: boolean
  addPmcConsultant: boolean
  viewPmcConsultant: boolean
  addProjectClassification: boolean
  viewProjectClassification: boolean
  addSubStatuses: boolean
  viewSubStatuses: boolean
  addProjectStageStatus: boolean
  viewProjectStageStatus: boolean
  viewSubOwningEntity: boolean
  addSubOwningEntity: boolean
}

import React, { ReactElement, forwardRef } from 'react'

interface CommercialsDeleteIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: string
}

const CommercialsDeleteIcon: React.FC<CommercialsDeleteIconProps> = forwardRef<
  SVGSVGElement,
  CommercialsDeleteIconProps
>((props, ref): ReactElement => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      width="24"
      height="24"
      x="0"
      y="0"
      viewBox="0 0 512 512"
      {...props}
      ref={ref}
    >
      <g>
        <path
          d="M362.575 121.954h-60.759v-3.979c0-13.22-10.756-23.976-23.976-23.976h-43.68c-13.221 0-23.977 10.755-23.977 23.976v3.979h-60.758c-15.629 0-28.345 12.716-28.345 28.345 0 14.311 10.663 26.171 24.459 28.072v215.652c0 13.221 10.756 23.977 23.978 23.977h178.207c13.221 0 23.977-10.756 23.977-23.977V177.128c11.164-3.808 19.219-14.392 19.219-26.829-.001-15.629-12.716-28.345-28.345-28.345zm-132.392-3.978c0-2.155 1.821-3.976 3.977-3.976h43.681c2.155 0 3.976 1.821 3.976 3.976v3.979h-51.633v-3.979zm-89.102 32.323c0-4.601 3.743-8.345 8.345-8.345h213.149c4.602 0 8.345 3.743 8.345 8.345 0 4.601-3.743 8.345-8.345 8.345h-213.15c-4.601 0-8.344-3.743-8.344-8.345zM347.724 398H169.517c-2.156 0-3.978-1.821-3.978-3.977V178.644H351.7v215.379c.001 2.156-1.821 3.977-3.976 3.977zM217.08 214.943v146.758c0 5.523-4.478 10-10 10s-10-4.477-10-10V214.943c0-5.523 4.478-10 10-10s10 4.477 10 10zm51.541 0v146.758c0 5.523-4.478 10-10 10s-10-4.477-10-10V214.943c0-5.523 4.478-10 10-10s10 4.477 10 10zm51.54 0v146.758c0 5.523-4.478 10-10 10s-10-4.477-10-10V214.943c0-5.523 4.478-10 10-10s10 4.477 10 10zM256 0C114.841 0 0 114.841 0 256s114.841 256 256 256 256-114.841 256-256S397.159 0 256 0zm0 492C125.87 492 20 386.131 20 256S125.87 20 256 20s236 105.869 236 236-105.87 236-236 236z"
          fill="#ff3e1d"
          opacity="1"
          data-original="#000000"
        ></path>
      </g>
    </svg>
  )
})
CommercialsDeleteIcon.displayName = 'CommercialsDeleteIcon'

export default CommercialsDeleteIcon

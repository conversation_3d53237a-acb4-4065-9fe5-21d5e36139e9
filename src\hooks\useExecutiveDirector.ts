import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterExecutiveDirector,
  deleteMasterExecutiveDirector,
  getMasterExecutiveDirectors,
  updateMasterExecutiveDirector,
} from '../services/executiveDirector'
import { IGetMasterExecutiveDirectorsResponse, IUpdateExecutiveDirector } from '../services/executiveDirector/interface'

export const QUERY_EXECUTIVE_DIRECTOR = 'executive-director'

/**
 * Hook to fetch Executive Directors
 * @param enabled - Enables or disables the query
 */
export const useGetMasterExecutiveDirectors = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterExecutiveDirectorsResponse>({
    queryKey: [QUERY_EXECUTIVE_DIRECTOR],
    queryFn: getMasterExecutiveDirectors,
    enabled,
  })

  return {
    executiveDirectors: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Executive Directors
 */
export const useCreateMasterExecutiveDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterExecutiveDirector,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_EXECUTIVE_DIRECTOR] })
    },
  })
}

/**
 * Hook to delete a Executive Directors
 */
export const useDeleteMasterExecutiveDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterExecutiveDirector,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_EXECUTIVE_DIRECTOR] })
    },
  })
}

/**
 * Hook to update a Executive Directors
 */
export const useUpdateMasterExecutiveDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterExecutiveDirector,
    onMutate: async (updatedData: IUpdateExecutiveDirector) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_EXECUTIVE_DIRECTOR] })
      const previousData = queryClient.getQueryData<IGetMasterExecutiveDirectorsResponse>([QUERY_EXECUTIVE_DIRECTOR])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterExecutiveDirectorsResponse>([QUERY_EXECUTIVE_DIRECTOR], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, executive_director: updatedData.executive_director }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_EXECUTIVE_DIRECTOR], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_EXECUTIVE_DIRECTOR] })
    },
  })
}

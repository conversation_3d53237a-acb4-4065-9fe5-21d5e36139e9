import React, { ReactElement, forwardRef } from 'react'

interface ResetIconProps {
  className?: string
  onClick?: (e: any) => void
}

const ResetIcon: React.FC<ResetIconProps> = forwardRef<SVGSVGElement, ResetIconProps>((props, ref): ReactElement => {
  const { className, onClick } = props

  return (
    <svg width="17" height="16" viewBox="0 0 17 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.742 2.03857C13.0181 2.03857 13.242 2.26243 13.242 2.53857V5.367C13.242 5.64314 13.0181 5.867 12.742 5.867H9.91356C9.63742 5.867 9.41356 5.64314 9.41356 5.367C9.41356 5.09086 9.63742 4.867 9.91356 4.867H11.4872C9.59023 3.37162 6.83182 3.499 5.08167 5.24915C3.19413 7.13668 3.19413 10.197 5.08167 12.0845C6.9692 13.972 10.0295 13.972 11.917 12.0845C13.1816 10.8199 13.5996 9.02868 13.1686 7.41474C13.0974 7.14795 13.2559 6.87391 13.5227 6.80267C13.7895 6.73142 14.0635 6.88994 14.1348 7.15673C14.6541 9.10138 14.1513 11.2644 12.6241 12.7916C10.3461 15.0697 6.65262 15.0697 4.37456 12.7916C2.0965 10.5136 2.0965 6.8201 4.37456 4.54204C6.52924 2.38737 9.95015 2.27067 12.242 4.19195V2.53857C12.242 2.26243 12.4658 2.03857 12.742 2.03857Z"
        fill="#444444"
      />
    </svg>
  )
})
ResetIcon.displayName = 'ResetIcon'

export default ResetIcon

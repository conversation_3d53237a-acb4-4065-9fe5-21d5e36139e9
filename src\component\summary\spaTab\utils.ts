import { differenceInDays, parse } from 'date-fns'
import { ISpa } from '@/src/redux/spaMileston/interface'
import { DDMMYYYYformate, isoToYYYYMMDD, payloadDateFormate } from '@/src/utils/dateUtils'

export const formatPayloadDates = (payload: any) => {
  payload['milestone_date'] = payload.milestone_date ? payloadDateFormate(payload.milestone_date) : null
  payload['plan_end_date'] = payload.plan_end_date ? payloadDateFormate(payload.plan_end_date) : null
  payload['forecasted_end_date'] = payload.forecasted_end_date ? payloadDateFormate(payload.forecasted_end_date) : null
  payload['revised_spa_date'] = payload.revised_spa_date ? payloadDateFormate(payload.revised_spa_date) : null
  payload['baseline_end_date'] = payload.baseline_end_date ? payloadDateFormate(payload.baseline_end_date) : null
  payload['actual_forecast_milestone_completion'] = payload.actual_forecast_milestone_completion
    ? payloadDateFormate(payload.actual_forecast_milestone_completion)
    : null
  payload['actual_forecast_milestone_collection'] = payload.actual_forecast_milestone_collection
    ? payloadDateFormate(payload.actual_forecast_milestone_collection)
    : null
  payload['revised_spa_communication'] = payload.revised_spa_communication
    ? payloadDateFormate(payload.revised_spa_communication)
    : null
  payload['eot'] = payload.eot ? payloadDateFormate(payload.eot) : null
  payload['latest_communication_date'] = payload.latest_communication_date
    ? payloadDateFormate(payload.latest_communication_date)
    : null
  return payload
}

export const filterByProjectName = (data: any[], projectName: string) => {
  return data.filter((item) => item.project_name === projectName)
}

export const preparePayload = (row: any, cell: any, newValue: any) => {
  let data: any = {}
  data[cell?.columnId?.toString()] = newValue
  const payload = { ...data }
  delete payload.updated_by
  return formatPayloadDates(payload)
}

export const getDateDifference = (date1?: string, date2?: string): number | null => {
  const format = 'yyyy-MM-dd' // Define the date format

  // If either date1 or date2 is missing, return null
  if (!date1 || !date2) return null

  const d1 = parse(date1, format, new Date())
  const d2 = parse(date2, format, new Date())

  return differenceInDays(d1, d2)
}

export const getSpaData = (data: ISpa[]): any => {
  return data?.map((item) => {
    return {
      ...item,
      lookup_project_to_phase_id: item?.LookupProjectToPhase?.id || '-',
      planned_completion_date: item.planned_completion_date ? DDMMYYYYformate(item.planned_completion_date) : null,
      milestone_date: item.milestone_date ? DDMMYYYYformate(item.milestone_date) : null,
      revised_spa_communication: item.revised_spa_communication
        ? DDMMYYYYformate(item.revised_spa_communication)
        : null,
      actual_forecast_milestone_collection: item.actual_forecast_milestone_collection
        ? DDMMYYYYformate(item.actual_forecast_milestone_collection)
        : null,
      contract_date: item.contract_date ? DDMMYYYYformate(item.contract_date) : null,
      eot: item.eot ? DDMMYYYYformate(item.eot) : null,
      forecasted_end_date: item.forecasted_end_date ? DDMMYYYYformate(item.forecasted_end_date) : null,
      actual_forecast_milestone_completion: item.actual_forecast_milestone_completion
        ? DDMMYYYYformate(item.actual_forecast_milestone_completion)
        : null,
      latest_communication_date: item.latest_communication_date
        ? DDMMYYYYformate(item.latest_communication_date)
        : null,
    }
  })
}

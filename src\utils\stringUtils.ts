export const cleanExtraSpaces = (text: any): string => {
  return String(text || '') // Safely convert to string
    .replace(/\s+/g, ' ')
    .trim()
}

export const extractAvatarUrlForPayload = (url: string) => {
  const parts = url.split('/avatar/')[1]?.split('?')
  return parts ? parts[0] : null
}

export const replaceNewlinesWithSpace = (str: string): string => {
  return str.replace(/\n/g, ' ')
}

/**
 * Compares two values for equality, treating null and undefined as empty string.
 * Returns true if values are different, false if they are the same.
 */
export function isLooseValueChanged(a: any, b: any): boolean {
  return (a ?? '') !== (b ?? '')
}

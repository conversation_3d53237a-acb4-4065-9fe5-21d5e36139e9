import React, { ReactElement, forwardRef } from 'react'

interface ContractsAndVOsProps {
  className?: string
  onClick?: (e: any) => void
}

const ContractsAndVOs: React.FC<ContractsAndVOsProps> = forwardRef<SVGSVGElement, ContractsAndVOsProps>(
  (props, ref): ReactElement => {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="26px" height="24px" viewBox="0 0 26 24" version="1.1">
        <g id="surface1">
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 23.792969 7.71875 C 23.652344 7.847656 23.554688 7.925781 23.46875 8.011719 C 21.628906 9.851562 19.785156 11.6875 17.960938 13.539062 C 17.796875 13.707031 17.675781 13.996094 17.671875 14.234375 C 17.652344 16.929688 17.664062 19.625 17.660156 22.320312 C 17.660156 23.421875 17.09375 23.988281 15.996094 23.988281 C 11.492188 23.988281 6.988281 23.988281 2.484375 23.988281 C 1.386719 23.988281 0.8125 23.410156 0.8125 22.320312 C 0.8125 15.433594 0.8125 8.550781 0.8125 1.667969 C 0.8125 0.601562 1.390625 0.0117188 2.449219 0.0117188 C 6.984375 0.0117188 11.519531 0.0117188 16.054688 0.0117188 C 17.070312 0.0117188 17.660156 0.59375 17.660156 1.59375 C 17.664062 3.847656 17.664062 6.097656 17.660156 8.351562 C 17.660156 8.460938 17.691406 8.601562 17.640625 8.679688 C 17.5625 8.792969 17.421875 8.933594 17.308594 8.933594 C 17.203125 8.933594 17.050781 8.789062 16.992188 8.671875 C 16.9375 8.554688 16.96875 8.390625 16.96875 8.25 C 16.96875 6.109375 16.96875 3.96875 16.96875 1.828125 C 16.96875 0.960938 16.757812 0.746094 15.917969 0.746094 C 11.460938 0.746094 7.003906 0.742188 2.550781 0.746094 C 1.742188 0.746094 1.527344 0.953125 1.527344 1.75 C 1.527344 8.585938 1.527344 15.421875 1.527344 22.257812 C 1.527344 23.03125 1.75 23.25 2.511719 23.25 C 7 23.253906 11.488281 23.253906 15.976562 23.25 C 16.710938 23.25 16.964844 23.011719 16.964844 22.300781 C 16.96875 19.84375 16.96875 17.382812 16.964844 14.925781 C 16.964844 14.835938 16.957031 14.75 16.945312 14.515625 C 16.410156 15.058594 15.925781 15.484375 15.519531 15.976562 C 14.722656 16.933594 13.59375 17.351562 12.523438 17.835938 C 12.160156 18 11.777344 18.082031 11.484375 18.386719 C 11.414062 18.457031 11.15625 18.4375 11.074219 18.363281 C 10.988281 18.285156 10.941406 18.03125 11.003906 17.953125 C 11.6875 17.078125 11.855469 15.945312 12.46875 15.035156 C 12.609375 14.824219 12.738281 14.601562 12.914062 14.425781 C 16.148438 11.179688 19.390625 7.941406 22.628906 4.699219 C 23.261719 4.066406 23.734375 4.070312 24.371094 4.703125 C 24.472656 4.804688 24.574219 4.90625 24.675781 5.007812 C 25.359375 5.691406 25.363281 6.125 24.660156 6.808594 C 24.429688 7.03125 24.367188 7.199219 24.476562 7.53125 C 24.671875 8.125 24.527344 8.695312 24.070312 9.152344 C 23.132812 10.09375 22.1875 11.035156 21.246094 11.976562 C 21.066406 12.160156 20.875 12.308594 20.636719 12.085938 C 20.390625 11.851562 20.554688 11.65625 20.730469 11.480469 C 21.640625 10.570312 22.546875 9.660156 23.457031 8.753906 C 23.726562 8.484375 23.917969 8.191406 23.792969 7.71875 Z M 14.4375 15.871094 C 17.375 12.929688 20.308594 10 23.226562 7.078125 C 22.9375 6.777344 22.613281 6.4375 22.332031 6.148438 C 19.382812 9.097656 16.453125 12.03125 13.523438 14.960938 C 13.816406 15.25 14.136719 15.574219 14.4375 15.871094 Z M 22.898438 5.484375 C 23.234375 5.84375 23.566406 6.195312 23.894531 6.546875 C 24.359375 6.125 24.335938 5.664062 23.816406 5.171875 C 23.464844 4.832031 23.414062 4.855469 22.898438 5.484375 Z M 13.8125 16.421875 C 13.523438 16.117188 13.273438 15.851562 13.003906 15.5625 C 12.738281 16.09375 12.480469 16.605469 12.222656 17.113281 C 12.246094 17.140625 12.269531 17.164062 12.292969 17.1875 C 12.800781 16.929688 13.3125 16.671875 13.8125 16.421875 Z M 13.8125 16.421875 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 9.253906 2.371094 C 10.535156 2.371094 11.820312 2.367188 13.101562 2.371094 C 13.859375 2.375 14.203125 2.714844 14.214844 3.46875 C 14.222656 3.867188 14.230469 4.261719 14.210938 4.660156 C 14.179688 5.257812 13.800781 5.613281 13.203125 5.625 C 12.808594 5.632812 12.40625 5.664062 12.015625 5.609375 C 11.855469 5.589844 11.71875 5.378906 11.570312 5.253906 C 11.722656 5.144531 11.863281 4.96875 12.019531 4.949219 C 12.378906 4.902344 12.75 4.929688 13.113281 4.929688 C 13.363281 4.929688 13.492188 4.828125 13.480469 4.558594 C 13.46875 4.195312 13.46875 3.828125 13.480469 3.464844 C 13.492188 3.1875 13.386719 3.058594 13.097656 3.058594 C 10.53125 3.0625 7.964844 3.0625 5.398438 3.058594 C 5.082031 3.058594 4.980469 3.195312 4.992188 3.496094 C 5.011719 3.808594 5.019531 4.128906 4.992188 4.445312 C 4.960938 4.832031 5.132812 4.9375 5.492188 4.933594 C 6.949219 4.925781 8.40625 4.929688 9.863281 4.933594 C 10.007812 4.933594 10.164062 4.898438 10.285156 4.953125 C 10.410156 5.007812 10.59375 5.160156 10.578125 5.25 C 10.5625 5.382812 10.398438 5.601562 10.296875 5.605469 C 8.523438 5.621094 6.75 5.636719 4.976562 5.582031 C 4.535156 5.570312 4.296875 5.144531 4.277344 4.683594 C 4.257812 4.242188 4.253906 3.796875 4.273438 3.355469 C 4.304688 2.710938 4.660156 2.375 5.308594 2.371094 C 6.621094 2.363281 7.9375 2.371094 9.253906 2.371094 Z M 9.253906 2.371094 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 5.285156 22.183594 C 4.390625 22.128906 3.527344 21.640625 3.050781 20.609375 C 2.570312 19.574219 2.683594 18.554688 3.417969 17.671875 C 4.125 16.832031 5.050781 16.507812 6.144531 16.742188 C 6.40625 16.800781 6.679688 16.863281 6.613281 17.207031 C 6.546875 17.554688 6.292969 17.5 6.019531 17.445312 C 4.921875 17.222656 3.914062 17.792969 3.589844 18.785156 C 3.277344 19.746094 3.707031 20.785156 4.605469 21.246094 C 5.371094 21.644531 6.308594 21.511719 6.941406 20.917969 C 7.546875 20.351562 7.746094 19.417969 7.421875 18.644531 C 7.386719 18.558594 7.34375 18.472656 7.292969 18.390625 C 7.160156 18.164062 7.085938 17.894531 7.382812 17.800781 C 7.523438 17.757812 7.828125 17.917969 7.921875 18.066406 C 8.324219 18.734375 8.40625 19.460938 8.179688 20.214844 C 7.820312 21.40625 6.757812 22.191406 5.289062 22.183594 Z M 5.285156 22.183594 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 9.179688 9.515625 C 7.292969 9.515625 5.410156 9.515625 3.523438 9.515625 C 3.398438 9.515625 3.242188 9.554688 3.152344 9.5 C 3.007812 9.414062 2.84375 9.269531 2.820312 9.128906 C 2.804688 9.035156 3 8.882812 3.128906 8.792969 C 3.195312 8.746094 3.3125 8.777344 3.410156 8.777344 C 7.289062 8.777344 11.171875 8.777344 15.050781 8.777344 C 15.097656 8.777344 15.144531 8.777344 15.195312 8.777344 C 15.457031 8.777344 15.71875 8.855469 15.664062 9.15625 C 15.640625 9.296875 15.382812 9.414062 15.210938 9.507812 C 15.125 9.554688 14.992188 9.515625 14.882812 9.515625 C 12.980469 9.515625 11.082031 9.515625 9.179688 9.515625 Z M 9.179688 9.515625 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 9.285156 6.859375 C 11.203125 6.859375 13.121094 6.859375 15.035156 6.859375 C 15.175781 6.859375 15.339844 6.832031 15.457031 6.890625 C 15.5625 6.941406 15.675781 7.09375 15.675781 7.203125 C 15.679688 7.3125 15.566406 7.472656 15.460938 7.519531 C 15.328125 7.578125 15.152344 7.558594 14.996094 7.558594 C 11.160156 7.5625 7.328125 7.5625 3.496094 7.558594 C 3.371094 7.558594 3.222656 7.59375 3.121094 7.539062 C 2.992188 7.46875 2.820312 7.324219 2.820312 7.214844 C 2.820312 7.09375 2.988281 6.949219 3.117188 6.875 C 3.210938 6.816406 3.363281 6.859375 3.492188 6.859375 C 5.421875 6.859375 7.355469 6.859375 9.289062 6.859375 Z M 9.285156 6.859375 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 7.769531 11.433594 C 6.296875 11.433594 4.824219 11.433594 3.351562 11.433594 C 3.09375 11.433594 2.804688 11.453125 2.800781 11.085938 C 2.792969 10.707031 3.09375 10.734375 3.347656 10.734375 C 6.292969 10.730469 9.238281 10.734375 12.179688 10.734375 C 12.4375 10.734375 12.734375 10.71875 12.734375 11.082031 C 12.738281 11.445312 12.449219 11.433594 12.1875 11.433594 C 10.714844 11.429688 9.242188 11.433594 7.769531 11.433594 Z M 7.769531 11.433594 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 13.539062 21.320312 C 13.054688 21.085938 12.597656 20.941406 12.230469 20.675781 C 11.53125 20.164062 11.164062 20.125 10.429688 20.578125 C 10.296875 20.660156 10.074219 20.589844 9.890625 20.589844 C 9.929688 20.398438 9.898438 20.136719 10.011719 20.023438 C 10.664062 19.386719 11.71875 19.378906 12.488281 19.96875 C 13.339844 20.625 13.503906 20.625 14.425781 20.109375 C 14.546875 20.042969 14.753906 20.132812 14.917969 20.152344 C 14.898438 20.320312 14.933594 20.585938 14.84375 20.640625 C 14.441406 20.894531 14.003906 21.085938 13.539062 21.320312 Z M 13.539062 21.320312 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 5.535156 14.902344 C 6.089844 14.902344 6.648438 14.878906 7.199219 14.921875 C 7.363281 14.933594 7.507812 15.140625 7.660156 15.261719 C 7.507812 15.378906 7.355469 15.597656 7.199219 15.601562 C 6.089844 15.628906 4.976562 15.628906 3.867188 15.601562 C 3.707031 15.59375 3.554688 15.378906 3.394531 15.261719 C 3.554688 15.144531 3.707031 14.933594 3.871094 14.921875 C 4.421875 14.875 4.980469 14.902344 5.535156 14.902344 Z M 5.535156 14.902344 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(34.901961%,34.901961%,34.901961%)',
              fillOpacity: 1,
            }}
            d="M 5.53125 19.796875 C 5.292969 19.796875 5.046875 19.839844 4.828125 19.78125 C 4.679688 19.742188 4.566406 19.566406 4.4375 19.453125 C 4.550781 19.324219 4.65625 19.101562 4.785156 19.089844 C 5.28125 19.046875 5.789062 19.050781 6.289062 19.085938 C 6.40625 19.097656 6.613281 19.3125 6.597656 19.40625 C 6.578125 19.546875 6.417969 19.742188 6.28125 19.777344 C 6.042969 19.835938 5.78125 19.792969 5.53125 19.792969 C 5.53125 19.792969 5.53125 19.796875 5.53125 19.796875 Z M 5.53125 19.796875 "
          />
        </g>
      </svg>
    )
  },
)
ContractsAndVOs.displayName = 'ContractsAndVOs'

export default ContractsAndVOs

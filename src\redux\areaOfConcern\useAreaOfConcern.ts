import { useDispatch, useSelector } from 'react-redux'
import {
  getAreaOfConcerns,
  addAreaOfConcern,
  updateAreaOfConcern,
  deleteAreaOfConcern,
  sortAreaOfConcerns,
  getMasterRastingList,
} from '.'
import { IAreaOfConcern } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useAreaOfConcern = () => {
  const dispatch: AppDispatch = useDispatch()

  const getAreaOfConcernStatus = useSelector((state: RootState) => state.areaOfConcern.getAreaOfConcernStatus)

  const areaOfConcerns = useSelector((state: RootState) => state.areaOfConcern.areaOfConcerns)
  const masterRating = useSelector((state: RootState) => state.areaOfConcern.masterRatingList)

  const getAreaOfConcernsApi = (data: { period: string; project_name?: string }) => dispatch(getAreaOfConcerns(data))
  const getMasterRastingListApi = () => dispatch(getMasterRastingList())
  const addAreaOfConcernApi = (data: IAreaOfConcern) => dispatch(addAreaOfConcern(data))
  const updateAreaOfConcernApi = (data: { id: number; data: IAreaOfConcern }) => dispatch(updateAreaOfConcern(data))
  const deleteAreaOfConcernApi = (data: number) => dispatch(deleteAreaOfConcern(data))
  const sortAreaOfConcernsApi = (data: any) => dispatch(sortAreaOfConcerns(data))

  return {
    getAreaOfConcernStatus,
    areaOfConcerns,
    masterRating,
    getMasterRastingListApi,
    getAreaOfConcernsApi,
    addAreaOfConcernApi,
    updateAreaOfConcernApi,
    deleteAreaOfConcernApi,
    sortAreaOfConcernsApi,
  }
}
export default useAreaOfConcern

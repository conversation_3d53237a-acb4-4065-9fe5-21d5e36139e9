import React, { ReactElement } from 'react'
import TextareaAutosize, { TextareaAutosizeProps } from '@mui/material/TextareaAutosize'
import styles from './TextArea.module.scss'

interface TextAreaTypes extends TextareaAutosizeProps {
  labelText?: string
  error?: boolean
  helperText?: string
  className?: string
  required?: boolean
  isResizeVertical?: boolean
}

const Textarea: React.FC<TextAreaTypes> = ({
  labelText = '',
  error = false,
  helperText = '',
  className,
  required = false,
  isResizeVertical = true,
  ...props
}): ReactElement => {
  return (
    <div className={`${styles.container}  ${isResizeVertical ? styles.vertical : ''} ${className}`}>
      {labelText.length ? (
        <label className={labelText.length ? styles.label : ''}>
          {labelText}
          {required ? <div>*</div> : ''}
        </label>
      ) : (
        ''
      )}
      <TextareaAutosize
        aria-label="empty textarea"
        className={`${styles.textArea} ${error ? styles.errorField : ''}`}
        {...props}
      />
      {helperText && <span className={`${styles.text} ${error ? styles.error : ''}`}>{helperText}</span>}
    </div>
  )
}

export default Textarea

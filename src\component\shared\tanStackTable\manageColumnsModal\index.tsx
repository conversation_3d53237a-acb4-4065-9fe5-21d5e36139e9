import React from 'react'
import { DragIndicator } from '@mui/icons-material'
import { Checkbox, FormControlLabel, Popover } from '@mui/material'
import { useDrag, useDrop, DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import styles from './ManageColumns.module.scss'
import { excludeColumns } from '../constant'
import { CustomColumnDef } from '../interface'

interface ManageColumnsModalProps {
  anchorEl: HTMLElement | null
  onClose: () => void
  cols: CustomColumnDef<any>[]
  columnVisibility: Record<string, boolean>
  onColumnVisibility: (columnId: string) => void
  onColumnReorder: (dragId: any, dropId: any) => void
}

const ColumnItem = ({ column, index, moveColumn, onColumnVisibility, columnVisibility }: any) => {
  const ref = React.useRef(null)
  const [, drop] = useDrop({
    accept: 'column',
    drop: (item: { index: number }) => {
      if (item.index !== index) {
        moveColumn(item.index, index)
        item.index = index
      }
    },
  })

  const [{ isDragging }, drag] = useDrag({
    type: 'column',
    item: { index },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  drag(drop(ref))

  return (
    <div ref={ref} className={styles.dragItem} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <DragIndicator />
      <div>
        <FormControlLabel
          control={
            <Checkbox
              checked={!!columnVisibility[column?.accessorKey]}
              onChange={() => onColumnVisibility(column?.accessorKey)}
            />
          }
          label={column?.header}
        />
      </div>
    </div>
  )
}

const ManageColumnsModal: React.FC<ManageColumnsModalProps> = ({
  anchorEl,
  onClose,
  cols,
  columnVisibility,
  onColumnVisibility,
  onColumnReorder,
}) => {
  const open = Boolean(anchorEl)
  const filterColumns = cols.filter((column) => !excludeColumns.includes(column?.accessorKey))

  const moveColumn = (fromIndex: number, toIndex: number) => {
    onColumnReorder(fromIndex, toIndex)
  }

  return (
    <Popover
      id={open ? 'simple-popover' : undefined}
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      classes={{ root: styles.menuRoot, paper: styles.menuPaper }}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      transformOrigin={{ vertical: 'top', horizontal: 'left' }}
    >
      <DndProvider backend={HTML5Backend}>
        <div className={styles.items}>
          {filterColumns.map((column, index) => (
            <ColumnItem
              key={column?.accessorKey}
              index={index}
              column={column}
              moveColumn={moveColumn}
              onColumnVisibility={onColumnVisibility}
              columnVisibility={columnVisibility}
            />
          ))}
        </div>
      </DndProvider>
    </Popover>
  )
}

export default ManageColumnsModal

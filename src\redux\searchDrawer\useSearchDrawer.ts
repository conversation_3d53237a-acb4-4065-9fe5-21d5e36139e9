// useSearchDrawer.ts
import { useDispatch, useSelector } from 'react-redux'
import {
  setSearchQuery,
  setCategoryValue,
  setEntityValue,
  setClassificationValue,
  setProjectTypeValue,
  setLocationValue,
  setPortFolio,
  setDesignManager,
  setExecutive,
  setControl,
  setProcure,
  resetFilters,
  setCpdsValue,
  setProjectStatus,
  setDesignProjectOwner,
  setProjectId,
  setStartYear,
  setSvp,
  setDeliveryDirector,
  setOverallForecastedFinishDate,
  setContractTypeValue,
  setDeviationStatus,
  setLabelValue,
  setUserFilterFields,
} from './index'
import { RootState, AppDispatch } from '@/src/redux/store'

const useSearchDrawer = () => {
  const dispatch: AppDispatch = useDispatch()

  const searchQuery = useSelector((state: RootState) => state.filters.searchQuery)
  const categoryValue = useSelector((state: RootState) => state.filters.categoryValue)
  const entityValue = useSelector((state: RootState) => state.filters.entityValue)
  const classificationValue = useSelector((state: RootState) => state.filters.classificationValue)
  const projectTypeValue = useSelector((state: RootState) => state.filters.projectType)
  const locationValue = useSelector((state: RootState) => state.filters.locationValue)
  const contractTypeValue = useSelector((state: RootState) => state.filters.contractTypeValue)
  const portFolio = useSelector((state: RootState) => state.filters.portFolio)
  const designManager = useSelector((state: RootState) => state.filters.designManager)
  const executive = useSelector((state: RootState) => state.filters.executive)
  const deliveryDirector = useSelector((state: RootState) => state.filters.deliveryDirector)
  const control = useSelector((state: RootState) => state.filters.control)
  const procure = useSelector((state: RootState) => state.filters.procure)
  const cpds = useSelector((state: RootState) => state.filters.cpds)
  const project_id = useSelector((state: RootState) => state.filters.project_id)
  const projectStatus = useSelector((state: RootState) => state.filters.projectStatus)
  const designProjectOwner = useSelector((state: RootState) => state.filters.designProjectOwner)
  const startYear = useSelector((state: RootState) => state.filters.startYear)
  const delivery_project_manager = useSelector((state: RootState) => state.filters.delivery_project_manager)
  const svp = useSelector((state: RootState) => state.filters.svp)
  const labelValue = useSelector((state: RootState) => state.filters.labelValue)
  const deviationStatusValue = useSelector((state: RootState) => state.filters.deviationStatusValue)
  const overall_forecasted_finish_date = useSelector((state: RootState) => state.filters.overall_forecasted_finish_date)
  const userFilterFields = useSelector((state: RootState) => state.filters.userFilterFields)

  const setSearchQueryApi = (query: string) => {
    dispatch(setSearchQuery(query))
  }

  const setCpdsApi = (query: string[]) => {
    dispatch(setCpdsValue(query))
  }

  const setProjectIdApi = (query: string[]) => {
    dispatch(setProjectId(query))
  }

  const setCategoryValueApi = (values: string[]) => {
    dispatch(setCategoryValue(values))
  }

  const setEntityValueApi = (values: string[]) => {
    dispatch(setEntityValue(values))
  }

  const setDesignProjectOwnerApi = (values: string[]) => {
    dispatch(setDesignProjectOwner(values))
  }

  const setClassificationValueApi = (values: string[]) => {
    dispatch(setClassificationValue(values))
  }

  const setProjectTypeValueApi = (values: string[]) => {
    dispatch(setProjectTypeValue(values))
  }

  const setProjectStatusApi = (values: string[]) => {
    dispatch(setProjectStatus(values))
  }
  const setSvpApi = (values: string[]) => {
    dispatch(setSvp(values))
  }

  const setLocationValueApi = (values: string[]) => {
    dispatch(setLocationValue(values))
  }

  const setContractTypeApi = (values: string[]) => {
    dispatch(setContractTypeValue(values))
  }

  const setPortFolioApi = (values: string[]) => {
    dispatch(setPortFolio(values))
  }

  const setDesignManagerApi = (values: string[]) => {
    dispatch(setDesignManager(values))
  }

  const setExecutiveApi = (values: string[]) => {
    dispatch(setExecutive(values))
  }

  const setDeliveryDirectorApi = (values: string[]) => {
    dispatch(setDeliveryDirector(values))
  }

  const setControlApi = (values: string[]) => {
    dispatch(setControl(values))
  }

  const setProcureApi = (values: string[]) => {
    dispatch(setProcure(values))
  }

  const resetFiltersApi = () => {
    dispatch(resetFilters())
  }
  const setStartYearApi = (values: string[]) => {
    dispatch(setStartYear(values))
  }

  const setDeviationStatusApi = (values: string[]) => {
    dispatch(setDeviationStatus(values))
  }

  const setUserFilterFieldsApi = (values: string[]) => {
    dispatch(setUserFilterFields(values))
  }

  const setLabelValueApi = (values: string[]) => {
    dispatch(setLabelValue(values))
  }

  const setOverallForecastedFinishDateApi = (values: string[]) => {
    dispatch(setOverallForecastedFinishDate(values))
  }

  return {
    delivery_project_manager,
    startYear,
    designProjectOwner,
    searchQuery,
    categoryValue,
    entityValue,
    classificationValue,
    projectTypeValue,
    locationValue,
    contractTypeValue,
    portFolio,
    designManager,
    executive,
    deliveryDirector,
    control,
    procure,
    projectStatus,
    deviationStatusValue,
    cpds,
    project_id,
    labelValue,
    overall_forecasted_finish_date,
    userFilterFields,
    setStartYearApi,
    setSvpApi,
    setProjectStatusApi,
    setCpdsApi,
    setContractTypeApi,
    setDeviationStatusApi,
    setProjectIdApi,
    setSearchQueryApi,
    setCategoryValueApi,
    setEntityValueApi,
    setClassificationValueApi,
    setProjectTypeValueApi,
    setDesignProjectOwnerApi,
    setLocationValueApi,
    setPortFolioApi,
    setDesignManagerApi,
    setExecutiveApi,
    setDeliveryDirectorApi,
    setControlApi,
    setProcureApi,
    resetFiltersApi,
    setLabelValueApi,
    setOverallForecastedFinishDateApi,
    setUserFilterFieldsApi,
  }
}

export default useSearchDrawer

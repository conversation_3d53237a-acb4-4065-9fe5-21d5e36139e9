import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createRiskDiscipline,
  deleteRiskDiscipline,
  getRiskDiscipline,
  updateRiskDiscipline,
} from '../services/riskDiscipline'
import { IGetRiskDisciplineResponse, IUpdateRiskDiscipline } from '../services/riskDiscipline/interface'

export const QUERY_RISK_DISCIPLINE = 'risk-discipline'

/**
 * Hook to fetch Risk Discipline
 * @param enabled - Enables or disables the query
 */
export const useGetRiskDiscipline = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetRiskDisciplineResponse>({
    queryKey: [QUERY_RISK_DISCIPLINE],
    queryFn: getRiskDiscipline,
    enabled,
  })

  return {
    riskDisciplines: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Risk Discipline
 */
export const useCreateRiskDiscipline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createRiskDiscipline,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_DISCIPLINE] })
    },
  })
}

/**
 * Hook to delete a Risk Discipline
 */
export const useDeleteRiskDiscipline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteRiskDiscipline,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_DISCIPLINE] })
    },
  })
}

/**
 * Hook to update a Risk Discipline
 */
export const useUpdateRiskDiscipline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateRiskDiscipline,
    onMutate: async (updatedData: IUpdateRiskDiscipline) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_RISK_DISCIPLINE] })
      const previousData = queryClient.getQueryData<IGetRiskDisciplineResponse>([QUERY_RISK_DISCIPLINE])
      // Optimistically update the cache
      queryClient.setQueryData<IGetRiskDisciplineResponse>([QUERY_RISK_DISCIPLINE], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, risk_discipline: updatedData.risk_discipline } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_RISK_DISCIPLINE], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_DISCIPLINE] })
    },
  })
}

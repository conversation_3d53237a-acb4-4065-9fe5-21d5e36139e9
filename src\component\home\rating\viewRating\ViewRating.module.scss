@import '/styles/color.scss';

.container {
  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;

    .headerTitle {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      text-align: left;
      padding: 13px 0 13px 20px;
    }

    .actionButtons {
      display: flex;
      gap: 20px;

      .closeButton {
        padding: 8px 10px;
      }
    }
  }

  .content {
    margin: 20px 20px 0px 20px;
    .addProjectButton {
      padding: 8px 10px;
    }
  }
}

.tableData {
  padding: 9px 10px;
  border-right: 1px solid $LIGHT_200;
  background: $WHITE;
  font-family: Poppins;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  text-align: left;
  color: $BLACK;
  position: relative;
  overflow: hidden;

  .editRow {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    vertical-align: top;
    width: 359px;
    .updateField {
      width: 100%;
      border-radius: 0px;
      display: flex;
      background-color: $FOCUS_TEXTFIELD_BG !important;

      :focus {
        border-radius: 0px;
        background: $FOCUS_TEXTFIELD_BG !important;
      }
      > textarea {
        color: $DARK;
        background-color: $FOCUS_TEXTFIELD_BG !important;
        border-radius: 0px !important;
        padding: 8px 0 0px 9px !important;
        font-size: 12px !important;
        font-weight: 400 !important;
        line-height: 18px !important;
        min-height: auto !important;
        max-height: 100% !important;
      }
    }

    .updateButton {
      margin-left: 2px;
      cursor: pointer;
      padding: 4px 9px;
      border-radius: 20px;
      border: 1px solid $DARK;
      margin-right: 10px;
      text-align: center;
      background-color: $DARK;

      .updateFont {
        color: $LIGHT;
      }
    }
  }

  //for the cell content without edit field
  .cellContent {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 340px;
    text-align: center;

    .rowValue {
      word-break: break-all;
      padding-top: 2px;
      padding-bottom: 2px;
      text-align: justify;
    }

    .badge {
      display: flex;
      justify-content: center;
      margin-left: 3px;
      .badgeValue {
        margin-right: 7px;
        border-radius: 20px;
        background: $LIGHT_200;
        letter-spacing: 0em;
        text-align: right;
        color: $DARK;
        padding: 4px 9px 4px 9px;
      }
    }
  }

  .actionCell {
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
.deleteCard {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .deleteHeader {
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    text-align: left;
    color: $DARK;

    .boldHead {
      font-weight: 700;
      color: $DARK;
    }
  }

  .deleteContent {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    color: $DARK_200;

    .boldContent {
      font-weight: 700;
      color: $DARK_200;
    }
  }

  .actionButtons {
    display: flex;
    gap: 8px;

    .confirmButton {
      border: 1px solid $ERROR;
      background: $ERROR;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
      color: $LIGHT;

      &:focus,
      &:hover {
        border: 1px solid $ERROR;
        background: $ERROR;
        color: $LIGHT;
      }
    }
  }
}

.editCell {
  padding: 3px 1px;
  background: $FOCUS_TEXTFIELD_BG;
  border: 1px solid $FOCUS_TEXTFIELD_BORDER;
}

.cursorPointer {
  cursor: pointer;
}

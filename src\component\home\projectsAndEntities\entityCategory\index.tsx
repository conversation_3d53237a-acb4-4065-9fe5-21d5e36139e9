import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './EntityCategory.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateEntityCategory,
  useDeleteEntityCategory,
  useGetEntityCategory,
  useUpdateEntityCategory,
} from '@/src/hooks/useEntityCategory'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const EntityCategory: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { entityCategory } = drawerStates
  //REACT-QUERY
  const { entityCategories } = useGetEntityCategory(entityCategory)
  // MUTATIONS
  const { mutate: addEntityCategory } = useCreateEntityCategory()
  const { mutate: deleteEntityCategory } = useDeleteEntityCategory()
  const { mutate: updateEntityCategory } = useUpdateEntityCategory()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const handleSubmit = async (values: any) => {
    const payload = { entity_category: values.entity_category }
    if (editIndex !== null) {
      updateEntityCategory(
        { id: editIndex, ...payload },
        handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
      )
      setEditIndex(null)
    } else {
      addEntityCategory(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
    }
    formik.resetForm()
  }

  const formik = useFormik({
    initialValues: { entity_category: '' },
    onSubmit: async (values) => {
      handleSubmit(values)
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteEntityCategory(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editEntity: any | undefined = entityCategories.find((entity) => entity.id === id)
    if (editEntity) {
      formik.setValues({ entity_category: editEntity.entity_category })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'entity_category',
      header: 'Entity Category',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: ({ row }: any) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
          <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.id)} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Entity Category'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="entity_category"
              labelText={'Entity Category'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.entity_category}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.entity_category?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.entity_category?.trim()}
              >
                {'+ Add Entity Category'}
              </Button>
            )}
          </div>
        </form>

        {/* <Table data={entityCategories} columns={columns} /> */}
        <TanStackTable
          rows={sortData(entityCategories, 'entity_category') as any}
          columns={columns}
          isOverflow={true}
        />

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default EntityCategory

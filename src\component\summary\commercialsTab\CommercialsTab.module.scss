@import '/styles/color.scss';

.tableWrapper {
  padding: 16px 24px;
  position: relative;
}

.addButtonWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 0px;
}

.addButtonPosition {
  position: absolute;
  top: -44px;
  right: 0px;
}

.container {
  // display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 92px;
}

.editRowIcon,
.CorrectIcon,
.deleteRowIcon {
  cursor: pointer;
}
.CorrectIcon {
  fill: green;
  path {
    fill: green;
  }
}
.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.mainContainer {
  height: 100%;
  width: 100%;
}
.inputField {
  > div {
  }
  > div {
    width: 100%;
    > div > input {
      text-align: center;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 12px !important;
    }
  }
}
.accordionContainer {
  display: flex;
  gap: 10px;
  flex-direction: column;
  // margin-top: 20px;
  // padding: 0 24px;

  .buttons {
    display: flex;
    justify-content: flex-end;
  }
}
.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.commercialSection {
  margin: 25px;
}
.timerIcon {
  display: flex;
  align-items: center;
  gap: 4px;
}

.accordion {
  background-color: #f9f9f9 !important;
  box-shadow: none;
  border-radius: 8px !important;
  margin-bottom: 28px;
  &::before {
    content: none;
  }
  .expandCollapseGroup {
    gap: 6px;
  }
  .expandedCollapse {
    width: 120px;
    margin-right: 12px;
  }
  .summary {
    // padding: 0 20px;
    padding-left: 0 !important;
    min-height: 56px;
    align-items: baseline;
    max-height: 72px;

    .expandIcon {
      width: 24px;
      height: 24px;
    }
    .content {
      margin: 16px 0 35px 0;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // &::after {
      //   content: "";
      //   position: absolute;
      //   left: 50px;
      //   right: 51px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   border: 1px solid $LIGHT_200;
      // }
      .summaryTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-right: 8px;

        .header {
          display: flex;
          gap: 10px;
          align-items: center;
          padding: 8px 20px;
          background: #eeeeec;
          border-radius: 0 8px 8px 0;
          font-size: 16px;
          font-weight: 700;
          line-height: 18px;
          color: $DARK;
          white-space: nowrap;
          position: relative;
          z-index: 1;
          padding-right: 8px;
          min-width: 212px;
        }
      }
      .editIcon {
        color: black !important;
        margin: 0 8px 0 auto;
      }
    }
  }
  .details {
    padding: 0;
    margin: 0 16px 25px;
  }
}
.container {
  padding: 0px 0px;
}

.accordionSection {
  margin: 16px 20px 24px 0;
}
.buttonPlus {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  border: 1px solid #444444;
  background: #444444;
  width: 20px;
  height: 20px;
}
.detailsAction {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // width: 100%;
  gap: 16px;
  position: relative;
  margin-right: 8px;
  // .btnOfAction {
  //   background-color: $PULSE_TURQUOISE;
  // }
  .lastUpdatedSection {
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-end;
  }
  .action {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

.summaryTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.content {
  padding-left: 0px !important;
}

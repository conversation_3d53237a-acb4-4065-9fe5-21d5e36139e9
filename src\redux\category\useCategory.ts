import { useDispatch, useSelector } from 'react-redux'
import { renameCategory, deleteCategory, addCategory } from '.'
import { IAddCategoryPayload, IDeleteCategoryPayload, IRenameCategoryPayload } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useCategory = () => {
  const dispatch: AppDispatch = useDispatch()

  const categories = useSelector((state: RootState) => state.category.categories)
  const localCategory = useSelector((state: RootState) => state.category.localCategory)

  const addCategoryApi = (data: IAddCategoryPayload) => dispatch(addCategory(data))
  const renameCategoryApi = (data: IRenameCategoryPayload) => dispatch(renameCategory(data))
  const deleteCategoryApi = (data: IDeleteCategoryPayload) => dispatch(deleteCategory(data))

  return {
    categories,
    localCategory,
    addCategory<PERSON>pi,
    deleteCategory<PERSON>pi,
    rename<PERSON>ategory<PERSON><PERSON>,
  }
}
export default useCategory

import React, { useEffect, useState, useMemo, useCallback } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './CategoryTab.module.scss'
import { getMultiPhaseCategoryTableData } from './helper'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { ERROR_MESSAGE } from '@/src/constant/enum'
import { useGetProjectToProjectPhaseCategories } from '@/src/hooks/useMasterProjectToProjectPhaseCategory'
import { useGetProjectPhaseCategories } from '@/src/hooks/useProjectPhaseCategory'
import { IRenameCategoryPayload } from '@/src/redux/category/interface'
import useCategory from '@/src/redux/category/useCategory'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import useStatus from '@/src/redux/status/useStatus'
import { IProjectPhaseCategoryPayload } from '@/src/services/masterProjectToProjectPhaseCategories/interface'
import {
  getEditedValue,
  getValue,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
} from '@/src/utils/arrayUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const CategoryTab: React.FC<{ isStageDrawer: boolean }> = ({ isStageDrawer }) => {
  const router = useRouter()
  const projectName = router.query.slug as string

  const { getStatusApi } = useStatus()
  const { projectPhaseCategories } = useGetProjectPhaseCategories()
  const { currentPeriod, getMasterPeriodStates } = useMasterPeriod()
  const { projectToProjectPhaseCategories, refetch: refetchProjectToProjectPhaseCategories } =
    useGetProjectToProjectPhaseCategories(currentPeriod, projectName)
  const { renameCategoryApi, deleteCategoryApi, addCategoryApi } = useCategory()
  const [editData, setEditData] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // useEffect(() => {
  //   if (isStageDrawer && currentPeriod) {
  //     getMasterPhaseCategoryApi({ period: currentPeriod, project_name: projectName })
  //     getStatusApi({ period: currentPeriod, project_name: projectName })
  //   }
  // }, [isStageDrawer, currentPeriod, projectName, getMasterPeriodStates])

  const projectPhaseCategoryOption: any = useMemo(() => {
    return prepareDropdownOptions(projectPhaseCategories, 'project_phase_category')
  }, [projectPhaseCategories])

  const deleteCategory = async () => {
    const response: Record<string, any> = await deleteCategoryApi({
      master_project_phase_category_id: deleteModel,
    })

    setDeleteModel(null)
    setIsLoading(false)
    if (!response?.payload?.success) {
      errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
      return
    }
    successToast('Record deleted successfully')
    refetchProjectToProjectPhaseCategories()
  }

  const handleSubmit = async (newCategoryId: number | null) => {
    setIsLoading(true)
    try {
      //* Manage add data
      const addPayload: IProjectPhaseCategoryPayload = {
        project_name: projectName,
        period: currentPeriod,
        master_project_phase_category_id: newCategoryId,
      }

      if (!editData) {
        let response: Record<string, any> = await addCategoryApi(addPayload)

        if (!response?.payload?.success) {
          errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
          return
        }

        refetchProjectToProjectPhaseCategories()
        setIsLoading(false)
        return
      }

      if (editData === newCategoryId) {
        errorToast('Please select a different category than the current one!')
        setIsLoading(false)
        return
      }

      //* Manage Edit data
      const payload: IRenameCategoryPayload = {
        project_name: projectName,
        period: currentPeriod,
        old_master_project_phase_category_id: editData,
        master_project_phase_category_id: newCategoryId,
      }

      const response: Record<string, any> = await renameCategoryApi(payload)

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
        setEditData(null)
        return
      }

      refetchProjectToProjectPhaseCategories()

      setEditData(null)
    } catch (error) {
      console.log('error: ', error)
      setEditData(null)
      setIsLoading(false)
    }
    setIsLoading(false)
  }

  const formik = useFormik({
    initialValues: { master_project_phase_category_id: null, editData: editData || null },
    // enableReinitialize: true,
    onSubmit: async ({ master_project_phase_category_id }) => {
      handleSubmit(master_project_phase_category_id)
      formik.resetForm()
    },
  })

  const columns: CustomColumnDef<any>[] = useMemo(
    () => [
      { accessorKey: 'category', header: 'Category', flex: 1, align: 'left' },
      {
        accessorKey: 'action',
        header: 'Action',
        cell: ({ row }) => (
          <div className={styles.actionBts}>
            <EditIcon
              className={styles.editIcon}
              onClick={() => {
                formik.setValues({
                  ...formik.values,
                  master_project_phase_category_id: row.original?.MasterProjectPhaseCategory?.id,
                })
                setEditData(row.original?.id)
              }}
            />
            <DeleteIcon
              className={styles.deleteRowIcon}
              onClick={() => {
                setDeleteModel(row.original?.id)
              }}
            />
          </div>
        ),
        size: 70,
      },
    ],
    [],
  )

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <TanStackTable
          rows={sortData(getMultiPhaseCategoryTableData(projectToProjectPhaseCategories), 'category')}
          columns={columns}
        />
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <ComboBox
            className={styles.selectField}
            focusCustomClass={styles.focusClass}
            options={projectPhaseCategoryOption}
            labelText="Project Phase Category *"
            placeholder="Select Project Phase Category..."
            value={
              formik.values.master_project_phase_category_id
                ? getValue(projectPhaseCategoryOption, formik.values.master_project_phase_category_id)
                : null
            }
            onChange={(val) => {
              formik.setValues({
                ...formik.values,
                master_project_phase_category_id: val?.value || null,
              })
            }}
            clearIcon={true}
            disabled={isLoading}
          />
          <Button
            className={styles.addProjectButton}
            type="submit"
            disabled={formik.isSubmitting || !formik.values?.master_project_phase_category_id || isLoading}
          >
            {editData ? 'Update' : 'Add'} Project Phase Category
          </Button>
        </form>
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => {
            setDeleteModel(null)
            editData && setEditData(null)
          }}
          handleConfirm={() => deleteCategory()}
          loading={isLoading}
          message={
            <span>
              Are you sure want to delete <strong>{deleteModel}</strong> ?
            </span>
          }
        />
      </div>
    </div>
  )
}

export default CategoryTab

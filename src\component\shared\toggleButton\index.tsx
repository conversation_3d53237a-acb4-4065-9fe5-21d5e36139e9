import React, { useState, useEffect } from 'react'
import { ButtonGroup } from '@mui/material'
import styles from './ToggleButton.module.scss'
import Button from '../button'

interface Props {
  className?: string
  labelText?: string
  leftName?: string
  rightName?: string
  selectValue?: boolean
  onChange: (value: boolean) => void
}

const ToggleButton: React.FC<Props> = ({
  className = '',
  labelText = '',
  leftName = 'Left',
  rightName = '',
  selectValue = true,
  onChange,
}) => {
  const [isActive, setIsActive] = useState(selectValue)

  useEffect(() => {
    setIsActive(selectValue)
  }, [selectValue])

  const handleToggle = (value: boolean) => {
    setIsActive(value)
    onChange(value)
  }

  return (
    <div className={className} style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <label className={labelText.length ? styles.label : ''}>{labelText}</label>
      <ButtonGroup variant="outlined" aria-label="outlined button group" classes={{ root: styles.buttonGroup }}>
        <Button
          className={styles.button}
          onClick={() => handleToggle(true)}
          color={isActive ? 'primary' : 'secondary'}
          variant={isActive ? 'outlined' : 'contained'}
        >
          {leftName}
        </Button>
        <Button
          className={`${styles.button}`}
          onClick={() => handleToggle(false)}
          color={!isActive ? 'primary' : 'secondary'}
          variant={!isActive ? 'outlined' : 'contained'}
        >
          {rightName}
        </Button>
      </ButtonGroup>
    </div>
  )
}

export default ToggleButton

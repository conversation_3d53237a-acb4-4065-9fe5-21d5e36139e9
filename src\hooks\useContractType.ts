import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterContractType,
  deleteMasterContractType,
  getMasterContractType,
  updateMasterContractType,
} from '../services/contractType'
import { IGetMasterContractTypesResponse, IUpdateContractType } from '../services/contractType/interface'

export const QUERY_CONTRACT_TYPE = 'contract-type'

/**
 * Hook to fetch ContractType
 * @param enabled - Enables or disables the query
 */
export const useGetContractType = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterContractTypesResponse>({
    queryKey: [QUERY_CONTRACT_TYPE],
    queryFn: getMasterContractType,
    enabled,
  })

  return {
    contractTypes: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a ContractType
 */
export const useCreateContractType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterContractType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACT_TYPE] })
    },
  })
}

/**
 * Hook to delete a ContractType
 */
export const useDeleteContractType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterContractType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACT_TYPE] })
    },
  })
}

/**
 * Hook to update a ContractType
 */
export const useUpdateContractType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterContractType,
    onMutate: async (updatedData: IUpdateContractType) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_CONTRACT_TYPE] })
      const previousData = queryClient.getQueryData<IGetMasterContractTypesResponse>([QUERY_CONTRACT_TYPE])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterContractTypesResponse>([QUERY_CONTRACT_TYPE], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, contract_type: updatedData.contract_type } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_CONTRACT_TYPE], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACT_TYPE] })
    },
  })
}

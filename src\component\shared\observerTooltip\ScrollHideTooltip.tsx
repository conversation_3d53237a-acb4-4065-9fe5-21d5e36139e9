import React, { useRef, useEffect } from 'react'
import { Box, Tooltip as MuiTooltip, TooltipProps } from '@mui/material'
import { useScrollHideTooltip } from './useScrollHideTooltip'

interface Pro<PERSON> extends TooltipProps {
  scrollHideDelay?: number
  enterDelay?: number
  leaveDelay?: number
  movementThreshold?: number // Minimum pixels moved to hide tooltip (default: 0)
  trackTargetElement?: boolean // Track the tooltip target element position (default: true)
  trackScrollableParents?: boolean // Track scrollable parent containers (default: true)
}

// Custom Tooltip component that hides on scroll with position tracking
const ScrollHideTooltip = ({
  children,
  title,
  placement = 'bottom',
  arrow = true,
  enterDelay = 500,
  leaveDelay = 200,
  movementThreshold = 40, // TODO : Make it dynamic set same size as children or ele-listener-wrapper
  trackTargetElement = true,
  trackScrollableParents = true,
  ...otherProps
}: Props) => {
  const spanRef = useRef<HTMLSpanElement>(null)

  const { isOpen, handleOpen, handleClose, handleMouseEnter, handleMouseLeave, setTargetElement } =
    useScrollHideTooltip({
      movementThreshold,
      trackTargetElement,
      trackScrollableParents,
    })

  // Set the target element reference when component mounts or isOpen changes
  useEffect(() => {
    if (spanRef.current) {
      setTargetElement(spanRef.current)
    }
  }, [setTargetElement, isOpen])

  return (
    <MuiTooltip
      title={title}
      placement={placement}
      arrow={arrow}
      enterDelay={enterDelay}
      leaveDelay={leaveDelay}
      open={isOpen}
      onOpen={handleOpen}
      onClose={handleClose}
      // Disable MUI's default hover behavior since we're controlling it
      disableHoverListener={true} // Changed to true to fully control hover behavior
      disableFocusListener={true}
      disableTouchListener={true}
      {...otherProps}
    >
      <Box
        component={'span'}
        className="ele-listener-wrapper"
        ref={spanRef}
        onMouseEnter={handleMouseEnter} // Use new handler
        onMouseLeave={handleMouseLeave} // Use new handler
        sx={{ display: 'inline-block', width: '100%' }}
      >
        {children}
      </Box>
    </MuiTooltip>
  )
}

export default ScrollHideTooltip

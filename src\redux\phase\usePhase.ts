import { useDispatch, useSelector } from 'react-redux'
import {
  getMasterPhase,
  addMasterPhase,
  updateMasterPhase,
  deleteMasterPhase,
  setLocalPhase,
  setLocalSubStage,
  getMasterPhaseCategory,
  addMasterPhaseCategory,
} from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const usePhase = () => {
  const dispatch: AppDispatch = useDispatch()

  const getMasterPhaseStatus = useSelector((state: RootState) => state.phase.getMasterPhaseStatus)
  const phases = useSelector((state: RootState) => state.phase.phases)
  const uniquePhaseCategories = useSelector((state: RootState) => state.phase.uniquePhaseCategories)
  const localPhase = useSelector((state: RootState) => state.phase.localPhase)
  const getMasterPhaseCategoryStatus = useSelector((state: RootState) => state.phase.getMasterPhaseCategoryStatus)
  const addMasterPhaseCategoryStatus = useSelector((state: RootState) => state.phase.addMasterPhaseCategoryStatus)

  const getMasterPhaseApi = () => dispatch(getMasterPhase())
  const getMasterPhaseCategoryApi = (data: { period: string; project_name?: string }) =>
    dispatch(getMasterPhaseCategory(data))
  const addMasterPhaseApi = (data: { project_phase: string }) => dispatch(addMasterPhase(data))
  const addMasterPhaseCategoryApi = (data: {
    period: string
    project_name?: string
    phase: string | null
    category: string
  }) => dispatch(addMasterPhaseCategory(data))
  const updateMasterPhaseApi = (data: { id: number; project_phase: string }) => dispatch(updateMasterPhase(data))
  const deleteMasterPhaseApi = (data: number) => dispatch(deleteMasterPhase(data))
  const setLocalPhaseApi = (data: any) => dispatch(setLocalPhase(data))
  const setLocalSubStageApi = (data: any) => dispatch(setLocalSubStage(data))

  return {
    getMasterPhaseCategoryStatus,
    addMasterPhaseCategoryStatus,
    getMasterPhaseStatus,
    phases,
    uniquePhaseCategories,
    localPhase,
    setLocalPhaseApi,
    setLocalSubStageApi,
    getMasterPhaseApi,
    getMasterPhaseCategoryApi,
    addMasterPhaseApi,
    addMasterPhaseCategoryApi,
    updateMasterPhaseApi,
    deleteMasterPhaseApi,
  }
}
export default usePhase

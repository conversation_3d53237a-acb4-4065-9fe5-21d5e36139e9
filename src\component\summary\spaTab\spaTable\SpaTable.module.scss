.actions {
  display: flex;
  gap: 10px;
  flex-direction: row;
}
.editRowIcon,
.CorrectIcon,
.deleteRowIcon {
  cursor: pointer;
}
.spaTableContainer {
  margin-top: 20px;
  position: relative;
  height: calc(100% - 40px);
  overflow: auto;
  @media (max-width: 1020px) {
    height: calc(100% - 40px);
  }

  :global(.SPATableWrapper) {
    max-height: 100% !important;
    overflow: auto !important;
  }
}

// .expandTable {
//   max-height: calc(100vh - 250px) !important;
// }

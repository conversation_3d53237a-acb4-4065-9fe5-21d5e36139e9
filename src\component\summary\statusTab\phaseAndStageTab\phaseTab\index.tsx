import React, { useEffect, useState, useCallback, useMemo } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import { getMultiProjectToPhaseTableData } from './helper'
import styles from './PhaseTab.module.scss'
import ConfirmationModal from '@/src/component/confirmationModal'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import Loader from '@/src/component/shared/loader'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import ConfirmationModel from '@/src/component/updateProgress/progressForm/confirmationModel'
import { ERROR_MESSAGE } from '@/src/constant/enum'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { useGetProjectToPhase } from '@/src/hooks/useMasterProjectToPhase'
import { useGetProjectToProjectPhaseCategories } from '@/src/hooks/useMasterProjectToProjectPhaseCategory'
import { useGetProjectPhaseCategories } from '@/src/hooks/useProjectPhaseCategory'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import { IAddPhasePayload, IRenamePhasePayload, IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { StatusEnum } from '@/src/redux/types'
import {
  getEditedValue,
  getValue,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
} from '@/src/utils/arrayUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const PhaseTab: React.FC<{ isStageDrawer: boolean }> = ({ isStageDrawer }) => {
  const router = useRouter()
  const projectName = router.query.slug as string

  const {
    getStatusApi,
    statuses,
    renamePhaseNameApi,
    addPhaseNameApi,
    deletePhaseApi,
    renamePhaseProcess,
    addPhaseProcess,
  } = useStatus()
  const { currentPeriod, getMasterPeriodStates } = useMasterPeriod()
  const [editData, setEditData] = useState<{
    id: number
    phase: string | null
    master_project_phase_category_id?: number | null
  } | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { projectPhaseCategories } = useGetProjectPhaseCategories()
  const { projectToProjectPhaseCategories } = useGetProjectToProjectPhaseCategories(currentPeriod, projectName)
  const { projectToPhase, refetch: refetchProjectToPhase } = useGetProjectToPhase(currentPeriod, projectName)
  const [isConfirmationModel, setIsConfirmationModel] = useState<boolean>(false)
  const [confirmationMessage, setConfirmationMessage] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const getPhaseById = (id: number | null) => {
    const record = getMultiProjectToPhaseTableData(projectToPhase).find(
      (item: any) => item?.id?.toString() === id?.toString(),
    )
    return record
  }

  // const categoryDropDownValue: string[] = useMemo(() => {
  //   const categorySet = new Set<string>()
  //   // Extract from localCategory
  //   localCategory.forEach((item: any) => {
  //     const value = item.category
  //     if (value?.includes(MULTI_SELECT_SEPARATOR)) {
  //       value.split(MULTI_SELECT_SEPARATOR).forEach((cat: string) => {
  //         if (cat?.trim()) categorySet.add(cat.trim())
  //       })
  //     } else if (value?.trim()) {
  //       categorySet.add(value.trim())
  //     }
  //   })

  //   return Array.from(categorySet)
  // }, [statuses, localCategory])

  /**
   * Utility function to get unique phases from statuses
   */
  const getUniquePhases = useCallback((phasesArray: { phase: string; isNew?: boolean }[]) => {
    const phasesSet = new Set()
    return phasesArray.filter(({ phase }) => {
      if (phasesSet.has(phase)) return false
      phasesSet.add(phase)
      return true
    })
  }, [])

  /*** Fetches phase statuses when component mounts or dependencies change. */
  useEffect(() => {
    // getMasterPhaseApi()

    if (isStageDrawer && currentPeriod) {
      getStatusApi({ period: currentPeriod, project_name: projectName })
    }
  }, [isStageDrawer, currentPeriod, projectName, getMasterPeriodStates])

  const projectPhaseCategoryOption: any = useMemo(() => {
    return prepareMultiPhaseCategoryDropdownOptions(
      projectToProjectPhaseCategories,
      'MasterProjectPhaseCategory',
      'project_phase_category',
      true,
    )
  }, [statuses])

  /**
   * Handles phase renaming
   */
  const handleRenamePhase = async () => {
    if (!editData) return

    setIsLoading(true)

    const payload: IRenamePhasePayload = {
      id: editData?.id,
      master_project_phase_category_id: formik.values?.master_project_phase_category_id || null,
      phase: formik.values?.phase?.trim() || null,
    }

    const response: Record<string, any> = await renamePhaseNameApi(payload)

    if (!response?.payload?.success) {
      formik.setValues({
        ...formik.values,
        phase: editData?.phase || null,
        master_project_phase_category_id: editData?.master_project_phase_category_id || null,
      })

      errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
      setIsLoading(false)
    } else {
      setEditData(null)
      handleClose()

      formik.setValues({ phase: '', master_project_phase_category_id: null })

      successToast(response?.payload?.message)

      refetchProjectToPhase()
      setIsLoading(false)
    }
    setIsConfirmationModel(false)
    setConfirmationMessage('')
  }

  /**
   * Handles phase renaming
   */
  const handleAddPhase = async ({
    phase,
    master_project_phase_category_id,
  }: {
    phase: string | null
    master_project_phase_category_id: number | null
  }) => {
    if (!master_project_phase_category_id) return

    const payload: IAddPhasePayload = {
      period: currentPeriod,
      project_name: projectName,
      master_project_phase_category_id,
      phase: phase ? phase?.trim() : null,
    }
    setIsLoading(true)
    const response: Record<string, any> = await addPhaseNameApi(payload)

    if (!response?.payload?.success) {
      errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
      setIsLoading(false)
      return
    }
    formik.setValues({ phase: '', master_project_phase_category_id: null })
    refetchProjectToPhase()
    setIsLoading(false)
  }

  /**
   * Deletes a phase
   */
  const deletePhase = async () => {
    if (!deleteModel) return

    setIsLoading(true)
    const response: Record<string, any> = await deletePhaseApi(deleteModel)

    setDeleteModel(null)
    if (!response?.payload?.success) {
      errorToast(response?.payload?.response?.data?.message || ERROR_MESSAGE)
      setIsLoading(false)
      return
    }
    successToast('Record deleted successfully')
    refetchProjectToPhase()
    setIsLoading(false)
  }

  /**
   * Initializes form values
   */
  const initialValues = useMemo(() => {
    return { phase: '', master_project_phase_category_id: null }
  }, [editData])

  /*
   * Formik Configuration
   */
  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    onSubmit: async (data: { phase: string | null; master_project_phase_category_id: number | null }) => {
      if (editData?.id) {
        setConfirmationMessage(
          'This will update this phase and all affected records. Are you sure you want to proceed?',
        )
        setIsConfirmationModel(true)
      } else {
        await handleAddPhase(data)
      }
    },
  })

  /**
   * Column Definitions
   */
  const columns: CustomColumnDef<any>[] = useMemo(
    () => [
      { accessorKey: 'category', header: 'Category', size: 250, align: 'left' },
      {
        accessorKey: 'phase',
        header: 'Phase/Package',
        cell: ({ row }) => {
          return <>{row.original.phase || '-'}</>
        },
        flex: 1,
        align: 'left',
      },
      {
        accessorKey: 'action',
        header: 'Action',
        cell: ({ row }) => (
          <div className={styles.actionBts}>
            <EditIcon
              className={styles.editIcon}
              onClick={() => {
                formik.setValues({
                  ...formik.values,
                  phase: row.original?.phase || null,
                  master_project_phase_category_id: row.original?.MasterProjectPhaseCategory?.id || null,
                })
                setEditData({
                  ...editData,
                  id: row.original?.id,
                  phase: row.original?.phase || null,
                  master_project_phase_category_id: row.original?.MasterProjectPhaseCategory?.id || null,
                })
              }}
            />
            <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original?.id)} />
          </div>
        ),
        size: 70,
      },
    ],
    [],
  )

  // const handleConfirm = () => {
  // }
  const handleConfirm = async () => {
    setIsLoading(true)
    try {
      if (editData?.id) {
        await handleRenamePhase()
      }
    } catch (error) {
      console.error('Error in handleConfirm:', error)
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setIsConfirmationModel(false)
    setConfirmationMessage('')
    formik.setValues({ phase: '', master_project_phase_category_id: null })
    setEditData(null)
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <TanStackTable rows={sortData(getMultiProjectToPhaseTableData(projectToPhase), 'category')} columns={columns} />
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <ComboBox
            className={styles.selectField}
            focusCustomClass={styles.focusClass}
            options={projectPhaseCategoryOption}
            labelText="Project Phase Category *"
            placeholder="Select Project Phase Category..."
            value={
              formik.values.master_project_phase_category_id
                ? getValue(projectPhaseCategoryOption, formik.values.master_project_phase_category_id)
                : null
            }
            onChange={(val) => {
              formik.setValues({
                ...formik.values,
                master_project_phase_category_id: val?.value || null,
              })
            }}
          />

          <TextInputField
            className={styles.statusField}
            name="phase"
            labelText="Phase/Package"
            placeholder="Type something ..."
            variant="outlined"
            value={formik.values.phase}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />

          <Button
            className={styles.addProjectButton}
            type="submit"
            disabled={formik.isSubmitting || !formik.values.master_project_phase_category_id}
          >
            <span className={styles.buttonText}>
              {editData ? (
                renamePhaseProcess === StatusEnum.Pending ? (
                  <Loader smallLoader={true} />
                ) : (
                  'Update Project Phase/Package'
                )
              ) : addPhaseProcess === StatusEnum.Pending ? (
                <Loader smallLoader={true} />
              ) : (
                'Add Project Phase/Package'
              )}
            </span>
          </Button>
        </form>
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => deletePhase()}
          message={
            <span>
              This will delete all <strong>{getPhaseById(deleteModel)?.phase || 'phase'}</strong> entries for the{' '}
              <strong>{getPhaseById(deleteModel)?.category || 'this category'}</strong>, along with all associated
              records. Are you sure you want to proceed?
            </span>
          }
          loading={isLoading}
        />
        <ConfirmationModal
          open={isConfirmationModel}
          message={confirmationMessage}
          handleConfirm={handleConfirm}
          onClose={handleClose}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

export default PhaseTab

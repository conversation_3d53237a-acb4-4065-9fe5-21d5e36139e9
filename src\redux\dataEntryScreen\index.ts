import { createSlice } from '@reduxjs/toolkit'
import { IRatingState } from './interface'

const initialState: IRatingState = {
  selectedTab: 'Summary',
}
// const initialState: IRatingState = {
//   selectedTab:
//     'Summary' ||
//     'Progress' ||
//     'Commercials' ||
//     'Project Overview' ||
//     'SPA & Milestones' ||
//     'Authority Approvals Tracking' ||
//     'Handover' ||
//     'Media',
// }

export const summaryTabSlice = createSlice({
  name: 'summaryTab',
  initialState,
  reducers: {
    setSelectedTab: (state, action) => {
      state.selectedTab = action.payload // Use assignment instead of comparison
    },
  },
})

export const { setSelectedTab } = summaryTabSlice.actions

// Export the reducer
export default summaryTabSlice.reducer

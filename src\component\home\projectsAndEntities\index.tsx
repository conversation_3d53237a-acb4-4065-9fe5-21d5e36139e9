import React, { useState } from 'react'
import { useRouter } from 'next/router'
import EntityCategory from './entityCategory'
import OwningEntityDrawer from './owningEntityDrawer'
import AddPricingType from './pricingType'
import styles from './ProjectsAndEntities.module.scss'
import AddProjectDrawer from '../../addProjectDrawer'
import Drawer from '../../shared/drawer'
import TypographyField from '../../shared/typography'
import AddEntityKeyAchievement from '../entityKeyAchievements'
import ImageCard from '../imageCard'
import { DrawerStates } from '../interface'
import AddProjectToBeCompletedIn from '../projectToBeComplatedIn'
import useModuleCount from '@/src/redux/moduleCount/useModuleCount'

interface IProjectsAndEntitiesProps {
  drawerStates: DrawerStates
  toggleDrawer: (drawer: keyof DrawerStates) => (open: boolean) => void
}

const ProjectsAndEntities: React.FC<IProjectsAndEntitiesProps> = ({ drawerStates, toggleDrawer }) => {
  const [project, setProject] = useState(false)
  const [isProjectComplate, setIsProjectComplate] = useState(false)
  const [isEntity, setIsEntity] = useState(false)
  const { moduleCounts } = useModuleCount()

  const router = useRouter()
  return (
    <div className={styles.cardContainer}>
      <TypographyField variant={'subheading'} text={'Entities & Projects'} />
      <div className={styles.cards}>
        <div className={styles.card}>
          <ImageCard
            label="Entity"
            value={moduleCounts.projects_and_entities?.entities}
            imageSrc="/svg/enttityBg.svg"
            imageWidth={43}
            imageHeight={50}
            // onCardClick={() => toggleDrawer("viewEntity")(true)}
            onAddButtonClick={() => toggleDrawer('addEntity')(true)}
          />

          <ImageCard
            label="Project"
            value={moduleCounts.projects_and_entities?.projects}
            imageSrc="/svg/projectBg.svg"
            imageWidth={53}
            imageHeight={50}
            // onCardClick={() => {
            //   setProject(true);
            //   toggleDrawer("addProject")(true);
            // }}
            onAddButtonClick={() => {
              setProject(false)
              toggleDrawer('addProject')(true)
            }}
          />

          {/* <ImageCard
            label={`Entity Key Achievements`}
            subLabel="(Portfolio)"
            value={moduleCounts.projects_and_entities?.project_key_achievements}
            imageSrc="/svg/projectBg.svg"
            imageWidth={53}
            imageHeight={50}
            // onCardClick={() => {
            //   setIsEntity(true);
            //   toggleDrawer("addEntityKeyAchievement")(true);
            // }}
            onAddButtonClick={() => {
              setIsEntity(false)
              toggleDrawer('addEntityKeyAchievement')(true)
            }}
          /> */}
        </div>
        <div className={styles.card}>
          <ImageCard
            label="Entity Category"
            value={moduleCounts.projects_and_entities?.entity_category}
            imageSrc="/svg/enttityBg.svg"
            imageWidth={43}
            imageHeight={50}
            // onCardClick={() => toggleDrawer("viewEntity")(true)}
            onAddButtonClick={() => toggleDrawer('entityCategory')(true)}
          />

          <ImageCard
            label="Pricing Type"
            value={moduleCounts.projects_and_entities?.pricing_type}
            imageSrc="/svg/projectBg.svg"
            imageWidth={53}
            imageHeight={50}
            // onCardClick={() => {
            //   setProject(true);
            //   toggleDrawer("pricingType")(true);
            // }}
            onAddButtonClick={() => {
              setProject(false)
              toggleDrawer('pricingType')(true)
            }}
          />
          {/* <ImageCard
            label="Project To Be Completed"
            subLabel="In (Portfolio)"
            value={moduleCounts.projects_and_entities?.project_to_be_completed_in}
            imageSrc="/svg/projectBg.svg"
            imageWidth={53}
            imageHeight={50}
            // onCardClick={() => {
            //   setIsProjectComplate(true);
            //   toggleDrawer("addProjectToBeCompletedIn")(true);
            // }}
            onAddButtonClick={() => {
              setIsProjectComplate(false)
              toggleDrawer('addProjectToBeCompletedIn')(true)
            }}
          /> */}
        </div>
      </div>

      {/* Add Entity */}
      <Drawer anchor="right" open={drawerStates.addEntity} onClose={() => toggleDrawer('addEntity')(false)}>
        <OwningEntityDrawer drawerStates={drawerStates} onClose={() => toggleDrawer('addEntity')(false)} />
      </Drawer>

      <Drawer anchor="right" open={drawerStates.entityCategory} onClose={() => toggleDrawer('entityCategory')(false)}>
        <EntityCategory drawerStates={drawerStates} onClose={() => toggleDrawer('entityCategory')(false)} />
      </Drawer>
      <Drawer anchor="right" open={drawerStates.pricingType} onClose={() => toggleDrawer('pricingType')(false)}>
        <AddPricingType drawerStates={drawerStates} onClose={() => toggleDrawer('pricingType')(false)} />
      </Drawer>

      {/* Add Project */}
      <Drawer
        anchor="right"
        open={drawerStates.addProject}
        onClose={() => {
          setProject(false)
          toggleDrawer('addProject')(false)
        }}
        paperSx={{ overflow: 'unset' }}
      >
        <AddProjectDrawer
          project={project}
          setProject={setProject}
          drawerStates={drawerStates}
          onClose={() => {
            setProject(false)
            toggleDrawer('addProject')(false)
          }}
        />
      </Drawer>

      {/*  <Drawer
        anchor="right"
        open={drawerStates.addEntityKeyAchievement}
        onClose={() => {
          setIsEntity(false)
          toggleDrawer('addEntityKeyAchievement')(false)
        }}
      >
        <AddEntityKeyAchievement
          isEntity={isEntity}
          setIsEntity={setIsEntity}
          drawerStates={drawerStates}
          onClose={() => {
            setIsEntity(false)
            toggleDrawer('addEntityKeyAchievement')(false)
          }}
        />
      </Drawer>
      <Drawer
        anchor="right"
        open={drawerStates.addProjectToBeCompletedIn}
        onClose={() => {
          setIsProjectComplate(true)
          toggleDrawer('addProjectToBeCompletedIn')(false)
        }}
      >
        <AddProjectToBeCompletedIn
          isProjectComplate={isProjectComplate}
          drawerStates={drawerStates}
          setIsProjectComplate={setIsProjectComplate}
          onClose={() => {
            setIsProjectComplate(true)
            toggleDrawer('addProjectToBeCompletedIn')(false)
          }}
        />
      </Drawer> */}
    </div>
  )
}

export default ProjectsAndEntities

@import '/styles/color.scss';

.root {
  .indicator {
    height: 1px;
    background-color: $ERROR_400;
  }
  .flexContainer {
    display: flex;
    align-items: center;
    gap: 24px;
  }
  .tabRoot {
    text-transform: none;
    padding: 4px 0;
    min-height: 32px;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
    justify-content: flex-start;
    max-width: none;
    min-width: auto;
    font-family: Poppins;
    > svg {
      height: 20px;
      width: 20px;
      fill: $DARK;
      margin-right: 4px;
    }
  }
  .selected {
    .label {
      font-weight: 700;
    }
    > svg {
      fill: $DARK;
    }
  }
  .label {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: $DARK;
    margin: 0;
  }
}

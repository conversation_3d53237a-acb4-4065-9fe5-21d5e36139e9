import { useDispatch, useSelector } from 'react-redux'
import { getCommercial, deleteCommercial, addCommercial, updateCommercial } from '.'
import { ICommercial } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useCommercial = () => {
  const dispatch: AppDispatch = useDispatch()

  const getCommercialStatus = useSelector((state: RootState) => state.commercial.getCommercialStatus)
  const commercials = useSelector((state: RootState) => state.commercial.commercials)

  const getCommercialApi = (data: { period: string; project_name?: string }) => dispatch(getCommercial(data))
  const addCommercialApi = (data: ICommercial) => dispatch(addCommercial(data))
  const updateCommercialApi = (id: number, data: ICommercial) => dispatch(updateCommercial({ id, data }))
  const deleteCommercialApi = (data: number) => dispatch(deleteCommercial(data))

  return {
    getCommercialStatus,
    commercials,
    getCommercial<PERSON>pi,
    addCommercial<PERSON><PERSON>,
    updateCommercial<PERSON>pi,
    deleteCommercial<PERSON>pi,
  }
}
export default useCommercial

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterProjectSubStage,
  deleteMasterProjectSubStage,
  getMasterProjectSubStage,
  updateMasterProjectSubStage,
} from '../services/projectSubStage'
import { IGetProjectSubStageResponse, IUpdateProjectSubStage } from '../services/projectSubStage/interface'

export const QUERY_PROJECT_SUB_STAGE = 'project-sub-stage'

/**
 * Hook to fetch Project Sub Stage
 * @param enabled - Enables or disables the query
 */
export const useGetProjectSubStage = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetProjectSubStageResponse>({
    queryKey: [QUERY_PROJECT_SUB_STAGE],
    queryFn: getMasterProjectSubStage,
    enabled,
  })

  return {
    projectSubStages: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project Sub Stage
 *  */
export const useCreateProjectSubStage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterProjectSubStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_SUB_STAGE] })
    },
  })
}

/**
 * Hook to delete a Project Sub Stage
 * */
export const useDeleteProjectSubStage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterProjectSubStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_SUB_STAGE] })
    },
  })
}

/**
 * Hook to update a Project Sub Stage
 * */
export const useUpdateProjectSubStage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterProjectSubStage,
    onMutate: async (updatedData: IUpdateProjectSubStage) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_SUB_STAGE] })
      const previousData = queryClient.getQueryData<IGetProjectSubStageResponse>([QUERY_PROJECT_SUB_STAGE])
      // Optimistically update the cache
      queryClient.setQueryData<IGetProjectSubStageResponse>([QUERY_PROJECT_SUB_STAGE], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((project) =>
            project.id === updatedData.id ? { ...project, project_sub_stage: updatedData.project_sub_stage } : project,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_SUB_STAGE], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_SUB_STAGE] })
    },
  })
}

export interface ISearchDrawerProps {
  className: string
  isDrawerOpen: boolean
  setIsDrawerOpen: (args: boolean) => void
  anchor: null | HTMLElement
  setAnchor: React.Dispatch<React.SetStateAction<HTMLElement | null>>
}

export interface FilterOptionType {
  label: string
  value: string
}

export interface SearchDrawerStateType {
  searchQuery: string
  entityValue: string[]
  cpds: string[]
  project_id: string[]
  classificationValue: string[]
  locationValue: string[]
  portFolio: string[]
  designManager: string[]
  executive: string[]
  control: string[]
  procure: string[]
  projectStatus: string[]
  startYear: any[]
  designProjectOwner: string[]
  delivery_project_manager: string[]
  svp?: string[]
  contractTypeValue: string[]
  deviationStatusValue: string[]
  labelValue: string[]
  setStartYearApi: (value: string[]) => void
  setProjectStatusApi: (value: string[]) => void
  setSearchQueryApi: (value: string) => void
  setCategoryValueApi: (value: string[]) => void
  setEntityValueApi: (value: string[]) => void
  setCpdsApi: (value: string[]) => void
  setProjectIdApi: (value: string[]) => void
  setClassificationValueApi: (value: string[]) => void
  setProjectTypeValueApi: (value: string[]) => void
  setLocationValueApi: (value: string[]) => void
  setPortFolioApi: (value: string[]) => void
  setDesignManagerApi: (value: string[]) => void
  setExecutiveApi: (value: string[]) => void
  setDeliveryDirectorApi: (value: string[]) => void
  setControlApi: (value: string[]) => void
  setProcureApi: (value: string[]) => void
  setDesignProjectOwnerApi: (value: string[]) => void
  setContractTypeApi: (value: string[]) => void
  setDeviationStatusApi: (value: string[]) => void
  setOverallForecastedFinishDateApi: (value: string[]) => void
  setLabelValueApi: (value: string[]) => void
  setSvpApi: (value: string[]) => void
  [key: string]: any
}

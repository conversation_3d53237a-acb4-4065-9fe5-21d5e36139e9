import { IKpiPayload, IUpdateKpi } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_KPI_URL = `/master-kpi`

// Get Master Kpis data
export const getMasterKpis = async () => {
  const response = await api.get(`${MASTER_KPI_URL}`)
  return response?.data
}

export const deleteMasterKpi = async (data: number) => {
  const response = await api.delete(`${MASTER_KPI_URL}/${data}`)
  return response?.data
}

// Create Master Kpis entry
export const createMasterKpi = async (data: IKpiPayload) => {
  const response = await api.post(`${MASTER_KPI_URL}`, data)
  return response
}

// Update Master Kpis entry
export const updateMasterKpi = async (data: IUpdateKpi): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_KPI_URL}/${data.id}`, { ...rest })
  return response
}

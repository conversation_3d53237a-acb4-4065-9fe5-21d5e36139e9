import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IModuleCountStatus } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IModuleCountStatus = {
  getModuleCountStatus: StatusEnum.Idle,
  moduleCounts: [],
}

export const getModuleCount = createAsyncThunk(
  '/dashboard/module-count',
  async (data: { period: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(`/dashboard/module-count?period=${period}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const moduleCountSlice = createSlice({
  name: 'moduleCount',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getModuleCount
    builder.addCase(getModuleCount.pending, (state) => {
      state.getModuleCountStatus = StatusEnum.Pending
    })
    builder.addCase(getModuleCount.fulfilled, (state, action) => {
      state.getModuleCountStatus = StatusEnum.Success
      const actionPayload = action.payload as any
      state.moduleCounts = actionPayload.data
    })
    builder.addCase(getModuleCount.rejected, (state) => {
      state.getModuleCountStatus = StatusEnum.Failed
    })
  },
})

export const {} = moduleCountSlice.actions

// Export the reducer
export default moduleCountSlice.reducer

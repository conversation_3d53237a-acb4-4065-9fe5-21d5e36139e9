import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './UnFreezeConfirm.module.scss'
import Button from '@/src/component/shared/button'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'

interface ModalProps {
  open: boolean
  onClose: () => void
}

const UnFreezeConfirm: React.FC<ModalProps> = ({ open, onClose }) => {
  const { unFreezePeriodApi, getMasterPeriodsApi } = useMasterPeriod()

  const handleConfirmFreeze = async () => {
    const response: Record<string, any> = await unFreezePeriodApi()
    if (response.payload.success === true) {
      getMasterPeriodsApi()
      onClose()
    }
  }

  const modalStyle = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '440px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    pt: '20px',
    px: '20px',
    pb: '10px',
    zIndex: 1,
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={modalStyle}>
        <div className={styles.confirmButtonContent}>
          <div className={styles.confirmContent}></div>

          <div className={styles.reassignButtons}>
            <Button onClick={handleConfirmFreeze}>Yes</Button>
            <Button color="secondary" onClick={onClose}>
              No
            </Button>
          </div>
        </div>
      </Box>
    </Modal>
  )
}

export default UnFreezeConfirm

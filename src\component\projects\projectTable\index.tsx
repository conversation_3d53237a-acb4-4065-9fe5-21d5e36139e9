import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import GlobalSearch from './globalSearch'
import styles from './ProjectTable.module.scss'
import {
  convertToProjectTableRecord,
  filterProjectsByEntity,
  findItemById,
  preparePayloadForSortOrder,
} from './projectUtils'
import DragCell from '../../customCells/dragCell'
import Checkbox from '../../shared/checkbox'
import Loader from '../../shared/loader'
import TanStackTable from '../../shared/tanStackTable'
import { numericValueSorting } from '../../shared/tanStackTable/helper'
import { CustomColumnDef } from '../../shared/tanStackTable/interface'
import { MOBILE, TABLET } from '@/src/constant/breakpoint'
import { ERROR_MESSAGE, Routes, userType, whiteListedUserList } from '@/src/constant/enum'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'
import { PROJECTS_QUERY_KEY, useUpdateMultiProject, useUpdateSortingOrder } from '@/src/hooks/useProjects'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useDataEntryScreen from '@/src/redux/dataEntryScreen/useDataEntryScreen'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useLastUpdated from '@/src/redux/lastUpdated/useLastUpdated'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectManagement from '@/src/redux/projectManagement/useProjectManagement'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import useStatus from '@/src/redux/status/useStatus'
import { getProjects } from '@/src/services/projects'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'
import { isRecentStatusUpdate } from '@/src/utils/statusTab/recentUpdates'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

export interface ProjectTableProps {
  isLoading: boolean
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
}

const tableHeights: Record<string, string> = {
  mobile: `calc(100vh - 270px)`,
  tablet: `calc(100vh - 210px)`,
  laptop: `calc(100vh - 210px)`,
  desktop: `calc(100vh - 240px)`,
  wideScreen: `calc(100vh - 220px)`,
}

const pageSizes: Record<string, number> = {
  mobile: 9,
  tablet: 10,
  laptop: 10,
  desktop: 11,
  wideScreen: 20,
}

const ProjectTable: React.FC<ProjectTableProps> = ({ isLoading, setIsLoading }) => {
  const router = useRouter()
  const [projectsTableData, setProjectsTableData] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [fullProjectsData, setFullProjectsData] = useState<any[]>([])
  const { currentPeriod, getAllPeriodApi, mainPeriod, freezeType } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const {
    setProjectFilterValueApi,
    projectFilterValue,
    setLocalFilteredDataApi,
    columnVisibilities,
    setColumnVisibilitiesApi,
    columnsWidth,
    setColumnsWidthApi,
  } = useProjectSummary()
  const { setSelectedTabApi } = useDataEntryScreen()
  const { statuses, getStatusApi } = useStatus()
  const { projectManagements, getProjectManagementsApi } = useProjectManagement()
  const { keyAchievements, getKeyAchievementsApi } = useKeyAchievement()
  const { areaOfConcerns, getAreaOfConcernsApi } = useAreaOfConcern()
  const { addLastUpdateOfProgressReducer, lastUpdateOfProgress } = useLastUpdated()
  const breakpoint = useBreakpoint()
  const tableHeight = tableHeights[breakpoint] || `calc(100vh - 238px)`
  const pageSize = pageSizes[breakpoint] || 10
  const [edit, setEdit] = useState(false)

  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    select: (response) => response.data, // This extracts 'data' directly
    // refetchOnWindowFocus: false,
  })
  const { mutate: sortProjects } = useUpdateSortingOrder()
  const { mutate: updateMultiProject } = useUpdateMultiProject()

  const [pendingChanges, setPendingChanges] = useState<
    Array<{
      project_name: string
      [key: string]: any
    }>
  >([])

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const fetchDataOfProjectTable = async () => {
    try {
      //TODO: Remove statusResponse because of task no.: 316.
      const [
        // statusResponse,
        masterProjectsResponse,
        // projectManagementsResponse,
        keyAchievementsResponse,
        areaOfConcernsResponse,
      ]: any[] = await Promise.all([
        getAllPeriodApi(),
        //TODO: Remove project-status ENDPOINT because of task no.: 316.
        // getStatusApi({ period: currentPeriod }),

        // getProjectManagementsApi({ period: currentPeriod }),
        getKeyAchievementsApi({ period: currentPeriod }),
        getAreaOfConcernsApi({ period: currentPeriod }),
      ])

      //TODO: Remove statusResponse?.payload?.success because of task no.: 316.
      if (
        // statusResponse?.payload?.success &&
        masterProjectsResponse?.payload?.success &&
        // projectManagementsResponse?.payload?.success &&
        keyAchievementsResponse?.payload?.success &&
        areaOfConcernsResponse?.payload?.success
      ) {
        !isEditForUser &&
          toast(`The current reporting period is locked`, {
            icon: <WarningAmberOutlined />,
            closeButton: false, // Optionally hide the close button
          })
        setIsLoading(false)
      } else {
        console.error('Failed to fetch project data')
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    }
  }

  useEffect(() => {
    setIsLoading(true)
    currentPeriod && fetchDataOfProjectTable()
  }, [currentPeriod])

  useEffect(() => {
    const data = projectsTableData?.map((item) => {
      const lastUpdateOfProgressResult = isRecentStatusUpdate(item.project, statuses, projectManagements)
      return lastUpdateOfProgressResult
    })
    const flattenedArray = data?.flatMap((item) => (Array.isArray(item) ? item : [item]))
    addLastUpdateOfProgressReducer(flattenedArray) // progress last updated things we are store in redux
  }, [statuses, keyAchievements, areaOfConcerns, projectsTableData])

  useEffect(() => {
    if (!Object.keys(columnVisibilities)?.length) {
      const defaultVisibilities: Record<string, boolean> = {}
      columns?.forEach((res) => {
        defaultVisibilities[res.accessorKey] = res.accessorKey === 'sortNumber' ? false : true
      })
      setColumnVisibilitiesApi(defaultVisibilities)
    }
  }, [])

  const filterDataBySearchTerm = (data: any[]) => {
    if (!searchTerm) {
      return data
    }

    const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s+/g, ' ').trim()

    return data.filter((item) => {
      return Object.entries(item).some(([key, val]) => {
        if (!val || typeof val !== 'string') {
          return false
        }
        const normalizedValue = val.toLowerCase().replace(/\s+/g, ' ').trim()
        return normalizedValue.includes(normalizedSearchTerm)
      })
    })
  }

  const handleFilterData = () => {
    if (!projects) return

    const projectData = convertToProjectTableRecord(projects, lastUpdateOfProgress, statuses)

    const sortedProjectData = sortArrayByKeyWithTypeConversion(projectData, 'sortNumber', true)

    setFullProjectsData(sortedProjectData)
    if (searchTerm) {
      const filteredData = filterDataBySearchTerm(sortedProjectData)
      setProjectsTableData(filteredData)
    } else {
      setProjectsTableData(sortedProjectData)
    }
  }

  useEffect(() => {
    handleFilterData()
  }, [projects, searchTerm])

  const handleRowClick = (row: any) => {
    router.push(`${Routes.SUMMARY}/${encodeURIComponent(row?.original?.project)}`)
    setSelectedTabApi('Summary')
  }

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    if (
      // isSuperAdmin(currentUser.user_type) ||
      whiteListedUserList.includes(currentUser.email) ||
      currentUser?.role?.view_permissions?.includes('Project Sorting')
    ) {
      setIsLoading(true)
      const dragItem = findItemById(projectsTableData, dragId)
      const dropItem = findItemById(projectsTableData, dropId)

      if (!dragItem) {
        console.error(`Drag item with id ${dragId} not found`)
        return
      }

      if (!dropItem) {
        console.error(`Drop item with id ${dropId} not found`)
        return
      }
      const sortedProjects = sortArrayByKeyWithTypeConversion(projects, 'project_sorting_order', true)

      const filteredProjects = filterProjectsByEntity(sortedProjects, dragItem.owningEntity)
      const payloadWithSortOrder = preparePayloadForSortOrder(
        filteredProjects,
        dragItem.sortNumber,
        dropItem.sortNumber,
      )

      try {
        sortProjects(
          {
            period: currentPeriod,
            projects: payloadWithSortOrder,
          },
          {
            onSuccess: () => {
              fetchDataOfProjectTable()
              successToast('Projects Reorder successfully!')
            },
            onError: (err: any) => {
              errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
            },
          },
        )
      } catch (error) {
        console.error('Error updating sorting order:', error)
      }
    }
  }

  const handleCheckboxChange = async (id: any, projectId: string, field: string, value: boolean) => {
    if (!edit) return
    const val = field === 'isExecutiveProject' ? (value === true ? 1 : 0) : value
    setProjectsTableData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          return { ...item, [field]: value }
        }
        return item
      }),
    )
    setFullProjectsData((prevData) =>
      prevData.map((item) => {
        if (item.id === id) {
          return { ...item, [field]: value }
        }
        return item
      }),
    )

    // Add or update pending changes
    setPendingChanges((prevChanges) => {
      const existingChangeIndex = prevChanges?.findIndex((change) => change.project_name === projectId)

      if (existingChangeIndex > -1) {
        // Update existing change
        const updatedChanges = [...prevChanges]
        updatedChanges[existingChangeIndex] = {
          ...updatedChanges[existingChangeIndex],
          [field]: val,
          period: currentPeriod,
        }
        return updatedChanges
      } else {
        // Add new change
        return [
          ...prevChanges,
          {
            project_name: projectId,
            period: currentPeriod,
            [field]: val,
          },
        ]
      }
    })
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'isRibbon',
      header: 'last updated',
      size: columnsWidth?.['isRibbon'] ? columnsWidth['isRibbon'] : 30,
      filterType: 'list',
      listOption: [
        { id: 'More Than 7 Days', name: 'More Than 7 Days' },
        { id: 'Not More Than 7 Days', name: 'Not More Than 7 Days' },
      ],
    },
    {
      accessorKey: 'actionCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: columnsWidth?.['actionCol'] ? columnsWidth['actionCol'] : 40,
      align: 'left',
    },
    // ...(isSuperAdmin(currentUser.user_type)
    //   ? [
    {
      accessorKey: 'sortNumber',
      header: 'Entity Sorting',
      size: columnsWidth?.['sortNumber'] ? columnsWidth['sortNumber'] : 45,
    },
    //   ]
    // : []),
    {
      accessorKey: 'project',
      header: 'Project Name',
      size: columnsWidth?.['project'] ? columnsWidth['project'] : 600,
      filterType: 'text',
      require: false,
      cursor: 'pointer',
      align: 'left',
      popOverWidth: breakpoint === MOBILE ? '150px' : breakpoint === TABLET ? '200px' : '800px',
      sortAlphabetically: true,
      // listOption: projects.map((item) => {
      //   return item.project_name
      // }),
      cell: ({ row }) => (
        <div className="project-name" onClick={() => handleRowClick(row)}>
          {row.original?.project}
        </div>
      ),
    },
    // {
    //   accessorKey: 'entity_category',
    //   header: 'Category',
    //   size: columnsWidth?.['entity_category'] ? columnsWidth['entity_category'] : 150,
    //   sortAlphabetically: true,
    //   require: true,
    // },
    {
      accessorKey: 'owningEntity',
      header: 'Owning Entity',
      size: columnsWidth?.['owningEntity'] ? columnsWidth['owningEntity'] : 150,
      sortAlphabetically: true,
      require: false,
      // flex: 1,
    },
    {
      accessorKey: 'project_id',
      header: 'ERP Project ID',
      size: columnsWidth?.['project_id'] ? columnsWidth['project_id'] : 150,
      sortAlphabetically: true,
    },
    {
      accessorKey: 'cpds_project_id',
      header: 'CPDS ID',
      sortAlphabetically: true,
      size: columnsWidth?.['cpds_project_id'] ? columnsWidth['cpds_project_id'] : 150,
    },
    { accessorKey: 'dof_number', header: 'DOF No', size: 200, flex: 1, sortAlphabetically: true },
    {
      accessorKey: 'project_classification',
      header: 'Classification',
      size: columnsWidth?.['project_classification'] ? columnsWidth['project_classification'] : 150,
      sortAlphabetically: true,
      require: false,
    },
    {
      accessorKey: 'project_status',
      header: 'Project Status',
      size: columnsWidth?.['project_status'] ? columnsWidth['project_status'] : 150,
      filterType: 'projectStatus',
    },
    {
      accessorKey: 'project_type',
      header: 'Project Type ',
      size: columnsWidth?.['project_type'] ? columnsWidth['project_type'] : 150,
      sortAlphabetically: true,
      require: false,
    },
    {
      accessorKey: 'location',
      header: 'Location',
      size: columnsWidth?.['location'] ? columnsWidth['location'] : 150,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'Sub_location',
      header: 'Sub Location',
      size: columnsWidth?.['Sub_location'] ? columnsWidth['Sub_location'] : 150,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'portfolio_manager',
      header: 'Portfolio Manager',
      size: columnsWidth?.['portfolio_manager'] ? columnsWidth['portfolio_manager'] : 150,
      sortAlphabetically: true,
      require: false,
    },
    {
      accessorKey: 'design_project_manager',
      header: 'Design Manager',
      size: columnsWidth?.['design_project_manager'] ? columnsWidth['design_project_manager'] : 150,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'controls_manager',
      header: 'Controls Manager',
      size: columnsWidth?.['controls_manager'] ? columnsWidth['controls_manager'] : 150,
      sortAlphabetically: true,
    },
    {
      accessorKey: 'procurement_manager',
      header: 'Procurement Manager',
      size: columnsWidth?.['procurement_manager'] ? columnsWidth['procurement_manager'] : 150,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'design_executive_director',
      header: 'Design Executive Director',
      size: columnsWidth?.['design_executive_director'] ? columnsWidth['design_executive_director'] : 150,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    // {
    //   accessorKey: 'designMangerByStatus',
    //   header: 'Design Manager',
    //   size: columnsWidth?.['designMangerByStatus'] ? columnsWidth['designMangerByStatus'] : 150,
    //   filterType: 'comma',
    //   listOption: convertAndSortData(convertMultiSelectOption(projectsFilter.designManagerOfStatus)),
    // },
    {
      accessorKey: 'delivery_manager',
      header: 'Delivery Executive Director',
      size: columnsWidth?.['delivery_manager'] ? columnsWidth['delivery_manager'] : 290,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'director',
      header: 'Delivery Director',
      size: columnsWidth?.['director'] ? columnsWidth['director'] : 150,
      sortAlphabetically: true,
    },
    {
      accessorKey: 'startYear',
      header: 'Start Year',
      size: columnsWidth?.['startYear'] ? columnsWidth['startYear'] : 150,
      sortAlphabetically: true,
    },
    {
      accessorKey: 'delivery_project_manager',
      header: 'Delivery PM',
      size: columnsWidth?.['delivery_project_manager'] ? columnsWidth['delivery_project_manager'] : 150,
      sortAlphabetically: true,
      filterType: 'multiSelect',
      sortUndefined: 'last',
    },
    {
      accessorKey: 'is_refurbishment_project',
      header: 'Refurbishment Project',
      sortAlphabetically: true,
      size: columnsWidth?.['is_refurbishment_project'] || 150,
      filterType: 'boolean',
      cell: ({ row }) => (
        <div className={` ${!edit ? styles.checkBoxBorder : styles.checkBox}`}>
          <Checkbox
            checked={row?.original?.is_refurbishment_project}
            onChange={() =>
              handleCheckboxChange(
                row?.id,
                row?.original?.project,
                'is_refurbishment_project',
                !row?.original?.is_refurbishment_project,
              )
            }
            className={` ${!edit ? styles.checkBoxBorder : styles.checkBox}`}
          />
        </div>
      ),
    },
    {
      accessorKey: 'is_musanada_project',
      header: 'Musanda Project',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['is_musanada_project'] || 150,
      cell: ({ row }) => (
        <div className={` ${!edit ? styles.checkBoxBorder : styles.checkBox}`}>
          <Checkbox
            checked={row?.original?.is_musanada_project}
            onChange={() =>
              handleCheckboxChange(
                row?.id,
                row?.original?.project,
                'is_musanada_project',
                !row?.original?.is_musanada_project,
              )
            }
            className={` ${!edit ? styles.checkBoxBorder : styles.checkBox}`}
          />
        </div>
      ),
    },
    {
      accessorKey: 'budget_uplift_submitted',
      header: 'Budget Uplift',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['budget_uplift_submitted'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox checked={row?.original?.budget_uplift_submitted} className={styles.checkBoxBorder} />
        </div>
      ),
    },
    {
      accessorKey: 'scope_change',
      header: 'Scope Change',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['scope_change'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox
            checked={row?.original?.scope_change}
            className={styles.checkBoxBorder}
            // onChange={() => handleCheckboxChange(row?.original?.project, 'scope_change', !row?.original?.scope_change)}
          />
        </div>
      ),
    },
    {
      accessorKey: 'is_subject_to_rebaseline',
      header: 'Subject to Rebaseline',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['is_subject_to_rebaseline'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox checked={row?.original?.is_subject_to_rebaseline} className={styles.checkBoxBorder} />
        </div>
      ),
    },
    {
      accessorKey: 'tip_submitted',
      header: 'Transfer In Progress',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['tip_submitted'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox checked={row?.original?.tip_submitted} className={styles.checkBoxBorder} />
        </div>
      ),
    },
    {
      accessorKey: 'eot_submitted',
      header: 'EOT Submission',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['eot_submitted'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox checked={row?.original?.eot_submitted} className={styles.checkBoxBorder} />
        </div>
      ),
    },
    {
      accessorKey: 'potential_budget_uplift_submitted',
      header: 'Potential Budget Uplift',
      sortAlphabetically: true,
      filterType: 'boolean',
      size: columnsWidth?.['potential_budget_uplift_submitted'] || 150,
      cell: ({ row }) => (
        <div className={styles.checkBoxBorder}>
          <Checkbox checked={row?.original?.potential_budget_uplift_submitted} className={styles.checkBoxBorder} />
        </div>
      ),
    },
  ]

  const updatedColumns = useMemo(() => {
    const executiveCols: CustomColumnDef<any>[] = [
      {
        accessorKey: 'isExecutiveProject',
        header: 'Executive Summary',
        sortAlphabetically: true,
        size: columnsWidth?.['isExecutiveProject'] || 150,
        filterType: 'boolean',
        cell: ({ row }) => {
          const editPermission =
            edit &&
            currentUser?.user_type === userType?.SUPER_ADMIN &&
            currentUser.role?.view_permissions.includes('Executive Sorting')
          return (
            <div
              className={` ${editPermission ? styles.checkBox : styles.checkBoxBorder}`}
              style={{ paddingLeft: editPermission ? '0px' : '20px' }}
            >
              <Checkbox
                checked={row?.original?.isExecutiveProject}
                onChange={(e) => {
                  currentUser.role?.view_permissions.includes('Executive Sorting')
                    ? handleCheckboxChange(row?.id, row?.original?.project, 'isExecutiveProject', e?.target.checked)
                    : null
                }}
                className={` ${editPermission ? styles.checkBox : styles.checkBoxBorder}`}
              />
            </div>
          )
        },
      },
      {
        accessorKey: 'executive_sorting_order',
        header: 'executive Sorting Order',
        size: 170,
        // isSaveConfirmationRequired: true,
        isEditableCell:
          edit &&
          currentUser?.user_type === userType?.SUPER_ADMIN &&
          currentUser.role?.view_permissions.includes('Executive Sorting'),
        editableType: 'number',
        filterType: 'number',
        sortingFn: (...rest) => numericValueSorting(...rest),
        onEditCell: (cell, newValue, row) => {
          return currentUser.role?.view_permissions.includes('Executive Sorting')
            ? handleCheckboxChange(cell?.rowId, row?.project_name, 'executive_sorting_order', newValue)
            : null
        },
      },
    ]

    if (currentUser?.user_type === userType?.SUPER_ADMIN) {
      return [...columns.slice(0, 3), ...executiveCols, ...columns.slice(3)]
    }

    return columns
  }, [currentUser, columns])

  const handleSave = async () => {
    try {
      if (pendingChanges?.length === 0) {
        setEdit(false)
        return
      }
      const payload = {
        period: currentPeriod,
        projects: [...pendingChanges],
      }

      const data = await updateMultiProject(payload, {
        onSuccess: () => {
          successToast('Changes saved successfully!')
        },
        onError: (error: any) => {
          errorToast(error?.response?.data?.message)
        },
      })
      setPendingChanges([])
      setEdit(false)
    } catch (error) {
      errorToast('Failed to save changes')
    }
  }

  const handleCancel = () => {
    setEdit(false)
    setPendingChanges([])
  }

  return (
    <>
      <div className={`${styles.table} ${styles.selectedTable}`}>
        {!isLoading && (
          <GlobalSearch
            setProjectsTableData={setProjectsTableData}
            fullData={fullProjectsData}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            edit={edit}
            setEdit={setEdit}
            handleSave={handleSave}
            handleCancel={handleCancel}
            isEditForUser={isEditForUser}
          />
        )}
        <div className={styles.tableContainer}>
          {isLoading ? (
            <div className={styles.loader}>
              <Loader />
            </div>
          ) : (
            <TanStackTable
              isResizeColumn={true}
              rows={projectsTableData || []}
              columns={updatedColumns}
              onDragEnd={handleDragAndDrop}
              gridFilters={projectFilterValue}
              setGridFilters={setProjectFilterValueApi}
              setFilterRows={setLocalFilteredDataApi}
              columnVisibilities={columnVisibilities}
              setColumnVisibilitiesApi={setColumnVisibilitiesApi}
              colWidth={columnsWidth}
              setColWidth={setColumnsWidthApi}
              isCount={true}
              showPagination={true}
              isOverflow={true}
              tableHeight={tableHeight}
              pageSize={pageSize}
            />
          )}
        </div>
      </div>
    </>
  )
}

export default ProjectTable

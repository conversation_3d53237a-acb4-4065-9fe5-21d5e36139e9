import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  addProject,
  deleteProject,
  getMasterOneProject,
  getProjects,
  updateMultiProject,
  updateProject,
  updateProjectName,
  updateSortingOrder,
} from '../services/projects'
import {
  IGetProjectsResponse,
  IMultiProjectUpdatePayload,
  IProjects,
  ISortingOrderPayload,
} from '../services/projects/interface'

export const PROJECTS_QUERY_KEY = 'projects-key'
export const PROJECT_QUERY_KEY = 'project-key'

export const useAddProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: IProjects) => addProject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
  })
}

export const useUpdateProjectName = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { project_name: string; new_project_name: string; environment: string }) =>
      updateProjectName(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
  })
}

export const useUpdateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { id: string; data: IProjects }) => updateProject(data),
    onMutate: async (updatedData: { id: string; data: IProjects }) => {
      await queryClient.cancelQueries({ queryKey: [PROJECTS_QUERY_KEY] })
      const previousData = queryClient.getQueryData<IGetProjectsResponse>([PROJECTS_QUERY_KEY])

      queryClient.setQueryData<IGetProjectsResponse>([PROJECTS_QUERY_KEY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((project) =>
            project.id === updatedData.id ? { ...project, ...updatedData.data } : project,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([PROJECTS_QUERY_KEY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
      queryClient.invalidateQueries({ queryKey: [PROJECT_QUERY_KEY] })
    },
  })
}

export const useUpdateMultiProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: IMultiProjectUpdatePayload) => updateMultiProject(data),
    // onMutate: async (updatedData: IMultiProjectUpdatePayload) => {
    //   await queryClient.cancelQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    //   const previousData = queryClient.getQueryData<IGetProjectsResponse>([PROJECTS_QUERY_KEY])

    //   queryClient.setQueryData<IGetProjectsResponse>([PROJECTS_QUERY_KEY], (old) => {
    //     if (!old) return old
    //     return {
    //       ...old,
    //       data: old.data?.map((project) => {
    //         const updateForThisProject = updatedData?.projects?.find((p) => p.project_name === project.project_name)
    //         return updateForThisProject ? { ...project, ...updateForThisProject } : project
    //       }),
    //     }
    //   })
    //   return { previousData }
    // },
    onError: (err, _, context: any) => {
      if (context?.previousData) {
        queryClient.setQueryData([PROJECTS_QUERY_KEY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
      // queryClient.invalidateQueries({ queryKey: [PROJECT_QUERY_KEY] })
    },
  })
}

// Hook for deleting a project
export const useDeleteProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { projectName: string; period: string }) => deleteProject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
  })
}

export const useUpdateSortingOrder = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ISortingOrderPayload) => updateSortingOrder(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PROJECTS_QUERY_KEY] })
    },
  })
}

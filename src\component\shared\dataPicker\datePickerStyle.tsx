// import { makeStyles } from '@mui/material'
// import { BLUE,  GRAY_500, WHITE } from '../../../constants/color'

// // export const DatePickerStyles:any = makeStyles({
// //   dateIcon: {
// //     height: '18px',
// //     width: '18px',
// //     fill: GRAY_500,
// //   },
// //   dialogRoot: {
// //     left: '14px !important',
// //     '& .MuiCalendarPicker-root': {
// //       '& > div > div': {
// //         color: GRAY_500,
// //         fontSize: '14px',
// //         '& > button:hover': {
// //           backgroundColor: BLUE,
// //         },
// //       },
// //       // styles for day
// //       '& .MuiTypography-caption': {
// //         color: GRAY_500,
// //         fontSize: '14px',
// //         fontWeight: 600,
// //         lineHeight: '14px',
// //       },
// //       // styles for date
// //       '& .MuiPickersDay-root': {
// //         color: GRAY_500,
// //         fontSize: '14px',
// //         borderRadius: '4px',
// //         '&:hover': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //         },
// //         '&.Mui-selected': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //           fontWeight: 600,
// //         },
// //         '&.Mui-disabled': {
// //           color: GRAY_500,
// //         },
// //       },
// //       // styles for month dialog
// //       '& .MuiMonthPicker-root > button': {
// //         fontSize: '14px',
// //         color: GRAY_500,
// //         borderRadius: '4px',
// //         '&:hover': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //         },
// //         '&.Mui-selected': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //           fontWeight: 600,
// //         },
// //         '&.Mui-disabled': {
// //           color: GRAY_500,
// //         },
// //       },
// //       // styles from year dialog
// //       '& .MuiYearPicker-root button': {
// //         fontSize: '14px',
// //         color: GRAY_500,
// //         borderRadius: '4px',
// //         margin: '2px 0',
// //         '&:hover': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //         },
// //         '&.Mui-selected': {
// //           backgroundColor: BLUE,
// //           color: BLUE,
// //           fontWeight: 600,
// //         },
// //         '&.Mui-disabled': {
// //           color: GRAY_500,
// //         },
// //       },
// //     },
// //     // clear button style
// //     '& .MuiDialogActions-root': {
// //       padding: '0 8px 8px 8px',
// //       '& > button': {
// //         height: '32px',
// //         backgroundColor: WHITE,
// //         color: BLUE,
// //         borderRadius: '4px',
// //         '&:hover': {
// //           color: BLUE,
// //         },
// //       },
// //     },
// //   },
// //   paperDialog: {
// //     marginTop: '5px',
// //     borderRadius: '4px',
// //     border: `1px solid ${GRAY_500}`,
// //     background: WHITE,
// //     boxShadow: '0px 1px 4px 0px rgba(0, 0, 0, 0.25)',
// //   },
// //   arrowIcon: {
// //     height: '24px',
// //     width: '24px',
// //     fill: GRAY_500,
// //   },
// // })

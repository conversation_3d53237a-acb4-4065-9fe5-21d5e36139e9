import { IProjectOwnerPayload, IUpdateProjectOwner } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_OWNER_URL = `/master-project-design-owner`

// Get Master Project Owners data
export const getProjectOwners = async () => {
  const response = await api.get(`${MASTER_PROJECT_OWNER_URL}`)
  return response?.data
}

export const deleteProjectOwner = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_OWNER_URL}/${data}`)
  return response?.data
}

// Create Master Project Owners entry
export const createProjectOwner = async (data: IProjectOwnerPayload) => {
  const response = await api.post(`${MASTER_PROJECT_OWNER_URL}`, data)
  return response
}

// Update Master Project Owners entry
export const updateProjectOwner = async (data: IUpdateProjectOwner): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_OWNER_URL}/${data.id}`, { ...rest })
  return response
}

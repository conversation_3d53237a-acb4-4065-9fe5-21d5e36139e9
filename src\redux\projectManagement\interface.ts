import { ILookupProjectToPhase } from '../status/interface'
import { StatusEnum } from '../types'

export interface IProjectManagementStatus {
  getProjectManagementStatus: StatusEnum
  addProjectManagementStatus: StatusEnum
  deleteProjectManagementStatus: StatusEnum
  updateProjectManagementStatus: StatusEnum
  projectManagements: IProjectManagement[]
  projectManagement: IProjectManagement
}

export interface IUploadProjectManagement {
  file?: File
}

export interface IUploadDocProjectManagement {
  id: number
  data: FormData
}

export interface IDeleteDocProjectManagementPayload {
  project_name: string
  period: string
  // field_type: string
  // field_name: string
  media_type: string
  file_path: string
}

export interface IAttachments {
  LookupProjectToPhase: ILookupProjectToPhase
  file_path: string
  id: number
  media_type: string
  mime_type: string
  url: string
}

export interface IProjectManagement {
  id?: number
  period?: string
  master_project_stage_status_id?: number
  sub_stage?: string
  LookupProjectToPhase?: ILookupProjectToPhase[]
  project_name?: string
  budget?: string
  spi?: number
  project_management_var?: number
  forecast_completion_last_period?: number
  delay_this_period?: number
  cumulative_delay?: number
  manpower_planned?: number
  manpower_actual?: number
  man_hours?: number
  incidents?: number
  fatalities?: number
  lti_ratio?: number
  time_elapsed_percentage?: number
  procurement_package?: string
  last_updated?: Date
  updated_by?: string
}

export interface IGetProjectManagementsResponse {
  data: IProjectManagement[]
  message: string
  success: boolean
}

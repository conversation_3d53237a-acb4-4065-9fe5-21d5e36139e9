import React, { Dispatch, ReactElement, useMemo, useState } from 'react'
import { Logout, Upload } from '@mui/icons-material'
import DashboardCustomizeOutlinedIcon from '@mui/icons-material/DashboardCustomizeOutlined'
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted'
import { Divider, Tooltip } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import aldarLogo from '/public/svg/aldarLogo.svg'
import { useRouter } from 'next/router'
import styles from './Sidebar.module.scss'
import ProjectManageIcon from '../../svgImages/projectManageIcon'
import SearchIcon from '../../svgImages/searchIcon'
import UserManagementIcon from '../../svgImages/userManagement'
import SearchDrawer from '../searchDrawer'
import { Routes, StorageKeys, userType, whiteListedUserList } from '@/src/constant/enum'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import { getProjects } from '@/src/services/projects'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'
import { removeLocalStorageItem } from '@/src/utils/storageUtils'
import { canEditUser } from '@/src/utils/userUtils'

interface Props {
  isSearchDrawerOpen: boolean
  setSearchIsDrawerOpen: Dispatch<React.SetStateAction<boolean>>
}

const Sidebar: React.FC<Props> = ({ isSearchDrawerOpen, setSearchIsDrawerOpen }) => {
  const router = useRouter()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { logOutUserApi, currentUser } = useAuthorization()

  //REACT_QUERY
  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    enabled: false,
    select: (response) => response.data, // This extracts 'data' directly
  })

  const { recentProject, setRecentProject } = useProjectSummary()
  const currentRoute = router.pathname

  const [filterAnchor, setFilterAnchor] = useState<null | HTMLElement>(null)

  const projectOptions = useMemo(() => populateDropdownOptions(projects, 'project_name'), [projects])

  const handleLogout = async () => {
    const response: Record<string, any> = await logOutUserApi()
    if (!response.payload.success) return
    removeLocalStorageItem(StorageKeys.IS_LOGIN)
    router.push(`${Routes.LOGIN}`)
    setRecentProject({ projectName: '' })
  }

  const closeDrawer = () => setSearchIsDrawerOpen(false)

  const toggleDrawer = () => setSearchIsDrawerOpen((prevState) => !prevState)

  const renderTooltip = (title: string, children: ReactElement<any, any>) => (
    <Tooltip
      title={title}
      arrow
      componentsProps={{
        tooltip: {
          sx: { fontFamily: 'poppins' },
        },
      }}
    >
      {children}
    </Tooltip>
  )

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const renderIconButton = (IconComponent: React.ElementType, path: string, colorCondition: boolean) => (
    <div className={colorCondition ? styles.selectContain : styles.contain}>
      <IconComponent
        className={styles.pointer}
        sx={{ color: colorCondition ? '#fff' : '#808080' }}
        fill={colorCondition ? '#fff' : '#808080'}
        onClick={() => router.push(path)}
      />
    </div>
  )

  return (
    <>
      <div className={`${styles.sidebar} ${isSearchDrawerOpen && styles.sidebarIndex}`}>
        <div className={styles.aldarLogo}>
          <Image src={aldarLogo} height={36} width={36} alt="aldar" />
          <div className={isSearchDrawerOpen ? styles.selectContain : styles.contain}>
            {renderTooltip(
              'Search project',
              <SearchIcon
                className={styles.pointer}
                color={isSearchDrawerOpen ? '#FFFFFF' : '#808080'}
                onClick={toggleDrawer}
              />,
            )}
          </div>
        </div>
        <Divider className={styles.divider} />
        <div className={styles.logos}>
          <div className={styles.upIcon}>
            {(!currentUser.user_type?.includes(userType.USER) || whiteListedUserList.includes(currentUser.email)) &&
              renderTooltip(
                'Admin Panel',
                renderIconButton(DashboardCustomizeOutlinedIcon, Routes.HOME, currentRoute === Routes.HOME),
              )}

            {renderTooltip(
              'Projects List',
              renderIconButton(
                FormatListBulletedIcon,
                Routes.PROJECTS,
                currentRoute === Routes.PROJECTS && !router.query.recent,
              ),
            )}

            {(recentProject?.projectName || projectOptions?.includes(recentProject?.projectName as any)) &&
              renderTooltip(
                'Project Details',
                <div
                  className={
                    currentRoute.includes('summary') || currentRoute.includes('update-progress')
                      ? styles.selectContain
                      : styles.contain
                  }
                >
                  <ProjectManageIcon
                    className={styles.pointer}
                    onClick={() =>
                      router.push(`${Routes.SUMMARY}/${encodeURIComponent(recentProject?.projectName as string)}`)
                    }
                    color={
                      currentRoute.includes('summary') || currentRoute.includes('update-progress') ? '#fff' : '#808080'
                    }
                  />
                </div>,
              )}

            {currentUser?.role.view_permissions.includes('Batch Upload') &&
              isEditForUser &&
              renderTooltip(
                'Batch Upload',
                renderIconButton(Upload, Routes.BATCH_UPLOAD, router.pathname.includes('upload')),
              )}
          </div>

          <div className={styles.downIcon}>
            {([userType.SUPER_ADMIN].includes(currentUser.user_type) ||
              whiteListedUserList.includes(currentUser.email)) &&
              isEditForUser &&
              renderTooltip(
                'User Management',
                renderIconButton(
                  UserManagementIcon,
                  Routes.USER_MANAGEMENT,
                  currentRoute === Routes.USER_MANAGEMENT || currentRoute === Routes.USER_MANAGEMENT_USER,
                ),
              )}

            {renderTooltip(
              'Logout',
              <div>
                <Logout sx={{ color: '#808080' }} className={styles.pointer} onClick={handleLogout} />
              </div>,
            )}
          </div>
        </div>
      </div>

      <div className={`${styles.myComponent} ${isSearchDrawerOpen ? styles.myComponentShow : styles.myComponentHide}`}>
        <div
          className={`${isSearchDrawerOpen && styles.drawerContent}`}
          onClick={(e) => {
            if (isSearchDrawerOpen) {
              e.stopPropagation()
              !filterAnchor && closeDrawer()
            }
          }}
        >
          <SearchDrawer
            className={styles.isOpen}
            isDrawerOpen={isSearchDrawerOpen}
            setIsDrawerOpen={setSearchIsDrawerOpen}
            anchor={filterAnchor}
            setAnchor={setFilterAnchor}
          />
        </div>
      </div>
    </>
  )
}

export default Sidebar

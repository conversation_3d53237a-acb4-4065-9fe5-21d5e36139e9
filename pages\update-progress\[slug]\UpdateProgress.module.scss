.container {
  width: 100%;
  height: calc(100% - 28px);
  // height: 100%;
  .editStatusHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header {
      cursor: pointer;
      display: flex;
      margin: 22px 0px 22px 24px;
      gap: 10px;

      .detailsText {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0em;
        text-align: left;
      }
    }
    .buttons {
      display: flex;
      gap: 10px;
      margin-right: 44px;
    }
  }
}
.btnOfAction {
  padding: 10px !important;
}

.actionHeader {
  display: flex;
  gap: 10px;
}

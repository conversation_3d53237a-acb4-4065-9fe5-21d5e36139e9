import { ColumnDef } from '@tanstack/react-table'
import { FilterValue } from '../../customCells/headerCell/interface'

export type CustomColumnDef<T> = ColumnDef<T> & {
  accessorKey: string
  header: string
  align?: 'left' | 'right' | 'center'
  visible?: boolean
  isObjectFilter?: boolean
  flex?: number
  filterRow?: string[]
  filterWithRelation?: { row: string; value: string }[]
  filterType?:
    | 'text'
    | 'list'
    | 'range'
    | 'comma'
    | 'custom'
    | 'date'
    | 'progress'
    | 'multiSelect'
    | 'projectStatus'
    | 'semiColon'
    | 'boolean'
    | 'wildcard-multi-select'
    | 'number'
    | 'wildcard-predecessor-successor'
    | 'phase/package'
  listOption?: any
  cursor?: 'pointer' | 'default'
  editableType?: 'date' | 'text' | 'textArea' | 'number' | 'dropDown' | 'richTextEditor' | 'multiDropDown'
  isEditableCell?: boolean | ((cell: any, row: any) => boolean)
  isSaveConfirmationRequired?: boolean | ((cell: any, row: any) => boolean)
  isCustomCellEditable?: boolean
  tableId?: string
  onEditCell?: (cell: any, newValue: any, row: any) => void
  editOption?: string[]
  popOverWidth?: string
  require?: boolean
  sortAlphabetically?: boolean
  pin?: boolean
  validationMessage?: (cell: any, newValue: any) => string
}

export type IRowData = { id: string } & Record<string, any>

export interface TanStackTableProps {
  rows: IRowData[]
  columns: CustomColumnDef<any>[]
  onDragEnd?: (data: IRowData[], dragId: string, dropId: string) => void
  handleRowClick?: (row: IRowData) => void
  gridFilters?: FilterValue[] | any
  setGridFilters?: any
  setFilterRows?: (row: any[]) => void // use For store filter row data in redux(projects we are using because for next and pre buttons)
  columnVisibilities?: Record<string, boolean>
  setColumnVisibilitiesApi?: (visibilities: Record<string, boolean>) => void
  colWidth?: any
  setColWidth?: any
  isResizeColumn?: boolean
  isCount?: boolean
  gridName?: string
  tableHeight?: string
  showPagination?: boolean
  pageSize?: number
  isOverflow?: boolean
  className?: string
  isLoading?: boolean
  stickyColumnCount?: number // Number of columns to make sticky (default: 4 only work if enableSticky true)
  enableSticky?: boolean // Whether to enable sticky columns (default: true)
}

import React, { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import styles from './DesignManager.module.scss'
import { IAddDesignManagersProps } from './interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import AvatarPicker from '@/src/component/shared/avatarPicker'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateDesignManager,
  useDeleteDesignManager,
  useGetDesignManager,
  useUpdateDesignManager,
} from '@/src/hooks/useDesignManager'
import { IDesignProjectManager } from '@/src/services/designManager/interface'
import { extractAvatarUrlForPayload } from '@/src/utils/stringUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/
const phoneNumberRegex = /^[\d+\- ()]{9,15}$/
//   /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/

const validationSchema = Yup.object({
  email: Yup.string().matches(emailRegex, 'Invalid email format').notRequired(),
  phone_number: Yup.string().notRequired().matches(phoneNumberRegex, 'Invalid phone number'),
  // .phone('any', 'Invalid phone number').matches(phoneNumberRegex, 'Phone number must be 10 digits, optionally with country code')
})

const AddDesignManagers: React.FC<IAddDesignManagersProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addDesignManagers } = drawerStates
  const { designManagers, isLoading: isFetching } = useGetDesignManager(addDesignManagers)
  // MUTATIONS
  const { mutate: addMasterDesignManager, isPending: isAdding } = useCreateDesignManager()
  const { mutate: deleteDesignManager } = useDeleteDesignManager()
  const { mutate: updateDesignManager, isPending: isUpdating } = useUpdateDesignManager()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string, setSubmitting?: Function) => ({
    onSuccess: () => {
      successToast(successMsg)
      setEditIndex(null)
      formik.resetForm()
    },
    onError: (err: any) => {
      setSubmitting && setSubmitting(false)
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { design_manager: '', email: '', phone_number: '', location: '' },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      const payload = {
        design_manager: values.design_manager,
        phone_number: values.phone_number,
        email: values.email,
        location: values.location,
      }
      if (editIndex !== null) {
        updateDesignManager(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record', setSubmitting),
        )
      } else {
        addMasterDesignManager(
          payload,
          handleMutationCallbacks('Record successfully added', 'Failed to add record', setSubmitting),
        )
      }
      // formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteDesignManager(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editDesignManagers: any = designManagers.find((designManager) => designManager.id === id)

    formik.setValues({
      design_manager: editDesignManagers?.design_manager,
      email: editDesignManagers?.email || '',
      phone_number: editDesignManagers?.phone_number || '',
      location: editDesignManagers?.location || '',
    })
    setEditIndex(id)
  }

  const handleUpdateAvatar = async (id: number, avatar: string) => {
    const editDesignManagers: any = designManagers.find(
      (designManager: IDesignProjectManager) => designManager.id === id,
    )
    if (editDesignManagers) {
      updateDesignManager(
        {
          id,
          avatar: extractAvatarUrlForPayload(avatar) as string,
          design_manager: editDesignManagers.design_manager,
          email: editDesignManagers?.email || '',
          phone_number: editDesignManagers?.phone_number || '',
          location: editDesignManagers?.location || '',
        },
        handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
      )
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'avatar',
      header: 'Avatar',
      size: 70,
      cell: ({ row }) => {
        const avatar = row?.original?.avatar ? [row?.original?.avatar?.url] : []
        return <AvatarPicker url={avatar} handleSelectAvatar={(val) => handleUpdateAvatar(Number(row?.id), val[0])} />
      },
    },
    {
      accessorKey: 'design_manager',
      header: 'Design Manager',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'email',
      header: 'Email',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'phone_number',
      header: 'Phone Number',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'location',
      header: 'Location',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Design Manager'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="design_manager"
              labelText={'Design Manager'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.design_manager}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="email"
              labelText={'Email'}
              error={Boolean(formik.touched.email && formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email && formik.errors.email}
              placeholder="Enter Email Address"
              variant={'outlined'}
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="phone_number"
              labelText={'Phone Number'}
              placeholder="Enter phone number"
              error={Boolean(formik.touched.phone_number && formik.errors.phone_number)}
              helperText={formik.touched.phone_number && formik.errors.phone_number && formik.errors.phone_number}
              variant={'outlined'}
              value={formik.values.phone_number}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="location"
              labelText={'location'}
              placeholder="Enter Location"
              variant={'outlined'}
              value={formik.values.location}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.design_manager?.trim()}
              >
                {'Update Design Manager'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.isValid}
              >
                {'+ Add Design Manager'}
              </Button>
            )}
          </div>
        </form>
        {designManagers?.length >= 1 ? (
          isFetching ? (
            <Loader />
          ) : (
            // <Table data={designManagers} columns={columns} />
            <TanStackTable rows={sortData(designManagers, 'design_manager')} columns={columns} isOverflow={true} />
          )
        ) : (
          ''
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddDesignManagers

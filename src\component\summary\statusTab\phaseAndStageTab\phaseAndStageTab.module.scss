@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.tabButtons {
  display: flex;
  align-items: center;
  gap: 30px;
  padding-right: 16px;
}

.tab {
  cursor: pointer;
  color: $DARK;
}

.selectedTab {
  height: fit-content;
  color: $DARK;
  border-bottom: 1px solid $ERROR;
}
.container {
  min-width: 1020px;
  width: 100%;
  height: 100vh;
  @include respond-to('mobile') {
    max-width: 22.5rem;
    min-width: 22rem;
  }

  @include respond-to('tablet') {
    max-width: 37.5rem;
    min-width: 40rem;
  }

  @include respond-to('laptop') {
    max-width: 60rem;
    min-width: 60rem;
  }

  @include respond-to('desktop') {
    max-width: 85rem;
    min-width: 85rem;
  }
}

.header {
  // background-color: $WHITE;
  // top: 0px;
  // position: sticky;
  // z-index: 10;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid $LIGHT_200;
  padding: 10px;
}
.contain {
  // position: relative;
  // z-index: 9;
}

.closeIcon {
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #555;
  height: 23px;
  width: 22px;
}

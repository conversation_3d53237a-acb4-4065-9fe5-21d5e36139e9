import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetGovernanceResponse, IEntityKeyAchievementStatus, IEntityKeyAchievement } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IEntityKeyAchievementStatus = {
  getEntityKeyAchievement: StatusEnum.Idle,
  addEntityKeyAchievement: StatusEnum.Idle,
  deleteEntityKeyAchievement: StatusEnum.Idle,
  updateEntityKeyAchievement: StatusEnum.Idle,
  entityKeyAchievements: [],
  entityKeyAchievement: {},
}

export const getEntityKeyAchievement = createAsyncThunk(
  '/get-project-key-achievements',
  async (data: { period: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(`/project-key-achievements?period=${period}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addEntityKeyAchievement = createAsyncThunk(
  '/add-project-key-achievements',
  async (data: IEntityKeyAchievement, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/project-key-achievements`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateEntityKeyAchievement = createAsyncThunk(
  '/update-project-key-achievements',
  async (
    data: {
      id: number
      data: IEntityKeyAchievement
    },
    thunkAPI,
  ) => {
    const { id, ...rest } = data
    try {
      const response = await ApiPutNoAuth(`/project-key-achievements/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteEntityKeyAchievement = createAsyncThunk(
  '/delete-project-key-achievements',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/project-key-achievements/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const entityKeyAchievementSlice = createSlice({
  name: 'entityKeyAchievement',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getEntityKeyAchievement
    builder.addCase(getEntityKeyAchievement.pending, (state) => {
      state.getEntityKeyAchievement = StatusEnum.Pending
    })
    builder.addCase(getEntityKeyAchievement.fulfilled, (state, action) => {
      state.getEntityKeyAchievement = StatusEnum.Success
      const actionPayload = action.payload as IGetGovernanceResponse
      state.entityKeyAchievements = actionPayload.data
    })
    builder.addCase(getEntityKeyAchievement.rejected, (state) => {
      state.getEntityKeyAchievement = StatusEnum.Failed
    })
    //addEntityKeyAchievement
    builder.addCase(addEntityKeyAchievement.pending, (state) => {
      state.addEntityKeyAchievement = StatusEnum.Pending
    })
    builder.addCase(addEntityKeyAchievement.fulfilled, (state, action) => {
      state.addEntityKeyAchievement = StatusEnum.Success
    })
    builder.addCase(addEntityKeyAchievement.rejected, (state) => {
      state.addEntityKeyAchievement = StatusEnum.Failed
    })
    //updateEntityKeyAchievement
    builder.addCase(updateEntityKeyAchievement.pending, (state) => {
      state.updateEntityKeyAchievement = StatusEnum.Pending
    })
    builder.addCase(updateEntityKeyAchievement.fulfilled, (state, action) => {
      state.updateEntityKeyAchievement = StatusEnum.Success
    })
    builder.addCase(updateEntityKeyAchievement.rejected, (state) => {
      state.updateEntityKeyAchievement = StatusEnum.Failed
    })
    //deleteEntityKeyAchievement
    builder.addCase(deleteEntityKeyAchievement.pending, (state) => {
      state.deleteEntityKeyAchievement = StatusEnum.Pending
    })
    builder.addCase(deleteEntityKeyAchievement.fulfilled, (state, action) => {
      state.deleteEntityKeyAchievement = StatusEnum.Success
    })
    builder.addCase(deleteEntityKeyAchievement.rejected, (state) => {
      state.deleteEntityKeyAchievement = StatusEnum.Failed
    })
  },
})

export const {} = entityKeyAchievementSlice.actions

// Export the reducer
export default entityKeyAchievementSlice.reducer

import React, { useEffect, useRef, useState, useMemo } from 'react'
import ClearIcon from '@mui/icons-material/Clear'
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined'
import { InputAdornment, Popover, useMediaQuery } from '@mui/material'
import { styled } from '@mui/system'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'

import FilterOption from './FilterOption'
import FilterPopover from './FilterPopover'
import { formattedData, getUniqueValuesFromArray, MainSearchFilterFields } from './helper'
import { ISearchDrawerProps, FilterOptionType } from './interface'
import styles from './SearchDrawer.module.scss'
import {
  applyFilters,
  filterProjects,
  getFieldMapping,
  processFilterOptions,
  resetAllFilters,
} from '../../../utils/searchDrawer/searchDrawer'
import Button from '../../shared/button'
import Loader from '../../shared/loader'
import TextInput<PERSON>ield from '../../shared/textInputField'
import Typography<PERSON>ield from '../../shared/typography'
import SearchIcon from '../../svgImages/searchIcon'

import { Routes } from '@/src/constant/enum'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useSearchDrawer from '@/src/redux/searchDrawer/useSearchDrawer'
import { getProjects } from '@/src/services/projects'
import { IProjects } from '@/src/services/projects/interface'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'

export const PopupBody = styled('div')(
  ({ theme }) => `
  width: 200px;
  padding: 10px 12px;
  padding-right: 5px;
  border-radius: 8px;
  background-color: ${theme.palette.mode === 'dark' ? '#333' : '#fff'};
  box-shadow: ${theme.palette.mode === 'dark' ? `0px 4px 8px rgba(0, 0, 0, 0.7)` : `0px 4px 8px rgba(0, 0, 0, 0.1)`};
  z-index: 110;
  
  @media (min-width: 768px) {
    width: 220px;
    padding: 12px 14px;
    padding-right: 5px;
  }
  
  @media (min-width: 1024px) {
    width: 250px;
    padding: 12px 16px;
    padding-right: 5px;
  }
`,
)

const SearchDrawer: React.FC<ISearchDrawerProps> = ({
  className,
  isDrawerOpen,
  setIsDrawerOpen,
  anchor,
  setAnchor,
}) => {
  const searchDrawerState = useSearchDrawer()
  const { currentPeriod } = useMasterPeriod()
  const router = useRouter()

  // Media query hooks for responsive design
  const isTablet = useMediaQuery('(min-width:768px)')
  const isLaptop = useMediaQuery('(min-width:1024px)')

  const [filterAnchor, setFilterAnchor] = useState<HTMLElement | null>(null)
  const [filterField, setFilterField] = useState<string>('')
  const [isRequireField, setIsRequireField] = useState<boolean>(false)
  const [filterOption, setFilterOption] = useState<FilterOptionType[]>([])
  const [selectedOptions, setSelectedOptions] = useState<FilterOptionType[]>([])
  const [loading, setLoading] = useState<boolean>(false)

  const searchInputRef = useRef<HTMLInputElement>(null)
  const open = Boolean(anchor)
  const id = open ? 'simple-popup' : undefined

  // Fetch projects data - only enabled when needed
  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY, currentPeriod],
    queryFn: () => getProjects({ period: currentPeriod }),
    enabled: isDrawerOpen,
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000, // 5 minutes cache
  })

  // Use memo to prevent unnecessary recalculations of filtered projects
  const searchResults = useMemo(() => {
    if (!projects) return []
    const updatedData = formattedData(projects as IProjects[])
    const filterProject = filterProjects(updatedData, searchDrawerState)
    const sortedProjectData = sortArrayByKeyWithTypeConversion(filterProject, 'project_sorting_order', true)
    return sortedProjectData
  }, [projects, searchDrawerState.searchQuery, searchDrawerState])

  // Focus search input when drawer opens
  useEffect(() => {
    if (isDrawerOpen && searchInputRef.current) {
      const timer = setTimeout(() => {
        searchInputRef.current?.focus()
      }, 0)
      return () => clearTimeout(timer)
    }
  }, [isDrawerOpen])

  // Reset anchor when drawer state changes
  useEffect(() => {
    if (!isDrawerOpen) {
      setAnchor(null)
    }
  }, [isDrawerOpen, setAnchor])

  // Show loading state when fetching data
  useEffect(() => {
    setLoading(isDrawerOpen && !projects)
  }, [isDrawerOpen, projects])

  // Handle window resize for responsive adjustments
  useEffect(() => {
    const handleResize = () => {
      // If filter popup is open on small screen and user resizes to larger screen, adjust UI
      if (anchor && window.innerWidth < 768) {
        setAnchor(null)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [anchor, setAnchor])

  // Navigate to project summary with preventable navigation
  const selectSearch = (projectName: string, e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()

    // Close drawer on mobile after selection
    if (!isTablet) {
      setIsDrawerOpen(false)
    }

    // Use shallow routing to prevent full page reload
    router.push(`${Routes.SUMMARY}/${encodeURIComponent(projectName)}?search=true`, undefined, { shallow: true })
  }

  // Handle search input change
  const handleSearchChange = (value: string) => {
    searchDrawerState.setSearchQueryApi(value)
  }

  // Handle Clear search input
  const handleClearSearchChange = () => {
    searchDrawerState.setSearchQueryApi('')
  }

  // Toggle filter popup
  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setAnchor(anchor ? null : event.currentTarget)
  }

  // Handle filter selection
  const handleFilterSelect = (
    e: React.MouseEvent<HTMLElement>,
    field: string,
    type?: string,
    // require: boolean = false,
  ) => {
    const fieldForFilter = getFieldMapping(field)
    e.stopPropagation()
    setFilterAnchor(e.currentTarget)
    // setIsRequireField(require)
    setFilterField(fieldForFilter)

    // Set selected options based on current values
    const currentValue = searchDrawerState[fieldForFilter as keyof typeof searchDrawerState]
    if (Array.isArray(currentValue)) {
      setSelectedOptions(
        currentValue.map((item) => ({
          label: item,
          value: item,
        })),
      )
    }

    // Get unique filter values from projects
    if (projects) {
      const updatedData = formattedData(projects as IProjects[])
      const filterProject = filterProjects(updatedData, searchDrawerState, field)
      const isNullValueExist = filterProject
        ?.map((item: any) => item[field])
        ?.filter((res) => res === null || res === undefined || res?.length === 0)
      if (isNullValueExist?.length > 0 && field !== 'label') {
        setIsRequireField(false)
      } else {
        setIsRequireField(true)
      }
      const filterData: string[] = [
        // ...getUniqueValuesFromArray(filterProject?.map((item: any) => item[field]).filter((item: any) => item) || []),
        ...getUniqueValuesFromArray(filterProject?.map((item: any) => item[field])),
      ]

      // Process and set filter options
      if (field === 'label') {
        const fieldArray = [
          { label: 'Rebaseline', value: 'is_subject_to_rebaseline' },
          { label: 'budget_uplift_submitted', value: 'budget_uplift_submitted' },
          { label: 'eot_submitted', value: 'eot_submitted' },
          { label: 'tip_submitted', value: 'tip_submitted' },
          { label: 'EOT_to_be_submitted', value: 'EOT_to_be_submitted' },
          { label: 'budget_uplift_submitted', value: 'budget_uplift_submitted' },
          { label: 'potential_budget_uplift_submitted', value: 'potential_budget_uplift_submitted' },
        ]
        setFilterOption(fieldArray)
        // NOTE : set all option selected initially
        if (!searchDrawerState.userFilterFields?.includes(fieldForFilter)) {
          setSelectedOptions(fieldArray)
        }
      } else {
        const array = processFilterOptions(filterData, type)
        setFilterOption(array)
        const uniqueData = Array.from(
          new Map(array.map((item) => [`${item.label?.toLowerCase()}-${item.value?.toLowerCase()}`, item])).values(),
        )
        const isAlreadyFilteredField = searchDrawerState.userFilterFields?.includes(fieldForFilter)
        const currentValue = searchDrawerState[fieldForFilter as keyof typeof searchDrawerState]
        const result = Array.isArray(currentValue) ? uniqueData.filter((item) => currentValue.includes(item.label)) : []
        if (isAlreadyFilteredField && result?.length > 0) {
          setSelectedOptions([...result])
        }
        if (!searchDrawerState.userFilterFields?.includes(fieldForFilter)) {
          // NOTE : set all option selected initially
          setSelectedOptions([...uniqueData])
        }
      }
    }
  }

  // Apply selected filters
  const handleApplyFilters = (selectedOption: FilterOptionType[]) => {
    const option = selectedOption?.map((item) => item?.value)
    const isFieldExist = searchDrawerState.userFilterFields?.find((res) => res === filterField)
    if (selectedOption?.length === (!isRequireField ? filterOption.length + 1 : filterOption.length)) {
      if (isFieldExist) {
        searchDrawerState.setUserFilterFieldsApi([
          ...searchDrawerState.userFilterFields?.filter((res) => res !== filterField),
        ])
      }
      return
    }
    // const opt =
    //   selectedOption?.length === (!isRequireField ? filterOption.length + 1 : filterOption.length) ? [] : option
    // applyFilters(filterField, opt, searchDrawerState)
    applyFilters(filterField, option, searchDrawerState)
    // NOTE : add apply filter field to userFilterField state
    if (!isFieldExist && filterField) {
      searchDrawerState.setUserFilterFieldsApi([...searchDrawerState.userFilterFields, filterField])
    }
  }

  // Reset all filters
  const handleResetAll = () => {
    resetAllFilters(searchDrawerState)
  }

  // Prevent propagation for the entire component
  const handleContainerClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  // Calculate the placement based on screen size
  const getPopupPlacement = () => {
    if (!isTablet) return 'bottom'
    if (!isLaptop) return 'bottom-end'
    return 'left-end'
  }

  const appliedFilterCount = useMemo(() => {
    // NOTE : Check filter is apply on how many fields
    return MainSearchFilterFields.filter(
      (key) =>
        Array.isArray(searchDrawerState[key as keyof typeof searchDrawerState]) &&
        (searchDrawerState[key as keyof typeof searchDrawerState] as unknown[]).length > 0,
    ).length
  }, [searchDrawerState])

  return (
    <div
      onClick={handleContainerClick}
      className={`${styles.mainContainer} ${isDrawerOpen ? styles.drawerOpen : ''} ${className}`}
    >
      {loading ? (
        <Loader />
      ) : (
        <div className={styles.container}>
          <div className={styles.searchComponent}>
            <TextInputField
              inputRef={searchInputRef}
              autoFocus={true}
              className={styles.searchField}
              placeholder="Search Projects..."
              variant={'outlined'}
              value={searchDrawerState.searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    {searchDrawerState.searchQuery ? (
                      <ClearIcon
                        className={styles.endClearAdornmentIcon}
                        role="button"
                        onClick={handleClearSearchChange}
                      />
                    ) : (
                      <SearchIcon className={styles.endAdornmentIcon} />
                    )}
                  </InputAdornment>
                ),
              }}
            />
            <div onClick={handleFilterClick}>
              <FilterAltOutlinedIcon className={styles.filterIcon} />
            </div>
          </div>

          <div className={styles.searchList}>
            {searchResults?.map((project: any, index) => (
              <div
                className={`${styles.listContainer} ${anchor ? styles.cursorDefault : ''}`}
                key={index}
                onClick={(e) => !anchor && selectSearch(project.project_name, e)}
              >
                <div className={styles.selectList}></div>
                <div className={styles.listItem}>
                  <TypographyField text={project.project_name} variant={'darkText'} />
                </div>
              </div>
            ))}

            {isDrawerOpen && (
              <Popover
                id={id}
                open={open}
                anchorEl={anchor}
                className={styles.popup}
                onClose={() => setAnchor(null)}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                sx={{
                  marginTop: '-20px',
                  marginLeft: '10px',
                }}
              >
                <PopupBody>
                  <FilterOption handleFilterSelect={handleFilterSelect} />
                  <FilterPopover
                    isBlank={!isRequireField}
                    selectedOptions={selectedOptions}
                    setSelectedOptions={setSelectedOptions}
                    anchorEl={filterAnchor}
                    onClose={() => setFilterAnchor(null)}
                    options={filterOption}
                    onApply={(selectedOption) => handleApplyFilters(selectedOption)}
                    type={filterField === 'overall_forecasted_finish_date' ? 'date' : 'other'}
                    filterField={filterField}
                  />
                  <div className={styles.applyFilter}>
                    <Button
                      variant="contained"
                      disabled={appliedFilterCount === 0}
                      onClick={(e) => {
                        e.stopPropagation()
                        // setAnchor(null)
                        setSelectedOptions([])
                        handleResetAll()
                        searchDrawerState.setUserFilterFieldsApi([])
                      }}
                    >
                      Reset All
                    </Button>
                    <Button
                      variant="contained"
                      disabled={appliedFilterCount === 0}
                      onClick={(e) => {
                        e.stopPropagation()
                        setAnchor(null)
                      }}
                    >
                      Apply Filters
                      {/* {appliedFilterCount === 0 ? 'Cancel' : 'Apply Filters'} */}
                    </Button>
                  </div>
                </PopupBody>
              </Popover>
            )}
          </div>

          {<span className={styles.count}>Count: {searchResults?.length}</span>}
        </div>
      )}
    </div>
  )
}

export default SearchDrawer

import { SwipeableDrawer, SxProps, Theme } from '@mui/material'
import styles from './Drawer.module.scss'

interface DrawerProps {
  open: boolean
  onClose: () => void
  anchor: 'left' | 'top' | 'right' | 'bottom'
  children: React.ReactNode
  paperSx?: SxProps<Theme> // More specific type for styling
}

const Drawer: React.FC<DrawerProps> = ({ open, onClose, children, anchor, paperSx }) => {
  return (
    <SwipeableDrawer
      anchor={anchor}
      open={open}
      onClose={onClose}
      onOpen={() => {}}
      classes={{
        root: styles.drawerRoot,
      }}
      PaperProps={{
        sx: paperSx,
      }}
      disableBackdropTransition={true}
    >
      {children}
    </SwipeableDrawer>
  )
}

export default Drawer

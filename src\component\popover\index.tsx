import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './PopOver.module.scss'
import Button from '../shared/button'

interface ModalProps {
  open: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode // Adjusted type for children
}

const PopOver: React.FC<ModalProps> = ({ open, children, onClose, title }) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    width: '800px',
    maxHeight: '600px', // Fixed maximum height
    transform: 'translate(-50%, -50%)',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    px: '20px',
    pb: '10px',
    zIndex: 1,
    overflowY: 'auto',
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.closeIcon}>
          <div className={styles.title}>{title}</div>
          <Button onClick={onClose}>Close</Button>
        </div>
        <div className={styles.confirmContent}>
          <div>{children}</div>
        </div>
      </Box>
    </Modal>
  )
}

export default PopOver

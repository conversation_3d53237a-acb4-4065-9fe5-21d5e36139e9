import React, { ReactElement } from 'react'
import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip'
import styles from './Tooltip.module.scss'

const TableTooltip: React.FC<any> = ({ children, title, PopperProps, ...rest }): ReactElement => {
  return (
    <>
      {title ? (
        <Tooltip PopperProps={PopperProps} title={title} enterDelay={500} {...rest}>
          {children}
        </Tooltip>
      ) : (
        <>{children}</>
      )}
    </>
  )
}

export default TableTooltip

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createEntityCategory,
  deleteEntityCategory,
  getEntityCategory,
  updateEntityCategory,
} from '../services/entityCategory'
import { IGetEntityCategoryResponse, IUpdateEntityCategory } from '../services/entityCategory/interface'

export const QUERY_ENTITY_CATEGORY = 'entity-category'

/**
 * Hook to fetch Entity Category
 * @param enabled - Enables or disables the query
 */
export const useGetEntityCategory = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetEntityCategoryResponse>({
    queryKey: [QUERY_ENTITY_CATEGORY],
    queryFn: getEntityCategory,
    enabled,
  })

  return {
    entityCategories: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Entity Category
 */
export const useCreateEntityCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createEntityCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_ENTITY_CATEGORY] })
    },
  })
}

/**
 * Hook to delete a Entity Category
 */
export const useDeleteEntityCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteEntityCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_ENTITY_CATEGORY] })
    },
  })
}

/**
 * Hook to update a Entity Category
 */
export const useUpdateEntityCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateEntityCategory,
    onMutate: async (updatedData: IUpdateEntityCategory) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_ENTITY_CATEGORY] })
      const previousData = queryClient.getQueryData<IGetEntityCategoryResponse>([QUERY_ENTITY_CATEGORY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetEntityCategoryResponse>([QUERY_ENTITY_CATEGORY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((entity) =>
            entity.id === updatedData.id ? { ...entity, entity_category: updatedData.entity_category } : entity,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_ENTITY_CATEGORY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_ENTITY_CATEGORY] })
    },
  })
}

@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.cardContainer {
  padding: 12px 15px;
  background-color: rgba(104, 178, 226, 0.1);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center; // Default (mobile)

  @include respond-to('tablet') {
    align-items: flex-start;
  }

  @include respond-to('laptop') {
    width: -webkit-fill-available;
  }

  @include respond-to('desktop') {
    width: unset;
  }
}

.cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
  // @include respond-to('mobile') {
  //   display: grid;
  //   grid-template-columns: 1fr 1fr;
  // }

  @include respond-to('tablet') {
    display: flex;
    flex-direction: column;
  }

  .card {
    display: flex;
    gap: 10px;
  }
}

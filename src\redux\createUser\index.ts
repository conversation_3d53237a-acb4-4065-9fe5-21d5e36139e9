import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterRatingResponse, ICreateUserState, IUser } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ICreateUserState = {
  getUserStatus: StatusEnum.Idle,
  addUserStatus: StatusEnum.Idle,
  deleteUserStatus: StatusEnum.Idle,
  updateUserStatus: StatusEnum.Idle,
  users: [],
  user: {},
}

export const getUser = createAsyncThunk('/get-user', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/user`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addUser = createAsyncThunk('/add-user', async (data: IUser, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/user`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateUser = createAsyncThunk('/update-user', async (data: { id: string; data: IUser }, thunkAPI) => {
  try {
    const response = await ApiPutNoAuth(`/user/${data.id}`, data.data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteUser = createAsyncThunk('/delete-user', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/user/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getUser
    builder.addCase(getUser.pending, (state) => {
      state.getUserStatus = StatusEnum.Pending
    })
    builder.addCase(getUser.fulfilled, (state, action) => {
      state.getUserStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterRatingResponse
      state.users = actionPayload.data
    })
    builder.addCase(getUser.rejected, (state) => {
      state.getUserStatus = StatusEnum.Failed
    })
    //addUser
    builder.addCase(addUser.pending, (state) => {
      state.addUserStatus = StatusEnum.Pending
    })
    builder.addCase(addUser.fulfilled, (state, action) => {
      state.addUserStatus = StatusEnum.Success
    })
    builder.addCase(addUser.rejected, (state) => {
      state.addUserStatus = StatusEnum.Failed
    })
    //updateUser
    builder.addCase(updateUser.pending, (state) => {
      state.updateUserStatus = StatusEnum.Pending
    })
    builder.addCase(updateUser.fulfilled, (state, action) => {
      state.updateUserStatus = StatusEnum.Success
    })
    builder.addCase(updateUser.rejected, (state) => {
      state.updateUserStatus = StatusEnum.Failed
    })
    //deleteUser
    builder.addCase(deleteUser.pending, (state) => {
      state.deleteUserStatus = StatusEnum.Pending
    })
    builder.addCase(deleteUser.fulfilled, (state, action) => {
      state.deleteUserStatus = StatusEnum.Success
    })
    builder.addCase(deleteUser.rejected, (state) => {
      state.deleteUserStatus = StatusEnum.Failed
    })
  },
})

export const {} = userSlice.actions

// Export the reducer
export default userSlice.reducer

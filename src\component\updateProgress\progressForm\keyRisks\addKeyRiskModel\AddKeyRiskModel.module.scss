@import '/styles/color.scss';

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.rightSection {
  display: flex;
  align-items: center;
  gap: 10px;
  .divider {
    width: 1px;
    height: 10px;
    gap: 0px;
    opacity: 0px;
    background: $GRAY_500;
  }
}
.buttons {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
}
.buttonGroup {
  cursor: pointer;
  display: flex;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
  .button {
    padding: 5px 36px;
    background-color: #fafafa;
    &.active {
      background-color: #b7b7b7ff;
      color: #212121 !important;
    }
    &.activeLow {
      background-color: #23c370;
      color: $WHITE !important;
    }
    &.activeMedium {
      background-color: #ff8c00;
      color: #212121 !important;
    }
    &.activeHigh {
      color: $WHITE !important;
      background-color: rgba(255, 27, 34, 0.75);
    }
  }
}
.buttonTextColorLight {
  color: #fafafa;
}
.buttonTextColorDark {
  color: #444444;
}
.labelOfRating {
  font-family: Poppins;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: left;
  color: #808080;
  margin-bottom: 5px;
}
.model {
  backdrop-filter: blur(5px);

  .comboBoxContainer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px;
    padding-bottom: 9px;

    .comboBoxInput > div > div {
      background-color: #fff !important;
    }
  }
}

import { toast } from 'sonner'

export const errorTosterStyle: any = {
  style: {
    backgroundColor: '#f44336',
    color: '#fff',
  },
}

export const successTosterStyle: any = {
  style: {
    backgroundColor: '#4caf50', // green
    color: '#fff',
  },
}

export const showCustomToastError = (message: string) => {
  toast.error(message, errorTosterStyle)
}

export const showCustomToastSuccess = (message: string) => {
  toast.success(message, successTosterStyle)
}

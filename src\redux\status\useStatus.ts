import { useDispatch, useSelector } from 'react-redux'
import {
  getStatus,
  addStatus,
  updateStatus,
  updateTableStatus,
  deleteStatus,
  getOneStatus,
  setProgressTableColumnVisibilities,
  statusSorting,
  setProgressFilterValue,
  renamePhaseName,
  deletePhase,
  addPhaseName,
} from '.'
import { IAddPhasePayload, IDeletePhasePayload, IRenamePhasePayload } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useStatus = () => {
  const dispatch: AppDispatch = useDispatch()

  const getStatusProcess = useSelector((state: RootState) => state.status.getStatusProcess)
  const getOneStatusProcess = useSelector((state: RootState) => state.status.getOneStatusProcess)
  const statuses = useSelector((state: RootState) => state.status.statuses)
  const status = useSelector((state: RootState) => state.status.status)
  const progressTableVisibilities = useSelector((state: RootState) => state.status.progressTableVisibilities)
  const progressFilterValue = useSelector((state: RootState) => state.status.progressFilterValue)
  const renamePhaseProcess = useSelector((state: RootState) => state.status.renamePhaseProcess)
  const addPhaseProcess = useSelector((state: RootState) => state.status.addPhaseProcess)

  const getStatusApi = (data: { period: string; project_name?: string }) => dispatch(getStatus(data))
  const statusSortingApi = (data: any) => dispatch(statusSorting(data))
  const getOneStatusApi = (data: { id: string; period: string; project_name: string }) => dispatch(getOneStatus(data))
  const addStatusApi = (data: any) => dispatch(addStatus(data))
  const updateStatusApi = (data: { id: number; data: any }) => dispatch(updateStatus(data))
  const updateTableStatusApi = (data: { id: number; data: any }) => dispatch(updateTableStatus(data))
  const deleteStatusApi = (data: number) => dispatch(deleteStatus(data))
  const setProgressTableColumnVisibilitiesApi = (data: any) => dispatch(setProgressTableColumnVisibilities(data))
  const setProgressFilterValueApi = (data: any) => dispatch(setProgressFilterValue(data))

  /*
   * Dispatch for phase
   */
  const deletePhaseApi = (id: number) => dispatch(deletePhase(id))
  const renamePhaseNameApi = (data: IRenamePhasePayload) => dispatch(renamePhaseName(data))
  const addPhaseNameApi = (data: IAddPhasePayload) => dispatch(addPhaseName(data))

  return {
    getStatusProcess,
    getOneStatusProcess,
    statuses,
    status,
    progressTableVisibilities,
    progressFilterValue,
    renamePhaseProcess,
    addPhaseProcess,
    statusSortingApi,
    getStatusApi,
    getOneStatusApi,
    addStatusApi,
    deleteStatusApi,
    updateStatusApi,
    setProgressTableColumnVisibilitiesApi,
    setProgressFilterValueApi,
    deletePhaseApi,
    renamePhaseNameApi,
    addPhaseNameApi,
    updateTableStatusApi,
  }
}

export default useStatus

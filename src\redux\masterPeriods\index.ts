import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterPeriodResponse, IMasterPeriodsState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IMasterPeriodsState = {
  getMasterPeriodStates: StatusEnum.Idle,
  addMasterPeriodStates: StatusEnum.Idle,
  getAllPeriodStatus: StatusEnum.Idle,
  finalFreezePeriodStatus: StatusEnum.Idle,
  userFreezePeriodStates: StatusEnum.Idle,
  unFreezePeriodStates: StatusEnum.Idle,
  mainPeriod: '',
  allPeriods: [],
  periods: [],
  period: {
    id: 0,
    period: '',
    freeze_type: '',
  },
  // currentPeriod: '',
  // isPeriodChangeFromProjectList: false,
}

export const getCurrentPeriod = createAsyncThunk('/get-master-project-period', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/current-period`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})
export const getAllPeriod = createAsyncThunk('/periods', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/periods`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterPeriods = createAsyncThunk(
  '/add-master-project-period',
  async (data: { period: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-project-period`, {
        period: data.period,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const userFreezePeriod = createAsyncThunk('/master-project-period/user-freeze-period', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-project-period/user-freeze-period`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const finalFreezePeriod = createAsyncThunk('/master-project-period/final-freeze-period', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-project-period/final-freeze-period`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const unFreezePeriod = createAsyncThunk('/master-project-period/un-freeze-period', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-project-period/un-freeze-period`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const periodSlice = createSlice({
  name: 'masterPeriod',
  initialState,
  reducers: {
    // setPeriod(state, action) {
    //   state.currentPeriod = action.payload
    // },
    // setIsPeriodChangeFromProjectList(state, action) {
    //   state.isPeriodChangeFromProjectList = action.payload
    // },
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterPeriods
    builder.addCase(getCurrentPeriod.pending, (state) => {
      state.getMasterPeriodStates = StatusEnum.Pending
    })
    builder.addCase(getCurrentPeriod.fulfilled, (state, action) => {
      state.getMasterPeriodStates = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterPeriodResponse
      state.period = actionPayload.data
      state.mainPeriod = actionPayload?.data?.period
    })
    builder.addCase(getCurrentPeriod.rejected, (state) => {
      state.getMasterPeriodStates = StatusEnum.Failed
    })
    //getAllPeriods
    builder.addCase(getAllPeriod.pending, (state) => {
      state.getAllPeriodStatus = StatusEnum.Pending
    })
    builder.addCase(getAllPeriod.fulfilled, (state, action) => {
      state.getAllPeriodStatus = StatusEnum.Success
      const actionPayload = action.payload as any
      state.allPeriods = actionPayload.data
    })
    builder.addCase(getAllPeriod.rejected, (state) => {
      state.getAllPeriodStatus = StatusEnum.Failed
    })
    //addMasterPeriods
    builder.addCase(addMasterPeriods.pending, (state) => {
      state.addMasterPeriodStates = StatusEnum.Pending
    })
    builder.addCase(addMasterPeriods.fulfilled, (state, action) => {
      state.addMasterPeriodStates = StatusEnum.Success
    })
    builder.addCase(addMasterPeriods.rejected, (state) => {
      state.addMasterPeriodStates = StatusEnum.Failed
    })
    //userFreezePeriod
    builder.addCase(userFreezePeriod.pending, (state) => {
      state.userFreezePeriodStates = StatusEnum.Pending
    })
    builder.addCase(userFreezePeriod.fulfilled, (state, action) => {
      state.userFreezePeriodStates = StatusEnum.Success
    })
    builder.addCase(userFreezePeriod.rejected, (state) => {
      state.userFreezePeriodStates = StatusEnum.Failed
    })
    //finalFreezePeriod
    builder.addCase(finalFreezePeriod.pending, (state) => {
      state.finalFreezePeriodStatus = StatusEnum.Pending
    })
    builder.addCase(finalFreezePeriod.fulfilled, (state, action) => {
      state.finalFreezePeriodStatus = StatusEnum.Success
    })
    builder.addCase(finalFreezePeriod.rejected, (state) => {
      state.finalFreezePeriodStatus = StatusEnum.Failed
    })
    //unFreezePeriod
    builder.addCase(unFreezePeriod.pending, (state) => {
      state.unFreezePeriodStates = StatusEnum.Pending
    })
    builder.addCase(unFreezePeriod.fulfilled, (state, action) => {
      state.unFreezePeriodStates = StatusEnum.Success
    })
    builder.addCase(unFreezePeriod.rejected, (state) => {
      state.unFreezePeriodStates = StatusEnum.Failed
    })
  },
})

// export const { setPeriod, setIsPeriodChangeFromProjectList } = periodSlice.actions

// Export the reducer
export default periodSlice.reducer

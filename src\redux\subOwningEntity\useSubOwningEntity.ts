import { useDispatch, useSelector } from 'react-redux'
import {
  getMasterSubOwningEntity,
  addMasterSubOwningEntity,
  updateMasterSubOwningEntity,
  deleteMasterSubOwningEntity,
} from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useSubOwningEntity = () => {
  const dispatch: AppDispatch = useDispatch()

  const getMasterSubOwningEntityStatus = useSelector(
    (state: RootState) => state.subOwningEntity.getMasterSubOwningEntityStatus,
  )
  const subOwningEntities = useSelector((state: RootState) => state.subOwningEntity.subOwningEntities)

  const getMasterSubOwningEntityApi = () => dispatch(getMasterSubOwningEntity())
  const addMasterSubOwningEntityApi = (data: { sub_owning_entity: string }) => dispatch(addMasterSubOwningEntity(data))
  const updateMasterSubOwningEntityApi = (data: { id: number; sub_owning_entity: string }) =>
    dispatch(updateMasterSubOwningEntity(data))
  const deleteMasterSubOwningEntityApi = (data: number) => dispatch(deleteMasterSubOwningEntity(data))

  return {
    getMasterSubOwningEntityStatus,
    subOwningEntities,
    getMasterSubOwningEntityApi,
    addMasterSubOwningEntityApi,
    updateMasterSubOwningEntityApi,
    deleteMasterSubOwningEntityApi,
  }
}
export default useSubOwningEntity

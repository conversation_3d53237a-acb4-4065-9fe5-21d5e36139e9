import React from 'react'
import {
  IViewMasterColumn,
  IViewMasterTableProps,
  TableBodyProps,
  TableCellProps,
  TableHeaderProps,
  TableRowProps,
} from './interface'
import styles from './ViewMasterTable.module.scss'

const TableHeader: React.FC<TableHeaderProps> = ({ columns }) => {
  return (
    <thead className={styles.tableHeadRow}>
      <tr>
        {columns.map((column: IViewMasterColumn, index: number) => (
          <th key={index} className={styles.tableHeader} style={column.headerStyle}>
            {column.label}
          </th>
        ))}
      </tr>
    </thead>
  )
}

const TableBody: React.FC<TableBodyProps> = ({ data, columns }) => {
  return (
    <tbody>
      {data.map((row, rowIndex) => (
        <TableRow key={rowIndex} row={row} rowIndex={rowIndex} columns={columns} />
      ))}
    </tbody>
  )
}

const TableRow: React.FC<TableRowProps> = ({ row, rowIndex, columns }) => {
  return (
    <tr className={`${styles.tableRow}`}>
      {columns.map((column: IViewMasterColumn, colIndex: number) => {
        return (
          <TableCell
            key={`${rowIndex}-${colIndex}`}
            row={row}
            column={column}
            value={row[column.key as keyof typeof row]}
            rowIndex={rowIndex}
          />
        )
      })}
    </tr>
  )
}

const TableCell: React.FC<TableCellProps> = ({ value, row, column, rowIndex }) => {
  return column.renderCell ? (
    column.renderCell(value, row, rowIndex)
  ) : (
    <td className={styles.tableData} style={column.cellStyle}>
      {value}
    </td>
  )
}

const ViewMasterTable: React.FC<IViewMasterTableProps> = ({ columns, data }) => {
  return (
    <div className={styles.tableContainer}>
      <table className={styles.table}>
        <TableHeader columns={columns} />
        <TableBody data={data} columns={columns} />
      </table>
    </div>
  )
}

export default ViewMasterTable

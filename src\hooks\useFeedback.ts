import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createFeedback, getFeedback, updateMasterFeedback, deleteMasterFeedback } from '../services/feedback'
import { FeedbackPayload, IGetMasterFeedbackResponse, IUpdateFeedback } from '../services/feedback/interface'

export const QUERY_FEEDBACK = 'feedback'

export const useGetFeedback = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterFeedbackResponse>({
    queryKey: [QUERY_FEEDBACK],
    queryFn: getFeedback,
    enabled,
  })

  return {
    feedback: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

export const useCreateFeedback = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: FeedbackPayload) => createFeedback(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_FEEDBACK] })
    },
    onError: (error) => {
      console.error('Feedback submission failed:', error)
    },
  })
}

export const useUpdateFeedback = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterFeedback,
    onMutate: async (updatedData: IUpdateFeedback) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_FEEDBACK] })
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_FEEDBACK] })
    },
  })
}

export const useDeleteFeedback = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterFeedback,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_FEEDBACK] })
    },
  })
}

@import '/styles/color.scss';

.container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url('../../public/svg/Login Bkcgd.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center bottom;
  position: relative;
  backdrop-filter: blur(2px);

  .startAdornment {
    width: 20px;
    height: 20px;
    fill: $DARK;
    margin-right: 0px;
  }

  .aldarLogo {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-top: 14px;
    margin-right: 14px;
  }

  .card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 360px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    border: 2px solid #fff;
    box-shadow: 0px 4px 200px 0px rgba(0, 0, 0, 0.1490196078);
    padding: 54px 40px;
    max-width: 352px;
    width: 100%;
    margin: 0;
    position: absolute;
    top: 50%;
    right: 6%;
    transform: translateY(-50%);
    box-shadow: 0px 8px 22px 4px rgba(0, 0, 0, 0.1);

    .contain,
    .fields,
    h1,
    .button {
      position: relative;
      z-index: 1;
    }

    h1 {
      text-align: center;
      font-family: Poppins;
      color: $BLACK;
    }

    .button {
      margin: auto;
      padding: 8px 24px;
      background-color: #0070c0;
      color: white;
      border: none;
      border-radius: 08px;
      min-width: 118px;
      display: block;
      font-family: 'Poppins';
      font-size: 18px;
    }
  }

  .header {
    font-family: Poppins;
    letter-spacing: -0.02em;
    display: flex;
    justify-content: center;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    text-align: left;
    color: $BLACK;
  }

  .fields {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .textField {
      width: 400px !important;
    }

    .passwords {
      display: flex;
      flex-direction: column;
      gap: 7px;

      .forgotPassword {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
  }
}

.requestAccessSection {
  width: 100%;
  margin-top: 2rem;

  .requestAccessDivider {
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin-bottom: 1.5rem;
  }

  .requestAccessContent {
    text-align: center;

    .requestAccessTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .requestAccessDescription {
      font-size: 14px;
      color: #666;
      margin: 0 0 1rem 0;
      line-height: 1.4;
    }

    .requestAccessLinks {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .requestAccessLink {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;

        svg {
          font-size: 18px;
        }

        &.portalLink {
          background-color: rgba(0, 112, 192, 0.1);
          color: #0070c0;
          border: 1px solid rgba(0, 112, 192, 0.2);

          &:hover {
            background-color: rgba(0, 112, 192, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 112, 192, 0.2);
          }

          &:focus {
            outline: 2px solid #0070c0;
            outline-offset: 2px;
          }
        }

        &.emailLink {
          background-color: rgba(30, 142, 62, 0.1); // translucent green
          color: #1e8e3e;
          border: 1px solid rgba(30, 142, 62, 0.2);
          font-weight: 500;

          &:hover {
            background-color: rgba(30, 142, 62, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(30, 142, 62, 0.2);
          }

          &:focus {
            outline: 2px solid #1e8e3e;
            outline-offset: 2px;
          }
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.requestAccessSectionpopup {
  width: 100%;

   .requestAccessContent {
    text-align: center;

    .requestAccessTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .requestAccessDescription {
      font-size: 14px;
      color: #666;
      margin: 0 0 1rem 0;
      line-height: 1.4;
    }

    .requestAccessLinks {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .requestAccessLink {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;

        svg {
          font-size: 18px;
        }

        &.portalLink {
          color: #0070c0;

          &:hover {
            background-color: rgba(0, 112, 192, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 112, 192, 0.2);
          }

          &:focus {
            outline-offset: 2px;
          }
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.popupOverlay {
  position: fixed;
  top: 20px;
  left: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.popupContainer {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  position: relative;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.popupHeader {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  padding: 24px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popupIcon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 16px 20px;
  backdrop-filter: blur(10px);
}

.lockIconLarge {
  width: 32px !important;
  height: 32px !important;
  color: white !important;
  font-size: 32px !important;
}

.closeBtn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }
}

.popupContent {
  padding: 32px 24px 24px;
  text-align: center;
}

.popupTitle {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 16px 0;
  font-family: 'Poppins', sans-serif;
}

.popupMessage {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 32px 0;
  font-family: 'Poppins', sans-serif;
}

.popupActions {
  display: flex;
  gap: 12px;
  flex-direction: column;
}

.contactAdminBtn {
  background: #0070c0;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;

  &:hover {
    background: #005a9a;
    transform: translateY(-1px);
  }

  &:focus {
    outline: 2px solid #0070c0;
    outline-offset: 2px;
  }

  &:active {
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    right: 20px;
    left: 20px;
    max-width: calc(100% - 40px);
    padding: 40px 30px;
  }

  .popupContainer {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .popupContent {
    padding: 24px 20px 20px;
  }

  .popupActions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .card {
    height: auto;
    min-height: 320px;
    padding: 30px 20px;
  }

  .popupTitle {
    font-size: 20px;
  }

  .popupMessage {
    font-size: 14px;
  }

  .contactAdminBtn,
  .closePopupBtn {
    padding: 10px 20px;
    font-size: 13px;
  }
}
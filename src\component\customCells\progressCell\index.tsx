import React, { useState, ChangeEvent, FocusEvent } from 'react'
import LinearProgress, { linearProgressClasses, LinearProgressProps } from '@mui/material/LinearProgress'
import { styled } from '@mui/material/styles'
import { CellContext } from '@tanstack/react-table'
import styles from './ProgressCell.module.scss'
import Loader from '../../shared/loader'
import ConfirmValueChangeModal from '../../shared/tanStackTable/editableCell/confirmValueChangeModal'
import TextInputField from '../../shared/textInputField' // Assuming you have a TextInputField component
import { WHITE } from '@/src/constant/color'
import { isLooseValueChanged } from '@/src/utils/stringUtils'

interface CustomLinearProgressProps extends LinearProgressProps {
  progressColor: string
}

const getColor = (revPlanPercentage: number, actualPlanPercentage: number, isPlanCell: boolean) => {
  const diff = Number(revPlanPercentage) - Number(actualPlanPercentage)
  if (isPlanCell) return '#7A7A7A'
  if (Number(revPlanPercentage) <= Number(actualPlanPercentage)) {
    return '#5EC078' // green
  } else if (diff > 10) {
    return '#ea3323' // red
  } else {
    return '#B29343' // yellow
  }
}

const BorderLinearProgress = styled(LinearProgress)<CustomLinearProgressProps>(({ progressColor }) => ({
  height: 8,
  borderRadius: 6,
  backgroundColor: WHITE,
  outline: `2px solid ${progressColor}`,
  border: '2px solid transparent',
  [`& .${linearProgressClasses.bar}`]: { borderRadius: 3, backgroundColor: progressColor },
}))

const ProgressCell: React.FC<any> = ({ columnId, isPlanCell, row, val, column, onCellUpdate, disabled = false }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [isLoader, setIsLoader] = useState(false)
  const [value, setValue] = useState<number | string>(
    // isPlanCell ? row.original.revPlanPercentage : row.original.actualPlanPercentage,
    val,
  )

  const actualPlanPercentage = row.original.actualPlanPercentage
  const revPlanPercentage = row.original.revPlanPercentage
  const progressColor = getColor(revPlanPercentage, actualPlanPercentage, isPlanCell ?? false)

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value
    if (/^\d*\.?\d*$/.test(newValue) && (newValue === '' || (Number(newValue) >= 0 && Number(newValue) <= 100))) {
      setValue(newValue)
    }
  }

  const handleBlur = () => {
    setIsLoader(true)
    const newValue = value
    if (onCellUpdate) {
      const res = onCellUpdate(newValue)
      if (res) {
        setIsLoader(false)
      }
    }
    setIsEditing(false)
  }

  // Common handler for blur and Enter key
  const handleEditComplete = () => {
    if (column.columnDef.isSaveConfirmationRequired) {
      const defaultValue = row?.original?.[columnId]?.toString() || ''
      if (isLooseValueChanged(value, defaultValue)) {
        setIsConfirmation(true)
      } else {
        setIsEditing(false)
        setValue(defaultValue)
      }
    } else {
      handleBlur()
    }
  }

  // Reset state and value when modal is closed
  const handleModalClose = () => {
    setIsConfirmation(false)
    const defaultValue = row?.original?.[columnId]?.toString() || ''
    setValue(defaultValue)
    setIsEditing(false)
  }

  return (
    <div
      className={`${styles.progressCell} ${column.columnDef.isCustomCellEditable && !disabled && styles.highlightEditableCell}`}
    >
      <div className={styles.progressBar}>
        <BorderLinearProgress variant="determinate" value={Number(value)} progressColor={progressColor as string} />
      </div>
      {isLoader ? (
        <Loader smallLoader={true} width={'56px'} />
      ) : isEditing && column.columnDef.isCustomCellEditable && !disabled ? (
        <TextInputField
          disabled={disabled}
          value={value}
          className={`${styles.textField} ${column.columnDef.isCustomCellEditable && !disabled && styles.highlightTextEditableCell}`}
          onChange={handleChange}
          onBlur={handleEditComplete}
          onKeyDown={(event) => {
            if (event.key === 'Enter') {
              handleEditComplete()
            }
          }}
          variant="outlined"
        />
      ) : (
        <div className={styles.text} onClick={() => setIsEditing(true)}>
          {value?.toString() ? value : '0.00'}
        </div>
      )}
      <ConfirmValueChangeModal
        open={column.columnDef.isSaveConfirmationRequired && isConfirmation}
        header={column.columnDef.header}
        value={value}
        onClose={handleModalClose}
        handleConfirm={() => handleBlur && handleBlur()}
      />
    </div>
  )
}

export default ProgressCell

import { IDirector } from '../director/interface'
import { IProjectClassification } from '../projectClassification/interface'
import { ILookupProjectToPhase } from '@/src/redux/status/interface'

export interface IProjectsManagersAvatar {
  name: string
  person_name: string
  url: string
}

export interface IMitigationRecoveryPlans {
  id: number
  mitigation_recovery_plan: string
}

export interface INonRecoverableDelayJustifications {
  id: number
  non_recoverable_delay_justification: string
}

export interface INextStepsToAdvanceProgress {
  id: number
  next_steps_to_advance_progress: string
}

export interface IExecutiveDirectors {
  id: number
  executive_director: string
}

export interface IDesignProjectManagers {
  id: number
  design_manager: string
}

export interface IDeliveryProjectManagers {
  id: number
  delivery_project_manager: string
}

export interface IDesignExecutiveDirectors {
  id: number
  design_executive_director: string
}

export interface IProcurementManagers {
  id: number
  procurement_manager: string
}
export interface IPortfolioManagers {
  id: number
  portfolio_manager: string
  avatar: string
}
export interface IControlManagers {
  id: number
  control_manager: string
  avatar: string
}

export interface ISubLocations {
  id: number
  sub_location: string
}

export interface ILocations {
  id: number
  location: string
}

export interface IReasonForDelay {
  id: number
  reason_for_delay: string
  reason_for_delay_code: any
}
export interface IImages {
  id: number
  file_path: string
  phase: string | null
  field_name?: string | null
  lookup_project_to_phase_id: number
  LookupProjectToPhase: ILookupProjectToPhase
}

export interface IProjects {
  id?: any
  period?: string
  is_external_lookup_for_health_and_safety?: boolean
  project_id?: string
  is_refurbishment_project?: boolean
  scope_change?: boolean
  is_musanada_project?: boolean
  cpds_project_id?: any
  design_executive_director?: string
  project_name?: string
  owning_entity?: string
  entity_category?: string
  sub_owning_entity?: string
  decree_date?: string
  project_status?: string
  static_images_files?: IImages[]
  progress_videos?: IImages[]
  dynamic_images_files?: IImages[]
  plot_plan_files?: IImages[]
  additional_documents_files?: IImages[]
  location?: string
  SubLocations?: ISubLocations[]
  Locations?: ILocations[]
  ExecutiveDirectors?: IExecutiveDirectors[]
  DesignProjectManagers?: IDesignProjectManagers[]
  DeliveryProjectManagers?: IDeliveryProjectManagers[]
  DesignExecutiveDirectors?: IDesignExecutiveDirectors[]
  dof_number?: string
  ldc?: string
  project_type?: string
  health_safety_last_updated?: Date
  project_management_last_updated?: string
  project_type2?: string
  decree_end_date?: string
  commencement_date?: string
  project_type3?: string
  MasterProjectClassification?: IProjectClassification | null
  overall_forecasted_finish_date?: string
  overall_planned_finish_date?: string
  planned_finish_percentage?: number | null
  actual_finish_percentage?: number | null
  incidents?: number | null
  budget_uplift_submitted?: boolean
  budget_uplift_submission_date?: string
  budget_uplift_last_submission_date?: string
  bond_expiry_date?: string
  budget_uplift_submission_notes?: string
  uplifted_budget?: string
  eot_submission_date?: string
  submitted_eot_date?: string
  tip_submission_date?: string
  tip_approval_date?: string
  eot_approval_date?: string
  eot_last_submission_date?: string
  scope_change_submission_date?: string
  scope_change_approval_date?: string
  scope_change_last_submission_date?: string
  tip_last_submission_date?: string
  potential_budget_uplift_submitted?: boolean
  budget_uplift_approval_date?: string
  planned_manpower?: number
  actual_manpower?: number
  erp_project_name?: string
  lti?: number
  man_hours?: number
  latitude?: number
  longitude?: number
  ProcurementManagers?: IProcurementManagers[]
  last_updated?: string
  is_executive_project?: boolean
  is_subject_to_rebaseline?: boolean
  reason_rebaseline?: string
  delay_reason?: string
  PrimaryReasonsForDelay?: IReasonForDelay[]
  ReasonsForDelay?: IReasonForDelay[]
  mitigation_recovery_plan?: string
  MitigationRecoveryPlans?: IMitigationRecoveryPlans[]
  NextStepsToAdvanceProgress?: INextStepsToAdvanceProgress[]
  non_recoverable_delay_justification?: string
  NonRecoverableDelayJustifications?: INonRecoverableDelayJustifications[]
  delay_code?: string
  MasterPortfolioManager?: IPortfolioManagers | null
  MasterControlManager?: IControlManagers | null
  project_sorting_order?: number
  updated_by?: string
  project_management_updated_by?: string
  health_safety_updated_by?: string
  delivery_project_manager?: string
  MasterDirector?: IDirector | null
  decree_files?: IImages[]
  portfolio_manager_avatar?: IProjectsManagersAvatar[]
  design_project_owner_avatar?: IProjectsManagersAvatar[]
  design_project_manager_avatar?: IProjectsManagersAvatar[]
  controls_manager_avatar?: IProjectsManagersAvatar[]
  procurement_manager_avatar?: IProjectsManagersAvatar[]
  director_avatar?: IProjectsManagersAvatar[]
  delivery_project_manager_avatar?: IProjectsManagersAvatar[]
  executive_director_avatar?: IProjectsManagersAvatar[]
  tip_submitted?: boolean
  eot_submitted?: boolean
  executive_sorting_order?: number | null
  isExecutiveProject?: string | boolean
}

export interface IGetProjectsResponse {
  data: IProjects[]
  message: string
  success: true
}

export interface ISortingOrderPayload {
  period: string
  projects: ISortListOfProject[]
}
export interface ISortListOfProject {
  project_name: string
  project_sorting_order: number
}

export interface IGetMasterOneProjectResponse {
  data: IProjects
  message: string
  success: true
}

export interface IProjectUpdate {
  project_name: string
  [key: string]: any
}

export interface IMultiProjectUpdatePayload {
  period: string
  projects: IProjectUpdate[]
}

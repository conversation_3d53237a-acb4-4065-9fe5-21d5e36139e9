import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  IAddPhasePayload,
  IDeletePhasePayload,
  IGetMasterRatingResponse,
  IRenamePhasePayload,
  IStatus,
  IStatusState,
} from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IStatusState = {
  getStatusProcess: StatusEnum.Idle,
  renamePhaseProcess: StatusEnum.Idle,
  deletePhaseProcess: StatusEnum.Idle,
  getOneStatusProcess: StatusEnum.Idle,
  addStatusProcess: StatusEnum.Idle,
  addPhaseProcess: StatusEnum.Idle,
  deleteStatusProcess: StatusEnum.Idle,
  updateStatusProcess: StatusEnum.Idle,
  updateTableStatusProcess: StatusEnum.Idle,
  statusSortingStatus: StatusEnum.Idle,
  statuses: [],
  status: null,
  progressTableVisibilities: {},
  progressFilterValue: [],
}

export const getStatus = createAsyncThunk(
  '/get-project-status',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(
        `/project-status?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const getOneStatus = createAsyncThunk(
  '/get-one-project-status',
  async (data: { id: string; period: string; project_name: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = (await ApiGetNoAuth(
        `/project-status?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )) as IGetMasterRatingResponse

      // const response = await ApiGetNoAuth(`/project-status/${data.id}?period=${period}`)
      if (response) {
        let FilteredResponse = response?.data?.find((item: IStatus) => item.id == Number(data?.id)) as IStatus | null

        return thunkAPI.fulfillWithValue({ ...response, data: FilteredResponse })
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addStatus = createAsyncThunk('/add-project-status', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-status`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const statusSorting = createAsyncThunk('/project-status/bulk-sort', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-status/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateStatus = createAsyncThunk(
  '/update-project-status',
  async (data: { id: number; data: any }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-status/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateTableStatus = createAsyncThunk(
  '/update-structure',
  async (data: { id: number; data: any }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-status/update-structure/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteStatus = createAsyncThunk('/delete-project-status', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/project-status/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const renamePhaseName = createAsyncThunk(
  '/project-status/rename-phase',
  async (data: IRenamePhasePayload, thunkAPI) => {
    const { id, ...rest } = data
    try {
      const response = await ApiPutNoAuth(`/master-project-to-phase/${id}`, rest)
      //  const { id, ...rest } = data
      // const response = await ApiPostNoAuth(`/project-status/rename-phase/${id}`, rest)

      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addPhaseName = createAsyncThunk('/project-status/add-phase', async (data: IAddPhasePayload, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/master-project-to-phase`, data)
    //  const response = await ApiPostNoAuth(`/project-status/delete-phase/${data?.id}`, data)

    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deletePhase = createAsyncThunk('/project-status/delete-phase', async (id: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/master-project-to-phase/${id}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const statusSlice = createSlice({
  name: 'status',
  initialState,
  reducers: {
    setProgressTableColumnVisibilities(state, action) {
      state.progressTableVisibilities = action.payload
    },
    setProgressFilterValue(state, action) {
      state.progressFilterValue = action.payload
    },
    //set selected rows
  },
  extraReducers: (builder) => {
    //getStatus
    builder.addCase(getStatus.pending, (state) => {
      state.getStatusProcess = StatusEnum.Pending
    })
    builder.addCase(getStatus.fulfilled, (state, action) => {
      state.getStatusProcess = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterRatingResponse
      state.statuses = actionPayload.data
    })
    builder.addCase(getStatus.rejected, (state) => {
      state.getStatusProcess = StatusEnum.Failed
    })
    //getOneStatus
    builder.addCase(getOneStatus.pending, (state) => {
      state.getOneStatusProcess = StatusEnum.Pending
    })
    builder.addCase(getOneStatus.fulfilled, (state, action) => {
      state.getOneStatusProcess = StatusEnum.Success
      const actionPayload = action.payload as { data: IStatus }
      state.status = actionPayload.data
    })
    builder.addCase(getOneStatus.rejected, (state) => {
      state.getOneStatusProcess = StatusEnum.Failed
    })
    //addStatusProcess
    builder.addCase(addStatus.pending, (state) => {
      state.addStatusProcess = StatusEnum.Pending
    })
    builder.addCase(addStatus.fulfilled, (state, action) => {
      state.addStatusProcess = StatusEnum.Success
    })
    builder.addCase(addStatus.rejected, (state) => {
      state.addStatusProcess = StatusEnum.Failed
    })
    //statusSorting
    builder.addCase(statusSorting.pending, (state) => {
      state.statusSortingStatus = StatusEnum.Pending
    })
    builder.addCase(statusSorting.fulfilled, (state, action) => {
      state.statusSortingStatus = StatusEnum.Success
    })
    builder.addCase(statusSorting.rejected, (state) => {
      state.statusSortingStatus = StatusEnum.Failed
    })
    //updateStatusProcess
    builder.addCase(updateStatus.pending, (state) => {
      state.updateStatusProcess = StatusEnum.Pending
    })
    builder.addCase(updateStatus.fulfilled, (state, action) => {
      state.updateStatusProcess = StatusEnum.Success
    })
    builder.addCase(updateStatus.rejected, (state) => {
      state.updateStatusProcess = StatusEnum.Failed
    })

    //updateTableStatus
    builder.addCase(updateTableStatus.pending, (state) => {
      state.updateTableStatusProcess = StatusEnum.Pending
    })
    builder.addCase(updateTableStatus.fulfilled, (state, action) => {
      state.updateTableStatusProcess = StatusEnum.Success
    })
    builder.addCase(updateTableStatus.rejected, (state) => {
      state.updateTableStatusProcess = StatusEnum.Failed
    })

    //deleteStatusProcess
    builder.addCase(deleteStatus.pending, (state) => {
      state.deleteStatusProcess = StatusEnum.Pending
    })
    builder.addCase(deleteStatus.fulfilled, (state, action) => {
      state.deleteStatusProcess = StatusEnum.Success
    })
    builder.addCase(deleteStatus.rejected, (state) => {
      state.deleteStatusProcess = StatusEnum.Failed
    })
    //addPhaseProcess
    builder.addCase(addPhaseName.pending, (state) => {
      state.addPhaseProcess = StatusEnum.Pending
    })
    builder.addCase(addPhaseName.fulfilled, (state, action) => {
      state.addPhaseProcess = StatusEnum.Success
    })
    builder.addCase(addPhaseName.rejected, (state) => {
      state.addPhaseProcess = StatusEnum.Failed
    })
    //renamePhaseProcess
    builder.addCase(renamePhaseName.pending, (state) => {
      state.renamePhaseProcess = StatusEnum.Pending
    })
    builder.addCase(renamePhaseName.fulfilled, (state, action) => {
      state.renamePhaseProcess = StatusEnum.Success
    })
    builder.addCase(renamePhaseName.rejected, (state) => {
      state.renamePhaseProcess = StatusEnum.Failed
    })
    //deletePhaseProcess
    builder.addCase(deletePhase.pending, (state) => {
      state.deletePhaseProcess = StatusEnum.Pending
    })
    builder.addCase(deletePhase.fulfilled, (state, action) => {
      state.deletePhaseProcess = StatusEnum.Success
    })
    builder.addCase(deletePhase.rejected, (state) => {
      state.deletePhaseProcess = StatusEnum.Failed
    })
  },
})

export const { setProgressTableColumnVisibilities, setProgressFilterValue } = statusSlice.actions

export default statusSlice.reducer

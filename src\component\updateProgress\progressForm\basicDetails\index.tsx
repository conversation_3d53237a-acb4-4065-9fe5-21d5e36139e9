import React, { useEffect, useMemo, useState } from 'react'
import { Box, InputAdornment, Stack, SxProps, Theme, Tooltip } from '@mui/material'
import { format, parseISO } from 'date-fns'
import { FormikValues } from 'formik'
import { toast } from 'sonner'
import styles from './BasicDetails.module.scss'
import Checkbox from '../../checkBox'
import { calculateDateDifference } from '../../helper'
import ComboBox from '@/src/component/shared/combobox'
import DatePicker from '@/src/component/shared/dataPicker'
import NumberInputField from '@/src/component/shared/numberInputField'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import PercentageIcon from '@/src/component/svgImages/percentageIcon'
import { DARK_200 } from '@/src/constant/color'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'
import { useGetConsultant } from '@/src/hooks/useConsultant'
import { useGetContractor } from '@/src/hooks/useContractor'
import { useGetPmcConsultant } from '@/src/hooks/usePmcConsultant'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useStatus from '@/src/redux/status/useStatus'
import { getValue, populateDropdownOptions, prepareDropdownOptions } from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate } from '@/src/utils/dateUtils'
import {
  hasDesignTeamRole,
  hasUpdateBaselinePlanFinishDateAndPlanDurationPermission,
  isUser,
} from '@/src/utils/userUtils'

interface IBasicDetailProps {
  formik: FormikValues
  edit: boolean
}

const BasicDetails: React.FC<IBasicDetailProps> = ({ formik, edit }) => {
  //react-hook
  const [isLdcDesignInitiation, setIsLdcDesignInitiation] = useState(false)
  const [isContractConstructionDlp, setIsContractConstructionDlp] = useState(false)
  const { consultants } = useGetConsultant()
  const { contractors } = useGetContractor()
  const { pmcConsultants } = useGetPmcConsultant()
  const { currentUser } = useAuthorization()
  const { statuses, status } = useStatus()

  const findIdsWithMinSortingOrder = () => {
    const data: any[] = statuses
      ?.filter((item: any) => item?.stage_status === 'Design' && item?.phase === status?.phase)
      ?.map((res: any) => {
        return {
          id: res?.id,
          phase: res?.phase,
          stage_status: res?.stage_status,
          sub_stage: res?.sub_stage,
          sorting_order: res?.project_status_sorting_order,
        }
      })
    const minOrder = Math.min(...data.map((item) => +item?.sorting_order))
    return data.filter((item) => +item.sorting_order === minOrder).map((item) => item.id)
  }

  // NOTO: Find first created design stage of selected phase
  const isFirstDesignStageOfPhase = useMemo(() => {
    const findFirstDesignStage = findIdsWithMinSortingOrder()
    return status?.id === findFirstDesignStage[0] && status?.stage_status === 'Design' && edit
  }, [edit, status, formik])

  const consultantsOptions = useMemo(() => {
    return prepareDropdownOptions(consultants, 'consultant')
  }, [consultants])

  const contractorsOption = useMemo(() => {
    return prepareDropdownOptions(contractors, 'contractor')
  }, [contractors])

  const pmcConsultantsOption = useMemo(() => {
    return prepareDropdownOptions(pmcConsultants, 'pmc_consultant')
  }, [pmcConsultants])

  useEffect(() => {
    setIsLdcDesignInitiation(
      formik.values.stage_status === LDC_PROCUREMENT ||
        formik.values.stage_status === DESIGN ||
        formik.values.stage_status === INITIATION,
    )

    setIsContractConstructionDlp(
      formik.values.stage_status === CONTRACTOR_PROCUREMENT ||
        formik.values.stage_status === CONSTRUCTION ||
        formik.values.stage_status === DLP_PROJECT_CLOSEOUT,
    )
  }, [formik.values.stage_status])

  const REGEX = useMemo(() => /^[0-9]*(\.[0-9]*)?$/, [])

  //* Only for LDC_PROCUREMENT,DESIGN,INITIATION
  const hasNotUpdateBaseLineAndPlanDurationPermission =
    (!edit && !hasDesignTeamRole(currentUser)) ||
    isUser(currentUser.user_type) ||
    !hasUpdateBaselinePlanFinishDateAndPlanDurationPermission(currentUser)

  //* Only for CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT
  const hasNotBS_PD_PermissionForContractConstructionDlp =
    !hasUpdateBaselinePlanFinishDateAndPlanDurationPermission(currentUser) ||
    (formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION
      ? !edit || isUser(currentUser.user_type)
      : (!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type))

  const baseLineCheckBoxSx: SxProps<Theme> = {
    display: 'flex',
    backgroundColor: '#f2f2f2',
    borderRadius: '4px',
    alignItems: 'center',
    gap: '0px',
    paddingLeft: '12px',
    '& .baseLineCheckbox': {
      backgroundColor: '#ffffff',
      // border: '1px solid #2865dc',
      borderRadius: '3px',
      padding: 0,
    },
  }

  const baseLineCheckHighlightedBoxSx: SxProps<Theme> = {
    backgroundColor: '#f0f8ff',
    border: '1px solid rgb(40, 101, 220)',
    '& .dataPickerInput': {
      'div > div': {
        backgroundColor: '#f0f8ff !important',
      },
    },
  }

  const handleBaseLineCheckbox = (_event: any, checked: boolean) => {
    formik?.setFieldValue('is_baseLinePlanFinish_edit', checked)
    if (checked) {
      formik?.setFieldValue('plan_duration', formik?.initialValues?.plan_duration)
    } else {
      formik?.setFieldValue('baseline_plan_finish', formik?.initialValues?.baseline_plan_finish)
    }
  }

  const isBaseLinePlanFinishEdit = formik?.values?.is_baseLinePlanFinish_edit

  return (
    <div>
      <div className={styles.fieldContainer}>
        {/* Baseline Plan Finish */}
        {isLdcDesignInitiation && (
          <>
            <div>
              <TypographyField style={{ color: DARK_200 }} variant="caption" text={'Baseline Plan Finish'} />
              <Box
                sx={
                  !hasNotUpdateBaseLineAndPlanDurationPermission
                    ? {
                        ...baseLineCheckBoxSx,
                        ...(isBaseLinePlanFinishEdit && {
                          ...baseLineCheckHighlightedBoxSx,
                        }),
                      }
                    : {}
                }
              >
                {!hasNotUpdateBaseLineAndPlanDurationPermission && (
                  <Checkbox size="small" className={'baseLineCheckbox'} onChange={handleBaseLineCheckbox} />
                )}

                <DatePicker
                  name="baseline_plan_finish"
                  // labelText="Baseline Plan Finish"
                  placeholder="DD-MM-YY"
                  disabled={!isBaseLinePlanFinishEdit || hasNotUpdateBaseLineAndPlanDurationPermission}
                  // className={`${
                  //   !isBaseLinePlanFinishEdit || hasNotUpdateBaseLineAndPlanDurationPermission
                  //     ? ''
                  //     : styles.highlightField
                  // }  ${styles.dataPickerInput}`}
                  className={`dataPickerInput ${styles.dataPickerInput}`}
                  value={convertDDMMYYYYToLongDate(formik.values.baseline_plan_finish)}
                  onChange={(value) => {
                    if (value == 'Invalid Date') {
                      formik.setFieldValue('baseline_plan_finish', null)
                      return null
                    }
                    const selectedDate = value && value?.toISOString()
                    const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                    if (formik.values.forecast_finish) {
                      const variance = calculateDateDifference(date, formik.values.forecast_finish)
                      formik.setValues({ ...formik.values, baseline_plan_finish: date, variance_in_days: variance })
                    } else {
                      formik.setFieldValue('baseline_plan_finish', date)
                    }
                  }}
                />
              </Box>
            </div>
            <NumberInputField
              name="plan_duration"
              disabled={isBaseLinePlanFinishEdit || hasNotUpdateBaseLineAndPlanDurationPermission}
              labelText={'Plan Duration'}
              placeholder="1,2,3..."
              className={`${isBaseLinePlanFinishEdit || hasNotUpdateBaseLineAndPlanDurationPermission ? '' : styles.highlightField} ${styles.inputFields}`}
              value={formik?.values?.plan_duration?.toString() ? Number(formik?.values?.plan_duration) : null}
              onChange={(value) => formik.setFieldValue('plan_duration', value?.toString() ? Number(value) : null)}
            />
          </>
        )}
        {isContractConstructionDlp && (
          <>
            <div>
              <TypographyField style={{ color: DARK_200 }} variant="caption" text={'Baseline Plan Finish'} />
              <Box
                sx={
                  !hasNotUpdateBaseLineAndPlanDurationPermission
                    ? {
                        ...baseLineCheckBoxSx,
                        ...(isBaseLinePlanFinishEdit && {
                          ...baseLineCheckHighlightedBoxSx,
                        }),
                      }
                    : {}
                }
              >
                {!hasNotUpdateBaseLineAndPlanDurationPermission && (
                  <Checkbox size="small" className={'baseLineCheckbox'} onChange={handleBaseLineCheckbox} />
                )}
                <DatePicker
                  name="plan_end_date"
                  // labelText="Baseline Plan Finish"
                  placeholder="DD/MM/YY"
                  disabled={!isBaseLinePlanFinishEdit || hasNotBS_PD_PermissionForContractConstructionDlp}
                  // className={`${
                  //   !isBaseLinePlanFinishEdit || hasNotBS_PD_PermissionForContractConstructionDlp
                  //     ? ''
                  //     : styles.highlightField
                  // }  ${styles.dataPickerInput}`}
                  className={`dataPickerInput ${styles.dataPickerInput}`}
                  value={convertDDMMYYYYToLongDate(formik.values.plan_end_date)}
                  onChange={(value) => {
                    if (value == 'Invalid Date') {
                      formik.setFieldValue('plan_end_date', null)
                      return null
                    }
                    const selectedDate = value && value?.toISOString()
                    const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                    if (formik.values.forecasted_end_date) {
                      const variance =
                        formik.values.stage_status === CONSTRUCTION
                          ? formik.values.variance_in_days
                          : calculateDateDifference(date, formik.values.forecasted_end_date)
                      formik.setValues({ ...formik.values, plan_end_date: date, variance_in_days: variance })
                    } else {
                      formik.setFieldValue('plan_end_date', date)
                    }
                  }}
                />
              </Box>
            </div>
            <NumberInputField
              name="plan_duration"
              disabled={isBaseLinePlanFinishEdit || hasNotBS_PD_PermissionForContractConstructionDlp}
              labelText={'Plan Duration'}
              placeholder="1,2,3..."
              className={`${isBaseLinePlanFinishEdit || hasNotBS_PD_PermissionForContractConstructionDlp ? '' : styles.highlightField} ${styles.inputFields}`}
              value={formik?.values?.plan_duration?.toString() ? Number(formik?.values?.plan_duration) : null}
              onChange={(value) => formik.setFieldValue('plan_duration', value?.toString() ? Number(value) : null)}
            />
          </>
        )}
        {/* Forecast Finish */}
        {isLdcDesignInitiation && (
          <div>
            <DatePicker
              name="forecast_finish"
              labelText="Forecast/Actual Finish"
              placeholder="DD/MM/YY"
              disabled={!edit}
              className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.forecast_finish)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('forecast_finish', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.baseline_plan_finish) {
                  const variance = calculateDateDifference(formik.values.baseline_plan_finish, date)
                  formik.setValues({ ...formik.values, forecast_finish: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('forecast_finish', date)
                }
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <DatePicker
              name="forecasted_end_date"
              labelText="Forecast/Actual Finish"
              placeholder="DD/MM/YY"
              disabled={!edit}
              value={convertDDMMYYYYToLongDate(formik.values.forecasted_end_date)}
              className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('forecasted_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                const dateToCalculate =
                  formik.values.stage_status === CONSTRUCTION
                    ? formik.values.eot_to_contractor
                      ? formik.values.eot_to_contractor
                      : formik.values.contract_end_date
                    : formik.values.plan_end_date
                if (dateToCalculate) {
                  const variance = calculateDateDifference(dateToCalculate, date)
                  formik.setValues({ ...formik.values, forecasted_end_date: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('forecasted_end_date', date)
                }
              }}
            />
          </div>
        )}
        {/* Plan Progress */}
        {isLdcDesignInitiation && (
          // <Tooltip title={'Todays date - baseline plan finish date'} arrow>
          <Tooltip title={'Plan Progress %'} arrow>
            <div>
              {/* BACKEND:AUTO_CALCULATED */}
              <TextInputField
                name="rev_plan_percentage"
                // placeholder="1,2,3,..."
                variant={'outlined'}
                classes={{ root: styles.planPercentage }}
                // disabled={!edit || isUser(currentUser.user_type)}
                disabled={true}
                // className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
                className={`${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
                labelText={'Plan Progress %'}
                onChange={(event) => {
                  const inputValue = event.target.value

                  if (Number(inputValue) <= 100) {
                    const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('rev_plan_percentage', parseFloat(inputValue) || 0)
                    }
                  }
                }}
                value={formik.values?.rev_plan_percentage}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <PercentageIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          </Tooltip>
        )}
        {isContractConstructionDlp && (
          <>
            <Tooltip
              title={
                // formik.values.stage_status === CONTRACTOR_PROCUREMENT ? 'Todays date - baseline plan finish date' : ''
                'Plan Progress %'
              }
              arrow
            >
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="revised_plan_percentage"
                  // disabled={edit && formik.values.stage_status === CONSTRUCTION ? false : true}
                  disabled={
                    formik.values.stage_status === CONTRACTOR_PROCUREMENT
                      ? true
                      : !edit || isUser(currentUser.user_type)
                  }
                  placeholder={formik.values.stage_status !== CONTRACTOR_PROCUREMENT ? '1,2,3,...' : ''}
                  variant={'outlined'}
                  labelText={'Plan Progress %'}
                  onChange={(event) => {
                    const inputValue = event.target.value

                    if (Number(inputValue) <= 100) {
                      const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                      if (isValidInput || inputValue === '') {
                        return formik.setFieldValue('revised_plan_percentage', parseFloat(inputValue) || 0)
                      }
                    }
                  }}
                  classes={{ root: styles.planPercentage }}
                  // className={`${edit && formik.values.stage_status === CONSTRUCTION ? styles.highlightField : ''} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
                  className={`${!edit || isUser(currentUser.user_type) || formik.values.stage_status === CONTRACTOR_PROCUREMENT ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
                  value={formik.values?.revised_plan_percentage}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start" className={styles.endAdornment}>
                        <PercentageIcon className={styles.endAdornmentIcon} />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
            </Tooltip>
          </>
        )}
        {/* Actual Progress */}
        {isLdcDesignInitiation && (
          <div>
            <TextInputField
              name="actual_percentage"
              placeholder="1,2,3,..."
              variant={'outlined'}
              disabled={!edit}
              classes={{ root: styles.planPercentage }}
              className={`${!edit ? '' : styles.highlightField}  ${styles.inputFields}`}
              labelText={'Actual Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value
                const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('actual_percentage', parseFloat(inputValue) || 0)
                }
              }}
              value={formik.values?.actual_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <TextInputField
              name="actual_plan_percentage"
              placeholder="1,2,3,..."
              classes={{ root: styles.planPercentage }}
              disabled={!edit}
              className={`${!edit ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              variant={'outlined'}
              labelText={'Actual Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value
                const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('actual_plan_percentage', parseFloat(inputValue) || 0)
                }
              }}
              value={formik.values?.actual_plan_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {/* Revised Plan Finish */}
        {/* {isLdcDesignInitiation && (
          <div>
            <DatePicker
              name="revised_baseline_finish"
              labelText="Revised Plan Finish"
              placeholder="DD/MM/YY"
              disabled={(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type)}
              className={`${(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type) ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.revised_baseline_finish)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('revised_baseline_finish', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('revised_baseline_finish', date)
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <DatePicker
              name="revised_plan_end_date"
              labelText="Revised Plan Finish"
              placeholder="DD/MM/YY"
              // disabled={!edit && !hasDesignTeamRole(currentUser)}
              disabled={
                formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION
                  ? !edit || isUser(currentUser.user_type)
                  : !edit && !hasDesignTeamRole(currentUser)
              }
              className={`${
                (!edit || isUser(currentUser.user_type)) &&
                (formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION)
                  ? ''
                  : !edit && !hasDesignTeamRole(currentUser)
                    ? ''
                    : styles.highlightField
              }  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.revised_plan_end_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('revised_plan_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('revised_plan_end_date', date)
              }}
            />
          </div>
        )} */}

        {/* TODO: PROCUREMENT_START_DATE */}
        {formik.values.stage_status === CONTRACTOR_PROCUREMENT ? (
          <div>
            <DatePicker
              name="procurement_start_date"
              labelText="Procurement Start Date"
              className={`${!edit ? '' : styles.highlightField} ${styles.dataPickerInput}`}
              disabled={!edit}
              placeholder="DD/MM/YY"
              value={
                formik.values.procurement_start_date
                  ? convertDDMMYYYYToLongDate(formik.values.procurement_start_date)
                  : ''
              }
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('procurement_start_date', null)
                  return
                }
                const selectedDate = value?.toISOString()
                const formattedDate = format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('procurement_start_date', formattedDate)
              }}
            />
          </div>
        ) : null}

        {/* uncomment-it  */}
        {/* Procurement Start Date || Contract Start Date */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <DatePicker
              name="contract_start_date"
              labelText={'Contract Start Date'}
              // minDate={new Date()}
              className={`${
                !edit || (formik.values.stage_status === CONSTRUCTION && isUser(currentUser.user_type))
                  ? ''
                  : styles.highlightField
              }
                   ${styles.dataPickerInput}`}
              // disabled={!edit}
              disabled={formik.values.stage_status === CONSTRUCTION ? !edit || isUser(currentUser.user_type) : !edit}
              placeholder="DD/MM/YY"
              value={
                formik.values.contract_start_date ? convertDDMMYYYYToLongDate(formik.values.contract_start_date) : ''
              }
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('contract_start_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.contract_end_date) {
                  const firstDate = formik.values.eot_to_contractor
                    ? formik.values.eot_to_contractor
                    : formik.values.contract_end_date
                  const durationInDays = calculateDateDifference(firstDate, date)
                  formik.setValues({ ...formik.values, contract_start_date: date, duration_in_days: durationInDays })
                } else {
                  formik.setFieldValue('contract_start_date', date)
                }
              }}
            />
          </div>
        ) : null}
        {/* Contract End Date */}
        {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            <DatePicker
              name="contract_end_date"
              labelText="Contract End Date"
              placeholder="DD/MM/YY"
              // disabled={!edit}
              disabled={!edit || isUser(currentUser.user_type)}
              // minDate={new Date()}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.contract_end_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('contract_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.forecasted_end_date || formik.values.contract_start_date) {
                  const variance =
                    formik.values.forecasted_end_date &&
                    !formik.values.eot_to_contractor &&
                    calculateDateDifference(date, formik.values.forecasted_end_date)
                  const durationInDays =
                    formik.values.contract_start_date &&
                    calculateDateDifference(date, formik.values.contract_start_date)
                  formik.setValues({
                    ...formik.values,
                    contract_end_date: date,
                    variance_in_days: formik.values.forecasted_end_date ? variance : '',
                    duration_in_days: formik.values.contract_start_date ? durationInDays : '',
                  })
                } else {
                  formik.setFieldValue('contract_end_date', date)
                }
              }}
            />
          </div>
        ) : null}
        {/* eot_to_contractor */}
        {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            <DatePicker
              name="eot_to_contractor"
              labelText="Potential EOT to Contractor"
              placeholder="DD/MM/YY"
              // disabled={!edit}
              disabled={!edit || isUser(currentUser.user_type)}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField} ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.eot_to_contractor)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('eot_to_contractor', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                const variance = calculateDateDifference(date, formik.values.forecasted_end_date)
                formik.setValues({ ...formik.values, eot_to_contractor: date, variance_in_days: variance })
              }}
            />
          </div>
        ) : null}

        {/* Plan L Week */}
        <div>
          {/* BACKEND:AUTO_CALCULATED */}
          <Tooltip title={'Plan Progress % Last Period'} arrow>
            <div>
              <TextInputField
                name="plan_l_week"
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Plan Progress % Last Period'}
                onChange={formik.handleChange}
                value={formik.values?.plan_l_week}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <PercentageIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          </Tooltip>
        </div>
        {/* Actual L Week */}
        <div>
          {/* BACKEND:AUTO_CALCULATED */}
          <Tooltip title={'Actual Progress % Last Period'} arrow>
            <div>
              <TextInputField
                name="actual_l_week"
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Actual Progress % Last Period'}
                onChange={formik.handleChange}
                value={formik.values?.actual_l_week}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <PercentageIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          </Tooltip>
        </div>
        {/* supervision_consultant  */}
        {/* <div>
          <TextInputField
            name="supervision_consultant"
            placeholder="Type here..."
            variant={"outlined"}
            className={styles.inputFields}
            labelText={"Supervision Consultant"}
            onChange={formik.handleChange}
            value={formik.values?.supervision_consultant}
          />
        </div> */}
        {/* supervision_consultant */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              clearIcon={true}
              options={pmcConsultantsOption}
              labelText={'Supervision Consultant '}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  `}
              disabled={!edit}
              value={
                formik.values?.master_supervision_consultant_id
                  ? getValue(pmcConsultantsOption, formik.values?.master_supervision_consultant_id)
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  master_supervision_consultant_id: val?.value || '',
                })
              }
            />
          </div>
        ) : null}

        {formik.values.stage_status === CONTRACTOR_PROCUREMENT ? (
          <>
            <div>
              <ComboBox
                clearIcon={true}
                options={pmcConsultantsOption}
                labelText={'PMC Consultant'}
                disabled={!edit}
                placeholder="Type of search..."
                className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
                value={
                  formik.values?.master_pmc_consultant_id
                    ? getValue(pmcConsultantsOption, formik.values?.master_pmc_consultant_id)
                    : null
                }
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    master_pmc_consultant_id: val?.value || null,
                  })
                }
              />
            </div>
            <div>
              <ComboBox
                options={contractorsOption}
                labelText={'Contractor'}
                clearIcon={true}
                className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  `}
                placeholder="Type of search..."
                disabled={!edit}
                value={
                  formik.values?.master_contractor_id
                    ? getValue(contractorsOption, formik.values?.master_contractor_id)
                    : null
                }
                onChange={(val) => formik.setValues({ ...formik.values, master_contractor_id: val?.value || null })}
              />
            </div>
          </>
        ) : null}

        {formik.values.stage_status === DESIGN || formik.values.stage_status === LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              options={consultantsOptions}
              labelText={'Consultant'}
              clearIcon={true}
              disabled={!edit}
              // disabled={formik.values.stage_status === DESIGN ? !isFirstDesignStageOfPhase : !edit}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
              value={
                formik.values?.master_consultant_id
                  ? getValue(consultantsOptions, formik.values?.master_consultant_id)
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  master_consultant_id: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {(formik.values.stage_status === INITIATION ||
          formik.values.stage_status === LDC_PROCUREMENT ||
          formik.values.stage_status === DLP_PROJECT_CLOSEOUT ||
          formik.values.stage_status === DESIGN) && (
          <div>
            {/* BACKEND:AUTO_CALCULATED */}
            <Tooltip title={'Baseline Plan Finish - Actual finish date'} arrow>
              <div>
                <TextInputField
                  name="variance_in_days"
                  variant={'outlined'}
                  disabled={true}
                  classes={{ root: styles.planPercentage }}
                  className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                  labelText={'Variance (In Days)'}
                  onChange={formik.handleChange}
                  value={formik.values?.variance_in_days}
                />
              </div>
            </Tooltip>
          </div>
        )}

        {formik.values.stage_status === DESIGN ? (
          <>
            <NumberInputField
              name="pte"
              disabled={!edit}
              labelText={'PTE'}
              placeholder="1,2,3..."
              className={`${!edit ? '' : styles.highlightField} ${styles.inputFields}`}
              value={formik?.values?.pte}
              onChange={(value) => {
                return formik.setFieldValue('pte', value)
              }}
              maxLength={20}
            />
          </>
        ) : null}
      </div>
      {formik.values.stage_status === CONSTRUCTION && <div className={styles.lineSeparator}></div>}
      <div className={styles.fieldContainer}>
        {/* -----------------line separator */}
        {/* supervision_consultant  */}
        {/* <div>
          <TextInputField
            name="supervision_consultant"
            placeholder="Type here..."
            variant={"outlined"}
            className={styles.inputFields}
            labelText={"Supervision Consultant"}
            onChange={formik.handleChange}
            value={formik.values?.supervision_consultant}
          />
        </div> */}

        {/* PMC consultant */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              clearIcon={true}
              options={pmcConsultantsOption}
              labelText={'PMC Consultant '}
              disabled={!edit}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
              value={
                formik.values?.master_pmc_consultant_id
                  ? getValue(pmcConsultantsOption, formik.values?.master_pmc_consultant_id)
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  master_pmc_consultant_id: val?.value || null,
                })
              }
            />
          </div>
        ) : null}
        {/* That there may be still some records in DB */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== CONSTRUCTION &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== LDC_PROCUREMENT &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT ? (
          <div>
            <ComboBox
              options={consultantsOptions}
              labelText={'Consultant'}
              clearIcon={true}
              disabled={!edit}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
              value={
                formik.values?.master_consultant_id
                  ? getValue(consultantsOptions, formik.values?.master_consultant_id)
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  master_consultant_id: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {/* Contractor */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              options={contractorsOption}
              labelText={'Contractor'}
              clearIcon={true}
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  `}
              placeholder="Type of search..."
              disabled={!edit}
              value={
                formik.values?.master_contractor_id
                  ? getValue(contractorsOption, formik.values?.master_contractor_id)
                  : null
              }
              onChange={(val) => formik.setValues({ ...formik.values, master_contractor_id: val?.value || null })}
            />
          </div>
        ) : null}
        {formik.values.stage_status === CONSTRUCTION ? (
          <>
            <div>
              <TextInputField
                name="plan_contractor_progress_percentage"
                placeholder="Type here..."
                variant={'outlined'}
                disabled={!edit}
                classes={{ root: styles.planPercentage }}
                className={`${!edit ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Contractor Plan Progress %'}
                onChange={(event) => {
                  const inputValue = event.target.value
                  const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                  if (isValidInput || inputValue === '') {
                    return formik.setFieldValue('plan_contractor_progress_percentage', inputValue)
                  }
                }}
                value={formik.values?.plan_contractor_progress_percentage}
              />
            </div>
            <div>
              <Tooltip title={'Contract Start Date - Contract end date'} arrow>
                <div>
                  <TextInputField
                    name="duration_in_days"
                    placeholder="Type here..."
                    variant={'outlined'}
                    disabled={true}
                    classes={{ root: styles.planPercentage }}
                    className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                    labelText={'Contract Duration (In Days)'}
                    onChange={(event) => {
                      const inputValue = event.target.value
                      const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                      if (isValidInput || inputValue === '') {
                        return formik.setFieldValue('duration_in_days', inputValue)
                      }
                    }}
                    value={formik.values?.duration_in_days}
                  />
                </div>
              </Tooltip>
            </div>
            <div>
              {/* BACKEND:AUTO_CALCULATED */}
              <Tooltip
                title={
                  formik.values.stage_status.eot_to_contractor
                    ? 'Baseline Plan Finish - Potential EOT to Contractor'
                    : 'Baseline Plan Finish - Contract end date'
                }
                arrow
              >
                <div>
                  <TextInputField
                    name="buffer_in_days"
                    variant={'outlined'}
                    disabled={true}
                    classes={{ root: styles.planPercentage }}
                    className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                    labelText={'Internal Buffer (In Days)'}
                    onChange={formik.handleChange}
                    value={formik.values?.buffer_in_days}
                  />
                </div>
              </Tooltip>
            </div>
            <div>
              <DatePicker
                name="kickoff_meeting_date"
                labelText="Kickoff Meeting date"
                placeholder="DD/MM/YY"
                disabled={!edit}
                className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
                value={convertDDMMYYYYToLongDate(formik.values.kickoff_meeting_date)}
                onChange={(value) => {
                  if (value == 'Invalid Date') {
                    formik.setFieldValue('kickoff_meeting_date', null)
                    return null
                  }
                  const selectedDate = value && value?.toISOString()
                  const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                  formik.setFieldValue('kickoff_meeting_date', date)
                }}
              />
            </div>
          </>
        ) : null}

        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            {/* BACKEND:AUTO_CALCULATED */}
            <Tooltip
              title={
                formik.values.stage_status === CONSTRUCTION
                  ? !!formik.values.eot_to_contractor
                    ? 'Potential EOT to Contractor - Actual finish date'
                    : 'Contract End date - Actual finish date'
                  : 'Baseline Plan Finish - Actual finish date'
              }
              arrow
            >
              <div>
                <TextInputField
                  name="variance_in_days"
                  variant={'outlined'}
                  disabled={true}
                  classes={{ root: styles.planPercentage }}
                  className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                  labelText={'variance (In Days)'}
                  onChange={formik.handleChange}
                  value={formik.values?.variance_in_days}
                />
              </div>
            </Tooltip>
          </div>
        ) : null}
        {formik.values.stage_status === CONSTRUCTION ? (
          <>
            <Tooltip title={'Today - Contract Start Date/Contract End Date - Contract Start Date * 100'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="time_elapsed_perc"
                  disabled={true}
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'Time Elapsed %'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('time_elapsed_perc', inputValue)
                    }
                  }}
                  value={formik.values?.time_elapsed_perc}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="start" className={styles.endAdornment}>
                        <PercentageIcon className={styles.endAdornmentIcon} />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
            </Tooltip>
            <Tooltip title={'Today - Contract End Date/Contract Start Date - Contract Start Date * 100'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="SPI"
                  disabled={true}
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'SPI'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('SPI', inputValue)
                    }
                  }}
                  value={formik.values?.SPI}
                />
              </div>
            </Tooltip>
            <Tooltip title={'Actual Progress % - Contractor Plan Progress %'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="VAR"
                  disabled={true}
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'VAR'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('VAR', inputValue)
                    }
                  }}
                  value={formik.values?.VAR}
                />
              </div>
            </Tooltip>
          </>
        ) : null}
      </div>
      {formik.values.stage_status === CONSTRUCTION && (
        <>
          <div className={styles.lineSeparator}></div>
          <div className={styles.fieldContainer}>
            <Tooltip title={'Previous period Forecast/Actual Finish Date'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="forecast_completion_last_week"
                  className={`${styles.inputFields}`}
                  disabled={true}
                  variant={'outlined'}
                  labelText={'Forecast Completion Last Period'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('forecast_completion_last_week', inputValue)
                    }
                  }}
                  value={formik.values?.forecast_completion_last_week}
                />
              </div>
            </Tooltip>
            <Tooltip title={'Forecast/Actual Finish Date - last period Forecast/Actual Finish Date'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="delay_this_week"
                  className={`${styles.inputFields}`}
                  disabled={true}
                  variant={'outlined'}
                  labelText={'Delay This Period'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('delay_this_week', inputValue)
                    }
                  }}
                  value={formik.values?.delay_this_week}
                />
              </div>
            </Tooltip>
            <Tooltip title={'Forecast/Actual Finish Date - Contract End Date'} arrow>
              <div>
                {/* BACKEND:AUTO_CALCULATED */}
                <TextInputField
                  name="cumulative_delay"
                  disabled={true}
                  variant={'outlined'}
                  className={`${styles.inputFields}`}
                  labelText={'Cumulative Delay'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                    if (isValidInput || inputValue === '') {
                      return formik.setFieldValue('cumulative_delay', parseFloat(inputValue) || 0)
                    }
                  }}
                  value={formik.values?.cumulative_delay}
                />
              </div>
            </Tooltip>
          </div>
          <div className={styles.lineSeparator}></div>
          <Box
            sx={{
              '& .pulse-checkbox': {
                flexDirection: 'row-reverse !important',
                gap: '5px',
                justifyContent: 'flex-end',
                alignItems: 'center',

                '& .pulse-checkbox-label': {
                  // lineHeight: 1,
                },

                '& .MuiButtonBase-root.MuiCheckbox-root': {
                  paddingTop: '0 !important',
                },
              },
            }}
            className={styles.fieldContainer}
          >
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Contract signature received"
                checked={formik.values.is_contract_signature_received}
                onChange={() =>
                  formik.setFieldValue('is_contract_signature_received', !formik.values.is_contract_signature_received)
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Advance payment bond received"
                checked={formik.values.is_advance_payment_bond_received}
                onChange={() =>
                  formik.setFieldValue(
                    'is_advance_payment_bond_received',
                    !formik.values.is_advance_payment_bond_received,
                  )
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Insurances received"
                checked={formik.values.is_insurances_received}
                onChange={() => formik.setFieldValue('is_insurances_received', !formik.values.is_insurances_received)}
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Advance payment released"
                checked={formik.values.is_advance_payment_released}
                onChange={() =>
                  formik.setFieldValue('is_advance_payment_released', !formik.values.is_advance_payment_released)
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Performance bond received"
                checked={formik.values.is_performance_bond_received}
                onChange={() =>
                  formik.setFieldValue('is_performance_bond_received', !formik.values.is_performance_bond_received)
                }
              />
            </div>
          </Box>
        </>
      )}
    </div>
  )
}

export default BasicDetails

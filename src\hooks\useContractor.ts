import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterContractor,
  deleteContractor,
  getMasterContractor,
  updateMasterContractor,
} from '../services/contractors'
import { IGetMasterContractorsResponse, IUpdateContractor } from '../services/contractors/interface'

export const QUERY_CONTRACTOR = 'contractor'

/**
 * Hook to fetch Contractor
 * @param enabled - Enables or disables the query
 */
export const useGetContractor = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterContractorsResponse>({
    queryKey: [QUERY_CONTRACTOR],
    queryFn: getMasterContractor,
    enabled,
  })

  return {
    contractors: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Contractor
 */
export const useCreateContractor = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterContractor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACTOR] })
    },
  })
}

/**
 * Hook to delete a Contractor
 */
export const useDeleteContractor = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteContractor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACTOR] })
    },
  })
}

/**
 * Hook to update a Contractor
 */
export const useUpdateContractor = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterContractor,
    onMutate: async (updatedData: IUpdateContractor) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_CONTRACTOR] })
      const previousData = queryClient.getQueryData<IGetMasterContractorsResponse>([QUERY_CONTRACTOR])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterContractorsResponse>([QUERY_CONTRACTOR], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, contractor: updatedData.contractor } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_CONTRACTOR], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTRACTOR] })
    },
  })
}

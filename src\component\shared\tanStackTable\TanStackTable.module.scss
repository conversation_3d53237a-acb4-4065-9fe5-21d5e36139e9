@import '/styles/color.scss';

.tableContainer {
  display: flex;
  display: flex;
  flex-direction: column;
  // gap: 16px;
  position: sticky;
  top: 0;
  z-index: 1;
  min-height: 100px;
}

.tanStackGrid {
  display: flex;
  flex-direction: column;
  // gap: 16px;
  position: sticky;
  top: 0;
  z-index: 1;
  min-width: fit-content;
  width: 100%;
}
.headerRow {
  position: sticky;
  top: 0;
  z-index: 12;
  width: 100%;
}

.headerRow {
  padding: 2px 0;
  border-bottom: 1px solid $BLACK;
  display: flex;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.05);
  align-items: center;
}

.dataRow {
  // padding: 2px 0;
  display: flex;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.headerRow {
  border: none;
  background-color: #d1d1d1;
  box-sizing: border-box;
}

.dataRowWrapper {
  border-bottom: 1px solid $BLACK;
}

.dataRow {
  background-color: $WHITE;
  // overflow: hidden;
}

.dataRow:hover {
  background-color: #f5f5f5;
  .rowCell {
    background-color: #f5f5f5;
  }
}

.rowCell {
  padding: 8px;
  font-size: 12px;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.ribbon {
  color: $WHITE;
  position: absolute;
  padding: 1px;
  height: 08px;
  display: flex;
  align-items: center;
  transform: rotate(-45deg);
  top: 6px;
  left: -18px;
  width: 60px;
  justify-content: center;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.05);
  font-size: 8px !important;
}
.highlightEditableCell {
  border-radius: 3px;
  background-color: #f0f8ff; // light blue or any color you prefer
  outline: 1px solid #2865dc; // primary blue or any color you prefer
  padding: 5px;
  word-wrap: break-word;
}
.verticalRibbon {
  width: 9px;
  height: fit-content;
  min-height: 25px;
  border-radius: 10px;
}
.count {
  font-size: 14px;
  display: flex;
  position: absolute;
  bottom: 0px;
  align-items: flex-end;
  right: 20px;
}

// //* Helper function for natural alphanumeric sorting
export const naturalSort = (a: string | null | undefined, b: string | null | undefined): number => {
  if ((!a || a === '') && (!b || b === '')) return 0
  if (!a || a === '') return -1
  if (!b || b === '') return 1

  const [prefixA, numA] = a?.match(/^(.*?)(\d+)(?!.*\d)/)?.slice(1) || [a, '0']
  const [prefixB, numB] = b?.match(/^(.*?)(\d+)(?!.*\d)/)?.slice(1) || [b, '0']

  // Compare prefix alphabetically
  const prefixCompare = prefixA.trim().localeCompare(prefixB.trim(), undefined, {
    sensitivity: 'base',
  })

  if (prefixCompare !== 0) return prefixCompare

  // Compare number as integer
  return parseInt(numA, 10) - parseInt(numB, 10)
}

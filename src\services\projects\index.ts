import { IMultiProjectUpdatePayload, IProjects, ISortingOrderPayload } from './interface'
import { api } from '@/src/api/axios'

export const PROJECTS_ENDPOINT = '/projects'

export const getProjects = async ({ period }: { period: string }) => {
  const response = await api.get(`${PROJECTS_ENDPOINT}/?period=${period}`)
  return response?.data
}

export const getMasterOneProject = async ({ projectName, period }: { projectName: string; period: string }) => {
  const response = await api.get(`${PROJECTS_ENDPOINT}/${encodeURIComponent(projectName)}?period=${period}`)
  return response.data
}

export const addProject = async (data: IProjects) => {
  const response = await api.post(PROJECTS_ENDPOINT, data)
  return response.data
}

export const updateProjectName = async ({
  project_name,
  new_project_name,
  environment,
}: {
  project_name: string
  new_project_name: string
  environment: string
}) => {
  const response = await api.post(`${PROJECTS_ENDPOINT}/update/project-name/${environment}`, {
    project_name,
    new_project_name,
  })
  return response.data
}

export const updateProject = async ({ id, data }: { id: string; data: IProjects }) => {
  const response = await api.post(`${PROJECTS_ENDPOINT}/${encodeURIComponent(id)}`, data)
  return response.data
}

export const deleteProject = async ({ projectName, period }: { projectName: string; period: string }) => {
  const response = await api.delete(`${PROJECTS_ENDPOINT}/${projectName}?period=${period}`)
  return response.data
}

export const updateSortingOrder = async (data: ISortingOrderPayload) => {
  const response = await api.post(`${PROJECTS_ENDPOINT}/sorting-update`, data)
  return response.data
}

export const updateMultiProject = async (data: IMultiProjectUpdatePayload) => {
  const response = await api.put(`${PROJECTS_ENDPOINT}/bulk-update-projects`, data)
  return response.data
}

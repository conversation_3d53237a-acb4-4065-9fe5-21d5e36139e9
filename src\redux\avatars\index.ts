import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetAvatarsResponse, IinitialAvatar } from './interface'
import { StatusEnum } from '../types'
import { ApiGetNoAuth } from '@/src/api'

const initialState: IinitialAvatar = {
  getAllAvatarsStatus: StatusEnum.Idle,
  avatars: null,
}

export const getAllAvatars = createAsyncThunk('/fetch-avatars', async (data: {}, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth('/files/fetch-avatars')
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const avatarSlice = createSlice({
  name: 'avatar',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    //getAllAvatars
    builder.addCase(getAllAvatars.pending, (state) => {
      state.getAllAvatarsStatus = StatusEnum.Pending
    })
    builder.addCase(getAllAvatars.fulfilled, (state, action) => {
      state.getAllAvatarsStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetAvatarsResponse
      state.avatars = actionPayload.data
    })
    builder.addCase(getAllAvatars.rejected, (state) => {
      state.getAllAvatarsStatus = StatusEnum.Failed
    })
  },
})

export const {} = avatarSlice.actions

// Export the reducer
export default avatarSlice.reducer

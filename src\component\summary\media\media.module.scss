@import '/styles/color.scss';

.mediaContainer {
  flex-direction: column;
  align-items: center;
  padding: 20px 20px 20px 0;

  // .categoriesContainer {
  //   display: flex;
  //   gap: 20px;
  //   justify-content: space-around;
  // }

  .categoriesContainer {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    justify-content: center; /* Optional: Center items if not filling full row */
  }

  .categoryBox {
    flex: 1;
    border: 1px solid #ccc;
    padding: 20px;
    border-radius: 5px;
    background-color: #f9f9f9;
    text-align: center;

    .dropZone {
      margin: 10px 0;
      padding: 20px;
      border: 2px dashed #ccc;
      text-align: center;
      background-color: #fafafa;
      border-radius: 5px;
      position: relative;

      .customFileInput {
        display: block;
        margin: 10px 0;
        position: relative;

        .fileInput {
          display: none;
        }

        span {
          display: block;
          cursor: pointer;
          background-color: #444444;
          color: white;
          padding: 10px;
          text-align: center;
          border-radius: 5px;
          opacity: 1;
        }
        .isEditForUser {
          opacity: 30%;
          cursor: default;
        }
      }
    }

    .error {
      color: red;
      margin-top: 10px;
      font-size: 12px;
    }

    .previews {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
      justify-content: center;
      position: relative;

      .preview {
        width: 265px;
        height: 175px;
        overflow: hidden;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: #fff;

        .previewImage {
          width: 100%;
          object-fit: cover;
        }
      }
    }

    .uploadButton {
      display: block;
      width: 100%;
      padding: 10px;
      background-color: #444444;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 10px;

      &:hover {
        background-color: #444444;
      }
    }
  }
}
.title {
  font-size: 20px;
  font-weight: bold;
  border-bottom: 3px solid red;
  display: inline-block;
  margin-top: 40px;
}

.media {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  .imageWrapper {
    flex: 1;
    max-width: 400px;
    width: 100%;
    background-color: white;
  }
  .imageName {
    padding: 0px 10px;
    margin-right: 10px;
    word-wrap: break-word;
  }
}

.deleteButton {
  position: absolute;
  z-index: 9;
  display: inline-block;
  right: 10px;
  top: 10px;
  background: white;
  display: flex;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 10px;
  > p {
    max-width: 88%;
  }
}
.pdfWrapper {
  display: flex;
  align-items: center;
  background-color: #f9f9f9; // Light gray background for a soft design
  border: 1px solid #e0e0e0; // Subtle border to define boundaries
  border-radius: 8px; // Rounded corners for a modern look
  padding: 10px 15px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); // Light shadow for depth
  transition:
    transform 0.2s,
    box-shadow 0.2s;

  .pdfIcon {
    font-size: 2rem; // Larger icon for visibility
    color: #d32f2f; // Red color for PDF files
    margin-right: 15px;
    cursor: pointer;
  }

  .content {
    gap: 5px;
    align-items: center;
    flex-grow: 1;

    .pdfName {
      font-size: 1rem; // Adjust font size for better readability
      font-weight: 500;
      color: #333; // Dark text color
      margin: 0;
      white-space: nowrap; // Prevent text wrap
      overflow: hidden; // Ellipsis for overflow
      text-overflow: ellipsis;
    }

    .deletePdf {
      font-size: 1.5rem; // Slightly larger delete icon
      color: #757575; // Neutral color for delete icon
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #d32f2f; // Match PDF icon hover color
      }
    }
  }
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.emptyPhase {
  background: #ffffff;
  padding-left: 16px;
  padding-top: 20px;
  margin-bottom: 20px;
  border-radius: 12px;
}

.deleteIcon {
  cursor: pointer !important;
}

.videoBox,
.imageBox {
  background-color: #f5f5f5;
  object-fit: contain;
}

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import styles from './Home.module.scss'
import { DrawerStates } from './interface'
import HomeHeader from './newHeader'
import PeriodCards from './periodCards'
import ProjectDetails from './projectDetails'
import ProjectManagement from './projectManagement'
import ProjectPhases from './projectPhases'
import ProjectsAndEntities from './projectsAndEntities'
import Loader from '../shared/loader'
import { whiteListedUserList } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useAvatar from '@/src/redux/avatars/useAvatar'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useModuleCount from '@/src/redux/moduleCount/useModuleCount'
import { isSuperAdmin, isSuperUser } from '@/src/utils/userUtils'

const initialDrawerStates: DrawerStates = {
  viewEntity: false,
  addEntity: false,
  addProject: false,
  viewControlManagers: false,
  addControlManagers: false,
  viewExecutiveDirectors: false,
  addExecutiveDirectors: false,
  addDesignManagers: false,
  viewPortFolioManagers: false,
  addPortFolioManagers: false,
  viewProjectStatus: false,
  addProjectStatus: false,
  viewLocation: false,
  addLocation: false,
  viewSubLocation: false,
  viewDevelopers: false,
  addDevelopers: false,
  viewTypologies: false,
  addTypologies: false,
  viewContractors: false,
  addContractors: false,
  viewConsultants: false,
  addConsultants: false,
  addProjectClassification: false,
  viewProjectClassification: false,
  addRating: false,
  viewRating: false,
  viewPmcConsultant: false,
  addPmcConsultant: false,
  addSubStatuses: false,
  viewSubStatuses: false,
  addProjectStageStatus: false,
  viewProjectStageStatus: false,
  viewSubOwningEntity: false,
  addSubOwningEntity: false,
  addProjectPhases: false,
  addEntityKeyAchievement: false,
  addProjectToBeCompletedIn: false,
  addKpiDrawer: false,
  addProjectOwnerCard: false,
  addContractType: false,
  addProcurementManagement: false,
  entityCategory: false,
  pricingType: false,
  riskCategory: false,
  riskOwner: false,
  riskDiscipline: false,
  addSubLocation: false,
  reasonForDelay: false,
  mitigationRecoveryPlan: false,
  nextStepsToAdvancedProgress: false,
  nonRecoverableDelayJustification: false,
  svp: false,
  director: false,
  projectPhaseCategory: false,
  handoverStep: false,
  newsFeeds: false,
  milestoneNumber: false,
  scoring: false,
}

const Home: React.FC = () => {
  const router = useRouter()
  const { currentUser } = useAuthorization()
  const { getModuleCountApi } = useModuleCount()
  const { getAllAvatarsApi } = useAvatar()

  const { currentPeriod } = useMasterPeriod()
  const [loading, setLoading] = useState(true)
  const [drawerStates, setDrawerStates] = useState<DrawerStates>(initialDrawerStates)

  const toggleDrawer = (drawer: keyof DrawerStates) => (open: boolean) => {
    setDrawerStates({ ...drawerStates, [drawer]: open })
    currentPeriod && currentUser && getModuleCountApi({ period: currentPeriod })
  }

  const handleModuleCount = async () => {
    setLoading(true)
    if (currentPeriod && currentUser) {
      try {
        getAllAvatarsApi()
        const [moduleCountRes]: any = await Promise.all([getModuleCountApi({ period: currentPeriod })])
        if (moduleCountRes.payload.success) {
          setLoading(false)
        }
      } catch (error) {
        setLoading(false)
      }
    } else {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (currentPeriod && currentUser) {
      if (
        isSuperUser(currentUser.user_type) ||
        isSuperAdmin(currentUser.user_type) ||
        whiteListedUserList.includes(currentUser.email)
      ) {
        handleModuleCount()
      }
    }
  }, [currentPeriod, currentUser])

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div className={styles.container}>
          <HomeHeader />
          {/* <Toaster position="bottom-right" /> */}
          <div className={styles.topContainer}>
            {(isSuperAdmin(currentUser.user_type) || whiteListedUserList.includes(currentUser.email)) && (
              <PeriodCards drawerStates={drawerStates} toggleDrawer={toggleDrawer} />
            )}
            <ProjectsAndEntities drawerStates={drawerStates} toggleDrawer={toggleDrawer} />
            <ProjectPhases drawerStates={drawerStates} toggleDrawer={toggleDrawer} />
          </div>
          <ProjectDetails drawerStates={drawerStates} toggleDrawer={toggleDrawer} />
          <ProjectManagement drawerStates={drawerStates} toggleDrawer={toggleDrawer} />
        </div>
      )}
    </>
  )
}

export default Home

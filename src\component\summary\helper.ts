import { statusOrder } from './summaryTab/constant'
import { STATUS_OPTIONS } from '@/src/constant/enum'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'

export const determineProjectStatus = (project_status: string, automationProjectStatus: string) => {
  if (project_status === STATUS_OPTIONS.RESUME) {
    return automationProjectStatus
  } else if (project_status?.toLocaleLowerCase() === STATUS_OPTIONS.ON_HOLD?.toLocaleLowerCase()) {
    return STATUS_OPTIONS.ON_HOLD
  } else if (project_status?.toLocaleLowerCase() === STATUS_OPTIONS.TRANSFER_IN_PROGRESS?.toLocaleLowerCase()) {
    return STATUS_OPTIONS.TRANSFER_IN_PROGRESS
  } else if (project_status?.toLocaleLowerCase() === STATUS_OPTIONS.TRANSFERRED?.toLocaleLowerCase()) {
    return STATUS_OPTIONS.TRANSFERRED
  } else if (project_status?.toLocaleLowerCase() === STATUS_OPTIONS.CANCELED?.toLocaleLowerCase()) {
    return STATUS_OPTIONS.CANCELED
  } else if (project_status?.toLocaleLowerCase() === STATUS_OPTIONS.CLOSED_OUT?.toLocaleLowerCase()) {
    return STATUS_OPTIONS.CLOSED_OUT
  } else {
    return automationProjectStatus
  }
}

interface FilteredStatus {
  project_stage_status?: string
  forecasted_date?: string
  planed_date?: string
  actual_percentage?: number
  phase?: string
}

export const generateAutoValueOfProject = (statuses: any[], project: any) => {
  /**
   * ======================== Stage Status Automation ========================
   *
   * - For the following stage statuses, the actual percentage is sourced from `actual_plan_percentage`:
   *   - Contractor Procurement
   *   - Construction
   *   - DLP and Project Closeout
   *
   * - For the following stage statuses, the actual percentage is sourced from `actual_percentage`:
   *   - LDC Procurement
   *   - Design
   *   - Initiation
   *
   * ------------------------ Filtering Out "Enabling" Phases ------------------------
   * Any stage where the phase name contains the word **"Enabling"** (case-insensitive)
   * is excluded from status calculations.
   *
   * -------------------- Handling Special Entity Cases --------------------
   * If the `owning_entity` is `'Aldar Investment'` and all stage statuses
   * have `null` as their actual percentage value,
   * the automation sets the **stage status** to **"Initiation"** by default.
   *
   * ------------------------ Stage Status Ordering ------------------------
   * The stage statuses are evaluated in the following order:
   *   1. Initiation
   *   2. LDC Procurement
   *   3. Design
   *   4. Contractor Procurement
   *   5. Construction
   *   6. DLP and Project Closeout
   *
   * The function checks for the **first stage status** with an actual percentage
   * value greater than `0`. That status is then considered the **stage status**.
   * If no such stage is found, the function defaults to the **first stage** in the list.
   */

  /**
   * ======================== Overall Planned & Forecasted Finish Date Automation ========================
   *Overall_Planned_Finish_Date & Overall_Forecasted_Finish_Date
   * - For the following stage statuses, the actual percentage is sourced from
   *   `forecasted_end_date` & `plan_end_date`:
   *   - Contractor Procurement
   *   - Construction
   *   - DLP and Project Closeout
   *
   * - For the following stage statuses, the actual percentage is sourced from
   *   `forecast_finish` & `baseline_plan_finish`:
   *   - LDC Procurement
   *   - Design
   *   - Initiation
   *
   * ------------------------ Filtering Out "DLP and Project Closeout" ------------------------
   * Any stage where the status is **"DLP and Project Closeout"** is excluded from further calculations.
   *
   * ------------------------ Determining the Stage Status Based on Maximum Date ------------------------
   * After filtering, the function identifies the **stage status with the latest date**
   * from the relevant date fields and considers it as the final **stage status**.
   */

  const statusFilterByProject = statuses.filter((item: any) => item.project_name === project?.project_name)

  const forecastDates: FilteredStatus[] = []
  const planFinishDate: FilteredStatus[] = []
  const projectStatuses: FilteredStatus[] = []

  statusFilterByProject.forEach((item: any) => {
    if (
      [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT].includes(
        item?.MasterProjectStageStatus?.project_stage_status,
      )
    ) {
      forecastDates.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        forecasted_date: item.forecasted_end_date,
      })
      planFinishDate.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        planed_date: item.plan_end_date,
      })
      projectStatuses.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        actual_percentage: Number(item.actual_plan_percentage) ?? null,
      })
    } else if ([LDC_PROCUREMENT, DESIGN, INITIATION].includes(item?.MasterProjectStageStatus?.project_stage_status)) {
      forecastDates.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        forecasted_date: item.forecast_finish,
      })
      planFinishDate.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        planed_date: item.baseline_plan_finish,
      })
      projectStatuses.push({
        phase: item?.phase,
        project_stage_status: item?.MasterProjectStageStatus?.project_stage_status,
        actual_percentage: Number(item.actual_percentage) ?? null,
      })
    }
  })
  //Note:- Not considering dlp project closeout value for automation of forecasted and plan date
  const filterOfForecast = forecastDates.filter((item: any) => item?.project_stage_status !== DLP_PROJECT_CLOSEOUT)
  const filterOfPlan = planFinishDate.filter((item: any) => item?.project_stage_status !== DLP_PROJECT_CLOSEOUT)

  const automationForecastedDate = findMaxDateOfForecastedDate(filterOfForecast)
  const automationPlanDate = findMaxDateOfPlanedDate(filterOfPlan)

  const targetWord = 'Enabling' // The word to check for

  // Filter out any string that contains the exact word "Enabling" (case-insensitive)
  const statusFilterByProjectByPhase = projectStatuses.filter(
    (item: any) => !item?.phase?.toLowerCase().includes(targetWord.toLowerCase()), // Exclude strings containing "Enabling"
  )
  const automationProjectStatus = getLatestStageStatuses(statusFilterByProjectByPhase, project?.owning_entity)

  return { automationForecastedDate, automationPlanDate, automationProjectStatus }
}

export const getLatestStageStatuses = (data: any, entity: any) => {
  if (entity === 'Aldar Investment') {
    const isNull = data?.every((item: any) => {
      return !item?.actual_percentage
    })
    if (isNull) {
      return 'Initiation'
    }
  }

  if (!data || data.length === 0) return null

  const orderedData = data?.sort((a: any, b: any) => {
    return statusOrder.indexOf(a.project_stage_status) - statusOrder.indexOf(b.project_stage_status)
  })

  const getLastNonZeroStageStatus = (data: any) => {
    // Iterate from the last item and check if actual_percentage is not "0", null, or ""
    const validStage = [...data].reverse().find((item) => parseFloat(item.actual_percentage) > 0)

    // If a valid stage is found, return its project_stage_status, otherwise return the first project_stage_status
    return validStage ? validStage.project_stage_status : data[0]?.project_stage_status
  }

  const result = getLastNonZeroStageStatus(orderedData)

  return result
}

export const findMaxDateOfForecastedDate = (stages: any) => {
  let maxDate: any = null
  stages.forEach((stage: any) => {
    if (!stage.forecasted_date) return ''
    const currentDate = new Date(stage.forecasted_date)
    if (!maxDate || currentDate > maxDate) {
      maxDate = currentDate
    }
  })
  if (!maxDate) return ''

  const date = new Date(maxDate)

  const day = date.getDate().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const year = date.getFullYear()
  const formattedDate = `${day}-${month}-${year}`

  return formattedDate
}

export const findMaxDateOfPlanedDate = (stages: any) => {
  let maxDate: any = null
  stages.forEach((stage: any) => {
    if (!stage.planed_date) return ''
    const currentDate = new Date(stage.planed_date)
    if (!maxDate || currentDate > maxDate) {
      maxDate = currentDate
    }
  })
  if (!maxDate) return ''

  const date = new Date(maxDate)

  const day = date.getDate().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const year = date.getFullYear()
  const formattedDate = `${day}-${month}-${year}`

  return formattedDate
}

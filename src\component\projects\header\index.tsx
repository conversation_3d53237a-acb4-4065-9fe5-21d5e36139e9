import React, { useMemo, useState } from 'react'
import Image from 'next/image'
import styles from './ProjectHeader.module.scss'
import FeedbackPopOver from '../../home/<USER>/FeedbackPopOver'
import Button from '../../shared/button'
import Typography<PERSON>ield from '../../shared/typography'
import TimerIcon from '../../svgImages/timerIcon'
import PeriodChanger from '../periodChanger'
import { canEditUser } from '@/src/helpers/helpers'
import { useCreateFeedback, useGetFeedback, useUpdateFeedback } from '@/src/hooks/useFeedback'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { errorToast, successToast } from '@/src/utils/toastUtils'
export interface IProjectHeader {
  isLoading: boolean
}

const ProjectHeader: React.FC<IProjectHeader> = ({ isLoading }) => {
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { mutate: submitFeedback } = useCreateFeedback()
  // // const { mutate: updateFeedback } = useUpdateFeedback()
  const [popoverOpen, setPopoverOpen] = useState(false)
  // const { feedback } = useGetFeedback()

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const handleClosePopover = () => {
    setPopoverOpen(false)
  }

  // // const isEdit = Boolean(feedback && feedback[0]?.id)
  // // const firstFeedback = feedback[0]

  const handleConfirmClone = async (values: any) => {
    const addPayload = { currentPeriod, ...values }
    submitFeedback(addPayload, handleMutationCallbacks('Feedback added successfully', 'Failed to add feedback'))
    handleClosePopover()
  }

  return (
    <div className={styles.headerContainer}>
      {/* Left Section */}
      <div className={styles.header}>
        <div>
          <div className={styles.heyText}>
            <TypographyField variant="h7" text="Hello," />
            <Image src="/svg/waveHand.svg" alt="wave hand" height={14} width={14} />
          </div>
          <TypographyField variant="h4" text={currentUser.name} />
        </div>
        <div className={styles.feedbackBtn}>
          <Button onClick={() => setPopoverOpen(true)} disabled={!isEditForUser}>
            Feedback
          </Button>
        </div>
      </div>

      {/* Right Section */}
      <div className={styles.buttons}>
        {!isLoading && (
          <div className={styles.timerIcon}>
            <TimerIcon />
            <span className={styles.date}>{currentPeriod || 'Select a period'}</span>
          </div>
        )}
        {!isLoading && currentUser?.role?.view_permissions?.includes(`Navigate To Historical Periods`) && (
          <PeriodChanger />
        )}
      </div>
      <FeedbackPopOver open={popoverOpen} onClose={handleClosePopover} handleConfirm={handleConfirmClone} />
    </div>
  )
}

export default ProjectHeader

@import '/styles/color.scss';

.container {
  padding: 0px 0px;
  .header {
    cursor: pointer;
    display: flex;
    margin: 22px 0px 22px 24px;
    display: inline-flex;
    gap: 10px;
    .detailsText {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
  .content {
    margin: 20px 20px 0px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .form {
    display: flex;
    gap: 20px;
    flex-direction: column;
    margin-bottom: 10px;
  }
}

.textField {
  > div > div > input {
    min-width: 400px;
    width: 100%;
    border: 1px solid $LIGHT;
    &:focus {
      border-radius: 4px;
      border: 1px solid $FOCUS_TEXTFIELD_BORDER;
      background: $FOCUS_TEXTFIELD_BG;
    }
  }
}

.headerTitle {
  padding: 13px 0px 14px 24px;
  border-bottom: 1px solid $LIGHT_200;
}

.checkbox {
  display: flex;
  align-items: center;
}

.multiSelect > div > div > div {
  overflow: unset !important;
  text-overflow: unset !important;
  display: block !important;
}

.permissionContainer {
  display: flex;
  flex-direction: row;
  gap: 250px;
}

.mainPermissions {
  margin-left: 30px;
}

.searchField {
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  background-color: white;
  top: 0;
}
.contentOfModel {
  overflow: auto;
  height: 100%;
  max-height: 590px;
}
.buttonsOfSelect {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.eyeIcon {
  cursor: pointer;
}

import React, { ReactElement, useState } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { LocalizationProvider, PickersActionBarProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
import ConfirmModel from './confirmModel'
import styles from './perminateFreezePicker.module.scss'
import Button from '@/src/component/shared/button'
import TypographyField from '@/src/component/shared/typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import { DARK_200 } from '@/src/constant/color'
import { formateParamsPeriod, parseDateFromString } from '@/src/utils/dateUtils'

interface MuiDatePickerProps {
  labelText?: string
  value?: string | number | Date | null
  onChange?: (date: any) => void
  className?: string
  minDate?: string | number | Date | null
  maxDate?: string | number | Date
  error?: boolean
  helperText?: string
  disableEditable?: boolean
  onBlur?: any
  placeholder?: string
  onError?: (reason?: string | null) => void
  shouldDisableDate?: any
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: any
  actionButtons?: boolean
  CustomDay?: any
  shouldCloseOnSelect?: boolean
  isAdd?: boolean
  setIsAdd?: (value: boolean) => void
  date?: string | null
  setDate?: (date: string | null) => void
}

const FreezePicker: React.FC<MuiDatePickerProps> = ({
  labelText,
  value,
  onChange = () => null,
  className,
  minDate,
  maxDate,
  setDate,
  date,
  error = false,
  helperText,
  disableEditable = false,
  onBlur,
  placeholder = '',
  onError = () => null,
  shouldDisableDate,
  actionButtons = false,
  disabled = false,
  isAdd,
  setIsAdd,
  required = false,
  name = '',
  sx,
  CustomDay,
  ...props
}): ReactElement => {
  const dateValue = value ? parseDateFromString(value as string) : ''
  const [isOpen, setIsOpen] = useState(false)
  const [datePeriod, setDatePeriod] = useState<string | null>(null)

  const CustomActionBar = ({ onAccept, onClear, onCancel }: PickersActionBarProps) => {
    const [idModel, setIsModel] = useState(false)

    const handleAccept = () => {
      const formattedDate: string = datePeriod ? formateParamsPeriod(datePeriod) : ''
      setDate?.(formattedDate as string)
      setIsAdd?.(true)
    }

    return (
      <div className={styles.buttonActions} style={{ display: 'flex', gap: '10px' }}>
        <Button color="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={() => {
            handleAccept()
            onCancel()
          }}
          disabled={!datePeriod}
        >
          Add
        </Button>
        <ConfirmModel
          open={idModel}
          onClose={() => setIsModel(false)}
          date={datePeriod ? formateParamsPeriod(datePeriod) : ''}
          onConfirmClose={() => {
            setIsModel(false)
            onClear()
          }}
          onConfirmOpen={onAccept}
        />
      </div>
    )
  }

  return (
    <div style={{ width: '100%' }}>
      {labelText && (
        <div>
          <TypographyField style={{ color: DARK_200 }} variant={`caption`} text={labelText} />
        </div>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          {...props}
          open={isOpen}
          onClose={() => setIsOpen(false)}
          format="dd/MM/yyyy"
          closeOnSelect={false}
          value={dateValue || null}
          disabled={disabled}
          onChange={(val) => {
            setDatePeriod(val as string | null)
            onChange(val)
          }}
          slots={{
            actionBar: CustomActionBar,
            openPickerIcon: () => (
              <CalendarIcon
                onClick={(e) => {
                  e.stopPropagation()
                  setIsOpen(true)
                }}
              />
            ),
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay,
          }}
          slotProps={{
            popper: {
              sx: { zIndex: '9999' },
            },
            layout: {
              sx: { display: 'block', textAlign: 'end' },
            },
            textField: {
              sx,
              disabled: true,
              onClick: (e) => {
                e.stopPropagation()
                setIsOpen(true)
              },
            },
          }}
          views={['month', 'year', 'day']}
          minDate={minDate}
          maxDate={maxDate}
          shouldDisableDate={shouldDisableDate}
          onError={(reason) => {
            onError(reason ? 'Invalid Value' : null)
          }}
        />
      </LocalizationProvider>
    </div>
  )
}

export default FreezePicker

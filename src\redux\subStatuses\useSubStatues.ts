import { useDispatch, useSelector } from 'react-redux'
import { addMasterSubStatues, deleteMasterSubStatues, getMasterSubStatues, updateMasterSubStatues } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useSubStatuses = () => {
  const dispatch: AppDispatch = useDispatch()

  const getMasterSubStatuesStatus = useSelector((state: RootState) => state.subStatuses.getMasterSubStatuesStatus)
  const subStatuses = useSelector((state: RootState) => state.subStatuses.subStatuses)

  const getMasterSubStatuesApi = () => dispatch(getMasterSubStatues())
  const addMasterSubStatuesApi = (data: { sub_status: string }) => dispatch(addMasterSubStatues(data))
  const updateMasterSubStatuesApi = (data: { id: number; sub_status: string }) => dispatch(updateMasterSubStatues(data))
  const deleteMasterSubStatuesApi = (data: number) => dispatch(deleteMasterSubStatues(data))

  return {
    getMasterSubStatuesStatus,
    subStatuses,
    getMasterSubStatuesApi,
    addMasterSubStatuesApi,
    updateMasterSubStatuesApi,
    deleteMasterSubStatuesApi,
  }
}
export default useSubStatuses

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createRiskCategory, deleteRiskCategory, getRiskCategory, updateRiskCategory } from '../services/riskCategory'
import { IGetRiskCategoryResponse, IUpdateRiskCategory } from '../services/riskCategory/interface'

export const QUERY_RISK_CATEGORY = 'risk-category'

/**
 * Hook to fetch Risk category
 * @param enabled - Enables or disables the query
 */
export const useGetRiskCategory = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetRiskCategoryResponse>({
    queryKey: [QUERY_RISK_CATEGORY],
    queryFn: getRiskCategory,
    enabled,
  })

  return {
    riskCategories: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Risk category
 */
export const useCreateRiskCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createRiskCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_CATEGORY] })
    },
  })
}

/**
 * Hook to delete a Risk category
 */
export const useDeleteRiskCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteRiskCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_CATEGORY] })
    },
  })
}

/**
 * Hook to update a Risk category
 */
export const useUpdateRiskCategory = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateRiskCategory,
    onMutate: async (updatedData: IUpdateRiskCategory) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_RISK_CATEGORY] })
      const previousData = queryClient.getQueryData<IGetRiskCategoryResponse>([QUERY_RISK_CATEGORY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetRiskCategoryResponse>([QUERY_RISK_CATEGORY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, risk_category: updatedData.risk_category } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_RISK_CATEGORY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_CATEGORY] })
    },
  })
}

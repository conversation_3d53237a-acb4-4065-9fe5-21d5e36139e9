@import '/styles/color.scss';

.actionButtons {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: end;
}

.model {
  z-index: 1500 !important;
  backdrop-filter: blur(8px); /* Apply the blur effect */
  border-radius: 12px !important;
}

.title {
  font-size: 16px;
  margin-bottom: 16px;
  font-weight: bold;
}

.selectFieldWrapper {
  min-width: 500px;
  max-width: 500px;
  margin: 30px 0;
}

.multiSelect {
  -webkit-line-clamp: unset !important;
}

.cursorPointer {
  cursor: pointer;
}

import React, { useMemo, useRef } from 'react'
import AttachFileIcon from '@mui/icons-material/AttachFile'
import CloseIcon from '@mui/icons-material/Close'
import DeleteIcon from '@mui/icons-material/Delete'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { Modal, Box, IconButton, Tooltip } from '@mui/material'
import { useFormik } from 'formik'

import styles from './FeedbackPopOver.module.scss'
import Button from '@/src/component/shared/button'
import Textarea from '@/src/component/shared/textArea'
// import { useDeleteFeedback } from '@/src/hooks/useFeedback'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
// import { getFeedbackFileURL } from '@/src/services/feedback'
import { errorToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

interface ModalProps {
  open: boolean
  onClose: () => void
  handleConfirm: (values: any) => void
}

const FeedbackPopOver: React.FC<ModalProps> = ({ open, onClose, handleConfirm }) => {
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  // const { mutate: deleteFeedback } = useDeleteFeedback()
  const submitAttempted = useRef(false)
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  // const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
  //   onSuccess: () => {
  //     successToast(successMsg)
  //     formik.resetForm()
  //   },
  //   onError: (err: any) => {
  //     errorToast(err.response?.data?.message || errorMsg)
  //   },
  // })

  const style = {
    maxHeight: '75%',
    overflow: 'auto',
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '40%',
    bgcolor: '#EAF6FF',
    borderRadius: 3,
    boxShadow: 24,
    p: 5,
  }

  const formik = useFormik({
    initialValues: {
      rating: '',
      workingWell: '',
      doBetter: '',
      attachment: [],
    },
    validateOnChange: false,
    validateOnBlur: false,
    validate: (values) => {
      const errors: Partial<typeof values> = {}

      const isEmpty = !values.workingWell?.trim() && !values.doBetter?.trim() && values.attachment.length === 0

      if (!String(values.rating).trim()) {
        if (submitAttempted.current) errorToast('Rating is required')
        errors.rating = 'Required'
      } else if (isEmpty) {
        if (submitAttempted.current) errorToast('At least one field is required')
        errors.workingWell = 'Add at least one comment or attachment'
      }

      return errors
    },
    enableReinitialize: true,
    onSubmit: async (values, { resetForm }) => {
      const newFiles = values.attachment.filter((file) => !(file as any).file_path)
      const existingFiles = values.attachment.filter((file) => (file as any).file_path)

      handleConfirm({
        ...values,
        attachment: newFiles, // send only new files
        existing_attachments: existingFiles,
      })

      resetForm()
      submitAttempted.current = false
    },
  })

  const onCloseModal = () => {
    onClose()
    formik.resetForm()
  }

  // const handleDeleteEntity = async (name: string) => {
  //   const deletePayload = {
  //     name: name,
  //     period: currentPeriod,
  //     id: feedback?.id,
  //   }

  //   deleteFeedback(
  //     deletePayload,
  //     handleMutationCallbacks('Delete attachment successfully', 'Failed to delete attachment record'),
  //   )
  // }

  const handleCustomSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    submitAttempted.current = true
    const errors = await formik.validateForm()
    if (Object.keys(errors).length > 0) return

    formik.handleSubmit()
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const renderToolTip = () => (
    <div style={{ fontFamily: 'poppins' }}>
      <span style={{ display: 'block', marginBottom: '8px', fontSize: '14px' }}>Please note:</span>
      <ul style={{ listStyleType: 'none', padding: 0, margin: 0 }}>
        <>
          <li style={{ marginBottom: '4px' }}>
            <span>File size :</span> Should be 5MB or less For Image.
          </li>
        </>
        <li style={{ marginBottom: '4px' }}>
          <span>Allowed file types : </span>
          {'jpg, jpeg, png, pdf, docs'}
        </li>
        <li style={{ marginBottom: '4px' }}>
          <span>File names :</span> Should contain only letters, numbers, commas, apostrophes, or dashes.
        </li>
        <li>
          <span>File selection : </span>A maximum of 10 files can be selected.
        </li>
      </ul>
    </div>
  )

  return (
    <Modal open={open} onClose={onCloseModal}>
      <Box sx={style}>
        <div className={styles.modalHeader}>
          <h3>Feedback</h3>
          <IconButton onClick={onCloseModal}>
            <CloseIcon />
          </IconButton>
        </div>

        <form onSubmit={(e) => handleCustomSubmit(e)} className={styles.feedbackModal}>
          <div className={styles.rating}>
            <p className={styles.title}>Rating</p>
            <div className={styles.ratingButtons}>
              {[1, 2, 3, 4, 5].map((num) => (
                <button
                  type="button"
                  key={num}
                  className={`${styles.ratingButton} ${formik.values.rating == String(num) ? styles.active : ''}`}
                  onClick={() => formik.setFieldValue('rating', String(num))}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
          <div>
            <p className={styles.title}>What is working well?</p>
            <Textarea
              className={styles.entityFieldTextArea}
              name="workingWell"
              value={formik.values.workingWell}
              onChange={formik.handleChange}
              minRows={1}
              maxRows={1}
            />
          </div>
          <div>
            <p className={styles.title}>What can we do better?</p>
            <Textarea
              className={styles.entityFieldTextArea}
              name="doBetter"
              value={formik.values.doBetter}
              onChange={formik.handleChange}
              minRows={1}
              maxRows={1}
            />
          </div>
          <div>
            <p className={styles.title}>Attachment</p>
            <div className={styles.attachmentContainer}>
              <Tooltip
                title={renderToolTip()}
                arrow
                componentsProps={{
                  tooltip: {
                    sx: {
                      background: 'linear-gradient(135deg, #3874cb 0%, #1e4f8f 100%)',
                      color: '#fff',
                      borderRadius: '8px',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
                      fontSize: '12px',
                      padding: '16px',
                    },
                  },
                  arrow: {
                    sx: {
                      color: '#3874cb',
                    },
                  },
                }}
              >
                <label htmlFor="file-upload" className={styles.fileInputWrapper}>
                  <span className={styles.filePlaceholder}>{'PNG, JPEG, PDF, Doc only'}</span>
                  <input
                    ref={fileInputRef}
                    disabled={!isEditForUser}
                    id="file-upload"
                    type="file"
                    accept=".png,.jpeg,.jpg,.pdf,.doc,.docx"
                    multiple
                    hidden
                    onChange={(e) => {
                      const files = Array.from(e.target.files || [])
                      const currentFiles = formik.values.attachment || []
                      const combinedFiles = [...currentFiles, ...files]

                      if (combinedFiles.length > 10) {
                        errorToast('You can only upload a maximum of 10 files.')
                        return
                      }

                      formik.setFieldValue('attachment', combinedFiles)
                    }}
                  />
                  <IconButton
                    sx={{
                      color: 'black',
                      transition: 'transform 0.2s ease-in-out',
                      padding: 0,
                    }}
                    onClick={() => {
                      if (isEditForUser && fileInputRef.current) {
                        fileInputRef.current.click()
                      }
                    }}
                  >
                    <AttachFileIcon sx={{ transform: 'rotate(45deg)' }} />
                  </IconButton>
                </label>
              </Tooltip>
            </div>

            {formik.values.attachment?.length > 0 && (
              <div className={styles.previewSection}>
                {formik.values.attachment.map((file: any, index: number) => {
                  // const isFromBackend = !!file.file_path

                  const getFileName = () => {
                    // if (isFromBackend) {
                    //   const fullName = file.file_path.split('/').pop() || ''
                    //   const byUnderscore = fullName.substring(fullName.lastIndexOf('_') + 1)
                    //   const byDash = fullName.substring(fullName.lastIndexOf('-') + 1)
                    //   return byUnderscore.length < byDash.length ? byUnderscore : byDash
                    // }
                    return file.name
                  }

                  return (
                    <div className={styles.fileRow} key={index}>
                      <span className={styles.fileName}>{getFileName()}</span>
                      <div className={styles.actionIcons}>
                        {/* {isFromBackend ? (
                          <>
                            <IconButton
                              onClick={async () => {
                                try {
                                  const fileName = file.file_path
                                  const viewUrl = await getFeedbackFileURL(fileName)
                                  const isPDF = fileName.toLowerCase().endsWith('.pdf')
                                  const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName)
                                  const isDoc = /\.(doc|docx)$/i.test(fileName)

                                  if (isPDF) {
                                    const googleViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(viewUrl.url)}&embedded=true`
                                    const newWindow = window.open(googleViewerUrl, '_blank')
                                    if (!newWindow) {
                                      errorToast('Popup blocked. Please allow popups for this site.')
                                    }
                                  } else if (isImage) {
                                    const newTab = window.open()
                                    if (newTab) {
                                      newTab.document.write(`
                                        <html>
                                          <head><title>Image Preview</title></head>
                                          <body style="margin:0">
                                            <img src="${viewUrl.url}" style="max-width:100vw;max-height:100vh;display:block;margin:auto;" />
                                          </body>
                                        </html>
                                      `)
                                      newTab.document.close()
                                    } else {
                                      errorToast('Popup blocked. Please allow popups for this site.')
                                    }
                                  } else if (isDoc) {
                                    const googleViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(viewUrl.url)}&embedded=true`
                                    const newWindow = window.open(googleViewerUrl, '_blank')
                                    if (!newWindow) {
                                      errorToast('Popup blocked. Please allow popups for this site.')
                                    }
                                  } else {
                                    errorToast('Unsupported file format')
                                  }
                                } catch (err) {
                                  errorToast('Failed to open file')
                                }
                              }}
                            >
                              <VisibilityIcon />
                            </IconButton>
                            <IconButton
                              onClick={() => {
                                handleDeleteEntity(file.file_path.split('/').pop())
                              }}
                              disabled={!isEditForUser}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </>
                        ) : ( */}
                        <>
                          <IconButton onClick={() => window.open(URL.createObjectURL(file), '_blank')}>
                            <VisibilityIcon />
                          </IconButton>
                          <IconButton
                            onClick={() => {
                              const updatedFiles = [...formik.values.attachment]
                              updatedFiles.splice(index, 1)
                              formik.setFieldValue('attachment', updatedFiles)
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </>
                        {/* )} */}
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
          <div className={styles.feedbackbtn}>
            <Button type="submit" disabled={!isEditForUser}>
              Submit
            </Button>
          </div>
        </form>
      </Box>
    </Modal>
  )
}

export default FeedbackPopOver

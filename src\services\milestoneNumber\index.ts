import { IMilestoneNumberPayload, IUpdateMilestoneNumber } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_MILESTONE_NUMBER_URL = `/master-milestone-number`

// Get Master Milestone Number data
export const getMilestoneNumber = async () => {
  const response = await api.get(`${MASTER_MILESTONE_NUMBER_URL}`)
  return response?.data
}
export const deleteMilestoneNumber = async (data: number) => {
  const response = await api.delete(`${MASTER_MILESTONE_NUMBER_URL}/${data}`)
  return response?.data
}

// Create Master Milestone Number entry
export const createMilestoneNumber = async (data: IMilestoneNumberPayload) => {
  const response = await api.post(`${MASTER_MILESTONE_NUMBER_URL}`, data)
  return response
}

// Update Master Milestone Number entry
export const updateMilestoneNumber = async (data: IUpdateMilestoneNumber): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_MILESTONE_NUMBER_URL}/${data.id}`, { ...rest })
  return response
}

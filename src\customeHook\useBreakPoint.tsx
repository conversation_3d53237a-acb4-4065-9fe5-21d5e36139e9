import { useState, useEffect } from 'react'

const breakpoints = {
  mobile: 319,
  tablet: 768,
  laptop: 1024,
  desktop: 1440,
  wideScreen: 2560,
}

export const useBreakpoint = () => {
  const getBreakpoint = (width: number) => {
    if (width >= breakpoints.wideScreen) return 'wideScreen'
    if (width >= breakpoints.desktop) return 'desktop'
    if (width >= breakpoints.laptop) return 'laptop'
    if (width >= breakpoints.tablet) return 'tablet'
    return 'mobile'
  }

  const [breakpoint, setBreakpoint] = useState<string>(getBreakpoint(window.innerWidth))

  useEffect(() => {
    const updateBreakpoint = () => setBreakpoint(getBreakpoint(window.innerWidth))

    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return breakpoint
}

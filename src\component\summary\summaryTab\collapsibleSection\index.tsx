import React from 'react'
import { Checkbox, Tooltip } from '@mui/material'
import styles from './CollapsibleSection.module.scss'
import TypographyField from '@/src/component/shared/typography'
import RotateIcon from '@/src/component/svgImages/rotateIcon'

interface CollapsibleSectionProps {
  title: string
  icon: React.ReactNode
  isCollapsed: boolean
  toggleCollapse: () => void
  lastUpdatedText: string
  updatedBy: string
  hasError?: boolean
  children?: React.ReactNode
  onChange?: () => void
  checked?: boolean
  edit?: boolean
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  edit,
  title,
  icon,
  isCollapsed,
  toggleCollapse,
  lastUpdatedText,
  updatedBy,
  hasError = false,
  children,
  checked,
  onChange,
}) => {
  return (
    <div className={styles.accordionWrapper} onClick={toggleCollapse}>
      <div className={styles.collapseContainer}>
        <div className={styles.accordionTitle}>
          {icon}
          <TypographyField variant="bodyBold" className={styles.collapseName} text={title} />
          <Tooltip
            title="External Look Up For Health & Safety"
            arrow
            componentsProps={{
              tooltip: {
                sx: {
                  fontFamily: 'poppins',
                },
              },
            }}
          >
            <span>
              {title === 'Health & Safety' && (
                <Checkbox
                  disabled={!edit}
                  checked={checked}
                  onClick={(e) => e.stopPropagation()}
                  onChange={onChange}
                  className={styles.checkbox}
                />
              )}
            </span>
          </Tooltip>
        </div>
        <div className={styles.updateDetail}>
          <div>
            <TypographyField variant="bodyBold" className={styles.lastUpdatedText} text={updatedBy} />
            <div className={styles.timerIcon}>
              <TypographyField variant="thin" className={styles.lastUpdatedText} text={lastUpdatedText} />
            </div>
          </div>
          {hasError && (
            <div className={styles.error}>
              <p>The total percentage of governance values should be 100%.</p>
              {/* <p>The total percentage of governance values does not equal 100%.</p> */}
            </div>
          )}
          <RotateIcon className={styles.cursorPointer} rotate={isCollapsed ? 'up' : 'down'} />
        </div>
      </div>
      {isCollapsed && <div className={styles.collapseContent}>{children}</div>}
    </div>
  )
}

export default CollapsibleSection

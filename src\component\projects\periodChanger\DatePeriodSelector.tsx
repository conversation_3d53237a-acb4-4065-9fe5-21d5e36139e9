import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { LocalizationProvider, PickersActionBarProps, PickersDayProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
import styles from './PeriodDatePicker.module.scss'
import Button from '../../shared/button'
import TypographyField from '../../shared/typography'
import CalendarIcon from '../../svgImages/calenderIcon'
import { DARK_200 } from '@/src/constant/color'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { parseDateFromString } from '@/src/utils/dateUtils'

type DateValue = string | number | Date | null

type PeriodDatePickerProps = {
  label?: string
  value: DateValue
  onChange?: (date: Date | null) => void
  minDate?: DateValue
  maxDate?: DateValue
  error?: boolean
  helperText?: string
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: object
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void
  shouldDisableDate?: (date: Date) => boolean
  showActionButtons?: boolean
  CustomDay?: React.ComponentType<PickersDayProps<Date>>
  onClose?: any
}

const DatePeriodSelector: React.FC<PeriodDatePickerProps> = ({
  label,
  value,
  onChange,
  minDate,
  maxDate,
  error = false,
  helperText,
  disabled = false,
  required = false,
  name,
  sx = {},
  onBlur,
  shouldDisableDate,
  showActionButtons = false,
  CustomDay,
  onClose,
  ...props
}) => {
  const { mainPeriod } = useMasterPeriod() // Get the current period date

  // Local state for the picker value and open state
  const [pickerValue, setPickerValue] = useState<Date | null>(value ? parseDateFromString(value as string) : null)
  const [open, setOpen] = useState(false)

  // When parent value changes, update local pickerValue
  useEffect(() => {
    setPickerValue(value ? parseDateFromString(value as string) : null)
  }, [value])

  // When opening the picker, reset pickerValue to the last selected value
  const handleOpen = () => {
    setPickerValue(value ? parseDateFromString(value as string) : null)
    setOpen(true)
  }
  const handleClose = () => {
    setOpen(false)
    if (onClose) onClose()
  }

  // Only call onChange when Add or Current Period is clicked
  const handleAccept = () => {
    if (onChange) onChange(pickerValue)
  }

  const handleCurrentPeriod = () => {
    if (onChange) onChange(new Date(mainPeriod))
  }

  const CustomActionBar: React.FC<PickersActionBarProps> = ({ onAccept, onCancel }) => (
    <div className={styles.buttonActions} style={{ display: 'flex', gap: '10px' }}>
      {/* <Button variant="contained" onClick={handleCurrentPeriod}>
        Current Period
      </Button> */}
      <a
        href="#"
        onClick={(e) => {
          e.preventDefault()
          onChange && onChange(new Date(mainPeriod))
        }}
        className={styles.currentPeriodLink}
      >
        Current Period
      </a>
      <Button
        color="secondary"
        onClick={() => {
          if (onCancel) onCancel()
        }}
      >
        Cancel
      </Button>
      <Button onClick={handleAccept}>Add</Button>
    </div>
  )

  const parsedMinDate = useMemo(() => (minDate ? new Date(minDate) : undefined), [minDate])
  const parsedMaxDate = useMemo(() => (maxDate ? new Date(maxDate) : undefined), [maxDate])

  return (
    <div className={styles.datePickerWrapper}>
      {label && <TypographyField style={{ color: DARK_200 }} variant="caption" text={label} />}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          format="dd/MM/yyyy"
          closeOnSelect={false}
          minDate={parsedMinDate}
          maxDate={parsedMaxDate}
          disabled={disabled}
          value={pickerValue}
          onChange={setPickerValue}
          shouldDisableDate={shouldDisableDate}
          open={open}
          onOpen={handleOpen}
          onClose={handleClose}
          slots={{
            actionBar: showActionButtons ? CustomActionBar : undefined,
            openPickerIcon: () => <CalendarIcon />,
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay,
          }}
          slotProps={{
            popper: {
              sx: {
                // zIndex: '99',
              },
            },
            layout: { sx: { display: 'block', textAlign: 'end' } },
            textField: {
              sx: { '& .Mui-error': { border: '1px solid #ffffff' }, ...sx },
              error,
              helperText,
              required,
              placeholder: 'Select a Period',
              name,
              value: pickerValue,
              onBlur,
              onClick: handleOpen,
            },
          }}
        />
      </LocalizationProvider>
    </div>
  )
}

export default DatePeriodSelector

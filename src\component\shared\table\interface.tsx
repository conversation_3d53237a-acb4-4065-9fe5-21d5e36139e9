export interface ITableColumn {
  key: string
  label: string
  filterCell?: () => JSX.Element | undefined
  renderCell?: (value: any, row: Record<string, any>, rowIndex: number) => JSX.Element | undefined
  cellStyle?: React.CSSProperties
  headerStyle?: React.CSSProperties
  isRabin?: boolean
  isDisplay?: boolean
}

export interface ITableProps {
  columns: ITableColumn[]
  data: Record<string, any>[]
}

export interface TableHeaderProps {
  columns: ITableColumn[]
}

export interface TableBodyProps {
  data: Record<string, any>[]
  columns: ITableColumn[]
}

export interface TableRowProps {
  row: Record<string, any>
  rowIndex: number
  columns: ITableColumn[]
}

export interface TableCellProps {
  value: any
  column: ITableColumn
  rowIndex: number
  row: Record<string, any>
}

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import IBatchUploadStatus from './interface'
import { StatusEnum } from '../types'
import { ApiPostNoAuth, ApiGetNoAuth } from '@/src/api'

const initialState: IBatchUploadStatus = {
  batchUploadStatus: StatusEnum.Idle,
}

export const batchUpload = createAsyncThunk(
  'batchUpload/files',
  async ({ formData, categoryKey }: { formData: FormData; categoryKey: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/files/batch-upload?sheetName=${categoryKey}`, formData, {
        isFormData: true,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

const batchUploadSlice = createSlice({
  name: 'batchUpload',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Reducers for file upload
    builder.addCase(batchUpload.pending, (state) => {
      state.batchUploadStatus = StatusEnum.Pending
    })
    builder.addCase(batchUpload.fulfilled, (state, action) => {
      state.batchUploadStatus = StatusEnum.Success
    })
    builder.addCase(batchUpload.rejected, (state) => {
      state.batchUploadStatus = StatusEnum.Failed
    })
  },
})

// Export actions from slice
export const {} = batchUploadSlice.actions

// Export the reducer
export default batchUploadSlice.reducer

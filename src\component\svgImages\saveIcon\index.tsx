import React, { ReactElement, forwardRef } from 'react'

interface SaveIconProps {
  className?: string
  color?: string
  onClick?: (e: any) => void
}

const SaveIcon: React.FC<SaveIconProps> = forwardRef<SVGSVGElement, SaveIconProps>((props, ref): ReactElement => {
  return (
    <svg width="17" height="16" viewBox="0 0 17 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        d="M6.5 3.50016C6.22386 3.50016 6 3.72402 6 4.00016C6 4.27631 6.22386 4.50016 6.5 4.50016H10.5C10.7761 4.50016 11 4.27631 11 4.00016C11 3.72402 10.7761 3.50016 10.5 3.50016H6.5Z"
        fill={props.color ? props.color : '#EAEAEA'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.46167 0.833496C7.07996 0.833486 5.99144 0.833479 5.14085 0.949076C4.26748 1.06777 3.57073 1.31662 3.02323 1.87005C2.47646 2.42274 2.23128 3.12484 2.1142 4.0051C1.99998 4.86384 1.99999 5.96324 2 7.36088V10.7595C1.99999 11.7645 1.99999 12.5603 2.06401 13.1594C2.12727 13.7514 2.26298 14.2855 2.65056 14.6427C2.9615 14.9293 3.35474 15.1099 3.77443 15.1579C4.29941 15.218 4.78959 14.9673 5.27246 14.6253C5.76108 14.2792 6.35384 13.7549 7.10169 13.0934L7.12603 13.0719C7.47274 12.7652 7.70748 12.5583 7.90333 12.4151C8.09261 12.2767 8.20818 12.2267 8.30559 12.207C8.43395 12.1812 8.56605 12.1812 8.69441 12.207C8.79182 12.2267 8.90739 12.2767 9.09667 12.4151C9.29252 12.5583 9.52726 12.7652 9.87397 13.0719L9.89835 13.0935C10.6462 13.7549 11.2389 14.2792 11.7275 14.6253C12.2104 14.9673 12.7006 15.218 13.2256 15.1579C13.6453 15.1099 14.0385 14.9293 14.3494 14.6427C14.737 14.2855 14.8727 13.7514 14.936 13.1594C15 12.5603 15 11.7645 15 10.7595V7.36086C15 5.96324 15 4.86383 14.8858 4.0051C14.7687 3.12484 14.5235 2.42274 13.9768 1.87005C13.4293 1.31662 12.7325 1.06777 11.8591 0.949076C11.0086 0.833479 9.92004 0.833486 8.53833 0.833496H8.46167ZM3.73413 2.57334C4.06531 2.23857 4.51501 2.04332 5.27552 1.93997C6.05097 1.83458 7.0715 1.8335 8.5 1.8335C9.9285 1.8335 10.949 1.83458 11.7245 1.93997C12.485 2.04332 12.9347 2.23857 13.2659 2.57334C13.5978 2.90885 13.7919 3.36562 13.8945 4.13695C13.999 4.92211 14 5.95498 14 7.39851V10.7274C14 11.7716 13.9993 12.5136 13.9417 13.0531C13.8826 13.606 13.7736 13.8135 13.6718 13.9074C13.5159 14.051 13.3199 14.1406 13.1119 14.1644C12.9788 14.1797 12.7557 14.1281 12.3056 13.8093C11.8665 13.4983 11.3141 13.0106 10.5365 12.3229L10.519 12.3074C10.1942 12.0201 9.92479 11.7818 9.68693 11.6079C9.43828 11.426 9.18749 11.2863 8.89188 11.2267C8.63318 11.1746 8.36682 11.1746 8.10812 11.2267C7.81251 11.2863 7.56172 11.426 7.31307 11.6079C7.07522 11.7818 6.80581 12.0201 6.48097 12.3074L6.46351 12.3229C5.68592 13.0106 5.13351 13.4983 4.69443 13.8093C4.2443 14.1281 4.02121 14.1797 3.88812 14.1644C3.68012 14.1406 3.48412 14.051 3.32825 13.9074C3.22642 13.8135 3.11743 13.606 3.05835 13.0531C3.00069 12.5136 3 11.7716 3 10.7274V7.39851C3 5.95498 3.00104 4.92211 3.10547 4.13695C3.20806 3.36562 3.40222 2.90885 3.73413 2.57334Z"
        fill={props.color ? props.color : '#EAEAEA'}
      />
    </svg>
  )
})
SaveIcon.displayName = 'SaveIcon'

export default SaveIcon

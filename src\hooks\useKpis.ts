import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createMaster<PERSON>pi, deleteMaster<PERSON><PERSON>, getMaster<PERSON>pis, updateMaster<PERSON><PERSON> } from '../services/kpi'
import { IGetMasterKpiResponse, IUpdateKpi } from '../services/kpi/interface'

export const QUERY_KPIS = 'kpis'

/**
 * Hook to fetch Kpis
 * @param enabled - Enables or disables the query
 */
export const useGetKpis = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterKpiResponse>({
    queryKey: [QUERY_KPIS],
    queryFn: getMasterKpis,
    enabled,
  })

  return {
    kpis: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/** Hook to create a Kpis */
export const useCreateKpis = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterKpi,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KPIS] })
    },
  })
}

/**
 * Hook to delete a Kpis
 */
export const useDeleteKpis = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterKpi,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KPIS] })
    },
  })
}

/**
 * Hook to update a Kpis
 */
export const useUpdateKpis = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterKpi,
    onMutate: async (updatedData: IUpdateKpi) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_KPIS] })
      const previousData = queryClient.getQueryData<IGetMasterKpiResponse>([QUERY_KPIS])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterKpiResponse>([QUERY_KPIS], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, project_type: updatedData.project_type, kpi: updatedData.kpi }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_KPIS], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KPIS] })
    },
  })
}

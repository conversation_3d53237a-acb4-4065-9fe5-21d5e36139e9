import { multiSelectOption, userType, whiteListedUserList } from '../constant/enum'
import { ICurrentUser } from '../redux/authorization/interface'

/**
 * Checks if the given user has a role that includes 'Design Team'.
 *
 * This function performs the following:
 * 1. Accesses the `role_name` property of the user's `role` object.
 * 2. Checks if the `role_name` includes the substring 'Design Team'.
 * 3. Returns true if 'Design Team' is found, otherwise returns false.
 *
 * @param user - An object representing the user, which may include a `role` object with a `role_name` property.
 * @returns True if the user's role includes 'Design Team', otherwise false.
 */
export const hasDesignTeamRole = (user: { role?: { role_name?: string } }) =>
  user?.role?.role_name?.includes('Design Team') || false

/**
 * Checks if the given user type is 'USER'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'USER', otherwise false.
 *
 * @example
 * const isUser = isUser('USER');
 * // Returns true if 'type' is 'USER'
 */
export const isUser = (type: string) => {
  return type === userType.USER
}

/**
 * Checks if the given user type is 'SUPER_USER'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'SUPER_USER', otherwise false.
 *
 * @example
 * const isSuperUser = isSuperUser('SUPER_USER');
 * // Returns true if 'type' is 'SUPER_USER'
 */
export const isSuperUser = (type: string) => {
  return type === userType.SUPER_USER
}

/**
 * Checks if the given user type is 'SUPER_ADMIN'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'SUPER_ADMIN', otherwise false.
 *
 * @example
 * const isSuperAdmin = isSuperAdmin('SUPER_ADMIN');
 * // Returns true if 'type' is 'SUPER_ADMIN'
 */
export const isSuperAdmin = (type: string) => {
  return type === userType.SUPER_ADMIN
}

// first freezeType  if is unfreeze return true
//userFreeze he  toh check kro condition Update Over User Freeze Period
export const canEditUser = (
  currentUser: ICurrentUser,
  freezeType: string,
  currentPeriod: string,
  mainPeriod: string,
) => {
  const { role, user_type } = currentUser

  const hasPermission = role.view_permissions.includes('Update Over User Freeze Period')

  if (currentPeriod !== mainPeriod) {
    return false
  }

  switch (freezeType) {
    case 'unFreeze':
      return true

    case 'userFreeze':
      return hasPermission

    default:
      return false
  }

  // if (currentUser.role.view_permissions.includes('Update Over User Freeze Period')) return true
  // else if (
  //   freezeType === 'userFreeze' &&
  //   (currentUser.user_type === userType.USER || currentUser.user_type === userType.SUPER_USER)
  // )
  //   return false
  // if (freezeType === 'finalFreeze' && currentUser.user_type === userType.SUPER_ADMIN) return false
  // return false
}

/*
 * Checks if the given user has a role that includes 'Edit Baseline Plan Finish Date And Plan Duration'.
 */
export const hasUpdateBaselinePlanFinishDateAndPlanDurationPermission = (user: any) => {
  return user?.role?.view_permissions?.includes('Edit Baseline Plan Finish Date And Plan Duration') || false
}

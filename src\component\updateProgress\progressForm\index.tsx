import React, { useEffect, useMemo, useState } from 'react'
import { Box, Chip, InputAdornment, Stack, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { FormikValues } from 'formik'
import { useRouter } from 'next/router'
import Accordion from './accordion'
import BasicDetails from './basicDetails'
import KeyAchievements from './keyAchievements'
import KeyHighlight from './keyHighlight'
import KeyRisks from './keyRisks'
import ProjectManagement from './projectManagement'
import styles from './StatusForm.module.scss'
import { predefinedProjectStatusOrder, reorderArray } from '../../customCells/headerCell/generateOptions'
import ComboBox from '../../shared/combobox'
import CustomComboBox from '../../shared/combobox/combobox'
import MultiAutoSelect from '../../shared/multiAutoSelect'
import TextInputField from '../../shared/textInputField'
import PercentageIcon from '../../svgImages/percentageIcon'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
  MULTI_SELECT_SEPARATOR,
} from '@/src/constant/stageStatus'
import { useGetDesignManager } from '@/src/hooks/useDesignManager'
import { useGetProjectToPhase } from '@/src/hooks/useMasterProjectToPhase'
import { useOptions } from '@/src/hooks/useOptions'
import { useGetProcurementManagers } from '@/src/hooks/useProcurementManagement'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import { useGetSvp } from '@/src/hooks/useSvp'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILookupProjectToPhase, IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { getMasterOneProject } from '@/src/services/projects'
import {
  convertMultiSelectOption,
  getMultiValues,
  getValue,
  getValueForMultiSelectOption,
  populateDropdownOptions,
  prepareDropdownOptions,
} from '@/src/utils/arrayUtils'
import { findLatestDate } from '@/src/utils/dateUtils'
import { getLatestUpdatedBy } from '@/src/utils/progressForm/getLatestUpdatedBy'
import { redirectToStatus } from '@/src/utils/progressForm/redirectToStatus'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'

interface StatusFormProps {
  formik: FormikValues
  edit: boolean
  projectName: string
  isEditForUser: boolean
}

const ProgressForm: React.FC<StatusFormProps> = ({ formik, edit, projectName, isEditForUser }) => {
  const router = useRouter()
  //hook
  const [isStatusChanged, setIsStatusChanged] = useState(false)
  const [expandedSections, setExpandedSections] = useState({
    basicDetails: true,
    projectManagement: true,
    keyHighlight: true,
    keyRisk: true,
    keyAchievement: true,
  })

  //custom-hook
  const { currentUser } = useAuthorization()
  const { keyAchievements } = useKeyAchievement()
  const { areaOfConcerns } = useAreaOfConcern()
  const { designManagers } = useGetDesignManager()
  const { status, statuses, getStatusApi } = useStatus()
  const { currentPeriod } = useMasterPeriod()
  // TODO
  const { svps } = useGetSvp()
  const { procureManagements } = useGetProcurementManagers()

  const getProjectPayload = {
    projectName: projectName,
    period: currentPeriod,
  }

  /**
   * TODO: Remove old code.
   */
  /*   const findIdsWithMinSortingOrder = () => {
    const data: any[] = statuses
      ?.filter((item) => item?.stage_status === 'Design' && item?.phase === status?.phase)
      ?.map((res) => {
        return {
          id: res?.id,
          phase: res?.phase,
          stage_status: res?.stage_status,
          sub_stage: res?.sub_stage,
          sorting_order: res?.project_status_sorting_order,
        }
      })
    const minOrder = Math.min(...data.map((item) => +item?.sorting_order))
    return data.filter((item) => +item.sorting_order === minOrder).map((item) => item.id)
  } */

  // NOTO: Find first created design stage of selected phase
  /* const isFirstDesignStageOfPhase = useMemo(() => {
    const findFirstDesignStage = findIdsWithMinSortingOrder()
    return status?.id === findFirstDesignStage[0] && status?.stage_status === 'Design' && edit
  }, [edit, status, formik]) */

  const {
    data: project,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: [PROJECT_QUERY_KEY, projectName, currentPeriod],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  useEffect(() => {
    refetch()
  }, [projectName])

  const keyAchievementLastUpdates = useMemo(() => {
    return populateDropdownOptions(keyAchievements, 'last_updated')
  }, [keyAchievements])

  const keyRiskLastUpdates = useMemo(() => {
    return populateDropdownOptions(areaOfConcerns, 'last_updated')
  }, [areaOfConcerns])

  const designManagerOptions = useMemo(() => {
    return prepareDropdownOptions(designManagers, 'design_manager')
    // return populateDropdownOptions(designManagers, 'design_manager')
  }, [designManagers])

  const DeliveryPMOptions = useMemo(() => {
    return prepareDropdownOptions(svps, 'delivery_project_manager')
    // return populateDropdownOptions(svps, 'delivery_project_manager')
  }, [svps])

  const procureManagementsOptions = useMemo(() => {
    return prepareDropdownOptions(procureManagements, 'procurement_manager')
  }, [procureManagements])

  useEffect(() => {
    currentPeriod &&
      getStatusApi({
        period: currentPeriod,
        project_name: projectName,
      })
  }, [currentPeriod])

  // Update form values and handle status change if `isStatusChanged` is true
  const handleStatusUpdate = (phaseStatusOptions: any[]) => {
    formik.setFieldValue('master_project_stage_status_id', phaseStatusOptions[0]?.MasterProjectStageStatus?.id)
    formik.setFieldValue(
      'stage_status',
      phaseStatusOptions[0]?.MasterProjectStageStatus?.master_project_stage_status_id,
    )
    formik.setFieldValue('master_project_sub_stage_id', phaseStatusOptions[0]?.MasterProjectSubStage?.id || null)
    formik.setFieldValue('project_sub_stage', phaseStatusOptions[0]?.MasterProjectSubStage?.project_sub_stage || '')
    redirectToStatus(
      formik.values.project_to_project_phase_ids,
      phaseStatusOptions[0]?.MasterProjectStageStatus?.master_project_stage_status_id as string,
      null,
      statuses,
      router,
    )
    setIsStatusChanged(false)
  }

  const { phases, stage_status, sub_stage } = useOptions(
    statuses,
    formik?.values?.project_to_project_phase_ids,
    currentUser,
    handleStatusUpdate,
    isStatusChanged,
  )

  const sortStageOptions = useMemo(() => {
    return reorderArray(stage_status, predefinedProjectStatusOrder)
  }, [stage_status])

  const phaseOptions: { label: string; value: number | number[]; tooltip: string }[] = useMemo(() => {
    return (
      phases
        ?.filter((group) => group && group.length > 0)
        .map((group) => {
          if (group.length > 1) {
            const phaseNames = group.map((p: ILookupProjectToPhase) => p.phase).join(', ')
            return {
              label: 'Multi phases',
              value: group.map((p: ILookupProjectToPhase) => p.id),
              tooltip: phaseNames,
            }
          }

          const p = group[0]
          return {
            label: p.phase,
            value: p.id,
            tooltip: '',
          }
        }) || []
    )
  }, [phases, formik.values.project_to_project_phase_ids])

  // const option = useMemo(() => {
  //   const statusesByPermission = statuses.filter((item: IStatus) =>
  //     getStageStatusByPermission(currentUser.role).includes(item.stage_status),
  //   )

  //   const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
  //     statusesByPermission.map((status) => ({
  //       ...status,
  //       project_status_sorting_order: Number(status.project_status_sorting_order),
  //     })),
  //     'project_status_sorting_order',
  //     true,
  //   )
  //   const phaseOptions = getUniqueValues(populateDropdownOptions(sortedStatusesByPermission, 'phase'))

  //   const statusesByPhase = statuses.filter((item) => formik.values.phase === item?.phase)

  //   const sortedStatusesByPhase = sortArrayByKeyWithTypeConversion(
  //     statusesByPhase.map((status) => ({
  //       ...status,
  //       project_status_sorting_order: Number(status.project_status_sorting_order),
  //     })),
  //     'project_status_sorting_order',
  //     true,
  //   )

  //   const stage_status_option = getUniqueValues(populateDropdownOptions(sortedStatusesByPhase, 'stage_status'))

  //   if (isStatusChanged) {
  //     handleStatusUpdate(statusesByPhase)
  //   }

  //   const sub_stage = getUniqueValues([...populateDropdownOptions(sortedStatusesByPhase, 'sub_stage')])

  //   return { phase: phaseOptions, stage_status: stage_status_option, sub_stage }
  // }, [statuses, project, formik?.values?.phase])

  const toggleSection = (section: keyof typeof expandedSections) => {
    const updatedSections = { ...expandedSections }
    updatedSections[section] = !expandedSections[section]
    setExpandedSections(updatedSections)
  }

  const handleMultiSelectChange = (field: keyof typeof formik.values, selectedOptions: string[]) => {
    const ids = selectedOptions.map((item) => item).filter((id) => id !== null && id !== '')
    formik.setValues({
      ...formik.values,
      [field]: Array.from(new Set(ids)),
    })
  }

  /**
   * TODO: Remove old code
   */
  // const projectPhaseOption: any = useMemo(() => {
  //   const optionMap = new Map()
  //   statuses.forEach((item: any) => {
  //     if (item.phase?.includes(MULTI_SELECT_SEPARATOR)) {
  //       const multiCategories = item.phase.split(MULTI_SELECT_SEPARATOR)
  //       multiCategories.forEach((category: any) => {
  //         optionMap.set(category, { id: category, name: category })
  //       })
  //     } else {
  //       optionMap.set(item.phase, { id: item.phase, name: item.phase })
  //     }
  //   })
  //   return Array.from(optionMap.values()).filter((item: any) => item.name?.length)
  // }, [statuses])

  //TODO: Comment old code
  // const getMultiSelectedValue = (value: string) => {
  //   if (value.includes(MULTI_SELECT_SEPARATOR)) {
  //     return value.split(MULTI_SELECT_SEPARATOR)
  //   }
  //   return typeof value === 'string' ? [value] : value
  // }

  //TODO: Comment old code
  // const handleMultiAutoSelectChange = (value: string[], fieldName: 'phase') => {
  //   // setEdit(true)
  //   setIsStatusChanged(true)
  //   const computedValue = value.length > 0 ? value.join(MULTI_SELECT_SEPARATOR) : ''
  //   redirectToStatus(computedValue, formik.values?.stage_status, null, statuses, router)
  //   formik.setValues({
  //     ...formik.values,
  //     [fieldName]: value?.filter((item: string) => item.length),
  //   })
  // }

  const currentPhase = useMemo(() => {
    return formik.values.project_to_project_phase_ids
      ? getMultiValues(phaseOptions, formik.values.project_to_project_phase_ids)
      : null
  }, [phaseOptions, formik.values.project_to_project_phase_ids])

  return (
    <div className={styles.container}>
      <div className={styles.phaseChipContainer}>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {currentPhase &&
            currentPhase?.length > 0 &&
            currentPhase.map((phase, index) => {
              return (
                phase.tooltip &&
                phase?.tooltip?.split(',').map((label) => (
                  <Chip
                    className={styles.phaseChip}
                    key={index + `${label}`}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="body2" className={styles.phaseChipText}>
                          {label}
                        </Typography>
                      </Box>
                    }
                    variant="outlined"
                    sx={{
                      backgroundColor: 'white',
                      borderColor: '#e0e0e0',
                      color: '#666666',
                      height: 'auto',
                      py: 0.5,
                      px: 1,
                      '& .MuiChip-label': {
                        fontWeight: 400,
                        px: 1,
                        py: 0.5,
                        color: '#666666',
                      },
                      '& .MuiChip-deleteIcon': {
                        marginLeft: 1,
                        marginRight: -0.5,
                      },
                      '&:hover': {
                        backgroundColor: '#f8f9fa',
                        borderColor: '#d0d0d0',
                      },
                    }}
                  />
                ))
              )
            })}
        </Stack>
      </div>
      <div className={styles.topSection}>
        <CustomComboBox
          options={phaseOptions}
          labelText={'Phase/Package'}
          placeholder="Type of search..."
          className={styles.comboBoxInput}
          // disabled={!edit}
          value={
            // formik.values?.phase
            //   ? {
            //       label: formik.values?.phase?.includes(MULTI_SELECT_SEPARATOR)
            //         ? formik.values?.phase?.split(MULTI_SELECT_SEPARATOR)?.length > 1
            //           ? 'Multi phases'
            //           : formik.values?.phase
            //         : formik.values?.phase,
            //       value: formik.values?.phase,
            //     }
            //   : null
            formik.values.project_to_project_phase_ids
              ? getMultiValues(phaseOptions, formik.values.project_to_project_phase_ids)
              : null
          }
          clearIcon={true}
          onChange={(val) => {
            formik.setValues({
              ...formik.values,
              project_to_project_phase_ids: val?.value ? (Array.isArray(val?.value) ? val.value : [val.value]) : null,
              stage_status: val?.label || null,
              master_project_stage_status_id: val?.value || null,
            })
            setIsStatusChanged(true)
            // redirectToStatus(val?.value, formik.values?.stage_status, null, statuses, router)
          }}
        />

        {/* <MultiAutoSelect
          labelText="Phase/Package"
          placeholder="Select phase..."
          isSubOption={false}
          isSx={false}
          options={projectPhaseOption}
          value={getMultiSelectedValue(formik.values.phase) as unknown as string[]}
          handleSelectedOption={(val) => handleMultiAutoSelectChange(val, 'phase')}
          className={styles.multiSelect}
        /> */}

        <ComboBox
          options={sortStageOptions}
          labelText={'Stage Status'}
          placeholder="Type of search..."
          className={styles.comboBoxInput}
          // disabled={!edit}
          value={
            formik.values?.master_project_stage_status_id
              ? stage_status.length > 0
                ? getValue(stage_status, formik.values?.master_project_stage_status_id)
                : { label: formik.values?.stage_status, value: formik.values?.master_project_stage_status_id }
              : null
          }
          clearIcon={isEditForUser}
          onChange={(val) => {
            formik.setValues({
              ...formik.values,
              stage_status: val?.label || null,
              master_project_stage_status_id: val?.value || null,
            })
            redirectToStatus(formik.values?.phase, val?.label, null, statuses, router)
          }}
          isCustomSorting={true}
        />

        {(stage_status.length > 0
          ? getValue(stage_status, formik.values?.master_project_stage_status_id)?.label
          : formik.values?.stage_status) === 'Design' && (
          <ComboBox
            options={sub_stage}
            labelText={'Sub Stage'}
            placeholder="Type of search..."
            className={styles.comboBoxInput}
            // disabled={!edit}
            value={
              formik.values?.master_project_sub_stage_id
                ? sub_stage.length > 0
                  ? getValue(sub_stage, formik.values?.master_project_sub_stage_id)
                  : { label: formik.values?.sub_stage_status, value: formik.values?.master_project_sub_stage_id }
                : null
            }
            clearIcon={true}
            onChange={(val) => {
              formik.setValues({
                ...formik.values,
                project_sub_stage: val?.label || '',
                master_project_sub_stage_id: val?.value || null,
              })
              redirectToStatus(formik.values?.phase, formik.values?.stage_status, val?.label, statuses, router)
            }}
          />
        )}

        <div>
          <TextInputField
            name="phase_weightage"
            placeholder="1,2,3,..."
            variant={'outlined'}
            labelText={'Phase Weightage'}
            classes={{ root: styles.planPercentage }}
            className={`${true ? '' : styles.comboHighlight}  ${styles.inputFields}`}
            disabled={true}
            onChange={formik.handleChange}
            value={formik.values?.phase_weightage}
            InputProps={{
              endAdornment: (
                <InputAdornment position="start" className={styles.endAdornment}>
                  <PercentageIcon className={styles.endAdornmentIcon} />
                </InputAdornment>
              ),
            }}
          />
        </div>

        {formik.values.stage_status === DESIGN ? (
          <div>
            <MultiAutoSelect
              isSx={false}
              labelText="Design Manager"
              disabled={!edit}
              // disabled={!isFirstDesignStageOfPhase}
              clearIcon={true}
              placeholder={formik.values.design_manager_ids ? '' : 'Type of search...'}
              options={convertMultiSelectOption(designManagerOptions).filter(
                (item: any) => item.id !== null && item.name !== null,
              )}
              value={getValueForMultiSelectOption(
                convertMultiSelectOption(designManagerOptions),
                formik.values.design_manager_ids,
              )}
              handleSelectedOption={(selectedOptions) => {
                handleMultiSelectChange('design_manager_ids', selectedOptions)
              }}
              className={`${!edit ? styles.multiSelect : styles.comboBoxHighlight}`}
            />
          </div>
        ) : null}
        {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            <MultiAutoSelect
              isSx={false}
              labelText="Delivery PM"
              disabled={!edit}
              clearIcon={true}
              placeholder={formik.values.delivery_project_manager_ids ? '' : 'Type of search...'}
              options={convertMultiSelectOption(DeliveryPMOptions).filter(
                (item: any) => item.id !== null && item.name !== null,
              )}
              value={getValueForMultiSelectOption(
                convertMultiSelectOption(DeliveryPMOptions),
                formik.values.delivery_project_manager_ids,
              )}
              handleSelectedOption={(selectedOptions) => {
                handleMultiSelectChange('delivery_project_manager_ids', selectedOptions)
              }}
              className={`${!edit ? styles.multiSelect : styles.comboBoxHighlight}`}
            />
          </div>
        ) : null}

        {formik.values.stage_status === LDC_PROCUREMENT || formik.values.stage_status === CONTRACTOR_PROCUREMENT ? (
          <div>
            <ComboBox
              options={procureManagementsOptions}
              labelText="Procurement Manager"
              placeholder=""
              className={`${!edit ? styles.comboBoxInput : styles.comboBoxHighlight}`}
              disabled={!edit}
              value={
                formik.values.master_procurement_manager_id
                  ? getValue(procureManagementsOptions, formik.values.master_procurement_manager_id)
                  : null
              }
              clearIcon={true}
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  master_procurement_manager_id: val?.value || null,
                })
              }
            />
          </div>
        ) : null}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== CONSTRUCTION &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <TextInputField
              name="design_stage_weightage"
              placeholder="1,2,3,..."
              variant={'outlined'}
              onChange={formik.handleChange}
              disabled={true}
              className={styles.inputFields}
              value={formik.values?.design_stage_weightage}
              labelText={'Design Stage Weightage'}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        ) : null}
      </div>

      <div className={styles.sectionSticky}>
        {/* Basic Details */}
        <Accordion
          expanded={expandedSections?.basicDetails}
          onChange={() => toggleSection('basicDetails')}
          title="Basic Details"
          lastUpdate={formik.values?.statusLastUpdate}
          updatedBy={formik.values.updated_by}
        >
          <BasicDetails formik={formik} edit={edit} />
        </Accordion>

        {/* Project Management */}
        {formik.values.stage_status !== INITIATION &&
          formik.values.stage_status !== DESIGN &&
          formik.values.stage_status !== DLP_PROJECT_CLOSEOUT && (
            <Accordion
              expanded={expandedSections.projectManagement}
              onChange={() => toggleSection('projectManagement')}
              title="Package Health & Safety"
              lastUpdate={formik.values?.projectManagementLastUpdate}
              updatedBy={formik.values.projectManagementUpdatedBy}
            >
              <ProjectManagement formik={formik} edit={edit} />
            </Accordion>
          )}

        {/* Key Achievements section */}
        <Accordion
          expanded={expandedSections.keyAchievement}
          onChange={() => toggleSection('keyAchievement')}
          title="Key Achievements"
          lastUpdate={findLatestDate(keyAchievementLastUpdates)}
          updatedBy={getLatestUpdatedBy(keyAchievementLastUpdates as string[], keyAchievements)}
        >
          <KeyAchievements project={project} edit={edit} statuses={statuses} />
        </Accordion>

        {/* Project Challenges/Risks */}
        <Accordion
          expanded={expandedSections.keyRisk}
          onChange={() => toggleSection('keyRisk')}
          title="Project Challenges/Risks"
          lastUpdate={findLatestDate(keyRiskLastUpdates)}
          updatedBy={getLatestUpdatedBy(keyAchievementLastUpdates as string[], keyAchievements)}
        >
          <KeyRisks project={project} edit={edit} />
        </Accordion>

        {/* Key Highlights section */}
        <Accordion
          expanded={expandedSections.keyHighlight}
          onChange={() => toggleSection('keyHighlight')}
          title="Key Highlights"
          lastUpdate={formik.values?.key_highlights_last_updated}
          updatedBy={formik.values.key_highlights_updated_by}
        >
          <KeyHighlight edit={edit} />
        </Accordion>
      </div>
    </div>
  )
}

export default ProgressForm

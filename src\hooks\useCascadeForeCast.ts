import { useMutation, UseMutationResult } from '@tanstack/react-query'
import { updateForecastDate } from '../services/cascadeForeCast'
import { UpdateForecastPayload } from '../services/cascadeForeCast/interface'
import { errorToast, successToast } from '../utils/toastUtils'

interface OnSuccessErrorProps {
  onSuccess?: (response: unknown) => void
  onError?: (error: unknown) => void
}

export const useCascadeForeCast = ({ onSuccess, onError }: OnSuccessErrorProps = {}): UseMutationResult<
  any,
  any,
  UpdateForecastPayload,
  unknown
> => {
  return useMutation({
    mutationFn: updateForecastDate,
    onSuccess: (response: any) => {
      onSuccess?.(response)
      successToast(response?.message || 'Forecast Finish Updated Successfully')
    },
    onError: (err: any) => {
      onError?.(err)
      errorToast(err?.response?.data?.message || 'Failed to Update Forecast Finish')
    },
  })
}

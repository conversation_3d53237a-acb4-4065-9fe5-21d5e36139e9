@import '/styles/color.scss';

.options {
  max-height: 75vh;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 5px;
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: #dddddd;
    border-radius: 5px;
    width: 4px;
  }
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  border-radius: 4px;
  background: $WHITE;
  border-bottom: 1px solid $LIGHT_200;
  transition: all 0.2s ease-in-out;
}

.title {
  flex: 1;
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: $DARK;
}

.actions {
  display: flex;
  align-items: center;
}

.filterIcon {
  cursor: pointer;
  height: 20px !important;
  width: 20px !important;
  color: $PULSE_TURQUOISE;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: scale(1.1);
    color: darken($PULSE_TURQUOISE, 10%);
  }
}

.resetIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  padding: 5px;
  transition: all 0.3s ease-in-out;

  .resetIcon {
    height: 20px;
    width: 20px;
    transition: all 0.3s ease-in-out;
    color: $DARK_200;

    &:hover {
      transform: rotate(360deg);
    }
  }

  &.activeResetIcon {
    background: rgba(255, 68, 68, 0.1);

    .resetIcon {
      color: #ea3323;

      &:hover {
        color: darken(#ea3323, 10%);
      }
    }
  }
}

.options {
  display: flex;
  flex-direction: column;
  width: fit-content;
  border-radius: 10px;
}

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createNewsFeed, deleteNewsFeed, getNewsFeed, updateNewsFeed } from '../services/newsFeed'
import { IGetNewsFeedResponse, IUpdateNewsFeed } from '../services/newsFeed/interface'

export const QUERY_NEWS_FEED = 'news-feed'

/**
 * Hook to fetch news feed
 * @param enabled - Enables or disables the query
 */
export const useGetNewsFeed = (period: string, enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetNewsFeedResponse>({
    queryKey: [QUERY_NEWS_FEED],
    queryFn: () => getNewsFeed(period),
    enabled,
  })

  return {
    newsFeed: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a news feed
 */
export const useCreateNewsFeed = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createNewsFeed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEWS_FEED] })
    },
  })
}

/**
 * Hook to delete a news feed
 */
export const useDeleteNewsFeed = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteNewsFeed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEWS_FEED] })
    },
  })
}

/**
 * Hook to update a news feed
 */
export const useUpdateNewsFeed = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateNewsFeed,
    onMutate: async (updatedData: IUpdateNewsFeed) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_NEWS_FEED] })
      const previousData = queryClient.getQueryData<IGetNewsFeedResponse>([QUERY_NEWS_FEED])
      // Optimistically update the cache
      queryClient.setQueryData<IGetNewsFeedResponse>([QUERY_NEWS_FEED], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((newsFeed) =>
            newsFeed.id === updatedData.id ? { ...newsFeed, feed: updatedData.feed } : newsFeed,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_NEWS_FEED], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NEWS_FEED] })
    },
  })
}

import React, { useRef, useState } from 'react'
import { PictureAsPdfOutlined, DescriptionOutlined, DeleteOutlineOutlined } from '@mui/icons-material'
import { CircularProgress, IconButton, Tooltip, Typography } from '@mui/material'
import { toast } from 'sonner'
import styles from './UploadFileField.module.scss'

export interface IUploadFileField {
  value: string
  attachments: { field_name: string; file_path: string } | undefined
  onUpload: any
  onDownload: any
  onDelete: any
  isProjectLoading: boolean
}

const UploadFileField: React.FC<IUploadFileField> = ({
  value,
  attachments,
  onUpload,
  onDownload,
  onDelete,
  isProjectLoading,
}) => {
  const [fileLoader, setFileLoader] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={styles.uploadDoc}
      style={{ display: 'flex', alignItems: 'center', gap: '2px' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Hidden File Input */}
      <input
        type="file"
        accept="application/pdf"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file && file.type === 'application/pdf') {
            setFileLoader(true)
            onUpload(
              e,
              () => setFileLoader(true),
              () => setFileLoader(false),
            )
          } else {
            if (fileInputRef.current) {
              fileInputRef.current.value = ''
            }
            toast('Please upload only PDF file')
          }
        }}
      />

      {/* Download Icon with Tooltip */}
      {attachments && (fileLoader || isProjectLoading) && <CircularProgress size={20} />}
      {attachments && !fileLoader && !isProjectLoading && (
        <>
          <Tooltip
            title={
              <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column' }}>
                <span style={{ fontFamily: 'Poppins' }}>Click to open</span>
                {/* <span style={{ fontFamily: 'Poppins', fontSize: '12px' }}>{attachments?.field_name}</span> */}
              </div>
            }
            arrow
          >
            <IconButton
              onClick={(e) => {
                e.stopPropagation()
                onDownload(
                  attachments,
                  () => setFileLoader(true),
                  () => setFileLoader(false),
                )
              }}
              sx={{
                color: '#ea3323',
                transition: 'transform 0.2s ease-in-out',
                '&:hover': { transform: 'scale(1.2)' },
              }}
            >
              <PictureAsPdfOutlined sx={{ fontSize: 20 }} />
            </IconButton>
          </Tooltip>
          {isHovered && (
            <Tooltip
              title={
                <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column' }}>
                  <span style={{ fontFamily: 'Poppins' }}>Click to delete</span>
                  {/* <span style={{ fontFamily: 'Poppins', fontSize: '12px' }}>{attachments?.field_name}</span> */}
                </div>
              }
              arrow
            >
              <IconButton
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(
                    attachments,
                    () => setFileLoader(true),
                    () => setFileLoader(false),
                  )
                }}
                sx={{
                  color: '#ea3323',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': { transform: 'scale(1.2)' },
                }}
              >
                <DeleteOutlineOutlined sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
          )}
        </>
      )}

      {/* Upload Icon with Tooltip */}
      {!attachments && (
        <Tooltip title="Upload a PDF file" arrow>
          <IconButton
            onClick={() => fileInputRef.current?.click()}
            sx={{
              fontFamily: 'Poppins',
              color: 'black',
              transition: 'transform 0.2s ease-in-out',
              '&:hover': { transform: 'scale(1.2)', color: '#007bff' },
            }}
          >
            {fileLoader || isProjectLoading ? (
              <CircularProgress size={20} />
            ) : (
              <DescriptionOutlined sx={{ fontSize: 20 }} />
            )}
          </IconButton>
        </Tooltip>
      )}

      {/* File Information */}
      <Typography sx={{ fontSize: '12px' }}>{value || ''}</Typography>
    </div>
  )
}

export default UploadFileField

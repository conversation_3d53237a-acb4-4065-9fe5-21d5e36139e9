import { IPortfolioManagerPayload, IUpdatePortfolioManager } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PORTFOLIO_MANAGER_URL = `/master-portfolio-manager`

// Get Master Portfolio Manager data
export const getMasterPortfolioManager = async () => {
  const response = await api.get(`${MASTER_PORTFOLIO_MANAGER_URL}`)
  return response?.data
}
export const deletePortFolioManager = async (data: number) => {
  const response = await api.delete(`${MASTER_PORTFOLIO_MANAGER_URL}/${data}`)
  return response?.data
}

// Create Master Portfolio Manager entry
export const createMasterPortfolioManager = async (data: IPortfolioManagerPayload) => {
  const response = await api.post(`${MASTER_PORTFOLIO_MANAGER_URL}`, data)
  return response
}

// Update Master Portfolio Manager entry
export const updateMasterPortfolioManager = async (data: IUpdatePortfolioManager): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PORTFOLIO_MANAGER_URL}/${data.id}`, { ...rest })
  return response
}

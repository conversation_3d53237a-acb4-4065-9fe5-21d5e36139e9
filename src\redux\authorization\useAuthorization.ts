import { useDispatch, useSelector } from 'react-redux'
import { authUrl, currentLogInUser, getAccessToken, logInUser, logOutUser } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useAuthorization = () => {
  const dispatch: AppDispatch = useDispatch()

  const authUrlStatus = useSelector((state: RootState) => state.authorization.authUrlStatus)
  const currentUser = useSelector((state: RootState) => state.authorization.currentUser)

  const authUrlApi = (data: string) => dispatch(authUrl(data))
  const getAccessTokenApi = (data: any) => dispatch(getAccessToken(data))
  const logInUserApi = (data: { period: string }) => dispatch(logInUser(data))
  const logOutUserApi = () => dispatch(logOutUser())
  const currentLogInUserApi = (data: string) => dispatch(currentLogInUser(data))

  return {
    authUrlStatus,
    currentUser,
    authUrlApi,
    getAccessToken<PERSON>pi,
    logOutUserApi,
    logInUserApi,
    currentLogInUserApi,
  }
}
export default useAuthorization

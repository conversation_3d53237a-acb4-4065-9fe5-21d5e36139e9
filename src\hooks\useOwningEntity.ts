import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterOwningEntity,
  deleteMasterOwningEntity,
  getMasterOwningEntity,
  updateMasterOwningEntity,
} from '../services/owningEntity'
import { IGetMasterOwningEntityResponse, IUpdateOwningEntity } from '../services/owningEntity/interface'

export const QUERY_OWNING_ENTITY = 'owning-entity'

/**
 * Hook to fetch Owning Entity
 * @param enabled - Enables or disables the query
 */
export const useGetOwningEntity = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterOwningEntityResponse>({
    queryKey: [QUERY_OWNING_ENTITY],
    queryFn: getMasterOwningEntity,
    enabled,
  })

  return {
    owningEntities: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Owning Entity
 */
export const useCreateOwningEntity = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterOwningEntity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_OWNING_ENTITY] })
    },
  })
}

/**
 * Hook to delete a Owning Entity
 */
export const useDeleteOwningEntity = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterOwningEntity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_OWNING_ENTITY] })
    },
  })
}

/**
 * Hook to update a Owning Entity
 */
export const useUpdateOwningEntity = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterOwningEntity,
    onMutate: async (updatedData: IUpdateOwningEntity) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_OWNING_ENTITY] })
      const previousData = queryClient.getQueryData<IGetMasterOwningEntityResponse>([QUERY_OWNING_ENTITY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterOwningEntityResponse>([QUERY_OWNING_ENTITY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((entity) =>
            entity.id === updatedData.id ? { ...entity, owning_entity: updatedData.owning_entity } : entity,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_OWNING_ENTITY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_OWNING_ENTITY] })
    },
  })
}

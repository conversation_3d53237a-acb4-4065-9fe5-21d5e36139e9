import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMitigationRecoveryPlan,
  deleteMitigationRecoveryPlan,
  getMitigationRecoveryPlans,
  updateMitigationRecoveryPlan,
} from '../services/mitigationRecoveryPlan'
import {
  IGetMitigationRecoveryPlansRes,
  IUpdateMitigationRecoveryPlan,
} from '../services/mitigationRecoveryPlan/interface'

export const QUERY_MITIGATION_RECOVERY_PLAN = 'mitigation-recovery-plan'

/**
 * Hook to fetch MitigationRecoveryPlan
 * @param enabled - Enables or disables the query
 */
export const useGetMitigationRecoveryPlan = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMitigationRecoveryPlansRes>({
    queryKey: [QUERY_MITIGATION_RECOVERY_PLAN],
    queryFn: getMitigationRecoveryPlans,
    enabled,
  })

  return {
    mitigationRecoveryPlans: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a MitigationRecoveryPlan
 */
export const useCreateMitigationRecoveryPlan = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMitigationRecoveryPlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MITIGATION_RECOVERY_PLAN] })
    },
  })
}

/**
 * Hook to delete a MitigationRecoveryPlan
 */
export const useDeleteMitigationRecoveryPlan = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMitigationRecoveryPlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MITIGATION_RECOVERY_PLAN] })
    },
  })
}

/**
 * Hook to update a MitigationRecoveryPlan
 */
export const useUpdateMitigationRecoveryPlan = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMitigationRecoveryPlan,
    onMutate: async (updatedData: IUpdateMitigationRecoveryPlan) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_MITIGATION_RECOVERY_PLAN] })
      const previousData = queryClient.getQueryData<IGetMitigationRecoveryPlansRes>([QUERY_MITIGATION_RECOVERY_PLAN])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMitigationRecoveryPlansRes>([QUERY_MITIGATION_RECOVERY_PLAN], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? {
                  ...manager,
                  mitigation_recovery_plan: updatedData.mitigation_recovery_plan,
                  mitigation_recovery_plan_code: updatedData.mitigation_recovery_plan_code,
                }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_MITIGATION_RECOVERY_PLAN], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MITIGATION_RECOVERY_PLAN] })
    },
  })
}

import React, { ReactElement, forwardRef } from 'react'

interface TimerIconProps {
  className?: string
  onClick?: (e: any) => void
}

const TimerIcon: React.FC<TimerIconProps> = forwardRef<SVGSVGElement, TimerIconProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.38579 3.37928C5.91601 0.852621 10.0291 0.879487 12.5748 3.42518C15.1216 5.97198 15.1474 10.0875 12.6174 12.6175C10.0875 15.1474 5.97195 15.1216 3.42516 12.5748C1.91592 11.0656 1.29269 9.00661 1.56031 7.05415C1.59781 6.78057 1.85 6.58918 2.12358 6.62668C2.39717 6.66418 2.58855 6.91637 2.55105 7.18995C2.3244 8.84349 2.85188 10.5873 4.13227 11.8677C6.29525 14.0307 9.77759 14.0431 11.9103 11.9104C14.0431 9.77762 14.0307 6.29528 11.8677 4.13229C9.70581 1.97041 6.22592 1.95693 4.0929 4.08638L4.59137 4.08889C4.86751 4.09027 5.09024 4.31525 5.08885 4.59139C5.08746 4.86753 4.86248 5.09026 4.58634 5.08887L2.88931 5.08034C2.61513 5.07897 2.3932 4.85704 2.39183 4.58286L2.3833 2.88583C2.38191 2.60969 2.60464 2.38471 2.88078 2.38332C3.15692 2.38194 3.3819 2.60466 3.38328 2.8808L3.38579 3.37928ZM7.99992 4.83328C8.27606 4.83328 8.49992 5.05714 8.49992 5.33328V7.79284L10.0201 9.31306C10.2154 9.50832 10.2154 9.8249 10.0201 10.0202C9.82488 10.2154 9.5083 10.2154 9.31303 10.0202L7.49992 8.20705V5.33328C7.49992 5.05714 7.72378 4.83328 7.99992 4.83328Z"
        fill="#808080"
        fillOpacity="0.5"
      />
    </svg>
  )
})
TimerIcon.displayName = 'TimerIcon'

export default TimerIcon

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './SubLocation.module.scss'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateSubLocations,
  useDeleteSubLocations,
  useGetSubLocations,
  useUpdateSubLocations,
} from '@/src/hooks/useSubLocations'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddSubLocations: React.FC<any> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { addSubLocation } = drawerStates
  //REACT-QUERY
  const { subLocations } = useGetSubLocations(addSubLocation)
  // MUTATIONS
  const { mutate: addMasterSubLocations } = useCreateSubLocations()
  const { mutate: deleteSubLocations } = useDeleteSubLocations()
  const { mutate: updateSubLocations } = useUpdateSubLocations()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { sub_location: '' },
    onSubmit: async (values) => {
      const payload = { sub_location: values.sub_location }
      if (editIndex !== null) {
        updateSubLocations(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterSubLocations(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteSubLocations(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = subLocations.find((sub_location) => {
      //TODO
      return sub_location.id === id
    })

    formik.setValues({ sub_location: editControlManagers?.sub_location })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'sub_location',
      header: 'Sub Location',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Sub Location'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="sub_location"
              labelText={'Sub Location'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.sub_location}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.sub_location?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.sub_location?.trim()}
              >
                {'+ Add Sub Location'}
              </Button>
            )}
          </div>
        </form>
        {subLocations?.length >= 1 && (
          //  <Table data={locations} columns={columns} />
          <TanStackTable rows={sortData(subLocations, 'sub_location') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddSubLocations

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterRatingResponse, IRole, IRoleState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IRoleState = {
  getRoleStatus: StatusEnum.Idle,
  addRoleStatus: StatusEnum.Idle,
  deleteRoleStatus: StatusEnum.Idle,
  updateRoleStatus: StatusEnum.Idle,
  viewPermissionsStatus: StatusEnum.Idle,
  projectAndEntityStatus: StatusEnum.Idle,
  projectAndEntities: [],
  permissions: [],
  roles: [],
  role: {},
}

export const getRole = createAsyncThunk('/get-role-templates', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/role-templates`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addRole = createAsyncThunk('/add-role-templates', async (data: IRole, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/role-templates`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateRole = createAsyncThunk('/update-role-templates', async (data: IRole, thunkAPI) => {
  const { id, ...rest } = data
  try {
    const response = await ApiPutNoAuth(`/role-templates/${id}`, { ...rest })
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteRole = createAsyncThunk('/delete-role-templates', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/role-templates/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const getViewPermissions = createAsyncThunk('/role-templates/view-permissions', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/role-templates/view-permissions`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const getProjectAndEntity = createAsyncThunk('/role-templates/projects', async (data: any, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/role-templates/projects?period=${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const roleSlice = createSlice({
  name: 'role',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterRatings
    builder.addCase(getRole.pending, (state) => {
      state.getRoleStatus = StatusEnum.Pending
    })
    builder.addCase(getRole.fulfilled, (state, action) => {
      state.getRoleStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterRatingResponse
      state.roles = actionPayload.data
    })
    builder.addCase(getRole.rejected, (state) => {
      state.getRoleStatus = StatusEnum.Failed
    })
    //addMasterRatings
    builder.addCase(addRole.pending, (state) => {
      state.addRoleStatus = StatusEnum.Pending
    })
    builder.addCase(addRole.fulfilled, (state, action) => {
      state.addRoleStatus = StatusEnum.Success
    })
    builder.addCase(addRole.rejected, (state) => {
      state.addRoleStatus = StatusEnum.Failed
    })
    //updateMasterRatings
    builder.addCase(updateRole.pending, (state) => {
      state.updateRoleStatus = StatusEnum.Pending
    })
    builder.addCase(updateRole.fulfilled, (state, action) => {
      state.updateRoleStatus = StatusEnum.Success
    })
    builder.addCase(updateRole.rejected, (state) => {
      state.updateRoleStatus = StatusEnum.Failed
    })
    //deleteMasterRatings
    builder.addCase(deleteRole.pending, (state) => {
      state.deleteRoleStatus = StatusEnum.Pending
    })
    builder.addCase(deleteRole.fulfilled, (state, action) => {
      state.deleteRoleStatus = StatusEnum.Success
    })
    builder.addCase(deleteRole.rejected, (state) => {
      state.deleteRoleStatus = StatusEnum.Failed
    })
    //getViewPermissions
    builder.addCase(getViewPermissions.pending, (state) => {
      state.viewPermissionsStatus = StatusEnum.Pending
    })
    builder.addCase(getViewPermissions.fulfilled, (state, action) => {
      state.viewPermissionsStatus = StatusEnum.Success
      const actionPayload = action.payload as any
      state.permissions = actionPayload.data
    })
    builder.addCase(getViewPermissions.rejected, (state) => {
      state.viewPermissionsStatus = StatusEnum.Failed
    })
    //getProjectAndEntity
    builder.addCase(getProjectAndEntity.pending, (state) => {
      state.projectAndEntityStatus = StatusEnum.Pending
    })
    builder.addCase(getProjectAndEntity.fulfilled, (state, action) => {
      state.projectAndEntityStatus = StatusEnum.Success
      const actionPayload = action.payload as any
      state.projectAndEntities = actionPayload.data
    })
    builder.addCase(getProjectAndEntity.rejected, (state) => {
      state.projectAndEntityStatus = StatusEnum.Failed
    })
  },
})

export const {} = roleSlice.actions

// Export the reducer
export default roleSlice.reducer

import React, { useEffect, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { statusOptions } from './constant'
import styles from './ProjectDetailsCollapse.module.scss'
import { CollapseField } from '../../collapseField'
import { determineProjectStatus, generateAutoValueOfProject } from '../../helper'
import { IProjectDetailsCollapse } from '../interface'
import RichTextEditor from '@/src/component/richTextEditor'
import MultiAutoSelect from '@/src/component/shared/multiAutoSelect'
import { userType, whiteListedUserList } from '@/src/constant/enum'
import { convertMultiSelectOption } from '@/src/helpers/helpers'
import { useGetLocations } from '@/src/hooks/useLocations'
import { useGetOwningEntity } from '@/src/hooks/useOwningEntity'
import { useGetPricingType } from '@/src/hooks/usePricingType'
import { useGetProjectClassifications } from '@/src/hooks/useProjectClassification'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import { useGetSubLocations } from '@/src/hooks/useSubLocations'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useStatus from '@/src/redux/status/useStatus'
import useSubOwningEntity from '@/src/redux/subOwningEntity/useSubOwningEntity'
import { getMasterOneProject } from '@/src/services/projects'
import {
  getValueForMultiSelectOption,
  getValues,
  populateDropdownOptions,
  prepareDropdownOptions,
} from '@/src/utils/arrayUtils'
// import Textarea from '@/src/component/shared/textArea'

const ProjectDetailsCollapse = ({ summaryEdit, formik }: IProjectDetailsCollapse) => {
  const { statuses } = useStatus()
  const router = useRouter()
  const { owningEntities } = useGetOwningEntity()
  const { currentUser } = useAuthorization()
  const { currentPeriod } = useMasterPeriod()
  const { locations } = useGetLocations()
  const { getMasterSubOwningEntityApi } = useSubOwningEntity()
  const { projectClassifications } = useGetProjectClassifications()
  const { pricingTypes } = useGetPricingType()
  const { subLocations } = useGetSubLocations()

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  // TODO: comment out 'getMasterSubOwningEntityApi()' because get this "404 Error: The API endpoint you are trying to access does not exist." error by BE
  // useEffect(() => {
  //   if (summaryEdit) {
  // getMasterOwningEntityApi()
  // getMasterLocationsApi()
  // getMasterSubOwningEntityApi()
  // getMasterProjectClassificationsApi()
  // getMasterProjectStatusesApi()
  // getMasterConsultantsApi()
  // getEntityCategoryApi()
  // getPricingTypeApi()
  // getMasterSubLocationsApi()
  //   }
  // }, [summaryEdit])

  const owningEntitiesOptions = useMemo(() => {
    return populateDropdownOptions(owningEntities, 'owning_entity')
  }, [owningEntities])

  const locationOptions = useMemo(() => {
    return populateDropdownOptions(locations, 'location')
  }, [locations])

  const projectClassificationOptions = useMemo(() => {
    return populateDropdownOptions(projectClassifications, 'project_classification')
  }, [projectClassifications])

  const subLocationsOption = useMemo(() => {
    return prepareDropdownOptions(subLocations, 'sub_location')
    // return populateDropdownOptions(subLocations, 'sub_location')
  }, [subLocations])

  const pricingTypeOptions = useMemo(() => {
    return populateDropdownOptions(pricingTypes, 'pricing_type')
  }, [pricingTypes])

  const isSuperAdminOrWhiteListed =
    currentUser.user_type === userType.SUPER_ADMIN || whiteListedUserList.includes(currentUser.email)

  const { automationProjectStatus } = generateAutoValueOfProject(statuses, project)

  return (
    <div
      className={styles.projectDetailsCollapse}
      onClick={(e) => {
        e.stopPropagation()
      }}
    >
      {/* {summaryEdit ? ( */}

      {/* <Textarea
          className={summaryEdit ? styles.textArea : styles.textAreaValue}
          name="project_brief"
          disabled={!summaryEdit}
          value={formik.values && formik.values?.project_brief}
          onChange={(e) => formik.setFieldValue('project_brief', e.target.value)}
          onBlur={formik.handleBlur}
        /> */}
      <RichTextEditor
        labelText="Project Brief"
        value={formik.values.project_brief}
        handleChange={(val) => formik.setFieldValue('project_brief', val)}
        isEdit={summaryEdit}
        className={styles.textEditor}
      />
      {/* // ) : (
      //   <div className={styles.projectBrif}>
      //     <div className={styles.projectBrifLabel}>Project Brief</div>
      //     <TypographyField className={styles.projectBriefText} sx="caption" text={formik?.values?.project_brief} />
      //   </div>
      // )} */}
      <div className={styles.fields}>
        <div className={styles.leftContent}>
          <CollapseField
            label="Project Name (Arabic)"
            value={formik.values?.project_name_ar}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="project_name_ar"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="ERP Project Name"
            value={formik.values?.erp_project_name}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="erp_project_name"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="ERP Project ID"
            value={formik.values?.project_id}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="project_id"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          {formik.values.owning_entity !== 'Aldar Development' && (
            <CollapseField
              label="CPDS Project ID"
              value={formik.values?.cpds_project_id}
              summaryEdit={summaryEdit}
              formik={formik}
              fieldName="cpds_project_id"
              labelStyle={styles.labelStyle}
              dotStyle={styles.dotStyle}
            />
          )}
          {formik.values.owning_entity !== 'Aldar Development' && (
            <CollapseField
              label="DOF Number"
              value={formik.values?.dof_number}
              summaryEdit={summaryEdit}
              formik={formik}
              fieldName="dof_number"
              labelStyle={styles.labelStyle}
              dotStyle={styles.dotStyle}
            />
          )}
          {/* <CollapseField
            label="Owning Entity"
            value={formik.values?.owning_entity}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="owning_entity"
            isComboBox={true}
            options={owningEntitiesOptions}
          /> */}
          {/* <CollapseField
            label="Entity Category"
            value={formik.values?.entity_category}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="entity_category"
            isComboBox={true}
            options={entityCategoryOptions}
          /> */}
          <CollapseField
            label="Location"
            value={formik.values?.Locations?.map((item: any) => item?.location).join(', ')}
            summaryEdit={false}
            formik={formik}
            disabled={true}
            isMultiSelect={true}
            fieldName="location"
            options={locationOptions}
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="Sub Location"
            isMultiSelect={true}
            disabled={true}
            value={
              summaryEdit
                ? formik.values?.sub_location_ids
                : getValues(subLocationsOption, formik.values?.sub_location_ids)
            }
            summaryEdit={summaryEdit}
            formik={formik}
            options={subLocationsOption}
            fieldName="sub_location_ids"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          {/* <CollapseField
            label="Subject to Rebaseline"
            value={formik.values?.is_subject_to_rebaseline}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="is_subject_to_rebaseline"
            isCheckBox={true}
            options={owningEntitiesOptions}
          />
          <CollapseField
            label="Rebaseline Reason"
            value={formik.values?.reason_rebaseline}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="reason_rebaseline"
          /> */}
          {/* <CollapseField
            label="Budget"
            value={formik.values?.budget}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="budget"
            endAdornment={
              <InputAdornment position="start" className={styles.endAdornment}>
                <AedIcon className={styles.endAdornmentIcon} />
              </InputAdornment>
            }
          /> */}
        </div>
        <div className={styles.rightContent}>
          {/* <CollapseField
            label="Project Type"
            value={formik.values?.project_type}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="project_type"
          /> */}
          <CollapseField
            label="Project Pricing Type"
            value={formik.values?.MasterPricingType?.pricing_type}
            summaryEdit={false}
            formik={formik}
            fieldName="project_pricing_type"
            isComboBox={true}
            disabled={true}
            options={pricingTypeOptions}
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="Project Classification"
            value={formik.values?.MasterProjectClassification?.project_classification}
            summaryEdit={false}
            options={projectClassificationOptions}
            formik={formik}
            isComboBox={true}
            fieldName="project_classification"
            disabled={true}
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="Project Status"
            isComboBox={true}
            // disabled={true}
            // value={determineProjectStatus(formik.values.project_status as string, automationProjectStatus)}
            value={formik.values.project_status}
            summaryEdit={summaryEdit}
            formik={formik}
            options={statusOptions?.map((res) => {
              return { label: res, value: res }
            })}
            fieldName="project_status"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
            isCustomSorting={true}
          />
          {/* <CollapseField
            label="LDC"
            options={consultantsOptions}
            isMultiSelect={true}
            value={formik.values?.ldc}
            summaryEdit={summaryEdit}
            formik={formik}
            fieldName="ldc"
          /> */}
          <CollapseField
            label="Latitude"
            value={formik.values?.latitude}
            summaryEdit={summaryEdit}
            formik={formik}
            isNumber={true}
            fieldName="latitude"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          <CollapseField
            label="Longitude"
            value={formik.values?.longitude}
            summaryEdit={summaryEdit}
            formik={formik}
            isNumber={true}
            fieldName="longitude"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          {/* // TODO: Uncomment this task when add field. */}
          <CollapseField
            label="Executive Sorting"
            value={formik.values?.executive_sorting_order}
            summaryEdit={summaryEdit}
            formik={formik}
            isNumber={true}
            fieldName="executive_sorting_order"
            labelStyle={styles.labelStyle}
            dotStyle={styles.dotStyle}
          />
          {isSuperAdminOrWhiteListed && (
            <CollapseField
              label="Executive Summary"
              value={String(formik.values?.is_executive_project)}
              summaryEdit={summaryEdit}
              formik={formik}
              fieldName="is_executive_project"
              isCheckBox={true}
              options={owningEntitiesOptions}
              labelStyle={styles.labelStyle}
              dotStyle={styles.dotStyle}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default ProjectDetailsCollapse

import { StatusEnum } from '../types'

export interface IEntityKeyAchievementStatus {
  getEntityKeyAchievement: StatusEnum
  addEntityKeyAchievement: StatusEnum
  deleteEntityKeyAchievement: StatusEnum
  updateEntityKeyAchievement: StatusEnum
  entityKeyAchievements: IEntityKeyAchievement[]
  entityKeyAchievement: IEntityKeyAchievement
}

export interface IEntityKeyAchievement {
  id?: string | number
  period?: string
  project_name?: string
  phase?: string
  value?: string
  description?: string
}

export interface IGetGovernanceResponse {
  data: IEntityKeyAchievement[]
  message: string
  success: true
}

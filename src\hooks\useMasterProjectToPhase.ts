import { useQuery } from '@tanstack/react-query'
import { getProjectToPhase } from '../services/masterProjectToPhase'
import { IGetProjectPhaseCategoryResponse } from '../services/masterProjectToProjectPhaseCategories/interface'

export const QUERY_PROJECT_TO_PHASE = 'project-to-phase'

/**
 * Hook to fetch Project To Phase
 * @param enabled - Enables or disables the query
 */
export const useGetProjectToPhase = (period: string, project_name: string, enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetProjectPhaseCategoryResponse>({
    queryKey: [QUERY_PROJECT_TO_PHASE],
    queryFn: () => getProjectToPhase(period, project_name),
    enabled,
  })

  return {
    projectToPhase: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

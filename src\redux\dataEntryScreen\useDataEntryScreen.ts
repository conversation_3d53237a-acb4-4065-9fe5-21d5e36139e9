import { useDispatch, useSelector } from 'react-redux'
import { setSelectedTab } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useDataEntryScreen = () => {
  const dispatch: AppDispatch = useDispatch()

  const selectedTab = useSelector((state: RootState) => state.summaryTab.selectedTab)
  const setSelectedTabApi = (data: string) => dispatch(setSelectedTab(data))

  return { selectedTab, setSelectedTabApi }
}
export default useDataEntryScreen

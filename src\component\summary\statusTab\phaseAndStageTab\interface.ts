import { Dispatch, SetStateAction } from 'react'
import { IMasterProjectStageStatus, IStatus } from '@/src/redux/status/interface'

export interface ITab {
  name: string
  onSelect: (name: string) => void
  isActive: boolean
  children: string
}

export interface IPhaseAndStageTab {
  onClose: () => void
  isStageDrawer?: boolean
  setIsStageVisited?: Dispatch<SetStateAction<boolean>>
  isStageVisited?: boolean
}

export interface IStageTab {
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
  handleScrollToTop: () => void
  selectTab?: string
  setIsStageVisited?: Dispatch<SetStateAction<boolean>>
  isStageVisited?: boolean
}

export interface IStageFormField {
  project_to_project_phase_ids: number[]
  project_to_project_phase_category_ids: number[]
  master_project_stage_status_id: number | null
  master_project_sub_stage_id: number | null
  designStageWeightage: string | null
  // project_phase_category: string
  phaseWeightage: string | null
  predecessor: number[]
  successor: number[]
}

export interface IStageForm {
  editData: number | null
  setEditData: React.Dispatch<React.SetStateAction<number | null>>
  statusData: IStageFormTable[]
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
  edit: boolean
  setEdit: (args: boolean) => void
  setIsStageVisited?: Dispatch<SetStateAction<boolean>>
  isStageVisited?: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
}

export interface IStageFormTable {
  id: number | null
  sortingOrder: number
  master_project_stage_status_id?: number | null
  master_project_sub_stage_id?: number
  project_to_project_phase_ids?: number[] | null
  project_to_project_phase_category_ids?: number[] | null
  designStageWeightage: number | null
  phaseWeightage: number | null
  predecessor?: number[]
  successor?: number[]
}

export interface IStageFormTablePayloadForValidation {
  id: string
  phase: string
  stage_status: string
  project_phase_category: string
  phase_weightage: string
  design_stage_weightage: string
  predecessor: string[]
  successor: string[]
}

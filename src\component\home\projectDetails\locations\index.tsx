import React, { useState } from 'react'
import { useFormik } from 'formik'
import { toast, Toaster } from 'sonner'
import { IAddControlManagerProps } from './interface'
import styles from './Locations.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useCreateLocations, useDeleteLocations, useGetLocations, useUpdateLocations } from '@/src/hooks/useLocations'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddLocations: React.FC<IAddControlManagerProps> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { addLocation } = drawerStates
  //REACT-QUERY
  const { locations } = useGetLocations(addLocation)
  // MUTATIONS
  const { mutate: addMasterLocations } = useCreateLocations()
  const { mutate: deleteLocations } = useDeleteLocations()
  const { mutate: updateLocations } = useUpdateLocations()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { location: '' },
    onSubmit: async (values) => {
      const payload = { location: values.location }
      if (editIndex !== null) {
        updateLocations(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterLocations(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteLocations(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = locations.find((location) => {
      //TODO
      return location.id === id
    })

    formik.setValues({ location: editControlManagers?.location })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'location',
      header: 'Location',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Location'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="location"
              labelText={'Location'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.location}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.location?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.location?.trim()}
              >
                {'+ Add Location'}
              </Button>
            )}
          </div>
        </form>
        {locations?.length >= 1 && (
          //  <Table data={locations} columns={columns} />
          <TanStackTable rows={sortData(locations, 'location') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddLocations

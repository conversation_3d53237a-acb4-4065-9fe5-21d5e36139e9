@import '/styles/color.scss';

// .container {
//   // margin-top: 20px;
//   // margin-left: 20px;
//   // margin-right: 20px;
// }

.searchField {
  > div > div {
    padding-right: 11px !important;
    background-color: $WHITE !important;
  }

  > div > div > input {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    letter-spacing: 0em !important;
    text-align: left !important;
    padding: 0px !important;
    padding-top: 11px !important;
    padding-bottom: 11px !important;
    padding-left: 8px !important;
    background-color: $WHITE;
    &::placeholder {
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
      color: $DARK_200 !important;
    }
  }
}

.endAdornment {
  background-color: $WHITE;
  margin-right: 0px !important;
}

.endAdornmentIcon {
  height: 16px;
  width: 16px;
}

.comboBoxSearch {
  > div > div {
    background-color: white !important;
  }
}

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 8px 0px 8px 24px;
  border-bottom: 1px solid $LIGHT_200;
}

.contain {
  padding-left: 24px;
  padding-right: 24px;
}

.checkbox {
  cursor: pointer;
}

.button {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  .buttonContent {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    color: $DARK;
  }
}
.buttonsContainer {
  display: flex;
}
.divider {
  margin-left: 5px;
  width: 1px;
  height: 15px;
  background: $LIGHT_200;
}
.actionButtons {
  display: flex;
  gap: 10px;
  padding: 0px 10px 0px 0px;
}

.tabContainer {
  margin: 10px 10px 10px 0px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.tabContain {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 15px;
}
.tabButtons {
  display: flex;
  align-items: center;
  gap: 30px;
  padding-right: 16px;
}

.tab {
  cursor: pointer;
  color: $DARK;
}

.selectedTab {
  height: fit-content;
  color: $DARK;
  border-bottom: 2px solid $ERROR;
}

.container {
  min-width: 1020px;
  width: 100%;
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid $LIGHT_200;
  padding: 10px;
}

.buttonsOfAction {
}
.tableContainer {
  overflow-y: auto;
  overflow-x: auto;
  height: calc(100vh - 164px);
}

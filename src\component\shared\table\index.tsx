import React from 'react'
import { ITableColumn, ITableProps, TableBodyProps, TableCellProps, TableHeaderProps, TableRowProps } from './interface'
import styles from './Table.module.scss'
import TypographyField from '@/src/component/shared/typography'
import { WHITE } from '@/src/constant/color'

const TableHeader: React.FC<TableHeaderProps> = ({ columns }) => {
  return (
    <thead className={styles.tableHeadRow}>
      <tr>
        {columns.map((column, index) => (
          <th key={index} className={styles.tableHeader} style={column.headerStyle}>
            <TypographyField variant={'caption'} style={{ color: WHITE }} text={column.label} />
          </th>
        ))}
      </tr>
    </thead>
  )
}

const TableBody: React.FC<TableBodyProps> = ({ data, columns }) => {
  return (
    <tbody>
      {data.map((row, rowIndex) => (
        <TableRow key={rowIndex} row={row} rowIndex={rowIndex} columns={columns} />
      ))}
    </tbody>
  )
}

const TableRow: React.FC<TableRowProps> = ({ row, rowIndex, columns }) => {
  return (
    <tr className={`${styles.tableRow}`}>
      {columns.map((column: ITableColumn, colIndex) => {
        return (
          <TableCell
            key={`${rowIndex}-${colIndex}`}
            row={row}
            column={column}
            value={row[column.key as keyof typeof row]}
            rowIndex={rowIndex}
          />
        )
      })}
    </tr>
  )
}

const TableCell: React.FC<TableCellProps> = ({ value, row, column, rowIndex }) => {
  return (
    <td className={styles.tableData} style={column.cellStyle}>
      {column.renderCell ? column.renderCell(value, row, rowIndex) : value}

      {column.isRabin ? (
        <TypographyField
          variant={'captionSemiBold'}
          style={{ color: WHITE, fontSize: '9px' }}
          className={styles.ribbon}
          text="New !"
        />
      ) : (
        ''
      )}
    </td>
  )
}

const Table: React.FC<ITableProps> = ({ columns, data }) => {
  return (
    <div className={styles.tableContainer}>
      <table className={styles.table}>
        <TableHeader columns={columns} />
        <TableBody data={data} columns={columns} />
      </table>
    </div>
  )
}

export default Table

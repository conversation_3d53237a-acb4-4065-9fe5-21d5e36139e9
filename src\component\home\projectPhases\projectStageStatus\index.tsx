import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import { IAddPmcConsultantProps } from './interface'
import styles from './ProjectStageStatus.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateProjectStageStatuses,
  useDeleteProjectStageStatuses,
  useGetProjectStageStatuses,
  useUpdateProjectStageStatuses,
} from '@/src/hooks/useProjectStageStatus'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddProjectStageStatus: React.FC<IAddPmcConsultantProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addProjectStageStatus } = drawerStates
  //REACT-QUERY
  const { projectStageStatuses } = useGetProjectStageStatuses(addProjectStageStatus)
  // MUTATIONS
  const { mutate: addProjectStageStatuses } = useCreateProjectStageStatuses()
  const { mutate: deleteProjectStageStatuses } = useDeleteProjectStageStatuses()
  const { mutate: updateProjectStageStatuses } = useUpdateProjectStageStatuses()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: {
      projectStageStatus: '',
    },
    onSubmit: async (values) => {
      const payload = { project_stage_status: values.projectStageStatus }
      if (editIndex !== null) {
        updateProjectStageStatuses(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addProjectStageStatuses(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteProjectStageStatuses(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPortfolioManagers: any = projectStageStatuses.find((projectStageStatus) => {
      //TODO
      return projectStageStatus.id === id
    })

    formik.setValues({
      projectStageStatus: editPortfolioManagers?.project_stage_status,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_stage_status',
      header: 'Project Stage Status',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text={'Add Project Stage Status'}
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="projectStageStatus"
              labelText={'Project Stage Status'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.projectStageStatus}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectStageStatus?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectStageStatus?.trim()}
              >
                {'+ Add Project Stage Status'}
              </Button>
            )}
          </div>
        </form>
        {projectStageStatuses?.length >= 1 && (
          // <Table data={projectStageStatuses} columns={columns} />
          <TanStackTable
            rows={sortData(projectStageStatuses, 'project_stage_status') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddProjectStageStatus

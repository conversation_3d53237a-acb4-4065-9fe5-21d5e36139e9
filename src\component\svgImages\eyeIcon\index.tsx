import React, { ReactElement, forwardRef } from 'react'

interface EyeIconProps {
  className?: string
  onClick?: (e: any) => void
}

const EyeIcon: React.FC<EyeIconProps> = forwardRef<SVGSVGElement, EyeIconProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.00065 5.49935C6.61994 5.49935 5.50065 6.61864 5.50065 7.99935C5.50065 9.38006 6.61994 10.4993 8.00065 10.4993C9.38136 10.4993 10.5007 9.38006 10.5007 7.99935C10.5007 6.61864 9.38136 5.49935 8.00065 5.49935ZM6.50065 7.99935C6.50065 7.17092 7.17222 6.49935 8.00065 6.49935C8.82908 6.49935 9.50065 7.17092 9.50065 7.99935C9.50065 8.82778 8.82908 9.49935 8.00065 9.49935C7.17222 9.49935 6.50065 8.82778 6.50065 7.99935Z"
        fill="#444444"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.00065 2.16602C4.99123 2.16602 2.96418 3.96881 1.7877 5.49726L1.76648 5.52482C1.50041 5.87037 1.25536 6.18863 1.08911 6.56495C0.911081 6.96793 0.833984 7.40714 0.833984 7.99935C0.833984 8.59156 0.911081 9.03077 1.08911 9.43375C1.25536 9.81007 1.50041 10.1283 1.76648 10.4739L1.7877 10.5014C2.96418 12.0299 4.99123 13.8327 8.00065 13.8327C11.0101 13.8327 13.0371 12.0299 14.2136 10.5014L14.2348 10.4739C14.5009 10.1283 14.7459 9.81007 14.9122 9.43375C15.0902 9.03077 15.1673 8.59156 15.1673 7.99935C15.1673 7.40714 15.0902 6.96793 14.9122 6.56495C14.7459 6.18862 14.5009 5.87036 14.2348 5.5248L14.2136 5.49726C13.0371 3.96881 11.0101 2.16602 8.00065 2.16602ZM2.58013 6.10721C3.66641 4.69596 5.43422 3.16602 8.00065 3.16602C10.5671 3.16602 12.3349 4.69596 13.4212 6.10721C13.7136 6.48708 13.8848 6.71405 13.9975 6.96905C14.1028 7.20736 14.1673 7.49864 14.1673 7.99935C14.1673 8.50006 14.1028 8.79134 13.9975 9.02965C13.8848 9.28464 13.7136 9.51162 13.4212 9.89148C12.3349 11.3027 10.5671 12.8327 8.00065 12.8327C5.43422 12.8327 3.66641 11.3027 2.58013 9.89148C2.28774 9.51162 2.11647 9.28464 2.00382 9.02965C1.89854 8.79134 1.83398 8.50006 1.83398 7.99935C1.83398 7.49864 1.89854 7.20736 2.00382 6.96905C2.11647 6.71405 2.28774 6.48708 2.58013 6.10721Z"
        fill="#444444"
      />
    </svg>
  )
})
EyeIcon.displayName = 'EyeIcon'

export default EyeIcon

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterDesignManager,
  deleteMasterDesignManager,
  updateMasterDesignManager,
  getMasterDesignManagers,
} from '../services/designManager'
import {
  IGetMasterDesignProjectManagersResponse,
  IUpdateDesignProjectManager,
} from '../services/designManager/interface'

export const QUERY_DESIGN_MANAGER = 'design-manager'

/**
 * Hook to fetch design managers
 * @param enabled - Enables or disables the query
 */
export const useGetDesignManager = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterDesignProjectManagersResponse>({
    queryKey: [QUERY_DESIGN_MANAGER],
    queryFn: getMasterDesignManagers,
    enabled,
  })

  return {
    designManagers: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a design manager
 */
export const useCreateDesignManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterDesignManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DESIGN_MANAGER] })
    },
  })
}

/**
 * Hook to delete a design manager
 */
export const useDeleteDesignManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterDesignManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DESIGN_MANAGER] })
    },
  })
}

/**
 * Hook to update a design manager
 */
export const useUpdateDesignManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterDesignManager,
    onMutate: async (updatedData: IUpdateDesignProjectManager) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_DESIGN_MANAGER] })
      const previousData = queryClient.getQueryData<IGetMasterDesignProjectManagersResponse>([QUERY_DESIGN_MANAGER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterDesignProjectManagersResponse>([QUERY_DESIGN_MANAGER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, design_project_manager: updatedData.design_manager }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_DESIGN_MANAGER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DESIGN_MANAGER] })
    },
  })
}

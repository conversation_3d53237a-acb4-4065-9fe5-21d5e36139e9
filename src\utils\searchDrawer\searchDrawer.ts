import { getYear, isValid, parse } from 'date-fns'
import { DDMMYYYYformate, payloadDateFormate } from '../dateUtils'
import { naturalSort } from '../sortingUtils'
import { FilterOptionType, SearchDrawerStateType } from '@/src/component/pageLayout/searchDrawer/interface'
import { IProjects } from '@/src/services/projects/interface'

/**
 * Maps project field names to state field names
 */
export const getFieldMapping = (field: string): string => {
  const mappings: Record<string, string> = {
    entity_category: 'categoryValue',
    contract_type: 'contractTypeValue',
    deviation_status: 'deviationStatusValue',
    owning_entity: 'entityValue',
    cpds_project_id: 'cpds',
    project_id: 'project_id',
    project_classification: 'classificationValue',
    project_type: 'projectTypeValue',
    design_executive_director: 'designProjectOwner',
    project_status: 'projectStatus',
    location: 'locationValue',
    portfolio_manager: 'portFolio',
    design_project_manager: 'designManager',
    delivery_project_manager: 'delivery_project_manager',
    controls_manager: 'control',
    procurement_manager: 'procure',
    executive_director: 'executive',
    director: 'deliveryDirector',
    decree_date: 'startYear',
    label: 'labelValue',
    overall_forecasted_finish_date: 'overall_forecasted_finish_date',
  }
  return mappings[field] || ''
}

const getValue = (val: any) => {
  return val === null || val === undefined || val?.length === 0 ? 'Blanks' : val
}
/**
 * Processes filter options based on type
 */
export const processFilterOptions = (filterData: (string | null)[], type?: string): FilterOptionType[] => {
  // Handle different filter types
  if (type === 'multiSelect' || type === 'semiColon') {
    // Split character based on type
    const splitChar = type === 'multiSelect' ? ',' : ';'

    // Split values and flatten array
    const separatedItems = filterData?.flatMap((item) => item?.split(splitChar)?.map((val: string) => val?.trim()))

    // Map to option format and sort
    const options = separatedItems?.map((item) => ({
      label: getValue(item),
      value: getValue(item),
    }))

    return sortItemsAlphabetically(options)
  } else if (type === 'projectStatus') {
    // Predefined order for project status
    const predefinedOrder = [
      'Initiation',
      'LDC Procurement',
      'Design',
      'Contractor Procurement',
      'Construction',
      'DLP and Project Closeout',
      'Transfer In Progress',
    ]

    // Map to option format
    const options = filterData.map((item) => ({
      label: item,
      value: item,
    }))

    // Reorder according to predefined order
    return reorderArray(options as any, predefinedOrder)
  } else if (type === 'startYear') {
    // Filter valid dates and extract years
    const validDates = filterData?.filter((date: string | null) => {
      const parsedDate = date ? parse(date, 'yyyy-MM-dd', new Date()) : null
      return date ? isValid(parsedDate) : null
    }) as string[]

    // Create unique year options
    const dateArray = Array.from(
      new Map(
        validDates?.map((date: string) => {
          const parsedDate = parse(date, 'yyyy-MM-dd', new Date())
          const year = getYear(parsedDate)
          return [year, { label: year?.toString(), value: year?.toString() }]
        }),
      ).values(),
    ).sort((a, b) => Number(a.value) - Number(b.value)) // Numeric sorting
    const isBlankExist = filterData?.includes(null)
    return isBlankExist ? [...dateArray, { label: 'Blanks', value: 'Blanks' }] : dateArray
  } else if (type === 'date') {
    const options = filterData?.map((res: any) => ({
      label:
        res !== null || res !== undefined || res?.length > 0 ? (DDMMYYYYformate(res as string) ?? 'Blanks') : 'Blanks',
      value:
        res !== null || res !== undefined || res?.length > 0 ? (DDMMYYYYformate(res as string) ?? 'Blanks') : 'Blanks',
    }))
    return options
  } else if (type === 'array') {
    // TODO
    const uniqueValues = [...new Set(filterData.flat())]
    const options = uniqueValues.map((item) => ({
      label: getValue(item),
      value: getValue(item),
    }))
    return sortItemsAlphabetically(options)
  } else {
    // Standard options processing
    const options = filterData.map((item) => ({
      label: getValue(item),
      value: getValue(item),
    }))

    return sortItemsAlphabetically(options)
  }
}

/**
 * Sorts items alphabetically by label
 */
export const sortItemsAlphabetically = (items: FilterOptionType[]): FilterOptionType[] => {
  return [...items].sort((a, b) => naturalSort(String(a.label), String(b.label)))
}

/**
 * Reorders array based on predefined order
 */
export const reorderArray = (inputArray: FilterOptionType[], order: string[]): FilterOptionType[] => {
  const orderedPart: FilterOptionType[] = []
  const unorderedPart: FilterOptionType[] = []

  inputArray.forEach((item) => {
    if (order.includes(String(item.value))) {
      orderedPart.push(item)
    } else {
      unorderedPart.push(item)
    }
  })

  // Sort the ordered part based on the predefined order
  orderedPart.sort((a, b) => order.indexOf(String(a.value)) - order.indexOf(String(b.value)))

  // Return the ordered array followed by the unordered items
  return [...orderedPart, ...unorderedPart]
}

export const isValueInclude = (filterValue: string[], val: any) => {
  return (
    ((val === null || val === undefined || val.toString().trim() === '') && filterValue?.includes('Blanks')) ||
    filterValue?.map((res) => res?.trim())?.includes(val)
  )
}

/**
 * Filters projects based on search criteria
 */
export const filterProjects = (
  projects: any[],
  state: SearchDrawerStateType,
  excludeField: string | null = null,
): IProjects[] => {
  return projects?.filter((project: any) => {
    // Normalize search query
    const normalizedSearchQuery = state.searchQuery?.toLowerCase().replace(/\s+/g, ' ').trim()
    const normalizedProjectName = project?.project_name?.toLowerCase().replace(/\s+/g, ' ').trim()

    // Apply all filters
    const matchesSearch = !normalizedSearchQuery || normalizedProjectName?.includes(normalizedSearchQuery)

    const matchCpds =
      excludeField !== 'CPDS_project_id' && state.cpds.length > 0
        ? isValueInclude(state.cpds, project?.CPDS_project_id)
        : true

    const matchProjectId = state.project_id.length === 0 || isValueInclude(state.project_id, project?.project_id)

    const matchCategory =
      excludeField !== 'entity_category' && state.categoryValue.length > 0
        ? isValueInclude(state.categoryValue, project?.MasterEntityCategory?.entity_category)
        : true

    const matchesContractType =
      excludeField !== 'contract_type' && state.contractTypeValue.length > 0
        ? matchSemicolonSeparated(state.contractTypeValue, project?.contract_type)
        : true

    const matchesDeviationStatus =
      excludeField !== 'deviation_status' && state.deviationStatusValue.length > 0
        ? isValueInclude(state.deviationStatusValue, project?.deviation_status)
        : true

    const matchesLabel =
      excludeField !== 'label' && state.labelValue.length > 0 ? matchLabelValue(state.labelValue, project) : true

    const matchesEntity =
      excludeField !== 'owning_entity' && state.entityValue.length > 0
        ? isValueInclude(state.entityValue, project?.owning_entity)
        : true

    const matchesClassification =
      excludeField !== 'project_classification' && state.classificationValue.length > 0
        ? isValueInclude(state.classificationValue, project?.project_classification?.trim())
        : true

    const matchesProjectType =
      excludeField !== 'project_type' && state.projectTypeValue.length > 0
        ? isValueInclude(state.projectTypeValue, project?.project_type)
        : true

    const matchesLocation =
      excludeField !== 'location' && state.locationValue.length > 0
        ? matchSemicolonSeparated(state.locationValue, project?.location)
        : true

    const matchesProjectStatus =
      excludeField !== 'project_status' && state.projectStatus.length > 0
        ? isValueInclude(state.projectStatus, project?.project_status)
        : true

    const portfolio_manager =
      excludeField !== 'portfolio_manager' && state.portFolio.length > 0
        ? isValueInclude(state.portFolio, project?.portfolio_manager)
        : true

    const design_project_manager =
      excludeField !== 'design_project_manager' && state.designManager.length > 0
        ? matchSemicolonSeparated(state.designManager, project?.design_project_manager)
        : true

    const matchSvp =
      (excludeField !== 'delivery_project_manager' && state.delivery_project_manager.length === 0) ||
      matchSemicolonSeparated(state.delivery_project_manager, project?.delivery_project_manager)

    const executive_director =
      excludeField !== 'executive_director' && state.executive.length > 0
        ? matchSemicolonSeparated(state.executive, project?.executive_director)
        : true

    const delivery_director =
      excludeField !== 'director' && state.deliveryDirector.length > 0
        ? isValueInclude(state.deliveryDirector, project?.director?.trim())
        : true

    const controls_manager =
      excludeField !== 'controls_manager' && state.control.length > 0
        ? isValueInclude(state.control, project?.controls_manager)
        : true

    const procurement_manager =
      excludeField !== 'procurement_manager' && state.procure.length > 0
        ? matchSemicolonSeparated(state.procure, project?.procurement_manager)
        : true

    const design_executive_director =
      excludeField !== 'design_executive_director' && state.designProjectOwner.length > 0
        ? matchSemicolonSeparated(state.designProjectOwner, project?.design_executive_director)
        : true

    const matchStartYear =
      excludeField !== 'decree_date' && state.startYear.length > 0
        ? matchDateYear(state.startYear, project?.decree_date)
        : true

    const matchOverallForecastedFinishDate =
      excludeField !== 'overall_forecasted_finish_date' && state.overall_forecasted_finish_date.length > 0
        ? isValueInclude(state.overall_forecasted_finish_date, DDMMYYYYformate(project?.overall_forecasted_finish_date))
        : true

    return (
      matchSvp &&
      matchesSearch &&
      matchCpds &&
      matchProjectId &&
      matchCategory &&
      matchesEntity &&
      matchesClassification &&
      matchesProjectType &&
      matchesLocation &&
      portfolio_manager &&
      design_project_manager &&
      executive_director &&
      delivery_director &&
      controls_manager &&
      procurement_manager &&
      matchesProjectStatus &&
      design_executive_director &&
      matchStartYear &&
      matchOverallForecastedFinishDate &&
      matchesDeviationStatus &&
      matchesContractType &&
      matchesLabel
    )
  })
}

/**
 * Matches array values
 */
export const matchArrayValue = (filterVal: string[], fieldValue: string[]): boolean => {
  return (
    filterVal.length === 0 ||
    (fieldValue?.length === 0 && filterVal?.includes('Blanks')) ||
    fieldValue?.some((val) => filterVal.includes(val))
  )
}

/**
 * Matches semicolon-separated values
 */
export const matchSemicolonSeparated = (filterVal: string[], fieldValue: string | undefined): boolean => {
  if (fieldValue === undefined) return false
  const fieldValuesArray = fieldValue !== null ? fieldValue?.split(';').map((val) => val.trim()) : []
  return (
    filterVal.length === 0 ||
    ((fieldValue === null || fieldValue.toString().trim() === '') && filterVal?.includes('Blanks')) ||
    fieldValuesArray?.some((val) => filterVal.includes(val))
  )
}

/**
 * Matches Boolean values
 */
// export const matchBooleanValue = (filterVal: string[], project: Record<string, any>) => {
//   console.log('filterVal: ', filterVal)
//   const isValueExist = filterVal.some((field: string) => {
//     const value = project[field]

//     if (typeof value === 'string') {
//       return value.trim() !== '' && value !== null
//     }

//     return Boolean(value)
//   })

//   return filterVal.length === 0 || isValueExist
// }

export const matchLabelValue = (filterVal: string[], project: Record<string, any>): boolean => {
  if (filterVal.length === 0) return true
  const today = new Date()

  const isValueExist = filterVal?.some((field: string) => {
    let rawValue = project[field]

    //NOTE : EOT_submission_date is greater then today's date and eot_submitted is true
    if (field === 'EOT_to_be_submitted') {
      const eotDate = new Date(project?.eot_submission_date)
      const isSubmitted = project?.eot_submitted
      const shouldBeTrue = isSubmitted && eotDate > today
      return shouldBeTrue
    }
    if (typeof rawValue === 'string') {
      const value = rawValue.trim()
      if (field === 'is_subject_to_rebaseline') {
        return value.includes('Subject to Rebaseline') || value.includes('1') || value.includes('true')
      }
      if (
        ['budget_uplift_submitted', 'tip_submitted', 'eot_submitted', 'potential_budget_uplift_submitted'].includes(
          field,
        )
      ) {
        return value.includes('1') || value.includes('true')
      }

      return value !== '' || value !== null
    }

    return Boolean(rawValue)
  })

  return isValueExist
}

/**
 * Matches comma-separated values
 */
export const matchCommaSeparatedValues = (filterValues: string[], projectLocation: string | undefined): boolean => {
  if (!projectLocation) return false
  const projectLocations = projectLocation.split(',').map((loc) => loc.trim())
  return filterValues.some((filterValue) => projectLocations.includes(filterValue))
}

/**
 * Matches year from date string
 */
export const matchDateYear = (startYears: any[], projectDate: string): boolean => {
  // if (!projectDate) return false

  const projectYear = projectDate !== null ? getYear(parse(projectDate, 'yyyy-MM-dd', new Date())) : ''
  return (
    startYears.length === 0 ||
    ((projectDate === null || projectDate.toString().trim() === '') && startYears?.includes('Blanks')) ||
    startYears.includes(projectYear?.toString())
  )
}

/**
 * Applies filters to the search drawer state
 */
export const applyFilters = (filterField: string, options: string[], state: SearchDrawerStateType): void => {
  const applyMap: Record<string, (value: string[]) => void> = {
    categoryValue: state.setCategoryValueApi,
    entityValue: state.setEntityValueApi,
    cpds: state.setCpdsApi,
    project_id: state.setProjectIdApi,
    classificationValue: state.setClassificationValueApi,
    projectTypeValue: state.setProjectTypeValueApi,
    locationValue: state.setLocationValueApi,
    portFolio: state.setPortFolioApi,
    designManager: state.setDesignManagerApi,
    executive: state.setExecutiveApi,
    deliveryDirector: state.setDeliveryDirectorApi,
    control: state.setControlApi,
    procure: state.setProcureApi,
    projectStatus: state.setProjectStatusApi,
    designProjectOwner: state.setDesignProjectOwnerApi,
    svp: state.setSvpApi,
    startYear: state.setStartYearApi,
    contractTypeValue: state.setContractTypeApi,
    deviationStatusValue: state.setDeviationStatusApi,
    labelValue: state.setLabelValueApi,
    overall_forecasted_finish_date: state.setOverallForecastedFinishDateApi,
  }

  const applyAction = applyMap[filterField]
  if (applyAction) {
    applyAction(options)
  } else {
    console.warn(`Unknown filter field: ${filterField}`)
  }
}

/**
 * Resets all filters in the search drawer state
 */
export const resetAllFilters = (state: SearchDrawerStateType): void => {
  const setters = [
    state.setCategoryValueApi,
    state.setEntityValueApi,
    state.setCpdsApi,
    state.setProjectIdApi,
    state.setClassificationValueApi,
    state.setProjectTypeValueApi,
    state.setLocationValueApi,
    state.setPortFolioApi,
    state.setDesignManagerApi,
    state.setExecutiveApi,
    state.setDeliveryDirectorApi,
    state.setControlApi,
    state.setProcureApi,
    state.setProjectStatusApi,
    state.setDesignProjectOwnerApi,
    state.setSvpApi,
    state.setStartYearApi,
    state.setContractTypeApi,
    state.setDeviationStatusApi,
    state.setLabelValueApi,
    state.setOverallForecastedFinishDateApi,
  ]

  setters.forEach((setter) => setter([]))
}

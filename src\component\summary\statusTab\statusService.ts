import { addDays, format, isValid, isWithinInterval, parse, parseISO } from 'date-fns'
import { showCustomToast } from '../../toast/ToastManager'
import { showCustomToastError } from '../../toast/utils'
import { getLatestStageStatuses } from '../helper'
import { ERROR_MESSAGE, STATUS_OPTIONS } from '@/src/constant/enum'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'

import { payloadDateFormate } from '@/src/utils/dateUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

// export const fetchData = async (currentPeriod: any, getStatusApi: any, getCommercialApi: any, setLoader: any) => {
//   try {
//     await Promise.all([getStatusApi({ period: currentPeriod }), getCommercialApi({ period: currentPeriod })])
//     setLoader(false)
//   } catch (error) {
//     console.error('Error fetching status data:', error)
//   }
// }

export function addDaysToDate(currentDate: string, count: number) {
  // Log the type of currentDate

  // Validate the input date and count
  if (!currentDate || isNaN(count)) {
    return ''
  }

  // Parse the current date string
  const dateObject = parseISO(currentDate)

  // Validate the date object
  if (isNaN(dateObject.getTime())) {
    return '' // Return an empty string if the date is invalid
  }

  // Add the specified count of days
  const newDate = addDays(dateObject, Number(count))

  // Return the new date in ISO format (YYYY-MM-DD)
  return format(newDate, 'yyyy-MM-dd') // Format YYYY-MM-DD
}

// Update project status based on automation logic
export const updateProjectStatusAutomation = async (
  project: any,
  statusesData: any,
  updateMasterProjectApi: any,
  currentPeriod: any,
) => {
  // If the project status is one of these special statuses, exit early

  // Filter statusesData to only include entries related to the current project
  const statusFilterByProject = statusesData.filter((item: any) => item.project_name === project?.project_name)

  let projectStatuses: any[] = []
  let forecastDates: any = []
  let planFinishDate: any = []
  // Collect relevant data based on the stage_status
  statusFilterByProject.forEach((item: any) => {
    if (
      [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT].includes(
        item.MasterProjectStageStatus.project_stage_status,
      )
    ) {
      forecastDates.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        forecasted_date: item.forecasted_end_date,
      })
      planFinishDate.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        planed_date: item.plan_end_date,
      })
      projectStatuses.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        actual_percentage: Number(item.actual_plan_percentage) ?? null, // Add actual percentage for relevant stages
      })
    } else if ([LDC_PROCUREMENT, DESIGN, INITIATION].includes(item.MasterProjectStageStatus.project_stage_status)) {
      forecastDates.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        forecasted_date: item.forecast_finish,
      })
      planFinishDate.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        planed_date: item.baseline_plan_finish,
      })
      projectStatuses.push({
        phase: item?.phase,
        project_stage_status: item.MasterProjectStageStatus.project_stage_status,
        actual_percentage: Number(item.actual_percentage) ?? null, // Add actual percentage for initial stages
      })
    }
  })

  const filterOfForecast = forecastDates.filter((item: any) => item?.project_stage_status !== DLP_PROJECT_CLOSEOUT)
  const filterOfPlan = planFinishDate.filter((item: any) => item?.project_stage_status !== DLP_PROJECT_CLOSEOUT)

  const findMaxDateOfForecastedDate = (stages: any) => {
    let maxDate: any = null
    stages.forEach((stage: any) => {
      if (!stage.forecasted_date) return ''
      const currentDate = new Date(stage.forecasted_date)
      if (!maxDate || currentDate > maxDate) {
        maxDate = currentDate
      }
    })
    if (!maxDate) return ''

    const date = new Date(maxDate)

    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const formattedDate = `${year}-${month}-${day}`

    return formattedDate
  }

  const findMaxDateOfPlanedDate = (stages: any) => {
    let maxDate: any = null
    stages.forEach((stage: any) => {
      if (!stage.planed_date) return ''
      const currentDate = new Date(stage.planed_date)
      if (!maxDate || currentDate > maxDate) {
        maxDate = currentDate
      }
    })
    if (!maxDate) return ''

    const date = new Date(maxDate)

    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const formattedDate = `${year}-${month}-${day}`

    return formattedDate
  }

  const forecastedMaxDate = findMaxDateOfForecastedDate(filterOfForecast)
  const planMaxDate = findMaxDateOfPlanedDate(filterOfPlan)

  // Clone the project data and remove unnecessary fields before updating
  const data: any = { period: currentPeriod, project_name: project?.project_name }

  const targetWord = 'Enabling' // The word to check for

  // Filter out any string that contains the exact word "Enabling" (case-insensitive)
  const statusFilterByProjectByPhase = projectStatuses.filter(
    (item: any) => !item?.phase?.toLowerCase().includes(targetWord.toLowerCase()), // Exclude strings containing "Enabling"
  )
  const status = getLatestStageStatuses(statusFilterByProjectByPhase, project?.owning_entity)
  const fieldsToRemove: any = [
    'decree_files',
    'plot_plan_files',
    'updated_by',
    'project_management_updated_by',
    'health_safety_updated_by',
    'static_images_files',
    'dynamic_images_files',
    'additional_documents_files',
    'section',
  ]

  fieldsToRemove.forEach((field: any) => delete data[field]) // Remove fields that are not needed in the update payload
  if (
    [
      STATUS_OPTIONS.ON_HOLD?.toLowerCase(),
      STATUS_OPTIONS.TRANSFER_IN_PROGRESS?.toLowerCase(),
      STATUS_OPTIONS.TRANSFERRED?.toLowerCase(),
      STATUS_OPTIONS.CANCELED?.toLowerCase(),
    ].includes(project.project_status?.toLowerCase())
  ) {
  } else {
    data['project_status'] = status // Set the project's new status based on the latest stage statuses
  }

  data['overall_forecasted_finish_date'] = forecastedMaxDate // Set the project's new status based on the latest stage statuses
  data['overall_planned_finish_date'] = planMaxDate
  data['decree_end_date'] = data?.decree_end_date ? project?.decree_end_date : planMaxDate
  // Set the project's new status based on the latest stage statuses
  // Update the project status via the API
  updateMasterProjectApi(
    { id: project?.project_name as string, data },
    {
      onSuccess: () => {
        // successToast('Project updated successfully!')
      },
      onError: (err: any) => {
        if (err?.response?.data?.exitCode === 140) {
          return
        }
        errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
      },
    },
  )
}

export const onCellService = async (
  cell: any,
  newValue: any,
  updateStatus: any,
  statuses: any[],
  statusData: any[],
  updateStatusApi: any,
  getStatusApi: any,
  currentPeriod: any,
  project: any,
  updateMasterProjectApi: any,
  refetch?: any,
) => {
  const status: any = statuses.find((item: any) => item.id.toString() === updateStatus?.id.toString())
  let row = statusData.find((item: any) => item.id.toString() === updateStatus?.id.toString())

  let nextRow = statusData.find(
    (item: any) => item.sortingOrder.toString() === Number(row?.sortingOrder + 1)?.toString(),
  )

  if (!row || !status) return

  let updatedData: any = {
    [cell.columnId]: newValue, // Dynamically set the updated field
  }

  const prepareUpdateData = (nextRow: any, updatedNextDate: any, field: string) => ({
    [field]: updatedNextDate,
    master_project_stage_status_id: status?.MasterProjectStageStatus?.id,
    last_updated: new Date().toISOString(),
    period: currentPeriod,
    project_name: project?.project_name,
  })

  const getUpdatedField = (nextRow: any, diffType: string) => {
    if (
      nextRow?.stageStatus === LDC_PROCUREMENT ||
      nextRow?.stageStatus === DESIGN ||
      nextRow?.stageStatus === INITIATION
    ) {
      return diffType === 'baseLineDiff' ? 'baseline_plan_finish' : 'forecast_finish'
    } else {
      return diffType === 'baseLineDiff' ? 'plan_end_date' : 'forecasted_end_date'
    }
  }

  const handleUpdate = async (row: any, payload: any) => {
    const res: Record<string, any> = await updateStatusApi({
      id: row.id,
      data: { ...payload },
    })
    if (res?.payload?.response?.data?.success === false) {
      const message = res?.payload?.response?.data?.message || 'Somethings went wrong'
      showCustomToastError(message)
    }
    if (res.payload.success) {
      refetch && refetch()
      const statusRes: Record<string, any> = await getStatusApi({
        period: currentPeriod,
        project_name: project?.project_name,
      })
      // if (statusRes.payload.success) {
      //   await updateProjectStatusAutomation(project, statusRes.payload.data, updateMasterProjectApi, currentPeriod)
      return statusRes.payload.success
      // }
    } else {
      showCustomToast('Error', 'error')
    }
    return true
  }

  // Check for baseline or forecast differences and handle updates
  if (nextRow && payloadDateFormate(row?.baselinePlanFinish) && cell.columnId === 'baseLineDiff') {
    const updatedField = getUpdatedField(nextRow, 'baseLineDiff')
    const currentDate: any = payloadDateFormate(row?.baselinePlanFinish)
    const updatedNextDate = addDaysToDate(currentDate, newValue)
    const payload = prepareUpdateData(nextRow, updatedNextDate, updatedField)
    await handleUpdate(nextRow, payload)
    return true
  } else if (nextRow && payloadDateFormate(row?.forecastFinish) && cell.columnId === 'forecastDiff') {
    const updatedField = getUpdatedField(nextRow, 'forecastDiff')
    const currentDate: any = payloadDateFormate(row?.forecastFinish)
    const updatedNextDate = addDaysToDate(currentDate, newValue)
    const payload = prepareUpdateData(nextRow, updatedNextDate, updatedField)
    await handleUpdate(nextRow, payload)
    return true
  }

  const getCommonUpdateData = (row: any) => {
    const payload: any = {}
    if (row.sortingOrder) {
      payload.project_status_sorting_order = row.sortingOrder
    }
    if (row.actualLWeek && row.actualLWeek !== '-') {
      payload.actual_progress_percentage_of_last_week = row.actualLWeek
    }
    if (row.contractor && row.contractor !== '-') {
      payload.contractor = row.contractor
    }
    if (row.pmcConsultant && row.pmcConsultant !== '-') {
      payload.pmc_consultant = row.pmcConsultant
    }
    if (row.planLWeek && row.planLWeek !== '-') {
      payload.actual_progress_percentage_for_last_week = row.planLWeek
    }
    if (row.phase) {
      payload.phase = row.phase
    }
    if (row.stageStatus) {
      // TODO : Need to check
      payload.stage_status = row.stageStatus
    }
    if (row.subStatus) {
      // TODO : Need to check
      payload.sub_stage = row.subStatus
    }
    // Always update the last_updated field
    payload.last_updated = new Date().toISOString()
    payload.period = currentPeriod
    payload.project_name = project?.project_name
    payload.master_project_stage_status_id = status?.MasterProjectStageStatus?.id

    if (row.designStageWeightage && !isNaN(row.designStageWeightage)) {
      payload.design_stage_weightage = Number(row.designStageWeightage) / 100
    }
    if (row.phaseWeightage && !isNaN(row.phaseWeightage)) {
      payload.phase_weightage = (Number(row.phaseWeightage) / 100).toString()
    }
    return payload
  }

  const additionalUpdateData = getAdditionalUpdateData(
    status?.MasterProjectStageStatus?.project_stage_status,
    updatedData,
  )

  await handleUpdate(row, { ...getCommonUpdateData(updatedData), ...additionalUpdateData })
  return true
}

export const getAdditionalUpdateData = (stage_status: string, row: any) => {
  const payload: any = {}
  const isLdcDesignOrInitiation = [LDC_PROCUREMENT, DESIGN, INITIATION].includes(stage_status)
  const isContractorOrConstruction = [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT].includes(stage_status)

  if (isLdcDesignOrInitiation) {
    if ('actualPlanPercentage' in row) {
      payload.actual_percentage = row.actualPlanPercentage.toString()
        ? parseFloat((Number(row.actualPlanPercentage) / 100).toFixed(4))
        : null
    }
    if ('revPlanPercentage' in row) {
      payload.rev_plan_percentage = row.revPlanPercentage.toString()
        ? parseFloat((Number(row.revPlanPercentage) / 100).toFixed(4))
        : null
    }
    if ('baselinePlanFinish' in row) {
      payload.baseline_plan_finish = payloadDateFormate(row.baselinePlanFinish)
    }
    if ('revisedBaselineFinish' in row) {
      payload.revised_baseline_finish = payloadDateFormate(row.revisedBaselineFinish)
    }
    if ('forecastFinish' in row) {
      payload.forecast_finish = payloadDateFormate(row.forecastFinish)
    }
  }

  if (isContractorOrConstruction) {
    if ('actualPlanPercentage' in row) {
      payload.actual_plan_percentage = row.actualPlanPercentage.toString()
        ? parseFloat((Number(row.actualPlanPercentage) / 100).toFixed(4))
        : null
    }
    if ('revPlanPercentage' in row) {
      payload.revised_plan_percentage = row.revPlanPercentage
        ? parseFloat((Number(row.revPlanPercentage) / 100).toFixed(4))
        : null
    }
    if ('baselinePlanFinish' in row) {
      payload.plan_end_date = payloadDateFormate(row.baselinePlanFinish)
    }
    if ('revisedBaselineFinish' in row) {
      payload.revised_plan_end_date = payloadDateFormate(row.revisedBaselineFinish)
    }
    if ('forecastFinish' in row) {
      payload.forecasted_end_date = payloadDateFormate(row.forecastFinish)
    }
  }

  if ('plan_duration' in row) {
    payload.plan_duration = row?.plan_duration?.toString() ? Number(row.plan_duration) : null
  }
  return payload
}

export const isWithinLast7DaysInArray = (dateStrings: string[]): boolean => {
  const currentDate = new Date()
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(currentDate.getDate() - 7)

  const parseDateWithFallback = (dateString: string): Date | null => {
    try {
      const trimmedDateString = dateString.trim()

      // Fix the 'T' separator in case the date is in ISO format or something similar
      const correctedDateString = trimmedDateString.replace('T', ' ') // Replace 'T' with a space

      // Attempt to parse with 'dd-MM-yyyy HH:mm' format
      const parsedDate = parse(correctedDateString, 'dd-MM-yyyy HH:mm', new Date())
      if (isValid(parsedDate)) return parsedDate

      // If parsing fails, fallback to standard Date constructor
      const fallbackDate = new Date(trimmedDateString)
      return isValid(fallbackDate) ? fallbackDate : null
    } catch (error) {
      return null
    }
  }

  return dateStrings.some((dateString) => {
    if (!dateString?.trim()) return false // Skip empty or whitespace-only strings

    const parsedDate = parseDateWithFallback(dateString)

    // Check if the date is valid and within the last 7 days
    return parsedDate && isValid(parsedDate) && isWithinInterval(parsedDate, { start: sevenDaysAgo, end: currentDate })
  })
}

export const handleLeadTime = async (
  dateGap: number,
  statuses: Record<string, any>,
  selectRecords: number[],
  updateStatusApi: (payload: any) => Promise<any>,
  getStatusApi: (payload: any) => Promise<any>,
  field: string,
  currentPeriod: string,
  projectName: string,
) => {
  // Sort selectRecords based on the sortingOrder in statuses
  selectRecords.sort((a, b) => {
    const orderA = statuses.find((item: any) => item.id === a)?.sortingOrder || 0
    const orderB = statuses.find((item: any) => item.id === b)?.sortingOrder || 0
    return orderA - orderB
  })

  // Iterate through each selected record
  for (const id of selectRecords) {
    const rec = statuses.find((item: any) => item?.id === id)
    if (!rec) {
      console.warn(`Record with id ${id} not found in statuses.`)
      continue // Skip if record is not found
    }

    const currentDate: any = payloadDateFormate(rec[field])
    const payload: {
      period: string
      project_name: string
      last_updated: string
      baseline_plan_finish?: string
      forecast_finish?: string
      plan_end_date?: string
      forecasted_end_date?: string
    } = {
      period: currentPeriod,
      project_name: projectName,
      last_updated: new Date().toISOString(),
    }

    const date = addDaysToDate(currentDate, dateGap)
    const getField = () => {
      if (rec?.stageStatus === LDC_PROCUREMENT || rec?.stageStatus === DESIGN || rec?.stageStatus === INITIATION) {
        return field === 'baselinePlanFinish' ? 'baseline_plan_finish' : 'forecast_finish'
      } else {
        return field === 'baselinePlanFinish' ? 'plan_end_date' : 'forecasted_end_date'
      }
    }
    const updateFieldName = getField()
    payload[updateFieldName] = date

    // Call updateStatusApi for the current record
    const res = await updateStatusApi({ id: rec?.id, data: { ...payload } })
    if (!res?.payload?.success) {
      console.error(`Update failed for record with id ${rec?.id}.`)
      return false // Exit if any update fails
    }
  }

  // After all updates are successful, call getStatusApi
  const statusRes: Record<string, any> = await getStatusApi({
    period: currentPeriod,
    project_name: projectName,
  })

  if (!statusRes?.payload?.success) return
  return true
}

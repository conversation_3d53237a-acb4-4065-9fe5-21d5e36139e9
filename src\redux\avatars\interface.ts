import { StatusEnum } from '../types'

export interface IinitialAvatar {
  getAllAvatarsStatus: StatusEnum
  avatars: AvatarData | null
}

interface Avatar {
  name: string
  url: string
}

export interface AvatarData {
  [category: string]: Avatar[]
}

export interface AvatarItem {
  name: string
  url: string
}

export interface IAvatar {
  categoryName: string
  avatars: AvatarItem[]
}

export interface IGetAvatarsResponse {
  data: AvatarData
  message: string
  success: boolean
}

@import '/styles/color.scss';

.container {
  background: linear-gradient(180deg, rgb(234, 234, 234) 0%, rgb(244, 246, 248) 40%);
  padding: 16px;
  .topSection {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px;
    margin-bottom: 16px;
  }
  .sectionSticky {
    height: calc(100vh - 280px);
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 4px;
    }
  }
}
.comboBoxInput > div > div {
  background-color: #fff !important;
}

.lastUpdatedText {
  margin-right: 10px !important;
}
.multiSelect {
  > div > div {
    padding: 8px !important;
    background-color: #ffffff !important;

    > div {
      line-height: 16px !important;
      font-size: 14px !important;
    }
  }
}
.header {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 8px 20px;
  background: #eeeeec;
  border-radius: 0 8px 8px 0;
  min-width: 178px;
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  color: $DARK;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  padding-right: 8px;
}
.content {
  justify-content: space-between;
  align-items: center;
  margin: 0;
  position: relative;
}

.phaseChipContainer {
  margin-bottom: 10px;

  .phaseChip {
    border-radius: 4px;
    border: none;
  }

  .phaseChipText {
    font-weight: 500;
    min-width: 35px;
    font-size: 14px;
  }
}

.comboBoxHighlight > div {
  border: 1px solid rgb(40, 101, 220) !important;
  border-radius: 3px;
}

.comboBoxHighlight > div > div {
  background-color: #f0f8ff !important;
}

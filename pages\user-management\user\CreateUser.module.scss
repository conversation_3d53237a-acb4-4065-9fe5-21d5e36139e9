@import '/styles/color.scss';

.container {
  .header {
    cursor: pointer;
    display: flex;
    margin: 22px 0px 22px 24px;
    gap: 10px;
    .detailsText {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
  .content {
    margin: 20px 20px 0px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .form {
    display: flex;
    gap: 20px;
    flex-direction: column;
  }
}

.textField {
  > div > div > input {
    min-width: 400px;
    width: 100%;
    border: 1px solid $LIGHT;
  }
}
.headerTitle {
  padding: 13px 0px 14px 24px;
  border-bottom: 1px solid $LIGHT_200;
}

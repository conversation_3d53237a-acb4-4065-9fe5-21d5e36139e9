@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 1.875rem; // 4px 30px → rem
  border-bottom: 1px solid $LIGHT_200;
  background-color: $WHITE;
}

.header {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem; // 12px → rem

  .heyText {
    display: flex;
    align-items: center;
    gap: 0.375rem; // 6px → rem
  }

  .feedbackBtn {
    margin-left: 20px;
  }
}

.buttons {
  display: flex;
  align-items: center;

  .timerIcon {
    cursor: default;
    display: flex;
    align-items: center;
    gap: 0.5rem; // 8px → rem
    padding: 0.375rem 0.75rem; // 6px 12px → rem
    border-radius: 0.5rem; // 8px → rem
    transition: all 0.3s ease-in-out;

    .date {
      font-family: 'Poppins', sans-serif;
      font-size: 0.875rem; // 14px → rem
      font-weight: 500;
      color: $DARK_100;
    }
  }
}

.drawerRoot {
  > div {
    backdrop-filter: blur(8px);
  }
}

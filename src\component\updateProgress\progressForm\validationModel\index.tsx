import React from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import styles from './ValidationModel.module.scss'
import TypographyField from '@/src/component/shared/typography'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'

interface ValidationModelProps {
  messages: string[]
  onClose: () => void
}

const ValidationModel: React.FC<ValidationModelProps> = ({ messages, onClose }) => {
  return (
    <div className={styles.container}>
      {/* Close Icon */}
      <div
        className={styles.closeIcon}
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      >
        <CloseCircleIcon />
      </div>

      {/* Validation Messages - Each message in a separate box */}
      <div className={styles.messageContainer}>
        {messages.map((message, index) => (
          <div key={index} className={styles.messageBox}>
            <WarningAmberOutlined className={styles.warningIcon} />
            <TypographyField variant="bodySemiBold" text={message} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default ValidationModel

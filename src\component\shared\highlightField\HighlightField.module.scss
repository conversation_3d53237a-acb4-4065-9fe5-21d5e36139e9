@import '/styles/color.scss';

.headerContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  .header {
    white-space: nowrap;
  }
  .divider {
    width: 100%;
    height: 1px;
    background-color: $LIGHT_200;
  }
}
.highlightField {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  justify-content: space-between;
  border-bottom: 1px solid $LIGHT_200;

  .dot {
    height: 2px;
    width: 2px;
    border: 30px;
  }
}
.deleteIcon,
.editIcon {
  cursor: pointer;
}
.heighLightFields {
  margin-top: 7px;
  border: 1px solid $LIGHT_200;
}

.actionBtnWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.textEditor {
  padding-right: 20px;
  width: 100%;
  height: auto !important;
  > div {
    width: 100%;
    background-color: #fafafa;
    border: none !important;
    border-radius: 4px;
    padding-top: 2px;
    padding-bottom: 5px;
  }
}

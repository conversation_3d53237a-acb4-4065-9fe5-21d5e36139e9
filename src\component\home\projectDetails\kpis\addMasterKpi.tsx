import React, { useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import styles from './AddMasterKpi.module.scss'
import ComboBox from '../../../shared/combobox'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData, sortStringArray } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useCreateK<PERSON>, useDelete<PERSON><PERSON>, useGetK<PERSON>, useUpdateKpis } from '@/src/hooks/useKpis'
import { useGetTypologies } from '@/src/hooks/useTypology'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'

const AddMasterKpi: React.FC<any> = ({ drawerStates, onClose }) => {
  const [tableData, setTableData] = useState([])
  const [selectedTopology, setSelectedTopology] = useState('')
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { addKpiDrawer } = drawerStates
  const { typologies } = useGetTypologies(addKpiDrawer)
  //REACT-QUERY
  const { kpis } = useGetKpis(addKpiDrawer)
  // MUTATIONS
  const { mutate: addMasterKpis } = useCreateKpis()
  const { mutate: deleteKpis } = useDeleteKpis()
  const { mutate: updateKpis } = useUpdateKpis()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      toast.success(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      toast.error(err.response?.data?.message || errorMsg)
    },
  })

  const typologyOption = useMemo(() => {
    return populateDropdownOptions(typologies, 'master_typology')
  }, [typologies])

  // const getData = async () => {
  //   getMasterKpisApi()
  //   // const res: Record<string, any> = await getMasterTypologiesApi()
  // }

  useEffect(() => {
    if (addKpiDrawer) {
      // getData()
      formik.resetForm()
      setTableData([])
      setSelectedTopology('')
    }
  }, [addKpiDrawer])

  useEffect(() => {
    kpis?.length > 0 && selectedTopology && handleFilter(selectedTopology)
  }, [kpis])

  const formik = useFormik({
    initialValues: {
      kpisValue: '',
      typology: '',
    },
    onSubmit: async (values) => {
      const payload = { kpi: values.kpisValue, project_type: values.typology }
      if (editIndex !== null) {
        updateKpis(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterKpis(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteKpis(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
  }

  const handleEditButtonClick = async (id: number) => {
    // const res: any = await getMasterKpisApi()
    const editControlKpis: any = kpis.find((kpi: any) => {
      //TODO
      return kpi.id === id
    })

    formik.setValues({
      kpisValue: editControlKpis?.kpi,
      typology: editControlKpis?.project_type,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_type',
      header: 'Project Type',
      flex: 1,
    },
    {
      accessorKey: 'kpi',
      header: 'Item',
      flex: 1,
    },

    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  const handleFilter = (val: any) => {
    const filterData: any = kpis?.filter((item) => {
      return item.project_type === val
    })
    setTableData(filterData)
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Item'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <ComboBox
              options={typologyOption}
              labelText={'Typology'}
              placeholder="Type of search..."
              value={
                formik.values.typology
                  ? {
                      label: formik.values.typology,
                      value: formik.values.typology,
                    }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  typology: val?.value || '',
                })
                setSelectedTopology(val?.value)
                handleFilter(val?.value)
              }}
            />
            <TextInputField
              className={styles.entityField}
              name="kpisValue"
              labelText={'Item'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.kpisValue}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.kpisValue?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={!formik.values.kpisValue?.trim() || !formik.values.typology?.trim()}
              >
                {'+ Add Item'}
              </Button>
            )}
          </div>
        </form>
        {kpis?.length >= 1 && (
          // <Table data={tableData} columns={columns} />
          <TanStackTable rows={sortData(kpis, 'project_type') as any} columns={columns} isOverflow={true} />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddMasterKpi

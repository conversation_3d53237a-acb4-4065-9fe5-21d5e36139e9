import React, { useState } from 'react'
import { useFormik } from 'formik'
import styles from './Consultants.module.scss'
import { IAddControlManagerProps } from './interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import NumberInputField from '@/src/component/shared/numberInputField'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateConsultant,
  useDeleteConsultant,
  useGetConsultant,
  useUpdateConsultant,
} from '@/src/hooks/useConsultant'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddConsultants: React.FC<IAddControlManagerProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)

  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addConsultants } = drawerStates
  //REACT-QUERY
  const { consultants } = useGetConsultant(addConsultants)
  // MUTATIONS
  const { mutate: addMasterConsultant } = useCreateConsultant()
  const { mutate: deleteConsultant } = useDeleteConsultant()
  const { mutate: updateConsultant } = useUpdateConsultant()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { consultant: '', icv_percentage: 0, long_name: '' },
    onSubmit: async (values) => {
      const payload = {
        consultant: values.consultant,
        icv_percentage: values.icv_percentage,
        long_name: values.long_name || null,
      }
      if (editIndex !== null) {
        updateConsultant(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterConsultant(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteConsultant(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = consultants.find((consultant) => {
      //TODO
      return consultant.id === id
    })

    formik.setValues({
      consultant: editControlManagers?.consultant,
      icv_percentage: editControlManagers.icv_percentage,
      long_name: editControlManagers?.long_name,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'consultant',
      header: 'Consultant',
      flex: 1,
    },
    {
      accessorKey: 'long_name',
      header: 'Long Name',
      flex: 1,
    },
    {
      accessorKey: 'icv_percentage',
      header: 'ICV(%)',
      size: 100,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Consultant'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="consultant"
              labelText={'Consultant'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.consultant}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.entityField}
              name="long_name"
              labelText={'Long Name'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.long_name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <NumberInputField
              className={styles.entityField}
              name="icv_percentage"
              labelText={'ICV(%)'}
              placeholder="1,2,3..."
              value={formik?.values?.icv_percentage}
              onChange={(value) => formik.setFieldValue('icv_percentage', parseFloat(String(value)) || 0)}
              isUpAndDowns={false}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.consultant?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.consultant?.trim()}
              >
                {'+ Add Consultant'}
              </Button>
            )}
          </div>
        </form>
        {consultants?.length >= 1 && (
          // <Table data={consultants} columns={columns} />
          <TanStackTable rows={sortData(consultants, 'consultant') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddConsultants

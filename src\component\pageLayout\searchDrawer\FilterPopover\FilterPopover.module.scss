.filterContainer {
  padding: 16px;
  width: var(--popover-width, 300px); // Default width, customizable via CSS variable
  max-height: 540px;
  overflow: auto;

  .MuiTextField-root {
    margin-bottom: 8px;
  }

  .MuiFormControlLabel-root {
    display: flex;
    align-items: center;
    margin-left: -8px;

    .MuiCheckbox-root {
      padding: 4px;
    }
  }

  .actionButtons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 12px;

    button {
      min-width: 60px;
    }
  }

  ::-webkit-scrollbar {
    width: 4px;
  }
  ::-webkit-scrollbar-track {
    border-radius: 5px;
  }
  ::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 5px;
  }
}

.options {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 400px;
  .opt {
    height: 400px;
    min-width: fit-content;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    margin-left: 40px;
    .filterBox {
      align-items: start;
      .filterOption {
        padding-top: 0px !important;
      }
    }
    .subs {
      margin-left: 20px;
    }
  }
}

.dateContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dateGroup {
  display: flex;
  flex-direction: column;
  .yearMonth {
    display: flex;
    align-items: center;

    .year {
      display: flex;
      align-items: center;

      span {
        font-size: 14px;
        color: #444444;
      }
    }
  }

  .months {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    gap: 2px;

    .monthGroup {
      display: flex;
      flex-direction: column;

      .month {
        display: flex;
        align-items: center;
        .monthCheckbox {
          width: 36px;
          height: 36px;
          margin-right: 6px;
        }

        span {
          font-size: 14px;
          color: #444444;
        }
      }

      .dates {
        margin-left: 56px;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        .dateCheckbox {
          width: 30px;
          height: 30px;
          margin-right: 10px;
        }
        .dateItem {
          display: flex;
          align-items: center;

          span {
            font-size: 14px;
            color: #444444;
          }
        }
      }
    }
  }
}

.accordion {
  box-shadow: none !important;
}

.summary {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  min-height: auto !important;
  > div {
    margin: 0px !important;
  }
}

.details {
  padding: 0px !important;
}

.monthDetails {
  padding: 0px !important;
}

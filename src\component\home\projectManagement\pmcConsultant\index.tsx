import React, { useState } from 'react'
import { useFormik } from 'formik'
import { IAddPmcConsultantProps } from './interface'
import styles from './PmcConsultant.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import NumberInputField from '@/src/component/shared/numberInputField'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreatePmcConsultant,
  useDeletePmcConsultant,
  useGetPmcConsultant,
  useUpdatePmcConsultant,
} from '@/src/hooks/usePmcConsultant'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddPmcConsultant: React.FC<IAddPmcConsultantProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)

  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addPmcConsultant } = drawerStates
  //REACT-QUERY
  const { pmcConsultants } = useGetPmcConsultant(addPmcConsultant)
  // MUTATIONS
  const { mutate: addMasterPmcConsultant } = useCreatePmcConsultant()
  const { mutate: deletePmcConsultant } = useDeletePmcConsultant()
  const { mutate: updatePmcConsultant } = useUpdatePmcConsultant()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { pmcConsultant: '', icv_percentage: 0, long_name: '' },
    onSubmit: async (values) => {
      const payload = {
        pmc_consultant: values.pmcConsultant,
        icv_percentage: values.icv_percentage,
        long_name: values.long_name || null,
      }
      if (editIndex !== null) {
        updatePmcConsultant(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterPmcConsultant(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deletePmcConsultant(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPortfolioManagers: any = pmcConsultants.find((pmcConsultant) => {
      //TODO
      return pmcConsultant.id === id
    })

    formik.setValues({
      pmcConsultant: editPortfolioManagers?.pmc_consultant,
      icv_percentage: editPortfolioManagers.icv_percentage,
      long_name: editPortfolioManagers?.long_name,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'pmc_consultant',
      header: 'PMC Consultant',
      flex: 1,
    },
    {
      accessorKey: 'long_name',
      header: 'Long Name',
      flex: 1,
    },
    {
      accessorKey: 'icv_percentage',
      header: 'ICV(%)',
      size: 100,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add PMC Consultant'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="pmcConsultant"
              labelText={'PMC Consultant'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.pmcConsultant}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.entityField}
              name="long_name"
              labelText={'Long Name'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.long_name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <NumberInputField
              className={styles.entityField}
              name="icv_percentage"
              labelText={'ICV(%)'}
              placeholder="1,2,3..."
              value={formik?.values?.icv_percentage}
              onChange={(value) => formik.setFieldValue('icv_percentage', parseFloat(String(value)) || 0)}
              isUpAndDowns={false}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.pmcConsultant?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.pmcConsultant?.trim()}
              >
                {'+ Add PMC Consultant'}
              </Button>
            )}
          </div>
        </form>
        {pmcConsultants?.length >= 1 && (
          // <Table data={pmcConsultants} columns={columns} />
          <TanStackTable rows={sortData(pmcConsultants, 'pmc_consultant') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddPmcConsultant

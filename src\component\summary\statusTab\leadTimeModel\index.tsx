import React, { useState } from 'react'
import styles from './LeadTimeModel.module.scss'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import NumberInputField from '@/src/component/shared/numberInputField'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'

interface ILeadModel {
  isLeadModel: string | null
  onClose: () => void
  applyLeadTime: (value: number | null, field: string) => Promise<any>
}

const LeadTimeModel: React.FC<ILeadModel> = ({ isLeadModel, onClose, applyLeadTime }) => {
  const [numberValue, setNumberValue] = useState<number | null>(null)
  const [loading, setLoading] = useState(false)

  const handleApply = async () => {
    setLoading(true)
    const field = isLeadModel === 'Apply Baseline Lead Time' ? 'baselinePlanFinish' : 'forecastFinish'
    try {
      await applyLeadTime(numberValue, field)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={styles.container}>
      {loading ? (
        <Loader />
      ) : (
        <>
          {' '}
          <CloseCircleIcon className={styles.closeIcon} onClick={onClose} />
          <NumberInputField
            value={numberValue}
            onChange={(value) => setNumberValue(value as number)}
            labelText={isLeadModel || 'Apply Lead Time'}
          />
          <Button variant="contained" onClick={handleApply} disabled={loading}>
            {loading ? 'Applying...' : 'Apply'}
          </Button>
        </>
      )}
    </div>
  )
}

export default LeadTimeModel

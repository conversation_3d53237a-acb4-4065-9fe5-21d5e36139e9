import { useEffect, useRef } from 'react'

type UseOutsideClickProps = {
  onOutsideClick: () => void
  enabled?: boolean
}

export function useOutsideClick<T extends HTMLElement = HTMLElement>({
  onOutsideClick,
  enabled = true,
}: UseOutsideClickProps) {
  const ref = useRef<T>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (enabled && ref.current && !ref.current.contains(event.target as Node)) {
        onOutsideClick()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onOutsideClick, enabled])

  return ref
}

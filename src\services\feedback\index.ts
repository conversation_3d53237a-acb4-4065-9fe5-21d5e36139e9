import { FeedbackPayload, IUpdateFeedback } from './interface'
import { api } from '@/src/api/axios'

export const FEEDBACK_CREATE_URL = `/feedback`

export const getFeedback = async () => {
  const response = await api.get(`${FEEDBACK_CREATE_URL}`)
  return response?.data
}

export const getFeedbackFileURL = async (fileName: string): Promise<{ url: string }> => {
  const response = await api.get(`${FEEDBACK_CREATE_URL}/file-fetch?name=${fileName}`)
  return response.data?.data
}

export const createFeedback = async (payload: FeedbackPayload) => {
  const formData = new FormData()
  formData.append('period', payload.currentPeriod)
  formData.append('rating', payload.rating)
  if (payload.workingWell) {
    formData.append('pros_feedback', payload.workingWell)
  }

  if (payload.doBetter) {
    formData.append('cons_feedback', payload.doBetter)
  }
  if (payload.attachment?.length) {
    payload.attachment.forEach((file) => {
      formData.append('attachments', file)
    })
  }
  const response = await api.post(`${FEEDBACK_CREATE_URL}`, formData)
  return response
}

export const updateMasterFeedback = async (payload: IUpdateFeedback): Promise<any> => {
  const { id } = payload
  const formData = new FormData()
  formData.append('period', payload.currentPeriod)
  formData.append('rating', payload.rating)
  if (payload.workingWell) {
    formData.append('pros_feedback', payload.workingWell)
  }

  if (payload.doBetter) {
    formData.append('cons_feedback', payload.doBetter)
  }

  if (payload.attachment?.length) {
    payload.attachment.forEach((file) => {
      formData.append('attachments', file)
    })
  }
  const response = await api.put(`${FEEDBACK_CREATE_URL}/${id}`, formData)
  return response
}

export const deleteMasterFeedback = async (payload: any) => {
  const payloadDelete = {
    period: payload.period,
    fileName: payload.name,
  }
  const response = await api.post(`${FEEDBACK_CREATE_URL}/${payload.id}/attachments`, payloadDelete)
  return response?.data
}

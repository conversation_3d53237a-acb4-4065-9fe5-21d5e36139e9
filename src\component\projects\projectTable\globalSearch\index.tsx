import React, { useState, useEffect, useCallback, useMemo, useRef, useReducer } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import RestartAltIcon from '@mui/icons-material/RestartAlt'
import { Chip, InputAdornment } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import styles from './GlobalSearch.module.scss'
import { globalSearchArray } from '../constant'
import { convertToProjectTableRecord } from '../projectUtils'
import Button from '@/src/component/shared/button'
import TextInputField from '@/src/component/shared/textInputField'
import SearchIcon from '@/src/component/svgImages/searchIcon'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import useStatus from '@/src/redux/status/useStatus'
import { getProjects } from '@/src/services/projects'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'

interface GlobalSearchProps {
  setProjectsTableData: React.Dispatch<React.SetStateAction<any[]>>
  fullData: any[]
  searchTerm: string
  setSearchTerm: (args: string) => void
  edit: boolean
  setEdit: (args: boolean) => void
  handleSave: () => void
  handleCancel: () => void
  isEditForUser?: boolean
}

type Header = {
  accessorKey: string
  header: string
  isSelect?: boolean
}

let searchTimeout: any = null

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  setProjectsTableData,
  fullData,
  searchTerm,
  setSearchTerm,
  isEditForUser,
  edit,
  setEdit,
  handleSave,
  handleCancel,
}) => {
  const { currentPeriod } = useMasterPeriod()
  const { setProjectFilterValueApi, projectFilterValue } = useProjectSummary()

  // Using reducer for managing header array state to prevent unnecessary re-renders
  const [headerArray, dispatchHeaderArray] = useReducer((state: Header[], action: { type: string; payload: any }) => {
    switch (action.type) {
      case 'TOGGLE_SELECT':
        return state.map((header) =>
          header.accessorKey === action.payload.accessorKey ? { ...header, isSelect: !header.isSelect } : header,
        )
      case 'RESET':
        return globalSearchArray
      default:
        return state
    }
  }, globalSearchArray)

  // Optimized filter function with memoization
  const applyFilter = useCallback(
    (value: string, updatedHeaderArray: Header[]) => {
      if (!value.trim()) {
        setProjectsTableData(fullData)
        return
      }

      const normalizedSearchTerm = value?.toLowerCase().replace(/\s+/g, ' ').trim()
      const selectedHeaders = updatedHeaderArray.filter((header) => header.isSelect)

      const filteredData = fullData.filter((item: any) => {
        return selectedHeaders.length > 0
          ? selectedHeaders.some((header: any) => {
              const fieldValue = item[header.accessorKey as string]
              const normalizedValue = fieldValue.toLowerCase().replace(/\s+/g, ' ').trim()
              return normalizedValue.includes(normalizedSearchTerm)
            })
          : Object.values(item).some((field: any) => {
              const normalizedValue = String(field).toLowerCase().replace(/\s+/g, ' ').trim()
              return normalizedValue.includes(normalizedSearchTerm)
            })
      })
      setProjectsTableData(filteredData)
    },
    [fullData, setProjectsTableData],
  )

  // When debounced search term changes, apply the filter

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value) // Update the parent state

    if (searchTimeout) clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => applyFilter(value, headerArray), 500)
  }

  // const handleToggleSelect = (item: Header) => {
  //   // Batching updates when multiple chips are clicked quickly
  //   if (batchTimeout.current) clearTimeout(batchTimeout.current)
  //   dispatchHeaderArray({ type: 'TOGGLE_SELECT', payload: item })

  //   batchTimeout.current = setTimeout(() => {
  //     // Apply filter after a short delay, to handle multiple toggles efficiently
  //     applyFilter(debouncedSearchTerm, headerArray)
  //   }, 1000)
  // }

  const handleResetFilters = () => {
    setSearchTerm('')
    setProjectFilterValueApi([]) // Reset filters
    dispatchHeaderArray({ type: 'RESET', payload: null }) // Reset column selections
  }

  const hasFilter = useMemo(
    () => projectFilterValue?.some((item: { colId: string; values: any }) => item.values.length > 0),
    [projectFilterValue],
  )

  useEffect(() => {
    if (searchTerm) {
      applyFilter(searchTerm, headerArray)
    }
  }, [fullData, applyFilter, searchTerm])

  return (
    <div>
      <div className={styles.resetFilterContain}>
        <div className={styles.globalSearchField}>
          <TextInputField
            variant="outlined"
            placeholder="Search ..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              endAdornment: (
                <InputAdornment position="start" className={styles.endAdornment}>
                  <SearchIcon className={styles.endAdornmentIcon} />
                </InputAdornment>
              ),
            }}
          />
        </div>
        <div>
          {edit && (
            <Button variant={'outlined'} onClick={handleCancel} sx={{ marginRight: '10px' }}>
              {'Discard'}
            </Button>
          )}
          <Button
            variant={'contained'}
            disabled={!isEditForUser}
            onClick={() => {
              edit ? handleSave() : setEdit(!edit)
            }}
            sx={{ width: '100px', marginRight: '10px' }}
          >
            {edit ? 'Save' : 'Edit Table'}
          </Button>
          {hasFilter && (
            <Button
              endIcon={<RestartAltIcon />}
              variant="contained"
              onClick={handleResetFilters}
              className={styles.resetButton}
            >
              Reset Filter
            </Button>
          )}
        </div>
      </div>

      {/* <div className={styles.globalSearchContainer}>
        <div className={styles.visibleColumnContainer}>
          {headerArray.map((item) => (
            <Chip
              key={item.accessorKey}
              className={styles.chip}
              variant={item.isSelect ? 'filled' : 'outlined'}
              label={item.header}
              onClick={() => handleToggleSelect(item)}
            />
          ))}
        </div>
      </div> */}
    </div>
  )
}

export default GlobalSearch

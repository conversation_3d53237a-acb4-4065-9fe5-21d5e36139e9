import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IPortfolioManagerPayload {
  portfolio_manager: string
  avatar?: string
  phone_number: string
  email: string
  location: string
}

export interface IUpdatePortfolioManager extends IPortfolioManagerPayload {
  id: number
}

export interface IPortfolioManager {
  id: number
  portfolio_manager: string
  avatar: AvatarItem
  phone_number: string
  email: string
  location: string
}

export interface IGetMasterPortfolioManagersResponse {
  data: IPortfolioManager[]
  message: string
  success: true
}

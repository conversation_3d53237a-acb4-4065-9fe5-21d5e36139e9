import { StatusEnum } from '../types'

export interface IPhaseState {
  getMasterPhaseStatus: StatusEnum
  getMasterPhaseCategoryStatus: StatusEnum
  addMasterPhaseStatus: StatusEnum
  addMasterPhaseCategoryStatus: StatusEnum
  deleteMasterPhaseStatus: StatusEnum
  updateMasterPhaseStatus: StatusEnum
  phases: IPhase[]
  uniquePhaseCategories: IPhaseCategory[]
  phase: IPhase
  localPhase: any[]
  localSubStage: any[]
}
export interface IPhase {
  id: number
  project_phase: string
}
export interface IPhaseCategory {
  id: string
  phase: string
  category: string
}

export interface IGetMasterPhaseResponse {
  data: IPhase[]
  message: string
  success: true
}
export interface IGetMasterPhaseCategoryResponse {
  data: IPhaseCategory[]
  message: string
  success: true
}

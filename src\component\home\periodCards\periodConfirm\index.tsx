import React, { useEffect, useState } from 'react'
import { Modal, Box } from '@mui/material'
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers'
import { isSameDay } from 'date-fns'
import styles from './PeriodConfirm.module.scss'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'

interface ModalProps {
  open: boolean
  onClose: () => void
  selectedAction: string
}

const PeriodConfirm: React.FC<ModalProps> = ({ open, onClose, selectedAction }) => {
  const [loader, setLoader] = useState(false)
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '440px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    // boxShadow: 24,
    pt: '20px',
    px: '20px',
    pb: '20px',
    zIndex: 1,
  }
  const { unFreezePeriodApi, userFreezePeriod<PERSON><PERSON>, finalFreezePeriodApi, getMasterPeriodsApi } = useMasterPeriod()

  useEffect(() => {
    open && setLoader(false)
  }, [open])

  const handleConfirmFreeze = async () => {
    setLoader(true)
    let response: Record<string, any> | null = null

    if (selectedAction === 'Unfreeze') {
      response = await unFreezePeriodApi()
      onClose()
    } else if (selectedAction === 'Freeze') {
      response = await userFreezePeriodApi()
      onClose()
    } else if (selectedAction === 'Permanent Freeze') {
      response = await finalFreezePeriodApi()
      onClose()
    }
    if (response && response.payload && response.payload.success === true) {
      const res: Record<string, any> = await getMasterPeriodsApi()
      if (!res.payload.success) return
      setLoader(false)
      onClose()
    }
  }

  const highlightedDays: Array<Date> = [new Date('2024-04-10')]

  const CustomDay = (props: PickersDayProps<Date>) => {
    const matchedStyles = highlightedDays.reduce((a, v) => {
      const date = new Date(props.day)
      return isSameDay(date, v) ? { backgroundColor: '#999999' } : a
    }, {})

    return <PickersDay {...props} sx={{ ...matchedStyles }} />
  }
  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        {loader ? (
          <Loader />
        ) : (
          <div className={styles.confirmButtonContent}>
            <div className={styles.confirmContent}>
              {`${
                selectedAction === 'Unfreeze'
                  ? 'Are you sure you want to unfreeze?'
                  : selectedAction === 'Permanent Freeze'
                    ? 'You are about to freeze the period for all users. After this step, no one will be able to enter data for the current period. Do you want to proceed?'
                    : 'You are about to freeze the period for all other users. After this step, only the  admin will be able to enter data for the current period. Do you want to proceed?'
              }`}
            </div>
            {/* <TypographyField sx="bodyBold" text={date ? date : ""} /> */}

            <div className={styles.reassignButtons}>
              <Button
                onClick={() => handleConfirmFreeze()}
                // disabled={loading}
              >
                Yes
              </Button>
              <Button color="secondary" onClick={() => onClose()}>
                No
              </Button>
            </div>
          </div>
        )}
      </Box>
    </Modal>
  )
}

export default PeriodConfirm

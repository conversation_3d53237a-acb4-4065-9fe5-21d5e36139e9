import React, { ReactElement, useMemo } from 'react'
import CloseIcon from '@mui/icons-material/Close'
import { Avatar } from '@mui/material'
import Select, { ControlProps, GroupBase, OptionProps, components, ClearIndicatorProps } from 'react-select'
import DropDownIcon from '../../svgImages/dropDownIcon'
import RotateIcon from '../../svgImages/rotateIcon'
import styles from '../combobox/Combobox.module.scss'
import { naturalSort } from '@/src/utils/sortingUtils'

interface OptionType {
  label: string
  value: string
  avatar?: string // Add avatar property to options
}

interface ComboBoxProps {
  value: any
  onChange: (option: any) => void
  options?: any[]
  labelText?: string
  error?: boolean
  helperText?: string
  onBlur?: (event: any) => void
  required?: boolean
  placeholder?: string
  disabled?: boolean
  labelKey?: string
  valueKey?: string
  className?: string
  focusCustomClass?: string
  placeholderStyles?: string
  isMulti?: boolean
  clearIcon?: boolean
  isAvatarOption?: boolean
  isCustomSorting?: boolean
}

const ComboBox: React.FC<ComboBoxProps> = ({
  value,
  onChange,
  options,
  labelText = '',
  error = false,
  helperText = '',
  onBlur,
  required = false,
  placeholder = '',
  disabled = false,
  labelKey = '',
  valueKey = '',
  className = '',
  focusCustomClass,
  isMulti = false,
  placeholderStyles,
  clearIcon = false,
  isAvatarOption = false,
  isCustomSorting = false,
}): ReactElement => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const isArrayOfStrings = useMemo(() => {
    return Array.isArray(options) && options.length > 0 && typeof options[0] === 'string'
  }, [options])

  const menuOptions = useMemo(() => {
    if (!options) return []
    if (isCustomSorting) return [...options]
    if (isArrayOfStrings && options) {
      return [...options]
        .sort((a, b) => {
          const aStr = typeof a === 'string' ? a.toLowerCase() : String(a)
          const bStr = typeof b === 'string' ? b.toLowerCase() : String(b)
          return naturalSort(aStr, bStr)
          // return aStr.localeCompare(bStr)
        })
        .map((res) => ({ label: res, value: res }))
    } else {
      return [...options]?.sort((a, b) => {
        const key = labelKey ? labelKey : 'label'
        const aStr = typeof a?.[key] === 'string' ? a?.[key].toLowerCase() : String(a?.[key])
        const bStr = typeof b?.[key] === 'string' ? b?.[key].toLowerCase() : String(b?.[key])
        // return aStr.localeCompare(bStr)
        return naturalSort(aStr, bStr)
      })
    }
  }, [isArrayOfStrings, options])

  const handleSelectBlur = (event: React.FocusEvent) => {
    if (onBlur) onBlur(event)
    setIsMenuOpen(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      setIsMenuOpen(false)
    }
  }

  const getOptionLabel = (option: any) => {
    if (labelKey) {
      return option[labelKey] || option.data[labelKey]
    }
    if (typeof option === 'object') {
      return option?.label || ''
    }
    return option || ''
  }

  const getOptionValue = (option: any) => {
    if (valueKey) {
      return option[valueKey]
    }
    if (typeof option === 'object') {
      return option?.value
    }
    return option
  }

  const DropdownIndicator = (props: any) => (
    <components.DropdownIndicator {...props}>
      {isMenuOpen ? (
        <RotateIcon rotate="up" fill="#444444" className={styles.searchIcon} />
      ) : (
        <DropDownIcon className={styles.arrowIcon} />
      )}
    </components.DropdownIndicator>
  )

  const ClearIndicator = (props: ClearIndicatorProps<any, boolean, GroupBase<any>>) => (
    <components.ClearIndicator {...props}>
      {clearIcon ? <CloseIcon className={styles.clearIcon} sx={{ width: '15px', height: '15px' }} /> : null}
    </components.ClearIndicator>
  )
  // Custom Option Component to show avatar with label
  const CustomOption = (props: OptionProps<OptionType>) => {
    const { data, innerRef, innerProps } = props
    return (
      <div ref={innerRef} {...innerProps} className={styles.option} style={{ display: 'flex' }}>
        {isAvatarOption && <Avatar src={data.avatar} sx={{ width: 24, height: 24, marginRight: 2 }} />}
        {data.label}
      </div>
    )
  }

  const getClassNames = (condition: boolean, trueClass: string, falseClass: string = '') =>
    condition ? trueClass : falseClass

  return (
    <div className={`${className} ${styles.root}`} onClick={(e) => e.stopPropagation()}>
      {labelText && (
        <label className={styles.label}>
          {labelText}
          {required && <div className={styles.required}>*</div>}
        </label>
      )}
      <Select
        isMulti={isMulti}
        menuIsOpen={isMenuOpen}
        openMenuOnFocus
        menuPosition="fixed"
        onBlur={handleSelectBlur}
        onKeyDown={handleKeyDown}
        closeMenuOnSelect
        onMenuOpen={() => setIsMenuOpen(true)}
        onMenuClose={() => setIsMenuOpen(false)}
        isSearchable
        isClearable={clearIcon}
        options={menuOptions}
        filterOption={(option, inputValue) => {
          const label = getOptionLabel(option)?.toLowerCase()
          const input = inputValue.toLowerCase()
          return label?.includes(input)
        }}
        getOptionLabel={getOptionLabel}
        getOptionValue={getOptionValue}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isDisabled={disabled}
        components={{
          DropdownIndicator,
          ClearIndicator: clearIcon ? ClearIndicator : undefined,
          Option: CustomOption as any,
        }}
        noOptionsMessage={() => 'No results Found'}
        classNames={{
          container: () => styles.container,
          control: (props: ControlProps<string, boolean, GroupBase<string>>) =>
            `${styles.control} ${getClassNames(props.isFocused, focusCustomClass || styles.focusBorder)} ${getClassNames(
              error,
              styles.errorBorder,
            )} ${getClassNames(props.isDisabled, styles.disabled)}`,
          valueContainer: () => styles.valueContainer,
          singleValue: () => styles.singleValue,
          placeholder: () => (placeholderStyles ? placeholderStyles : styles.placeholder),
          input: () => styles.inputField,
          indicatorsContainer: () => styles.indicatorsContainer,
          indicatorSeparator: () => styles.indicatorSeparator,
          menuPortal: () => styles.menuPortal,
          menu: () => styles.menu,
          menuList: () => styles.menuList,
          option: (props: OptionProps<string, boolean, GroupBase<string>>) =>
            `${styles.option} ${getClassNames(props.isSelected, styles.selectedOption)} ${getClassNames(
              props.isFocused,
              styles.focusedOption,
            )}`,
          noOptionsMessage: () => styles.noOptionsMessage,
        }}
        styles={{
          menuPortal: (base) => ({ ...base, zIndex: 9999 }),
          control: (base) => ({
            ...base,
            borderRadius: '5px',
            border: '1px solid #f2f2f2',
          }),
        }}
        menuPortalTarget={document.body}
      />
      {helperText && <span className={`${styles.text} ${getClassNames(error, styles.error)}`}>{helperText}</span>}
    </div>
  )
}

export default ComboBox

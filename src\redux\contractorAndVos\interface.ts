import { StatusEnum } from '../types'

export interface IContractorAndVosStates {
  getContractorAndVosStatus: StatusEnum
  addContractorAndVosStatus: StatusEnum
  deleteContractorAndVosStatus: StatusEnum
  updateContractorAndVosStatus: StatusEnum
  contractorAndVoses: IContractorAndVos[]
  contractorAndVo: IContractorAndVos
}
export interface IContractorAndVos {
  period?: string
  project_name?: string
  last_updated?: Date
  phase?: string
  approved_vo_value?: number
  contract_type?: string
  contractor_name?: string
  description?: string
  forecasted_contract_value?: string
  original_contract_value?: string
  pvr_value?: number
  revised_contract_value?: string
  contract_signature?: string
  bonds?: string
  insurance?: string
  advanced_payment?: string
}

export interface IGetContractorAndVosStates {
  data: IContractorAndVos[]
  message: string
  success: true
}

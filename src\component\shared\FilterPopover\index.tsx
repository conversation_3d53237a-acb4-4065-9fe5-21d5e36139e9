import React, { useState, ChangeEvent, FC, useMemo } from 'react'
import { Popover, Checkbox, FormControlLabel, Button, TextField } from '@mui/material'
import { format, getDate, getYear, parse } from 'date-fns'
import DateFilter, { YearData } from './dateFilter'
import styles from './FilterPopover.module.scss'
import useSearchDrawer from '@/src/redux/searchDrawer/useSearchDrawer'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'

export interface FilterOption {
  label: string
  value: string
  subItem?: FilterOption[]
}

interface FilterPopoverProps {
  type?: 'date' | 'other'
  anchorEl: HTMLElement | null
  selectedOptions: FilterOption[]
  setSelectedOptions: React.Dispatch<React.SetStateAction<FilterOption[]>>
  onClose: () => void
  options: FilterOption[]
  onApply: (selected: FilterOption[]) => void
  isBlank?: boolean
  popOverWidth?: string
  filterField?: string
}

const useFilterLogic = (options: FilterOption[], searchTerm: string, type: string | undefined) => {
  const uniqueOptions: { [key: string]: FilterOption } = {}

  options?.forEach((option) => {
    const normalizedValue = String(option.value || '')
      .toLowerCase()
      .trim() // Ensure it's a string before calling toLowerCase()

    if (!uniqueOptions[normalizedValue]) {
      uniqueOptions[normalizedValue] = option
    }
  })

  const filteredOptionsArray = Object.values(uniqueOptions)

  const filterOptions = () => {
    if (type === 'date') {
      const groupedData: YearData[] = []

      filteredOptionsArray.forEach((item) => {
        const parsedDate = parse(item.value, 'dd-MM-yyyy', new Date())

        if (isNaN(parsedDate.getTime())) {
          console.warn(`Invalid date format: ${item.value}`)
          return
        }

        const year = getYear(parsedDate)
        const month = format(parsedDate, 'MMMM')
        const date = getDate(parsedDate)

        let yearGroup = groupedData.find((group) => group.year === year)
        if (!yearGroup) {
          yearGroup = { year, month: [] }
          groupedData.push(yearGroup)
        }

        let monthGroup = yearGroup.month.find((monthItem) => monthItem.monthName === month)
        if (!monthGroup) {
          monthGroup = { monthName: month, date: [] }
          yearGroup.month.push(monthGroup)
        }

        if (!monthGroup.date.includes(date)) {
          monthGroup.date.push(date)
        }
      })

      return groupedData
    }

    return filteredOptionsArray.reduce<FilterOption[]>((acc, option) => {
      const seenSubItemValues = new Set<string>()
      let filteredSubItems =
        option.subItem?.filter((sub) => String(sub.label?.trim()).toLowerCase().includes(searchTerm.toLowerCase())) ||
        []

      // Remove duplicate subItems by value
      filteredSubItems = filteredSubItems.filter((sub) => {
        if (seenSubItemValues.has(sub.value?.toLowerCase())) return false
        seenSubItemValues.add(sub.value?.toLowerCase())
        return true
      })

      const alreadyExists = acc.some((item) => item.value === option.value)
      if (
        (filteredSubItems.length > 0 || String(option.label).toLowerCase().includes(searchTerm.toLowerCase())) &&
        !alreadyExists
      ) {
        acc.push({
          label: option.label,
          value: option.value,
          ...(filteredSubItems.length > 0 && {
            subItem: sortArrayByKeyWithTypeConversion(filteredSubItems, 'label', type === 'number'),
          }),
        })
      }
      return acc
    }, [])
  }

  return type === 'projectStatus' || type === 'wildcard-predecessor-successor' || type === 'phase/package'
    ? filterOptions()
    : sortArrayByKeyWithTypeConversion(filterOptions(), 'label', type === 'number')
}

const FilterPopover: FC<FilterPopoverProps> = ({
  type,
  anchorEl,
  selectedOptions,
  setSelectedOptions,
  onClose,
  options,
  onApply,
  isBlank = false,
  popOverWidth,
  filterField,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const filteredOptions = useFilterLogic(options, searchTerm, type)
  const { userFilterFields, setUserFilterFieldsApi } = useSearchDrawer()

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value.toLowerCase())
  }

  const isOptionSelected = (value: string, isSubItem: boolean, parentValue?: string) => {
    if (isSubItem && parentValue) {
      const parent = selectedOptions.find((opt) => opt.value === parentValue)
      return parent?.subItem?.some((sub) => sub.value === value) ?? false
    }
    if (typeof value === 'boolean') {
      return selectedOptions.some((opt) => opt.value === value)
    }
    return selectedOptions.some((opt) => opt.value === value)
  }

  const handleToggle = (value: string, isSubItem: boolean, parentValue?: string) => {
    setSelectedOptions((prevOptions) => {
      const newOptions = [...prevOptions]

      if (value === 'Blanks') {
        // Toggle the 'Blanks' value independently
        const blankIndex = newOptions.findIndex((opt) => opt.value === 'Blanks')
        if (blankIndex !== -1) {
          newOptions.splice(blankIndex, 1) // Remove Blanks if already selected
        } else {
          newOptions.push({ label: 'Blanks', value: 'Blanks' }) // Add Blanks if not selected
        }
      } else if (isSubItem && parentValue) {
        const parentIndex = newOptions.findIndex((opt) => opt.value === parentValue)
        if (parentIndex !== -1) {
          const parent = newOptions[parentIndex]
          const subIndex = parent.subItem?.findIndex((sub) => sub.value === value)

          if (subIndex !== undefined && subIndex !== -1) {
            parent.subItem?.splice(subIndex, 1)
            if (!parent.subItem || parent.subItem.length === 0) {
              // If no subItems left — remove parent
              newOptions.splice(parentIndex, 1)
            }
          } else {
            parent.subItem = [...(parent.subItem || []), { label: value, value }]
          }
        } else {
          newOptions.push({ label: parentValue, value: parentValue, subItem: [{ label: value, value }] })
        }
      } else {
        const optionIndex = newOptions.findIndex((opt) => opt.value === value)
        if (optionIndex !== -1) {
          newOptions.splice(optionIndex, 1)
        } else {
          const option = options.find((opt) => opt.value === value)
          if (option) {
            // If option has subItems — add with all subItems
            if (option.subItem) {
              newOptions.push({
                label: option.label,
                value: option.value,
                subItem: [...option.subItem],
              })
            } else {
              newOptions.push({ ...option })
            }
          }
        }
      }

      return newOptions
    })
  }

  const handleSelectAll = (checked: boolean) => {
    const data: any = isBlank ? [...filteredOptions, { label: 'Blanks', value: 'Blanks' }] : filteredOptions
    setSelectedOptions(checked ? data : [])
  }

  const handleReset = () => {
    // setSelectedOptions([])
    onApply([])
    onClose()
    // NOTE :  remove filterField name from userFilterFields
    const filterFields = userFilterFields?.filter((res) => res !== filterField)
    setUserFilterFieldsApi([...filterFields])
  }

  const count = useMemo(() => {
    const addVal = isBlank ? 1 : 0
    return filteredOptions?.length + addVal
  }, [filteredOptions])

  return (
    <Popover open={Boolean(anchorEl)} anchorEl={anchorEl} onClose={onClose}>
      <div
        className={styles.filterContainer}
        style={{ '--popover-width': popOverWidth || '300px' } as React.CSSProperties}
      >
        {type !== 'date' && (
          <TextField
            placeholder="Search in filters"
            fullWidth
            margin="dense"
            variant="outlined"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mb: 2 }}
          />
        )}

        <div className={styles.options}>
          {type === 'date' ? (
            <DateFilter
              filteredOptions={filteredOptions as unknown as YearData[]}
              selectedOptions={selectedOptions}
              setSelectedOption={setSelectedOptions}
              onApply={onApply}
            />
          ) : (
            <>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={
                      selectedOptions.length > 0 &&
                      selectedOptions.length === (isBlank ? filteredOptions.length + 1 : filteredOptions.length)
                    }
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                }
                label={`Select all (${count})`}
              />
              <div className={styles.opt}>
                {(filteredOptions as FilterOption[]).map((option) => (
                  <div key={option.value}>
                    <FormControlLabel
                      className={styles.filterBox}
                      control={
                        <Checkbox
                          className={styles.filterOption}
                          checked={isOptionSelected(option.value, false)}
                          onChange={() => handleToggle(option.value, false)}
                        />
                      }
                      label={option.label}
                      sx={{
                        '.MuiTypography-root': {
                          wordBreak: 'break-all',
                        },
                      }}
                    />
                    {option.subItem && (
                      <div className={styles.subs}>
                        {option.subItem.map((subItem) => (
                          <FormControlLabel
                            key={subItem.value}
                            control={
                              <Checkbox
                                sx={{ padding: '5px' }}
                                checked={isOptionSelected(subItem.value, true, option.value)}
                                onChange={() => handleToggle(subItem.value, true, option.value)}
                              />
                            }
                            sx={{
                              '.MuiTypography-root': {
                                wordBreak: 'break-all',
                              },
                              display: 'block',
                            }}
                            label={subItem.label}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {isBlank && (
                  <FormControlLabel
                    className={styles.filterBox}
                    control={
                      <Checkbox
                        className={styles.filterOption}
                        checked={isOptionSelected('Blanks', false)}
                        onChange={() => handleToggle('Blanks', false)}
                      />
                    }
                    label={'( Blanks )'}
                  />
                )}
              </div>
            </>
          )}
        </div>

        <div className={styles.actionButtons}>
          <Button onClick={handleReset} variant="contained" disabled={selectedOptions?.length === 0}>
            Reset
          </Button>
          <Button
            onClick={() => {
              onApply(selectedOptions)
              onClose()
            }}
            disabled={selectedOptions?.length === 0}
          >
            Ok
          </Button>
        </div>
      </div>
    </Popover>
  )
}

export default FilterPopover

import React, { useState } from 'react'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { Accordion, AccordionDetails, AccordionSummary, Checkbox } from '@mui/material'
import styles from '../FilterPopover.module.scss'

// Define the type for a single date item
interface DateItem {
  value: string
  label: string
}

// Define the structure for a month with its dates
export interface Month {
  monthName: string
  date: number[]
}

// Define the structure for a year with its months
export interface YearData {
  year: number
  month: Month[]
}

// Define the props for the DateFilter component
interface DateFilterProps {
  filteredOptions: YearData[] // Array of years with their months and dates
  selectedOptions: DateItem[] // Array of selected dates
  setSelectedOption: React.Dispatch<React.SetStateAction<DateItem[]>> // Function to update the selected dates
  onApply: (selectedOptions: DateItem[]) => void // Function to handle applying the selected options
}

// Helper function to format date in "dd-MM-yyyy" format
const formatDate = (year: number, month: string, date: number): string => {
  const monthIndex = new Date(Date.parse(`${month} 1, 2000`)).getMonth() + 1 // Convert month name to number
  return `${date < 10 ? `0${date}` : date}-${monthIndex < 10 ? `0${monthIndex}` : monthIndex}-${year}`
}

// Helper function to check if the date is selected
const isDateSelected = (selectedOptions: DateItem[], year: number, month: string, date: number): boolean => {
  const value = formatDate(year, month, date)
  return selectedOptions.some((opt) => opt.value === value)
}

// Toggle individual date selection
const toggleDateSelection = (
  selectedOptions: DateItem[],
  setSelectedOption: React.Dispatch<React.SetStateAction<DateItem[]>>,
  year: number,
  month: string,
  date: number,
) => {
  const value = formatDate(year, month, date)
  const label = `${date < 10 ? `0${date}` : date}-${month} ${year}` // Example label "01-January 2024"
  const isSelected = isDateSelected(selectedOptions, year, month, date)

  // Toggle individual date
  if (isSelected) {
    setSelectedOption(selectedOptions.filter((opt) => opt.value !== value))
  } else {
    setSelectedOption([...selectedOptions, { value, label }])
  }
}

// Toggle month selection (without selecting all dates automatically)
const toggleMonthSelection = (
  selectedOptions: DateItem[],
  setSelectedOption: React.Dispatch<React.SetStateAction<DateItem[]>>,
  year: number,
  month: string,
  dates: number[],
) => {
  const selectedDates = dates.filter((date) => isDateSelected(selectedOptions, year, month, date))

  // If all dates are selected, remove them
  if (selectedDates.length === dates.length) {
    const newSelections = dates.map((date) => formatDate(year, month, date))
    setSelectedOption(selectedOptions.filter((opt) => !newSelections.includes(opt.value)))
  } else {
    // Otherwise, add the selected dates
    const newSelections = dates
      .filter((date) => !isDateSelected(selectedOptions, year, month, date))
      .map((date) => {
        const value = formatDate(year, month, date)
        const label = `${date < 10 ? `0${date}` : date}-${month} ${year}`
        return { value, label }
      })
    setSelectedOption([...selectedOptions, ...newSelections])
  }
}

// Toggle year selection (without selecting all dates automatically)
const toggleYearSelection = (
  selectedOptions: DateItem[],
  setSelectedOption: React.Dispatch<React.SetStateAction<DateItem[]>>,
  year: number,
  filteredOptions: YearData[],
) => {
  const allDatesInYear = filteredOptions
    .filter((item) => item.year === year)
    .flatMap((item) =>
      item.month.flatMap((month) =>
        month.date.map((date) => {
          const value = formatDate(year, month.monthName, date)
          const label = `${date < 10 ? `0${date}` : date}-${month.monthName} ${year}`
          return { value, label }
        }),
      ),
    )

  const allSelected = allDatesInYear.every((date) => selectedOptions.some((opt) => opt.value === date.value))

  // If all dates in the year are selected, remove them
  if (allSelected) {
    setSelectedOption(selectedOptions.filter((opt) => !allDatesInYear.some((date) => date.value === opt.value)))
  } else {
    // Add only the dates in the year that are not already selected
    const newSelections = allDatesInYear.filter((date) => !selectedOptions.some((opt) => opt.value === date.value))
    setSelectedOption([...selectedOptions, ...newSelections])
  }
}

const DateFilter: React.FC<DateFilterProps> = ({ filteredOptions, selectedOptions, setSelectedOption, onApply }) => {
  const [expandedYear, setExpandedYear] = useState<number | null>(
    filteredOptions?.length ? filteredOptions[0].year : null,
  )
  const [expandedMonth, setExpandedMonth] = useState<{ [key: number]: string | null }>({})
  const handleSelectionChange = () => {
    // onApply(selectedOptions) // Call onApply after selections are changed
  }

  React.useEffect(() => {
    handleSelectionChange() // Make sure onApply is called on initial load or change in selectedOptions
  }, [selectedOptions])

  const sortYearMonthDate = (dataArray: YearData[]) => {
    const monthOrder = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ]

    return dataArray
      .sort((a, b) => a.year - b.year) // Sort by year (ascending)
      .map((data) => ({
        ...data,
        month: data.month
          .sort((a, b) => monthOrder.indexOf(a.monthName) - monthOrder.indexOf(b.monthName)) // Sort by month order
          .map((m) => ({
            ...m,
            date: m.date.sort((a, b) => a - b), // Sort dates in ascending order
          })),
      }))
  }

  const array = sortYearMonthDate(filteredOptions)

  return (
    <div className={styles.dateContainer}>
      {array.map((item) => (
        <div key={item.year} className={styles.dateGroup}>
          <Accordion expanded={expandedYear === item.year} className={styles.accordion}>
            <AccordionSummary
              expandIcon={
                <ExpandMoreIcon onClick={() => setExpandedYear(expandedYear === item.year ? null : item.year)} />
              }
              className={styles.summary}
            >
              <div className={styles.yearMonth}>
                {/* Year selection checkbox */}
                <div className={styles.year}>
                  <Checkbox
                    checked={item.month?.some((month) =>
                      month.date.some((date) => isDateSelected(selectedOptions, item.year, month.monthName, date)),
                    )}
                    onChange={() => toggleYearSelection(selectedOptions, setSelectedOption, item.year, filteredOptions)}
                  />
                  <span>{item.year}</span>
                </div>
              </div>
            </AccordionSummary>
            <AccordionDetails className={styles.details}>
              {/* Render months and dates for the selected year */}
              <div className={styles.months}>
                {item.month?.map((month) => (
                  <div key={`${item.year}-${month.monthName}`} className={styles.monthGroup}>
                    <Accordion
                      expanded={expandedMonth ? expandedMonth[item.year] === month.monthName : false}
                      className={styles.accordion}
                    >
                      <AccordionSummary
                        expandIcon={
                          <ExpandMoreIcon
                            onClick={() =>
                              setExpandedMonth((prev) => ({
                                ...(prev || {}),
                                [item.year]: prev ? (prev[item.year] === month.monthName ? null : month.monthName) : '',
                              }))
                            }
                          />
                        }
                        className={styles.summary}
                      >
                        {/* Month selection checkbox */}
                        <div className={styles.month}>
                          <Checkbox
                            className={styles.monthCheckbox}
                            checked={month.date.some((date) =>
                              isDateSelected(selectedOptions, item.year, month.monthName, date),
                            )}
                            onChange={() =>
                              toggleMonthSelection(
                                selectedOptions,
                                setSelectedOption,
                                item.year,
                                month.monthName,
                                month.date,
                              )
                            }
                          />
                          <span>{month.monthName}</span>
                        </div>
                      </AccordionSummary>
                      <AccordionDetails className={styles.monthDetails}>
                        {/* Dates for the selected month */}
                        <div className={styles.dates}>
                          {month.date.map((date) => (
                            <div key={`${item.year}-${month.monthName}-${date}`} className={styles.dateItem}>
                              <Checkbox
                                className={styles.dateCheckbox}
                                checked={isDateSelected(selectedOptions, item.year, month.monthName, date)}
                                onChange={() =>
                                  toggleDateSelection(
                                    selectedOptions,
                                    setSelectedOption,
                                    item.year,
                                    month.monthName,
                                    date,
                                  )
                                }
                              />
                              <span>{date}</span>
                            </div>
                          ))}
                        </div>
                      </AccordionDetails>
                    </Accordion>
                  </div>
                ))}
              </div>
            </AccordionDetails>
          </Accordion>
        </div>
      ))}
    </div>
  )
}

export default DateFilter

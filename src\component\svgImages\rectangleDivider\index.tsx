import React, { ReactElement, forwardRef } from 'react'

interface ProjectStatusIconProps {
  className?: string
  onClick?: (e: any) => void
}

const RectangleDivider: React.FC<ProjectStatusIconProps> = forwardRef<SVGSVGElement, ProjectStatusIconProps>(
  (props, ref): ReactElement => {
    const { className, onClick } = props

    return (
      <svg
        className={`rectangle-divider ${className}`}
        width="1137"
        height="1"
        viewBox="0 0 1137 1"
        xmlns="http://www.w3.org/2000/svg"
        onClick={onClick}
        {...props}
        ref={ref}
      >
        <rect width="1137" height="1" fill="#EAEAEA" />
      </svg>
    )
  },
)
RectangleDivider.displayName = 'RectangleDivider'

export default RectangleDivider

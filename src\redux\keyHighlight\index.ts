import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetKeyHighlightsResponse, IKeyHighlights, IKeyHighlightStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IKeyHighlightStates = {
  getKeyHighlightStatus: StatusEnum.Idle,
  addKeyHighlightStatus: StatusEnum.Idle,
  deleteKeyHighlightStatus: StatusEnum.Idle,
  updateKeyHighlightStatus: StatusEnum.Idle,
  keyHighlights: [],
  keyHighlight: {},
  highlight: [''],
  isUpdateHighlight: false,
}

export const getKeyHighlights = createAsyncThunk(
  '/get-project-key-achievements',
  async (data: { period: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(`/project-key-achievements?period=${period}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addKeyHighlight = createAsyncThunk(
  '/add-project-key-achievements',
  async (data: IKeyHighlights, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/project-key-achievements`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateKeyHighlight = createAsyncThunk(
  '/update-project-key-achievements',
  async (data: { id: number; data: IKeyHighlights }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-key-achievements/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteKeyHighlight = createAsyncThunk(
  '/delete-project-key-achievements',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/project-key-achievements/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const keyHighlightSlice = createSlice({
  name: 'keyHightlight',
  initialState,
  reducers: {
    //set selected rows
    updateHighlight(state, action) {
      state.highlight = action.payload
    },
    updateIsUpdateHighlight(state, action) {
      state.isUpdateHighlight = action.payload
    },
  },
  extraReducers: (builder) => {
    //getKeyHighlights
    builder.addCase(getKeyHighlights.pending, (state) => {
      state.getKeyHighlightStatus = StatusEnum.Pending
    })
    builder.addCase(getKeyHighlights.fulfilled, (state, action) => {
      state.getKeyHighlightStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetKeyHighlightsResponse
      state.keyHighlights = actionPayload.data
    })
    builder.addCase(getKeyHighlights.rejected, (state) => {
      state.getKeyHighlightStatus = StatusEnum.Failed
    })
    //addKeyHighlight
    builder.addCase(addKeyHighlight.pending, (state) => {
      state.addKeyHighlightStatus = StatusEnum.Pending
    })
    builder.addCase(addKeyHighlight.fulfilled, (state, action) => {
      state.addKeyHighlightStatus = StatusEnum.Success
    })
    builder.addCase(addKeyHighlight.rejected, (state) => {
      state.addKeyHighlightStatus = StatusEnum.Failed
    })
    //updateKeyHighlight
    builder.addCase(updateKeyHighlight.pending, (state) => {
      state.updateKeyHighlightStatus = StatusEnum.Pending
    })
    builder.addCase(updateKeyHighlight.fulfilled, (state, action) => {
      state.updateKeyHighlightStatus = StatusEnum.Success
    })
    builder.addCase(updateKeyHighlight.rejected, (state) => {
      state.updateKeyHighlightStatus = StatusEnum.Failed
    })
    //deleteKeyHighlight
    builder.addCase(deleteKeyHighlight.pending, (state) => {
      state.deleteKeyHighlightStatus = StatusEnum.Pending
    })
    builder.addCase(deleteKeyHighlight.fulfilled, (state, action) => {
      state.deleteKeyHighlightStatus = StatusEnum.Success
    })
    builder.addCase(deleteKeyHighlight.rejected, (state) => {
      state.deleteKeyHighlightStatus = StatusEnum.Failed
    })
  },
})

export const { updateHighlight, updateIsUpdateHighlight } = keyHighlightSlice.actions

// Export the reducer
export default keyHighlightSlice.reducer

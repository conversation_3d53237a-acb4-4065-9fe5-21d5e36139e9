import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createProjectPhaseCategory,
  deleteProjectPhaseCategory,
  getProjectPhaseCategories,
  updateProjectPhaseCategory,
} from '../services/projectPhaseCategory'
import {
  IGetProjectPhaseCategoryResponse,
  IUpdateProjectPhaseCategory,
} from '../services/projectPhaseCategory/interface'

export const QUERY_PROJECT_PHASE_CATEGORY = 'project-phase-category'

/**
 * Hook to fetch Project Phase Categories
 * @param enabled - Enables or disables the query
 */
export const useGetProjectPhaseCategories = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetProjectPhaseCategoryResponse>({
    queryKey: [QUERY_PROJECT_PHASE_CATEGORY],
    queryFn: getProjectPhaseCategories,
    enabled,
  })

  return {
    projectPhaseCategories: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project Phase Categories
 */
export const useCreateProjectPhaseCategories = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createProjectPhaseCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_PHASE_CATEGORY] })
    },
  })
}

/**
 * Hook to delete a Project Phase Categories
 */
export const useDeleteProjectPhaseCategories = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteProjectPhaseCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_PHASE_CATEGORY] })
    },
  })
}

/**
 * Hook to update a Project Phase Categories
 */
export const useUpdateProjectPhaseCategories = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateProjectPhaseCategory,
    onMutate: async (updatedData: IUpdateProjectPhaseCategory) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_PHASE_CATEGORY] })
      const previousData = queryClient.getQueryData<IGetProjectPhaseCategoryResponse>([QUERY_PROJECT_PHASE_CATEGORY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetProjectPhaseCategoryResponse>([QUERY_PROJECT_PHASE_CATEGORY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, project_phase_category: updatedData.project_phase_category }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_PHASE_CATEGORY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_PHASE_CATEGORY] })
    },
  })
}

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetGovernanceResponse, IConsultantStatus, IGovernance } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IConsultantStatus = {
  getGovernanceStatus: StatusEnum.Idle,
  addGovernanceStatus: StatusEnum.Idle,
  deleteGovernanceStatus: StatusEnum.Idle,
  updateGovernanceStatus: StatusEnum.Idle,
  governances: [],
  governance: {
    period: null,
    project_name: '',
    initiation: 0,
    ldc_procurement: 0,
    design: 0,
    contractor_procurement: 0,
    construction: 0,
    handover: 0,
    overall: 0,
    last_updated: new Date(),
  },
}

export const getGovernance = createAsyncThunk(
  '/get-project-stage-weightage',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await <PERSON>pi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
        `/project-stage-weightage?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addGovernance = createAsyncThunk('/add-project-stage-weightage', async (data: IGovernance, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-stage-weightage`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateGovernance = createAsyncThunk(
  '/update-project-stage-weightage',
  async (data: IGovernance, thunkAPI) => {
    const { id, ...rest } = data
    try {
      const response = await ApiPutNoAuth(`/project-stage-weightage/${id}`, {
        ...rest,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteGovernance = createAsyncThunk('/delete-project-stage-weightage', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/project-stage-weightage/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const governanceSlice = createSlice({
  name: 'governance',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getGovernance
    builder.addCase(getGovernance.pending, (state) => {
      state.getGovernanceStatus = StatusEnum.Pending
    })
    builder.addCase(getGovernance.fulfilled, (state, action) => {
      state.getGovernanceStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetGovernanceResponse
      state.governances = actionPayload.data
    })
    builder.addCase(getGovernance.rejected, (state) => {
      state.getGovernanceStatus = StatusEnum.Failed
    })
    //addGovernance
    builder.addCase(addGovernance.pending, (state) => {
      state.addGovernanceStatus = StatusEnum.Pending
    })
    builder.addCase(addGovernance.fulfilled, (state, action) => {
      state.addGovernanceStatus = StatusEnum.Success
    })
    builder.addCase(addGovernance.rejected, (state) => {
      state.addGovernanceStatus = StatusEnum.Failed
    })
    //updateGovernance
    builder.addCase(updateGovernance.pending, (state) => {
      state.updateGovernanceStatus = StatusEnum.Pending
    })
    builder.addCase(updateGovernance.fulfilled, (state, action) => {
      state.updateGovernanceStatus = StatusEnum.Success
    })
    builder.addCase(updateGovernance.rejected, (state) => {
      state.updateGovernanceStatus = StatusEnum.Failed
    })
    //deleteGovernance
    builder.addCase(deleteGovernance.pending, (state) => {
      state.deleteGovernanceStatus = StatusEnum.Pending
    })
    builder.addCase(deleteGovernance.fulfilled, (state, action) => {
      state.deleteGovernanceStatus = StatusEnum.Success
    })
    builder.addCase(deleteGovernance.rejected, (state) => {
      state.deleteGovernanceStatus = StatusEnum.Failed
    })
  },
})

export const {} = governanceSlice.actions

// Export the reducer
export default governanceSlice.reducer

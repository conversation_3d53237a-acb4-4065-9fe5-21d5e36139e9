import React, { useMemo, useState } from 'react'
import { PictureAsPdfOutlined, WarningAmberOutlined } from '@mui/icons-material'
import { Tooltip } from '@mui/material'
import { toast } from 'sonner'
import styles from './MediaUploadBox.module.scss'
import Button from '../../../shared/button'
import DeleteIcon from '../../../svgImages/deleteIcon'

interface IMediaUploadBox {
  categoryKey: string
  header: string
  isEditForUser?: boolean
  errors: Record<string, string | null>
  previews: Record<string, { fileName: string; preview: string; type: string }[]>
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>, fieldType: string) => void
  onDropHandler: (e: React.DragEvent<HTMLDivElement>, fieldType: string) => void
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void
  handleFileUpload: (fieldType: string) => void
  handleDeleteImage: (fieldType: string, index: number) => void
}

const MediaUploadBox: React.FC<IMediaUploadBox> = ({
  header,
  categoryKey,
  isEditForUser,
  errors,
  previews,
  handleFileChange,
  onDropHandler,
  onDragOver,
  handleFileUpload,
  handleDeleteImage,
}) => {
  const [isDragOver, setIsDragOver] = useState(false)

  const MAX_IMAGE_UPLOAD = header === 'Plot Plan' ? 3 : 5
  const MAX_TOTAL_IMAGE_SIZE_MB = 5
  const MAX_VIDEO_SIZE_MB = 100

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }

    const files = e.target.files
    if (!files) return

    const isImageCategory = !(categoryKey === 'Additional Documents' || categoryKey === 'Decree')
    const isProgressMedia = categoryKey === 'Progress Videos'

    // Handle Progress Media category (images + videos)
    if (isProgressMedia) {
      const existingFiles = previews[categoryKey] || []
      const existingImages = existingFiles.filter((file) => file.type.startsWith('image/'))

      // Check video files
      const newFiles = Array.from(files)
      const newVideos = newFiles.filter((file) => file.type === 'video/mp4')

      // Validate video size
      for (const video of newVideos) {
        const videoSizeInMB = video.size / (1024 * 1024)
        if (videoSizeInMB > MAX_VIDEO_SIZE_MB) {
          toast(`Video files must be under ${MAX_VIDEO_SIZE_MB}MB`, {
            icon: <WarningAmberOutlined />,
          })
          return
        }
      }

      // Handle image validations for Progress Media
      const newImages = newFiles.filter((file) => file.type.startsWith('image/'))
      const totalImages = existingImages.length + newImages.length

      if (totalImages > MAX_IMAGE_UPLOAD) {
        toast(`You can only upload up to ${MAX_IMAGE_UPLOAD} images.`, {
          icon: <WarningAmberOutlined />,
        })
        return
      }
      const existingImagesSize = existingImages.reduce((acc, img: any) => acc + (img.size || 0), 0)
      const newImagesSize = newImages.reduce((acc, file) => acc + file.size, 0)
      const totalImageSizeInMB = (existingImagesSize + newImagesSize) / (1024 * 1024)

      if (totalImageSizeInMB > MAX_TOTAL_IMAGE_SIZE_MB) {
        toast(`Total image size should not exceed ${MAX_TOTAL_IMAGE_SIZE_MB} MB.`, {
          icon: <WarningAmberOutlined />,
        })
        return
      }
    }
    // Handle other image categories
    else if (isImageCategory) {
      const existingImages = previews[categoryKey]?.filter((file) => file.type.startsWith('image/')) || []
      const totalImages = existingImages.length + files.length

      if (totalImages > MAX_IMAGE_UPLOAD) {
        toast(`You can only upload up to ${MAX_IMAGE_UPLOAD} images.`, {
          icon: <WarningAmberOutlined />,
        })
        return
      }

      const existingTotalSize = existingImages.reduce((acc, img: any) => acc + (img.size || 0), 0)
      const newFilesSize = Array.from(files).reduce((acc, file) => acc + file.size, 0)
      const totalSizeInMB = (existingTotalSize + newFilesSize) / (1024 * 1024)

      if (totalSizeInMB > MAX_TOTAL_IMAGE_SIZE_MB) {
        toast(`Total image size should not exceed ${MAX_TOTAL_IMAGE_SIZE_MB} MB.`, {
          icon: <WarningAmberOutlined />,
        })
        return
      }
    }

    handleFileChange(e, categoryKey)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)

    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    } else {
      onDropHandler(e, categoryKey)
    }
  }

  const handleDragOverInternal = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
    onDragOver(e)
  }

  const handleDragLeave = () => {
    setIsDragOver(false)
  }

  const renderToolTip = () => (
    <div style={{ fontFamily: 'poppins' }}>
      <span style={{ display: 'block', marginBottom: '8px', fontSize: '14px' }}>Please note:</span>
      <ul style={{ listStyleType: 'none', padding: 0, margin: 0 }}>
        {categoryKey === 'Progress Videos' ? (
          <>
            <li style={{ marginBottom: '4px' }}>
              <span>File size :</span> Should be 5MB or less For Image.
            </li>
            <li style={{ marginBottom: '4px' }}>
              <span>File size :</span> Should be 100MB or less For Video.
            </li>
          </>
        ) : (
          <li style={{ marginBottom: '4px' }}>
            <span>File size :</span> Should be 5MB or less.
          </li>
        )}
        <li style={{ marginBottom: '4px' }}>
          <span>Allowed file types : </span>
          {categoryKey === 'Additional Documents' || categoryKey === 'Decree' ? 'pdf' : 'jpg, jpeg, png'}
        </li>
        <li style={{ marginBottom: '4px' }}>
          <span>File names :</span> Should contain only letters, numbers, commas, apostrophes, or dashes.
        </li>
        <li>
          <span>File selection : </span>
          {categoryKey === 'Additional Documents' || categoryKey === 'Decree'
            ? `Only 1 file can be selected at a time.`
            : `Only ${MAX_IMAGE_UPLOAD} file can be selected at a time.`}
        </li>
      </ul>
    </div>
  )

  const renderPreview = () => (
    <div className={styles.previews}>
      {previews[categoryKey]?.map((preview, index) => (
        <div key={index} className={styles.previewWrapper}>
          {preview?.type?.includes('pdf') ? (
            <div className={styles.pdfPreview}>
              <PictureAsPdfOutlined />
              <div>{preview?.fileName}</div>
              <div onClick={() => handleDeleteImage(categoryKey, index)} className={styles.deletePreviewButton}>
                <DeleteIcon />
              </div>
            </div>
          ) : preview?.type?.includes('video') ? (
            <div className={styles.preview}>
              <video src={preview?.preview} />
              <div onClick={() => handleDeleteImage(categoryKey, index)} className={styles.deleteButton}>
                <DeleteIcon />
              </div>
            </div>
          ) : (
            <div className={styles.preview}>
              <img src={preview?.preview} alt={`Preview ${index}`} className={styles.previewImage} />
              <div onClick={() => handleDeleteImage(categoryKey, index)} className={styles.deleteButton}>
                <DeleteIcon />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )

  const handleUploadClick = () => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    } else {
      handleFileUpload(categoryKey)
    }
  }

  const title = useMemo(() => {
    return header === 'Progress Videos' ? 'Progress Media' : header
  }, [header])

  const renderFileInput = () => (
    <div>
      <input
        type="file"
        multiple={categoryKey === 'Additional Documents' || categoryKey === 'Decree' ? false : true}
        accept={
          categoryKey === 'Additional Documents' || categoryKey === 'Decree'
            ? 'application/pdf'
            : categoryKey === 'Progress Videos'
              ? 'image/jpeg,image/png,image/gif,video/mp4'
              : 'image/jpeg,image/png,image/gif'
        }
        onChange={handleFileInputChange}
        className={styles.fileInput}
      />
      <span
        className={!isEditForUser ? styles.isEditForUser : ''}
        onClick={(e) => {
          if (!isEditForUser) {
            e.preventDefault()
            toast(`The current reporting period is locked`, {
              icon: <WarningAmberOutlined />,
            })
          }
        }}
      >
        Select Files
      </span>
    </div>
  )

  return (
    <div className={styles.categoryBox}>
      <p>{title}</p>
      <div
        className={`${styles.dropZone} ${isDragOver ? styles.dragOver : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOverInternal}
        onDragLeave={handleDragLeave}
      >
        <label className={styles.customFileInput}>
          {isEditForUser ? (
            <Tooltip
              title={renderToolTip()}
              arrow
              componentsProps={{
                tooltip: {
                  sx: {
                    background: 'linear-gradient(135deg, #3874cb 0%, #1e4f8f 100%)',
                    color: '#fff',
                    borderRadius: '8px',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
                    fontSize: '12px',
                    padding: '16px',
                  },
                },
                arrow: {
                  sx: {
                    color: '#3874cb',
                  },
                },
              }}
            >
              {renderFileInput()}
            </Tooltip>
          ) : (
            renderFileInput()
          )}
        </label>
        Drag and drop files here
      </div>

      {errors[categoryKey] && <div className={styles.error}>{errors[categoryKey]}</div>}
      {renderPreview()}
      <Button
        onClick={handleUploadClick}
        className={!isEditForUser ? styles.uploadButton : ''}
        disabled={!isEditForUser}
      >
        Upload {title}
      </Button>
    </div>
  )
}

export default MediaUploadBox

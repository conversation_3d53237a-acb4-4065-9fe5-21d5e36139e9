import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetSpaResponse, ISpaStatus, ISpa } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ISpaStatus = {
  getSpaStatus: StatusEnum.Idle,
  addSpaStatus: StatusEnum.Idle,
  deleteSpaStatus: StatusEnum.Idle,
  updateSpaStatus: StatusEnum.Idle,
  spaSortStatus: StatusEnum.Idle,
  spas: [],
  spa: {},
}

export const getSpa = createAsyncThunk(
  '/get-spa',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(
        `/project-milestone?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addSpa = createAsyncThunk('/add-spa', async (data: ISpa, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(
      `/project-milestone`,
      data /* , {
      isFormData: true,
    } */,
    )
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateSpa = createAsyncThunk('/update-spa', async (data: { id: number; data: ISpa }, thunkAPI) => {
  const { id } = data
  try {
    const response = await ApiPutNoAuth(
      `/project-milestone/${id}`,
      data.data /* , {
      isFormData: true,
    } */,
    )
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updatePDFSpa = createAsyncThunk(
  '/update-pdf-spa',
  async (data: { id: number; data: FormData }, thunkAPI) => {
    const { id } = data
    try {
      const response = await ApiPutNoAuth(`/project-milestone/${id}`, data.data, {
        isFormData: true,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const spaSort = createAsyncThunk('/project-milestone/bulk-sort', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-milestone/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteSpa = createAsyncThunk('/delete-spa', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/project-milestone/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const spaSlice = createSlice({
  name: 'spa',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getGovernance
    builder.addCase(getSpa.pending, (state) => {
      state.getSpaStatus = StatusEnum.Pending
    })
    builder.addCase(getSpa.fulfilled, (state, action) => {
      state.getSpaStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetSpaResponse
      state.spas = actionPayload.data
    })
    builder.addCase(getSpa.rejected, (state) => {
      state.getSpaStatus = StatusEnum.Failed
    })
    //addGovernance
    builder.addCase(addSpa.pending, (state) => {
      state.addSpaStatus = StatusEnum.Pending
    })
    builder.addCase(addSpa.fulfilled, (state, action) => {
      state.addSpaStatus = StatusEnum.Success
    })
    builder.addCase(addSpa.rejected, (state) => {
      state.addSpaStatus = StatusEnum.Failed
    })
    //updateGovernance
    builder.addCase(updateSpa.pending, (state) => {
      state.updateSpaStatus = StatusEnum.Pending
    })
    builder.addCase(updateSpa.fulfilled, (state, action) => {
      state.updateSpaStatus = StatusEnum.Success
    })
    builder.addCase(updateSpa.rejected, (state) => {
      state.updateSpaStatus = StatusEnum.Failed
    })
    //updatePDFInSPA
    builder.addCase(updatePDFSpa.pending, (state) => {
      state.updateSpaStatus = StatusEnum.Pending
    })
    builder.addCase(updatePDFSpa.fulfilled, (state, action) => {
      state.updateSpaStatus = StatusEnum.Success
    })
    builder.addCase(updatePDFSpa.rejected, (state) => {
      state.updateSpaStatus = StatusEnum.Failed
    })
    //sortSPA
    builder.addCase(spaSort.pending, (state) => {
      state.spaSortStatus = StatusEnum.Pending
    })
    builder.addCase(spaSort.fulfilled, (state, action) => {
      state.spaSortStatus = StatusEnum.Success
    })
    builder.addCase(spaSort.rejected, (state) => {
      state.spaSortStatus = StatusEnum.Failed
    })
    //deleteGovernance
    builder.addCase(deleteSpa.pending, (state) => {
      state.deleteSpaStatus = StatusEnum.Pending
    })
    builder.addCase(deleteSpa.fulfilled, (state, action) => {
      state.deleteSpaStatus = StatusEnum.Success
    })
    builder.addCase(deleteSpa.rejected, (state) => {
      state.deleteSpaStatus = StatusEnum.Failed
    })
  },
})

export const {} = spaSlice.actions

// Export the reducer
export default spaSlice.reducer

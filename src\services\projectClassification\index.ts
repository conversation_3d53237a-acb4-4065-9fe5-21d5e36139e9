import { IProjectClassificationPayload, IUpdateProjectClassification } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_CLASSIFICATION_URL = `/master-Project-Classification`

// Get Master Project Classification data
export const getMasterProjectClassifications = async () => {
  const response = await api.get(`${MASTER_PROJECT_CLASSIFICATION_URL}`)
  return response?.data
}

export const deleteMasterProjectClassification = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_CLASSIFICATION_URL}/${data}`)
  return response?.data
}

// Create Master Project Classification entry
export const createMasterProjectClassification = async (data: IProjectClassificationPayload) => {
  const response = await api.post(`${MASTER_PROJECT_CLASSIFICATION_URL}`, data)
  return response
}

// Update Master Project Classification entry
export const updateMasterProjectClassification = async (data: IUpdateProjectClassification): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_CLASSIFICATION_URL}/${data.id}`, { ...rest })
  return response
}

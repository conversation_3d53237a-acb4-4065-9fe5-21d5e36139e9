import React, { ReactElement, forwardRef } from 'react'

interface CommercialsSaveIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: string
}

const CommercialsSaveIcon: React.FC<CommercialsSaveIconProps> = forwardRef<SVGSVGElement, CommercialsSaveIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        version="1.1"
        width="28"
        height="28"
        x="0"
        y="0"
        viewBox="0 0 54 54"
        {...props}
        ref={ref}
      >
        <g>
          <path
            d="M33.807 10H20.193A5.2 5.2 0 0 0 15 15.193V41a1 1 0 0 0 1.588.808L27 34.236l10.412 7.572A1 1 0 0 0 38 42a1 1 0 0 0 1-1V15.193A5.2 5.2 0 0 0 33.807 10zM37 39.036l-9.412-6.845a1 1 0 0 0-1.176 0L17 39.036V15.193A3.2 3.2 0 0 1 20.193 12h13.614A3.2 3.2 0 0 1 37 15.193z"
            fill="#069d55"
            opacity="1"
            data-original="#000000"
          ></path>
          <path
            d="M27 4a23 23 0 1 0 23 23A23.026 23.026 0 0 0 27 4zm0 44a21 21 0 1 1 21-21 21.023 21.023 0 0 1-21 21z"
            fill="#069d55"
            opacity="1"
            data-original="#000000"
          ></path>
        </g>
      </svg>
    )
  },
)
CommercialsSaveIcon.displayName = 'CommercialsSaveIcon'

export default CommercialsSaveIcon

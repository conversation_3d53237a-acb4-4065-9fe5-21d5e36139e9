@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.cardContainer {
  padding: 12px 15px;
  background-color: rgba(104, 178, 226, 0.1);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center; // Default for mobile
  @include respond-to('tablet') {
    align-items: flex-start;
  }
}

.cards {
  display: flex;
  flex-direction: column; // Default (mobile & tablet)
  gap: 12px;
  margin-top: 12px;

  // @include respond-to('mobile') {
  //   display: grid;
  //   grid-template-columns: 1fr 1fr;
  // }

  @include respond-to('tablet') {
    display: flex;
    flex-direction: column;
  }

  @include respond-to('desktop') {
    display: flex;
    flex-direction: row;
  }
}

.card {
  display: flex;
  flex-direction: column; // Default (mobile)
  gap: 10px;

  @include respond-to('tablet') {
    flex-direction: row;
  }

  @include respond-to('desktop') {
    flex-direction: column;
  }
}

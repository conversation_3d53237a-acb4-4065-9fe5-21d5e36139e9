@import '/styles/breakpoints.scss';
@import '/styles/color.scss';

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;

  @include respond-to('mobile') {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }
  @include respond-to('tablet') {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
  }
}

/* Pagination Controls */
.paginationControls {
  display: flex;
  align-items: center;
  gap: 8px;

  @include respond-to('mobile') {
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }
}

/* Adjust Page Buttons */
.pageButton {
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 6px;
  transition: 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: $DARK;

  &:hover {
    transform: scale(1.1);
    background: rgba($DARK_200, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: $GRAY_500;
  }

  @include respond-to('mobile') {
    padding: 4px;
    font-size: 12px;
  }
}

/* Pagination */
.pagination {
  .MuiPaginationItem-root {
    font-size: 14px;
    font-family: 'Poppins';
    border-radius: 6px;
    color: $DARK;

    &:hover {
      background: rgba($DARK_200, 0.1);
    }

    &.Mui-selected {
      background-color: $DARK !important;
      color: $WHITE !important;
    }
  }

  @include respond-to('mobile') {
    .MuiPaginationItem-root {
      font-size: 12px;
      padding: 2px 5px;
    }
  }
}

/* Adjust Row Summary */
.summary {
  font-size: 14px;
  font-weight: 500;
  color: $DARK;

  @include respond-to('mobile') {
    font-size: 12px;
    text-align: center;
  }
}
.summaryContainer {
  display: flex;
  gap: 5px;
  align-items: center;
  @include respond-to('mobile') {
    flex-direction: column;
  }
  @include respond-to('tablet') {
    display: flex;
    flex-direction: row;
  }
}

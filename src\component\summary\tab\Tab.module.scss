@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.tab {
  cursor: pointer;
  color: $WHITE !important;
  backdrop-filter: contrast(0.7);
  border-radius: 6px 6px 0 0;
  padding: 0.5rem 1rem; // 8px 16px → 0.5rem 1rem
  text-align: center;
  font-weight: normal;
  display: inline-block; // Prevent stretching in flex containers
  white-space: nowrap; // Prevent text from wrapping

  transition:
    transform 0.3s cubic-bezier(0.25, 1, 0.5, 1),
    opacity 0.3s ease-in-out;

  &:hover {
    transform: scale(1.03);
    opacity: 0.9;
  }

  p {
    font-size: 0.75rem; // 12px → 0.75rem

    @include respond-to('mobile') {
      font-size: 0.75rem;
    }
    @include respond-to('tablet') {
      font-size: 0.75rem;
    }
    @include respond-to('laptop') {
      font-size: 0.875rem; // 14px → 0.875rem
    }
  }

  &.selectedTab {
    color: $DARK !important;
    background-color: #fff;
    padding: 0;
    display: flex;
    align-items: center;
    border-radius: 0.5rem 0.5rem 0 0; // 8px → 0.5rem
    transition:
      background-color 0.4s ease-in-out,
      transform 0.3s cubic-bezier(0.25, 1, 0.5, 1);

    transform: scale(1.05);

    .label {
      height: -webkit-fill-available;
      margin: 0.5rem 1rem 0; // 8px 16px → 0.5rem 1rem
      border-bottom: 0.125rem solid #7ac9d4; // 2px → 0.125rem
      transition: border-bottom 0.3s ease-in-out;
    }

    &:first-child {
      border-radius: 0.75rem 0.5rem 0 0; // 12px 8px → 0.75rem 0.5rem
    }
  }
}

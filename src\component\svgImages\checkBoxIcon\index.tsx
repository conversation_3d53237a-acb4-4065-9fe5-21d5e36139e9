import React, { ReactElement, forwardRef } from 'react'

interface CheckboxIconProps {
  className?: string
  onClick?: (e: any) => void
}

const CheckboxIcon: React.FC<CheckboxIconProps> = forwardRef<SVGSVGElement, CheckboxIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <rect x="0.5" y="0.5" width="19" height="19" rx="1.5" stroke="#EAEAEA" />
      </svg>
    )
  },
)
CheckboxIcon.displayName = 'CheckboxIcon'

export default CheckboxIcon

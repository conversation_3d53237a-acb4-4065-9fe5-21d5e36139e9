@import '/styles/color.scss';

.container {
  width: 100%;
  height: 100%;
  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;

    .headerTitle {
      font-family: Poppins;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      text-align: left;
      padding: 13px 0 13px 20px;
    }

    .actionButtons {
      display: flex;
      gap: 20px;

      .closeButton {
        padding: 8px 10px;
      }
    }
  }

  .content {
    margin: 20px 20px 0px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .contentHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .leftContent {
      display: flex;
      gap: 10px;
      width: 100%;
      flex: 1;
      align-items: center;
      .projectName {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        text-align: left;
        color: $DARK;
        white-space: nowrap;
      }

      .dropDown {
        max-width: 246px;
      }
    }

    .actionStagesButtons {
      display: flex;
      gap: 10px;
    }
  }
}
.customCell {
  display: flex;
  gap: 2px;
  flex-direction: column;
}
.actionBts {
  display: flex;
  gap: 5px;
  cursor: pointer;
}

.subStatus {
  font-family: Poppins;
  color: #444444;
  font-size: 10px;
  font-weight: 400;
  line-height: 15px;
}
.loader {
  width: 100%;
  height: calc(100vh - 115px);
}

.tableWrapper {
  margin: 10px 20px 0px;
  overflow-y: auto;
  overflow-x: auto;
  height: calc(100vh - 510px);
}

.infoContainer {
  display: flex;
  align-items: center;
  gap: 5px;

  .infoIcon {
    cursor: pointer;
    margin-top: 5px;
  }
}

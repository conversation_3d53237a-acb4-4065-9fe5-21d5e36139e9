@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.model {
  z-index: 1500 !important;
  backdrop-filter: blur(8px);
  /* Apply the blur effect */
  border-radius: 12px !important;
}

.confirmButtonContent {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  display: flex;
  gap: 16px;
  flex-direction: column;

  .header {
    display: flex;
    justify-content: space-between;
    color: $DARK;
  }

  .confirmContent {
    .name {
      font-family: Poppins;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
    }

    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
  }
}

.reassignButtons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.reassignButton {
  border: 1px solid $ERROR !important;
  background: $ERROR !important;
  //styleName: Body/Body;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0em;
  text-align: center;
  color: $LIGHT;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 10px 0;
  }
}

.rating {
  margin-top: 16px;

  p {
    font-weight: 500;
    margin-bottom: 8px;
  }
}

.ratingButtons {
  display: flex;
  gap: 25px;
}

.ratingButton {
  border: 1px solid #ccc;
  background: $WHITE;
  border-radius: 8px;
  width: 60px;
  height: 50px;
  cursor: pointer;
  font-size: 16px;

  &.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
}

.attachmentInput {
  margin-bottom: 25px;
}

.fileLabel {
  display: block;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  text-align: center;
}

.entityFieldTextArea {
  background-color: $WHITE;
  border-radius: 4px;
  border: 1px solid #ccc;
  height: 77px;

  textarea {
    background-color: $WHITE !important;

    &:hover {
      cursor: default; // keep text cursor unless over scrollbar
    }

    &::-webkit-scrollbar {
      width: 8px;
      cursor: pointer; // shows hand on scrollbar track
    }

    &::-webkit-scrollbar-thumb {
      background-color: $BLACK;
      border-radius: 4px;
      cursor: pointer; // shows hand on scrollbar thumb
    }

    &:focus {
      border-radius: 4px;
      background: $WHITE;
      border: 1px solid $FOCUS_TEXTFIELD_BORDER;
    }
  }
}

.entityField {
  > div > div > input {
    border: 1px solid #ccc;
    background-color: $WHITE;
    color: $BLACK;
    border-radius: 4px;
    min-width: 200px;

    @include respond-to('mobile') {
      min-width: 150px;
    }

    @include respond-to('tablet') {
      min-width: 170px;
    }

    &:focus {
      border-radius: 4px;
      background: $WHITE;
      border: 1px solid $FOCUS_TEXTFIELD_BORDER;
    }
  }
}

.feedbackModal {
  .title {
    margin-top: 10px;
    margin-bottom: 4px;
  }

  .feedbackbtn {
    margin-top: 30px;
    text-align: end;

    button {
      background: $BLACK;
      padding: 10px 30px;
    }
  }
}

.attachmentContainer {
  margin-bottom: 20px;
}

.fileInputWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $WHITE; // match to the modal blueish bg
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 10px 14px;
  cursor: pointer;
  position: relative;
  font-size: 14px;
  color: #888;
  font-family: 'Poppins', sans-serif;
  transition: border 0.2s ease;

  &:hover {
    border-color: #007bff;
  }
}

.filePlaceholder {
  flex: 1;
  color: #999;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.fileIcon {
  margin-left: 10px;
  font-size: 18px;
  color: #444;
}

.previewSection {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fileRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fileName {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actionIcons {
  display: flex;
  gap: 18px;

  button {
    padding: 1px 0;
  }
}

.previewImage {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
}

@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.container {
  padding: 10px 15px 10px 10px;
  overflow: auto;
  height: fit-content;
  // @media (max-width: 1440px) {
  //   height: inherit;
  // }

  @include respond-to('mobile') {
    padding: 10px 15px 10px 20px;
  }

  @include respond-to('tablet') {
    padding: 10px 20px 10px 20px;
  }

  @include respond-to('laptop') {
    padding: 10px 15px 10px 10px;
  }
}

.topContainer {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 12px;

  @include respond-to('mobile') {
    flex-wrap: nowrap;
  }

  @include respond-to('tablet') {
    flex-wrap: wrap;
  }

  @include respond-to('laptop') {
    gap: 14px;
    flex-direction: row;
    flex-wrap: wrap;
    width: auto;
  }

  // For desktop and wider
  @include respond-to('desktop') {
    gap: 17px;
    flex-direction: row;
    flex-wrap: nowrap;
  }
}

import { useSensor, useSensors, MouseSensor, TouchSensor, KeyboardSensor, DragEndEvent } from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import { IRowData } from '../interface'

export const useDragAndDrop = (
  rows: IRowData[],
  onDragEnd?: (data: IRowData[], dragId: string, dropId: string) => void,
) => {
  const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}))

  const handleRowDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!active || !over || active.id === over.id) return

    const oldIndex = rows.findIndex((row) => row.id === active.id)
    const newIndex = rows.findIndex((row) => row.id === over.id)

    if (oldIndex !== -1 && newIndex !== -1) {
      const newRows = arrayMove(rows, oldIndex, newIndex)
      onDragEnd?.(newRows, active.id.toString(), over.id.toString())
    }
  }

  return { sensors, handleRowDragEnd }
}

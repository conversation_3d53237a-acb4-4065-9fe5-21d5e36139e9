@import '/styles/color.scss';

.model {
  z-index: 1500 !important;
  backdrop-filter: blur(8px); /* Apply the blur effect */
  border-radius: 12px !important;
}

.confirmButtonContent {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  display: flex;
  gap: 10px;
  flex-direction: column;
  // background-color: #fafafa;
  .header {
    display: flex;
    justify-content: space-between;
    color: $DARK;
  }

  .confirmContent {
    .name {
      font-family: Poppins;
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
    }

    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
  }
}

.reassignButtons {
  display: flex;
  gap: 16px;
}

.reassignButton {
  border: 1px solid $ERROR !important;
  background: $ERROR !important;
  //styleName: Body/Body;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0em;
  text-align: center;
  color: $LIGHT;
}

import { format, parseISO } from 'date-fns'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'
import {
  ILookupProjectToPhase,
  IMasterProjectStageStatus,
  IMasterProjectSubStage,
  IStatus,
} from '@/src/redux/status/interface'
import { getDateDifference, payloadDateFormate } from '@/src/utils/dateUtils'
import { convertToPercentage, toString } from '@/src/utils/numberUtils'

export const findProjectManagementData = (
  projectManagement: any,
  LookupProjectToPhase: ILookupProjectToPhase[] | undefined,
  stageStatus: IMasterProjectStageStatus | undefined,
  subStage: IMasterProjectSubStage | undefined,
) => {
  let data = {}

  projectManagement.forEach((item: any) => {
    const hasPhaseValue =
      LookupProjectToPhase &&
      LookupProjectToPhase?.length > 0 &&
      !!item?.LookupProjectToPhase?.some((itemPhase: any) =>
        LookupProjectToPhase?.some((lookupPhase) => lookupPhase.id === itemPhase.id),
      )

    const hasStageStatus = item?.MasterProjectStageStatus?.id === stageStatus?.id
    const hasSubStage = item?.MasterProjectSubStage?.id === subStage?.id

    if (hasPhaseValue) {
      if (stageStatus?.project_stage_status === 'Design') {
        if (item?.MasterProjectSubStage?.project_sub_stage === subStage?.project_sub_stage) {
          data = item
        }
      } else if (hasStageStatus) {
        data = item
      }
    }
  })
  return data
}

export const buildProjectManagementValue = (data: any, isThatProject: boolean) => {
  return {
    budget: isThatProject ? data?.budget : '',
    cost_per_sqm: Boolean(isThatProject ? data?.cost_per_sqm : ''),
    spi: isThatProject ? data?.spi : 0,
    project_management_var: isThatProject ? data?.project_management_var : 0,
    // forecast_completion_last_week: isThatProject ? data?.forecast_completion_last_week : '',
    procurement_package: isThatProject ? data?.procurement_package : null,
    delay_this_period: isThatProject ? (data?.delay_this_period ?? 0) : 0,
    cumulative_delay: isThatProject ? (data?.cumulative_delay ?? 0) : 0,
    manpower_planned: isThatProject ? (data?.manpower_planned ?? 0) : 0,
    manpower_actual: isThatProject ? (data?.manpower_actual ?? 0) : 0,
    man_hours: isThatProject ? (data?.man_hours ?? 0) : 0,
    bua: isThatProject ? data?.bua : null,
    gfa: isThatProject ? data?.gfa : null,
    no_plots: isThatProject ? data?.no_plots : null,
    no_units: isThatProject ? data?.no_units : null,
    incidents: isThatProject ? (data?.incidents ?? 0) : 0,
    fatalities: isThatProject ? (data?.fatalities ?? 0) : 0,
    lti_ratio: isThatProject ? (data?.lti_ratio ?? 0) : 0,
    time_elapsed_percentage: isThatProject ? (data?.time_elapsed_percentage ?? 0) : 0,
    projectManagementLastUpdate: isThatProject ? data?.last_updated : null,
    projectManagementUpdatedBy: isThatProject ? data?.updated_by : null,
  }
}

export const buildStatusData = (status: IStatus | null, statuses: IStatus[]) => {
  const baseData = {
    id: status?.id,
    period: status?.period,
    project_name: status?.project_name,
    // phase: status?.phase ?? '',
    project_to_project_phase_ids: status?.LookupProjectToPhase?.map((item) => item.id),
    // project_stage_status: status?.MasterProjectStageStatus?.project_stage_status ?? '',
    stage_status: status?.MasterProjectStageStatus?.project_stage_status ?? '',
    sub_stage_status: status?.MasterProjectSubStage?.project_sub_stage ?? '',
    actual_progress_percentage_of_last_week: convertToPercentage(status?.actual_progress_percentage_of_last_week),
    actual_progress_percentage_for_last_week: convertToPercentage(status?.actual_progress_percentage_for_last_week),
    eot_to_entity: status?.eot_to_entity ?? '',
    eot_to_contractor: status?.eot_to_contractor ?? '',
    master_supervision_consultant_id: status?.SupervisionConsultant?.id ?? '',
    master_consultant_id: status?.Consultant?.id ?? null,
    key_highlights: status?.key_highlights ?? '',
    design_manager_ids: status?.DesignManagers?.map((item: any) => item?.id),
    delivery_project_manager_ids: status?.DeliveryProjectManagers?.map((item: any) => item?.id),
    master_project_sub_stage_id: status?.MasterProjectSubStage?.id ?? null,
    project_sub_stage: status?.MasterProjectSubStage?.project_sub_stage ?? '',
    master_project_stage_status_id: status?.MasterProjectStageStatus?.id ?? '',
    master_procurement_manager_id: status?.MasterProcurementManager?.id ?? '',
    sub_stage: toString(status?.sub_stage),
    design_stage_weightage: convertToPercentage(status?.design_stage_weightage),
    executing_entity: toString(status?.executing_entity),
    phase_weightage: convertToPercentage(status?.phase_weightage),
    master_contractor_id: status?.MasterContractor?.id ?? null,
    contract_start_date: status?.contract_start_date ?? '',
    procurement_start_date: status?.procurement_start_date ?? '',
    contract_end_date: status?.contract_end_date ?? '',
    decree_end_date: status?.decree_end_date,
    master_pmc_consultant_id: status?.MasterPMCConsultant?.id ?? null,
    latitude: status?.latitude,
    longitude: status?.longitude,
    statusLastUpdate: status?.last_updated,
    key_highlights_last_updated: status?.key_highlights_last_updated,
    key_highlights_updated_by: status?.key_highlights_updated_by,
    updated_by: status?.updated_by,
    plan_contractor_progress_percentage: status?.plan_contractor_progress_percentage,
    duration_in_days: status?.duration_in_days,
    buffer_in_days: status?.buffer_in_days,
    is_contract_signature_received: status?.is_contract_signature_received,
    is_advance_payment_bond_received: status?.is_advance_payment_bond_received,
    is_insurances_received: status?.is_insurances_received,
    is_advance_payment_released: status?.is_advance_payment_released,
    is_performance_bond_received: status?.is_performance_bond_received,
    variance_in_days: status?.variance_in_days,
    forecast_completion_last_week: status?.forecast_completion_last_week
      ? format(parseISO(status?.forecast_completion_last_week as string), 'dd/MM/yyyy')
      : '',
    kickoff_meeting_date: status?.kickoff_meeting_date ?? '',
    pte: Number(status?.pte),
    plan_duration: status?.plan_duration,
    is_baseLinePlanFinish_edit: false, //* Managed only at FE
    /**
     ** Project management fields
     */
    budget: status?.ProjectManagement?.budget ?? null,
    cost_per_sqm: Boolean(status?.cost_per_sqm ?? null),
    spi: status?.ProjectManagement?.spi ?? 0,
    project_management_var: status?.ProjectManagement?.project_management_var ?? null,
    // forecast_completion_last_week:  status?.ProjectManagement?.forecast_completion_last_week ,
    procurement_package: status?.ProjectManagement?.procurement_package,
    delay_this_period: status?.ProjectManagement?.delay_this_period ?? 0,
    cumulative_delay: status?.ProjectManagement?.cumulative_delay ?? 0,
    manpower_planned: status?.ProjectManagement?.manpower_planned ?? 0,
    manpower_actual: status?.ProjectManagement?.manpower_actual ?? 0,
    man_hours: status?.ProjectManagement?.man_hours ?? 0,
    bua: status?.bua,
    gfa: status?.gfa,
    no_plots: status?.no_plots,
    no_units: status?.no_units,
    incidents: status?.ProjectManagement?.incidents ?? 0,
    fatalities: status?.ProjectManagement?.fatalities ?? 0,
    lti_ratio: status?.ProjectManagement?.lti_ratio ?? 0,
    time_elapsed_percentage: status?.ProjectManagement?.time_elapsed_percentage ?? 0,
    projectManagementLastUpdate: status?.ProjectManagement?.last_updated,
    projectManagementUpdatedBy: status?.ProjectManagement?.updated_by,
  }

  const additionalData =
    status?.MasterProjectStageStatus?.project_stage_status === CONTRACTOR_PROCUREMENT ||
    status?.MasterProjectStageStatus?.project_stage_status === CONSTRUCTION ||
    status?.MasterProjectStageStatus?.project_stage_status === DLP_PROJECT_CLOSEOUT
      ? {
          actual_plan_percentage: convertToPercentage(status?.actual_plan_percentage),
          revised_plan_percentage: convertToPercentage(status?.revised_plan_percentage),
          plan_end_date: status?.plan_end_date,
          revised_plan_end_date: status?.revised_plan_end_date,
          forecasted_end_date: status?.forecasted_end_date,
        }
      : status?.MasterProjectStageStatus?.project_stage_status === LDC_PROCUREMENT ||
          status?.MasterProjectStageStatus?.project_stage_status === DESIGN ||
          status?.MasterProjectStageStatus?.project_stage_status === INITIATION
        ? {
            actual_percentage: convertToPercentage(status?.actual_percentage),
            rev_plan_percentage: convertToPercentage(status?.rev_plan_percentage),
            baseline_plan_finish: status?.baseline_plan_finish ?? null,
            revised_baseline_finish: status?.revised_baseline_finish,
            forecast_finish: status?.forecast_finish,
          }
        : {}
  return { ...baseData, ...additionalData }
}

export const hasFieldChanged = (initialValues: any, values: any, field: string): boolean =>
  initialValues[field] !== values[field]

export const calculateDateDifference = (planDate: string, contractDate: string) => {
  const formattedPlanDate = payloadDateFormate(planDate)
  const formattedContractDate = payloadDateFormate(contractDate)

  return formattedPlanDate && formattedContractDate
    ? getDateDifference(formattedPlanDate, formattedContractDate)
    : formattedPlanDate || formattedContractDate
      ? 0
      : null
}

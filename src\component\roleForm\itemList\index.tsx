import React from 'react'
import { Mo<PERSON>, Box, Popover } from '@mui/material'
import styles from './ItemList.module.scss'
import Button from '../../shared/button'
import TypographyField from '../../shared/typography'
import CloseCircleIcon from '../../svgImages/closeCircleIcon'

interface ModalProps {
  open: boolean
  anchorEl: any
  onClose: () => void
  items: any
  itemName: any
}

const ItemList: React.FC<ModalProps> = ({ open, onClose, items, itemName, anchorEl }) => {
  const style = {
    // position: 'absolute' as 'absolute',
    // top: '50%',
    // left: '50%',
    // width: '900px',
    // maxHeight: '820px', // Fixed maximum height
    height: '600px',
    // transform: 'translate(-50%, -50%)',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    px: '20px',
    // zIndex: 1,
    // overflowY: 'hidden',
    width: '750px',
  }

  return (
    <Popover
      className={styles.model}
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.confirmContent}>
          <div className={styles.closeIcon}>
            <div className={styles.title}>{`${itemName}`}</div>
            <CloseCircleIcon className={styles.icon} onClick={onClose} />
          </div>
          <div className={styles.itemList}>
            {items.map((item: any, index: any) => {
              return (
                <div className={styles.list}>
                  <TypographyField text={index + 1} style={{ fontSize: '14px' }} />
                  <TypographyField text={`. ${item}`} style={{ fontSize: '14px' }} />
                </div>
              )
            })}
          </div>
        </div>
      </Box>
    </Popover>
  )
}

export default ItemList

import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './ConfirmValueChangeModal.module.scss'
import Button from '@/src/component/shared/button'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'

interface ModalProps {
  open: boolean
  header?: string
  value?: any
  message?: any
  onClose: () => void
  handleConfirm: () => void
}

const ConfirmValueChangeModal: React.FC<ModalProps> = ({ open, header, value, message, onClose, handleConfirm }) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '400px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    pt: '20px',
    px: '20px',
    pb: '20px',
    zIndex: 1,
  }

  let displayValue = ''

  if (value?.toString().split(MULTI_SELECT_SEPARATOR)?.length > 1) {
    displayValue = value?.split(MULTI_SELECT_SEPARATOR).join(', ')
  } else {
    displayValue = value
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.confirmButtonContent}>
          <div className={styles.confirmContent}>
            {`Are you sure you want to `}
            {displayValue ? `change the ` : `remove the `}
            <strong>{header}</strong>
            {displayValue ? ` value to ` : ` value?`}
            {displayValue && <strong>{displayValue}?</strong>}
          </div>
          {message && <div className={styles.confirmContent}>{message}</div>}
          <div className={styles.reassignButtons}>
            <Button onClick={() => handleConfirm()}>Yes</Button>
            <Button color="secondary" onClick={() => onClose()}>
              No
            </Button>
          </div>
        </div>
      </Box>
    </Modal>
  )
}

export default ConfirmValueChangeModal

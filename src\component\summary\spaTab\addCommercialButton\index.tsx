import React from 'react'
import { IAddCommercialButtonProps } from '../interface'
import styles from '../SpaTab.module.scss'
import Button from '@/src/component/shared/button'

const AddCommercialButton: React.FC<IAddCommercialButtonProps> = ({
  setIsOpenDrawer,
  className,
  startIcon,
  buttonColor,
}) => {
  const handleButtonClick = () => {
    setIsOpenDrawer(true)
  }

  return (
    <Button
      startIcon={startIcon}
      onClick={handleButtonClick}
      className={`${styles.addCommercialBtn} ${className}`}
      color={buttonColor ? buttonColor : 'primary'}
    >
      Add SPA & Milestones
    </Button>
  )
}

export default AddCommercialButton

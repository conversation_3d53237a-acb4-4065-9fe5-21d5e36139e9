import { useDispatch, useSelector } from 'react-redux'
import { getAllAvatars } from '.'
import { AvatarData, IAvatar } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useAvatar = () => {
  const dispatch: AppDispatch = useDispatch()

  const transformAvatarData = (data: AvatarData): IAvatar[] => {
    if (data) {
      return Object.keys(data).map((category) => ({
        categoryName: category,
        avatars: data[category],
      }))
    }
    return []
  }

  const originalAllAvatars = useSelector((state: RootState) => state.avatars.avatars)
  const allAvatars: IAvatar[] = transformAvatarData(originalAllAvatars as AvatarData)
  const getAllAvatarsApi = () => dispatch(getAllAvatars({}))

  return {
    allAvatars,
    getAllAvatarsApi,
  }
}
export default useAvatar

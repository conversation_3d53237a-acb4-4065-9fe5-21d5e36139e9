export interface IAddEntityProps {
  drawerStates: any
  onClose: () => void
}
export interface IEntity {
  id: number
  owning_entity: string
}

export interface IEntityColumn {
  key: string
  label: string
  filterCell?: () => JSX.Element | undefined
  renderCell?: (value: any, row: IEntity, rowIndex: number) => JSX.Element | undefined
  cellStyle?: React.CSSProperties
  headerStyle?: React.CSSProperties
  isRabin?: boolean
}

export interface IAddEntityTableProps {
  columns: IEntityColumn[]
  data: IEntity[]
}

export interface TableHeaderProps {
  columns: IEntityColumn[]
}

export interface TableBodyProps {
  data: IEntity[]
  columns: IEntityColumn[]
}

export interface TableRowProps {
  row: IEntity
  rowIndex: number
  columns: IEntityColumn[]
}

export interface TableCellProps {
  value: any
  column: IEntityColumn
  rowIndex: number
  row: IEntity
}

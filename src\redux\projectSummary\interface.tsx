import { StatusEnum } from '../types'
import { IProjects } from '@/src/services/projects/interface'

export interface IProjectStates {
  localFilteredData: any
  filterData: IProjects[]
  projectFilterValue: { colId: string; values: any }[]
  recentProject: IRecentProject | null
  projectFilter: { field: string; value: any }[]
  columnVisibilities: Record<string, boolean>
  columnsWidth: Record<string, number>
}

export interface IRecentProject {
  projectName?: string
  owningEntity?: string
  SearchProjectName?: string
}

export interface IGetMasterProjectsResponse {
  data: IProjects[]
  message: string
  success: true
}

import { StatusEnum } from '../types'

export interface IDeveloperStates {
  getMasterDevelopersStatus: StatusEnum
  addMasterDeveloperStatus: StatusEnum
  deleteMasterDeveloperStatus: StatusEnum
  updateMasterDeveloperStatus: StatusEnum
  developers: IDeveloper[]
  developer: IDeveloper
}
export interface IDeveloper {
  id: number
  developers: string
}

export interface IGetMasterDeveloperResponse {
  data: IDeveloper[]
  message: string
  success: true
}

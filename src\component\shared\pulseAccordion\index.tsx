import React, { ReactNode } from 'react'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material'

interface PulseAccordionProps {
  expanded: boolean
  onChange: (event: React.SyntheticEvent, isExpanded: boolean) => void // ✅ Updated signature
  summaryContent: ReactNode
  detailsContent: ReactNode
  customIcon?: ReactNode
  className?: string
  summaryClassName?: string
  detailsClassName?: string
  disabled?: boolean
}

const PulseAccordion: React.FC<PulseAccordionProps> = ({
  expanded,
  onChange,
  summaryContent,
  detailsContent,
  customIcon,
  className = '',
  summaryClassName = '',
  detailsClassName = '',
  disabled = false,
}) => {
  return (
    <Accordion
      expanded={expanded}
      onChange={onChange}
      className={className}
      disabled={disabled}
      sx={{
        '&::before': {
          backgroundColor: 'transparent',
        },
      }}
    >
      <AccordionSummary expandIcon={customIcon || <ExpandMoreIcon />} className={summaryClassName}>
        {summaryContent}
      </AccordionSummary>
      <AccordionDetails className={detailsClassName}>{detailsContent}</AccordionDetails>
    </Accordion>
  )
}

export default PulseAccordion

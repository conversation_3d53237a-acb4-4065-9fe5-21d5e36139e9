import React, { CSSProperties } from 'react'
import styles from './HighlightField.module.scss'
import RichTextEditor from '../../richTextEditor'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import PencilIcon from '@/src/component/svgImages/pencilIcon'
import { IStatus } from '@/src/redux/status/interface'

// Define the props type for the HighlightField component
interface HighlightFieldProps {
  text: string
  style?: React.CSSProperties
}

export const HighlightField = ({
  text,
  style,
  onDeleteClick,
  isDelete = true,
  isEdit = true,
  onEditClick,
}: {
  text: string
  style?: CSSProperties
  onDeleteClick?: any
  isDelete?: boolean
  isEdit?: boolean
  onEditClick?: any
}) => (
  <div className={styles.highlightField} style={style}>
    <RichTextEditor value={text} isEdit={false} className={styles.textEditor} />
    <div className={styles.actionBtnWrapper}>
      {isEdit && <PencilIcon className={styles.editIcon} onClick={() => onEditClick()} />}
      {isDelete && <DeleteIcon className={styles.deleteIcon} onClick={() => onDeleteClick()} />}
    </div>
  </div>
)

import { StatusEnum } from '../types'

export interface IAreaOfConcernStatus {
  getAreaOfConcernStatus: StatusEnum
  addAreaOfConcernStatus: StatusEnum
  deleteAreaOfConcernStatus: StatusEnum
  updateAreaOfConcernStatus: StatusEnum
  areaOfConcernsSortStatus: StatusEnum
  areaOfConcerns: IAreaOfConcern[]
  masterRatingList: IMastrRating[]
  areaOfConcern: IAreaOfConcern
}

export interface IAreaOfConcern {
  id?: number
  phase?: string
  period?: string
  project_name?: string
  description?: string
  mitigation_measures?: string
  forecasted_closure_date?: string | null
  forecast_closure_date_note?: string
  rating?: string
  risk_category?: string
  risk_discipline?: string
  risk_impact?: string
  risk_owner?: string
  last_updated?: Date
  key_risk_sorting_order: number | null
  MasterRating?: IMastrRating
}

export interface IGetAreaOfConcernsResponse {
  data: IAreaOfConcern[]
  message: string
  success: boolean
}

export interface IMastrRating {
  id: number
  rating: string
}
export interface IGetMastrRatingListResponse {
  data: IMastrRating[]
  message: string
  success: boolean
}

export interface StageStatus {
  id: number
  project_stage_status: string
}

export interface SubStage {
  id: number
  project_sub_stage: string
}

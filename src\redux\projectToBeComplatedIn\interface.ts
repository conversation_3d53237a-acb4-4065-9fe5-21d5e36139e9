import { StatusEnum } from '../types'

export interface IProjectToBeCompletedInStatus {
  getProjectToBeCompletedInStatus: StatusEnum
  addProjectToBeCompletedInStatus: StatusEnum
  deleteProjectToBeCompletedInStatus: StatusEnum
  updateProjectToBeCompletedInStatus: StatusEnum
  projectToBeCompletedIns: IProjectToBeCompletedIn[]
  projectToBeCompletedIn: IProjectToBeCompletedIn
}

export interface IProjectToBeCompletedIn {
  id?: string | number
  period?: string
  project_name?: string
  phase?: string
  value?: string
  main_description?: string
  details_description?: string
}

export interface IGetGovernanceResponse {
  data: IProjectToBeCompletedIn[]
  message: string
  success: true
}

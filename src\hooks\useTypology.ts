import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterTypology,
  deleteMasterTypology,
  getMasterTypologies,
  updateMasterTypology,
} from '../services/typology'
import { IGetMasterTypologiesResponse, IUpdateTypology } from '../services/typology/interface'

export const QUERY_TYPOLOGY = 'typology'

/**
 * Hook to fetch Typologies
 * @param enabled - Enables or disables the query
 */
export const useGetTypologies = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterTypologiesResponse>({
    queryKey: [QUERY_TYPOLOGY],
    queryFn: getMasterTypologies,
    enabled,
  })

  return {
    typologies: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Typologies
 */
export const useCreateTypologies = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterTypology,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_TYPOLOGY] })
    },
  })
}

/**
 * Hook to delete a Typologies
 */
export const useDeleteTypologies = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterTypology,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_TYPOLOGY] })
    },
  })
}

/**
 * Hook to update a Typologies
 */
export const useUpdateTypologies = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterTypology,
    onMutate: async (updatedData: IUpdateTypology) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_TYPOLOGY] })
      const previousData = queryClient.getQueryData<IGetMasterTypologiesResponse>([QUERY_TYPOLOGY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterTypologiesResponse>([QUERY_TYPOLOGY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, master_typology: updatedData.master_typology } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_TYPOLOGY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_TYPOLOGY] })
    },
  })
}

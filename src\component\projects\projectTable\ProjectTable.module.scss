@import '/styles/color.scss';

.searchField {
  > div > div {
    padding-right: 11px !important;
    background: #f5f5f5 !important; //TODO
  }
  > div > div > input {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    letter-spacing: 0em !important;
    text-align: left !important;
    padding: 0px !important;
    padding-top: 11px !important;
    padding-bottom: 11px !important;
    padding-left: 8px !important;
    &::placeholder {
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
      color: #999999 !important;
    }
  }
}

.tableContainer {
}
.loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.collapse {
  display: flex;
  gap: 10px;
  width: 100%;
  align-items: flex-start;
  .collapseContain {
    display: flex;
  }

  .iconCollapse {
    cursor: pointer;
    display: flex;
    height: 16px;
    width: 16px;
  }

  .collapseValues {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .collapseValue {
    margin-top: 5px;
    display: flex;
    text-align: left;
    flex-direction: column;
    justify-content: flex-start;
  }
}

.phaseHover {
  text-align: left;
  color: #2333c2bf;
}

.phaseHoverContent {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  cursor: pointer;
}

.table {
  padding-top: 54px;
  padding-left: 20px;
  padding-right: 20px;
  th > div > div > div > div > div {
    background-color: $WHITE !important;
  }
}
.selectedTable {
  padding-top: 10px !important;
}
.cursorPointer {
  cursor: pointer;
}
.withOutPhaseValueCollapse {
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: left;
}

.searchField {
  > div > div {
    padding-right: 11px !important;
    background-color: $WHITE !important;
  }
  > div > div > input {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    letter-spacing: 0em !important;
    text-align: left !important;
    padding: 0px !important;
    padding-top: 11px !important;
    padding-bottom: 11px !important;
    padding-left: 8px !important;
    background-color: $WHITE;
    &::placeholder {
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
      color: $DARK_200 !important;
    }
  }
}
.endAdornment {
  background-color: $WHITE;
  margin-right: 0px !important;
}
.endAdornmentIcon {
  height: 16px;
  width: 16px;
}
.textField {
  width: 40px !important;
  > div {
  }
  > div {
    width: 40px;
    > div > input {
      text-align: center;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 12px !important;
    }
  }
}
.cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.comboBox {
  height: 100%;
  .focusCompoBox {
    border-radius: 0px;
  }
  > div > div {
    background: $WHITE !important;
    border-radius: 0px;
    padding: 6px 0 6px 8px !important;
    min-height: 100% !important;
    > div {
      padding: 0px !important;
      padding-right: 5px !important;
      > div {
        color: $DARK !important;
        font-size: 12px !important;
        font-weight: 400 !important;
        padding: 0px !important;
        line-height: 18px !important;
      }
    }
    &:focus {
      border-radius: 0px;
      background: $FOCUS_TEXTFIELD_BG !important;
      border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    }
  }
}

.count {
  display: flex;
  align-items: flex-end;
}

.checkBox {
  // width: 90%;
  border-radius: 10px !important;

  > span {
    background: $FOCUS_TEXTFIELD_BG !important;
    border: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    justify-content: flex-start;
    padding: 10px 40px;
  }
}

.checkBoxBorder {
  // width: 90%;
  border-radius: 10px !important;
  > span {
    border: none !important;
    justify-content: flex-start;
  }
}

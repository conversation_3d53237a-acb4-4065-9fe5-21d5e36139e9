import React, { useState } from 'react'
import { useFormik } from 'formik'
import styles from './MilestoneNumber.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateMilestoneNumber,
  useDeleteMilestoneNumber,
  useGetMilestoneNumber,
  useUpdateMilestoneNumber,
} from '@/src/hooks/useMilestoneNumber'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddMilestoneNumber: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { milestoneNumber } = drawerStates
  //REACT-QUERY
  const { milestoneNumbers, isLoading: isFetching } = useGetMilestoneNumber(milestoneNumber)
  // MUTATIONS
  const { mutate: addMasterMilestoneNumber } = useCreateMilestoneNumber()
  const { mutate: deleteMilestoneNumber } = useDeleteMilestoneNumber()
  const { mutate: updateMilestoneNumber } = useUpdateMilestoneNumber()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { milestoneNumber: '' },
    onSubmit: async (values) => {
      const payload = { milestone_number: values.milestoneNumber }
      if (editIndex !== null) {
        updateMilestoneNumber(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterMilestoneNumber(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteMilestoneNumber(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editMilestoneNumber: any = milestoneNumbers?.find((milestoneNumber) => milestoneNumber?.id === id)
    formik.setValues({ milestoneNumber: editMilestoneNumber?.milestone_number })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'milestone_number',
      header: 'Milestone Number',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 40,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Milestone Number'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="milestoneNumber"
              labelText={'Milestone Number'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.milestoneNumber}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.milestoneNumber?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.milestoneNumber?.trim()}
              >
                {'+ Add Milestone number'}
              </Button>
            )}
          </div>
        </form>
        {milestoneNumbers?.length >= 1 && (
          <TanStackTable
            rows={sortData(milestoneNumbers, 'milestone_number') as any}
            columns={columns}
            isOverflow={true}
          />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddMilestoneNumber

import { useDispatch, useSelector } from 'react-redux'
import { addContractorAndVos, deleteContractorAndVos, getContractorAndVos, updateContractorAndVos } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useContractorAndVos = () => {
  const dispatch: AppDispatch = useDispatch()

  const getContractorAndVosStatus = useSelector((state: RootState) => state.contractorAndVos.getContractorAndVosStatus)
  const contractorAndVoses = useSelector((state: RootState) => state.contractorAndVos.contractorAndVoses)

  const getContractorAndVosApi = (data: { period: string; project_name?: string }) =>
    dispatch(getContractorAndVos(data))
  const addContractorAndVosApi = (data: any) => dispatch(addContractorAndVos(data))
  const updateContractorAndVosApi = (data: { id: number; data: any }) => dispatch(updateContractorAndVos(data))
  const deleteContractorAndVosApi = (data: number) => dispatch(deleteContractorAndVos(data))

  return {
    getContractorAndVosStatus,
    contractorAndVoses,
    getContractorAndVosApi,
    addContractorAndVosApi,
    updateContractorAndVosApi,
    deleteContractorAndVosApi,
  }
}
export default useContractorAndVos

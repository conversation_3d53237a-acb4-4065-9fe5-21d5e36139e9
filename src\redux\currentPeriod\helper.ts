import { CurrentPeriodState } from '.'

//* Accepts a partial update, merges with current state from store
export const byPassSessionForCurrentPeriodData = async (data: Partial<CurrentPeriodState>) => {
  try {
    const persistRaw = await sessionStorage.getItem('persist:root')
    if (persistRaw) {
      const persistData = JSON.parse(persistRaw)
      let prev = { currentPeriod: '' }
      if (persistData.currentPeriodData) {
        prev = JSON.parse(persistData.currentPeriodData)
      }
      const currentPeriodData = {
        ...prev,
        ...data,
      }
      persistData.currentPeriodData = JSON.stringify(currentPeriodData)
      await sessionStorage.setItem('persist:root', JSON.stringify(persistData))
      console.log('Updated currentPeriodData in persisted state.')
    } else {
      console.log('No persisted root found to update.')
    }
  } catch (error) {
    console.error('Error setting currentPeriodData:', error)
  }
}

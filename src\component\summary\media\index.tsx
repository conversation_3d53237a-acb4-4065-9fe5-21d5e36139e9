import React, { useState, ChangeEvent, Drag<PERSON>vent, useEffect, useMemo, useCallback } from 'react'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import MediaDisplay from './displayView'
import styles from './media.module.scss'
import MediaUploadBox from './mediaUploadBox'
import Loader from '../../shared/loader'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILookupProjectToPhase } from '@/src/redux/status/interface'
import useFile from '@/src/redux/uploadPicture/useFile'
import { getMasterOneProject } from '@/src/services/projects'
import { IProjects } from '@/src/services/projects/interface'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const MAX_FILE_SIZE = 5 * 1024 * 1024

type FileType = {
  field: string
  files: File[]
}

export interface IMediaCategory {
  key: string
  permission: string
  header: string
}

const mediaCategory: IMediaCategory[] = [
  { key: 'Plot Plan', permission: 'Media-Plot Plan', header: 'Plot Plan' },
  { key: 'Static Images', permission: 'Media-Static Images', header: 'Perspective Images' },
  // NOTE : Progress Videos is contain both Progress Videos and Dynamic Images
  { key: 'Progress Videos', permission: 'Media-Dynamic Images', header: 'Progress Videos' },
  { key: 'Decree', permission: 'Media-Decree', header: 'Decree' },
  { key: 'Additional Documents', permission: 'Media-Additional Documents', header: 'Additional Documents' },
]

const Media: React.FC = () => {
  const router = useRouter()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { UploadFileApi, getImageApi } = useFile()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<Record<string, File[]>>({})
  const [previews, setPreviews] = useState<Record<string, { fileName: string; preview: string; type: string }[]>>({})
  const [errors, setErrors] = useState<Record<string, string | null>>({})
  const [images, setImages] = useState<any>({})
  const [plotImages, setPlotImages] = useState<any>({})
  const [decreeFile, setDecreeFile] = useState<any>({})
  const [imageIds, setImageIds] = useState<Record<string, string[]>>({})

  const getProjectPayload = useMemo(
    () => ({
      projectName: encodeURIComponent(router.query.slug?.toString() as string),
      period: currentPeriod,
    }),
    [router.query.slug, currentPeriod],
  )

  const getImages = async () => {
    const res: any = await getMasterOneProject(getProjectPayload)
    fetchImages(res?.data)
  }

  // Helper to group by phase
  const groupByPhase = (images: any[]) => {
    const grouped = images.reduce(
      (acc, image) => {
        const phase =
          image?.lookup_project_to_phase_id && image?.LookupProjectToPhase?.phase
            ? image.LookupProjectToPhase.phase
            : ''

        if (!acc[phase]) {
          acc[phase] = []
        }
        acc[phase].push(image)

        //TODO: Comment old logic
        // phases?.forEach((phase: any) => {
        // })
        return acc
      },
      {} as Record<string, any[]>,
    )

    // Sort the grouped object by keys
    const sortedGrouped = Object.keys(grouped)
      .sort()
      .reduce(
        (sortedAcc, key) => {
          sortedAcc[key] = grouped[key]
          return sortedAcc
        },
        {} as Record<string, any[]>,
      )

    return sortedGrouped
  }

  const fetchImages = useCallback(
    async (project: IProjects) => {
      if (!project) return

      setIsLoading(true)
      const staticImg = (project?.static_images_files && project?.static_images_files?.map((res) => res)) || []
      const progressVideos = (project?.progress_videos && project?.progress_videos?.map((res) => res)) || []
      const dynamicImg = (project?.dynamic_images_files && project?.dynamic_images_files?.map((res) => res)) || []
      const decreeImg = (project?.decree_files && project?.decree_files?.map((res) => res)) || []
      const plotImg = (project?.plot_plan_files && project?.plot_plan_files?.map((res) => res)) || []
      const additionalDocs =
        (project?.additional_documents_files && project?.additional_documents_files?.map((res) => res)) || []

      const array = [
        ...(staticImg || []),
        ...(progressVideos || []),
        ...(dynamicImg || []),
        ...(plotImg || []),
        ...(decreeImg || []),
        ...(additionalDocs || []),
      ]

      try {
        const fetchedImages = await Promise.all(
          array.map(
            async (item: {
              file_path: string
              lookup_project_to_phase_id: number | null
              LookupProjectToPhase: ILookupProjectToPhase
              id: number
              field_name?: string | null
            }) => {
              const params = {
                name: item?.file_path,
                projectName: router.query.slug,
                period: currentPeriod,
              }
              const fetchImage: Record<string, any> = await getImageApi(params)

              return fetchImage.payload.success
                ? {
                    ...fetchImage.payload.data,
                    file_path: item?.file_path,
                    lookup_project_to_phase_id: item?.LookupProjectToPhase?.id,
                    LookupProjectToPhase: item?.LookupProjectToPhase,
                    lookup_project_to_media_id: item?.id,
                  }
                : []
            },
          ),
        )
        const flatImages = fetchedImages.flat()
        // Group images by type, then by phase
        const groupedImages = {
          'Perspective Images': groupByPhase(flatImages.filter((item) => item?.type === 'Static Images')),
          //NOTE : Combine both types into 'Progress Videos'
          'Progress Videos': groupByPhase(
            flatImages.filter((item) => item?.type === 'Dynamic Images' || item?.type === 'Progress Videos'),
          ),
          Decree: groupByPhase(flatImages.filter((item) => item?.type === 'Decree')),
          'Plot Plan': groupByPhase(flatImages.filter((item) => item?.type === 'Plot Plan')),
          'Additional Documents': groupByPhase(flatImages.filter((item) => item?.type === 'Additional Documents')),
        }

        setPlotImages(project?.plot_plan_files)
        setDecreeFile(project?.decree_files)

        // Set the images state with the entire grouped object
        setImages(groupedImages)
      } catch (error) {
        console.error('Error fetching images:', error)
      } finally {
        setIsLoading(false)
      }
    },
    [router.query.slug, currentPeriod, getImageApi],
  )

  useEffect(() => {
    getImages()
  }, [router?.query?.slug, currentPeriod])

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>, fieldType: string) => {
    const files = e.target.files ? Array.from(e.target.files) : []
    handleFiles({ field: fieldType, files })
    e.target.value = ''
  }

  const MAX_VIDEO_SIZE = 100 * 1024 * 1024 // 100MB in bytes

  const handleFiles = ({ field, files }: FileType) => {
    const validFiles = files.filter((file) => {
      // Check file type validity
      let isValidType
      if (field === 'Progress Videos') {
        // Allow both images and MP4 for Progress Media
        isValidType = (file.type.startsWith('image/') && file.type !== 'image/svg+xml') || file.type === 'video/mp4'
        // Check size based on file type
        const isValidSize = file.type === 'video/mp4' ? file.size <= MAX_VIDEO_SIZE : file.size <= MAX_FILE_SIZE
        return isValidType && isValidSize
      } else if (field === 'Additional Documents' || field === 'Decree') {
        isValidType =
          (file.type === 'application/pdf' || file.type.startsWith('image/')) && file.type !== 'image/svg+xml'
      } else {
        isValidType = file.type.startsWith('image/') && file.type !== 'image/svg+xml'
      }

      return file.size <= MAX_FILE_SIZE && isValidType
    })

    if (validFiles.length !== files.length) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [field]:
          field === 'Progress Videos'
            ? `Some files were not selected. For images, maximum size allowed is ${MAX_FILE_SIZE / (1024 * 1024)}MB. For videos, maximum size allowed is 100MB. SVG files are not supported.`
            : `Some files were not selected. The maximum file size allowed is ${MAX_FILE_SIZE / (1024 * 1024)}MB, and SVG files are not supported.`,
      }))
    } else {
      setErrors((prevErrors) => ({ ...prevErrors, [field]: null }))
    }

    setSelectedFiles((prev) => ({ ...prev, [field]: validFiles }))

    Promise.all(
      validFiles.map((file) => {
        return new Promise<{ id: string; preview: string; fileName: string; type: string }>((resolve) => {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onloadend = () => {
            const id = `${field}_${Date.now()}_${file.name}`
            resolve({ id, preview: reader.result as string, fileName: file.name, type: file.type })
          }
        })
      }),
    ).then((newPreviews) => {
      setPreviews((prev) => ({
        ...prev,
        [field]: newPreviews.map((item) => ({
          fileName: item.fileName,
          preview: item.preview,
          type: item.type,
        })),
      }))
      setImageIds((prev) => ({ ...prev, [field]: newPreviews.map((item) => item.id) }))
    })
  }

  const onDropHandler = (ev: DragEvent<HTMLDivElement>, fieldType: string) => {
    ev.preventDefault()
    const files = ev.dataTransfer.files ? Array.from(ev.dataTransfer.files) : []
    handleFiles({ field: fieldType, files })
  }

  const onDragOver = (ev: DragEvent<HTMLDivElement>) => {
    ev.preventDefault()
  }

  const handleFileUpload = async (fieldType: string) => {
    const files = selectedFiles[fieldType] || []

    // Handle Plot Plan validation
    if (fieldType === 'Plot Plan') {
      const existingFilesCount = plotImages?.length || 0
      const totalFilesCount = existingFilesCount + files.length

      if (totalFilesCount > 3) {
        toast.error(`You can only upload up to 3 files. You already have ${existingFilesCount} files uploaded.`, {
          duration: 4000,
          closeButton: false,
        })
        return
      }
    }

    // Handle Decree validation
    if (fieldType === 'Decree') {
      const existingFilesCount = decreeFile?.length || 0
      const totalFilesCount = existingFilesCount + files.length

      if (totalFilesCount > 1) {
        toast.error(
          `You can only upload up to 1 file for Decree. You already have ${existingFilesCount} file uploaded.`,
          { duration: 4000, closeButton: false },
        )
        setPreviews((prev) => ({ ...prev, Decree: [] }))
        return
      }
    }

    if (files.length === 0) return

    setIsLoading(true)
    try {
      const imageFormData = new FormData()
      const videoFormData = new FormData()

      files.forEach((file) => {
        if (file.type === 'video/mp4') {
          videoFormData.append('files', file, `video_${file.name}`)
        } else if (file.type.startsWith('image/') || file.type === 'application/pdf') {
          imageFormData.append('files', file)
        }
      })

      // Append shared fields to both FormData objects
      const projectName = router.query.slug?.toString() ?? ''
      const period = currentPeriod

      const formDatas = [imageFormData, videoFormData]

      formDatas.forEach((fd) => {
        fd.set('project_name', projectName)
        fd.set('period', period)

        // Dynamically set field_type based on file type
        if (fd === imageFormData && fieldType === 'Progress Videos') {
          fd.set('field_type', 'Dynamic Images')
        } else {
          fd.set('field_type', fieldType)
        }
      })

      // Upload images if available
      if (imageFormData.has('files')) {
        const imageRes: any = await UploadFileApi(imageFormData)
        if (!imageRes.payload.success) {
          errorToast(imageRes.payload.response?.data?.message)
          setIsLoading(false)
          return
        } else {
          successToast(imageRes?.payload?.message)
        }
      }

      // Upload videos if available
      if (videoFormData.has('files')) {
        const videoRes: any = await UploadFileApi(videoFormData)
        if (!videoRes.payload.success) {
          errorToast(videoRes.payload.response?.data?.message)
          setIsLoading(false)
          return
        } else {
          successToast(videoRes?.payload?.message)
        }
      }

      getImages()
      setSelectedFiles((prev) => ({ ...prev, [fieldType]: [] }))
      setPreviews((prev) => ({ ...prev, [fieldType]: [] }))
      setImageIds((prev) => ({ ...prev, [fieldType]: [] }))

      // Clean up any video object URLs
      if (fieldType === 'Progress Media') {
        const videoUrls = previews[fieldType]?.filter((p) => p.type === 'video/mp4')?.map((p) => p.preview) || []
        videoUrls.forEach((url) => URL.revokeObjectURL(url))
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast.error('Failed to upload file', {
        duration: 4000,
        closeButton: false,
      })
    } finally {
      // setIsLoading(false)
    }
  }
  const handleDeleteImage = (fieldType: string, index: number) => {
    setPreviews((prev) => ({
      ...prev,
      [fieldType]: prev[fieldType].filter((_, i) => i !== index),
    }))

    setSelectedFiles((prev) => ({
      ...prev,
      [fieldType]: prev[fieldType].filter((_, i) => i !== index),
    }))

    setImageIds((prev) => ({
      ...prev,
      [fieldType]: prev[fieldType].filter((_, i) => i !== index),
    }))
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  return (
    <div className={styles.mediaContainer}>
      <div className={styles.categoriesContainer}>
        {mediaCategory.map(
          ({ key, permission, header }) =>
            currentUser?.role?.view_permissions?.includes(permission) && (
              <MediaUploadBox
                key={key}
                header={header}
                categoryKey={key}
                isEditForUser={isEditForUser}
                errors={errors}
                previews={previews}
                handleFileChange={handleFileChange}
                onDropHandler={onDropHandler}
                onDragOver={onDragOver}
                handleFileUpload={handleFileUpload}
                handleDeleteImage={handleDeleteImage}
              />
            ),
        )}
      </div>
      <div>
        {isLoading && <p className={styles.title}></p>}
        {isLoading ? (
          <Loader />
        ) : (
          <div>
            {mediaCategory?.map((category: any) => (
              <React.Fragment key={category.key}>
                {Object.values(images[category.header] || {}).flat().length > 0 ? (
                  <MediaDisplay
                    mediaCategory={category}
                    images={images}
                    fetchImages={fetchImages}
                    setIsLoading={setIsLoading}
                    isEditForUser={isEditForUser}
                  />
                ) : null}
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default Media

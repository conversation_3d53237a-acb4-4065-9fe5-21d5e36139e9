import { useDispatch, useSelector } from 'react-redux'
import { getMasterDevelopers, addMasterDeveloper, updateMasterDeveloper, deleteMasterDeveloper } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useDevelopers = () => {
  const dispatch: AppDispatch = useDispatch()

  const getMasterDevelopersStatus = useSelector((state: RootState) => state.developer.getMasterDevelopersStatus)
  const developers = useSelector((state: RootState) => state.developer.developers)

  const getMasterDevelopersApi = () => dispatch(getMasterDevelopers())
  const addMasterDeveloperApi = (data: { developers: string }) => dispatch(addMasterDeveloper(data))
  const updateMasterDeveloperApi = (data: { id: number; developers: string }) => dispatch(updateMasterDeveloper(data))
  const deleteMasterDeveloperApi = (data: number) => dispatch(deleteMasterDeveloper(data))

  return {
    getMasterDevelopersStatus,
    developers,
    getMasterDevelopersApi,
    addMasterD<PERSON>loper<PERSON><PERSON>,
    updateMasterDeveloperApi,
    deleteMasterDeveloperApi,
  }
}
export default useDevelopers

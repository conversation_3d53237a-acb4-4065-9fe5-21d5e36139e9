import React, { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useFormik } from 'formik'
import styles from './ProjectToBeComplatedIn.module.scss'
import ComboBox from '../../shared/combobox'
import TanStackTable from '../../shared/tanStackTable'
import { sortData } from '../../shared/tanStackTable/helper'
import { CustomColumnDef } from '../../shared/tanStackTable/interface'
import { showCustomToast } from '../../toast/ToastManager'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import useProjectToBeComplatedIn from '@/src/redux/projectToBeComplatedIn/useProjectToBeComplatedIn'
import { getProjects } from '@/src/services/projects'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'

const AddProjectToBeCompletedIn: React.FC<any> = ({
  isProjectComplate,
  setIsProjectComplate,
  drawerStates,
  onClose,
}) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const {
    getProjectToBeCompletedInStatus,
    projectToBeCompletedIns,
    getProjectToBeCompletedInApi,
    addProjectToBeCompletedInApi,
    updateProjectToBeCompletedInApi,
    deleteProjectToBeCompletedInApi,
  } = useProjectToBeComplatedIn()
  const { currentPeriod } = useMasterPeriod()
  const { phases, getMasterPhaseApi } = usePhase()
  const { addProjectToBeCompletedIn } = drawerStates

  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    enabled: addProjectToBeCompletedIn,
    select: (response) => response.data, // This extracts 'data' directly
  })

  const fetchData = async () => {
    if (addProjectToBeCompletedIn) {
      if (currentPeriod) {
        await getProjectToBeCompletedInApi({ period: currentPeriod })
      }
      await getMasterPhaseApi()
    }
  }

  useEffect(() => {
    fetchData()
  }, [addProjectToBeCompletedIn, currentPeriod])

  const projectOption = useMemo(() => {
    return populateDropdownOptions(projects, 'project_name')
  }, [projects])

  const phasesOption = useMemo(() => {
    return populateDropdownOptions(phases, 'project_phase')
  }, [phases])

  const formik = useFormik({
    initialValues: {
      project_name: '',
      phase: '',
      value: '',
      main_description: '',
      details_description: '',
    },
    onSubmit: async (values) => {
      const { project_name, phase, value, main_description, details_description } = values
      if (editIndex !== null) {
        const response: Record<string, any> = await updateProjectToBeCompletedInApi({
          id: editIndex,
          data: {
            period: currentPeriod,
            project_name,
            phase,
            value,
            main_description,
            details_description,
          },
        })
        if (response.payload.success === true) {
          showCustomToast('Record successfully updated', 'success')
          getProjectToBeCompletedInApi({ period: currentPeriod })
          setEditIndex(null)
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
      } else {
        const response: Record<string, any> = await addProjectToBeCompletedInApi({
          period: currentPeriod,

          project_name,
          phase,
          value,
          main_description,
          details_description,
        })
        if (response.payload.success === true) {
          showCustomToast('Record successfully added', 'success')
          getProjectToBeCompletedInApi({ period: currentPeriod })
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    const response: Record<string, any> = await deleteProjectToBeCompletedInApi(id)
    if (response.payload.success === true) {
      setDeleteModel(null)
      getProjectToBeCompletedInApi({ period: currentPeriod })
    }
  }

  const handleEditButtonClick = (id: number) => {
    const editContractor: any = projectToBeCompletedIns.find(
      (projectToBeCompletedIn) => projectToBeCompletedIn.id === id,
    )
    if (editContractor) {
      formik.setValues({
        project_name: editContractor.project_name,
        phase: editContractor.phase,
        value: editContractor.value,
        main_description: editContractor.main_description,
        details_description: editContractor.details_description,
      })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_name',
      header: 'Project Name',
    },
    {
      accessorKey: 'phase',
      header: 'Phase',
      cell: ({ row }) => {
        return <>{row.original.phase || '-'}</>
      },
    },
    {
      accessorKey: 'value',
      header: 'Value',
    },
    {
      accessorKey: 'main_description',
      header: 'Main Description',
    },
    {
      accessorKey: 'details_description',
      header: 'Details Description',
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text={'Add Project To Be Completed In'}
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {!isProjectComplate && (
          <form className={styles.form} onSubmit={formik.handleSubmit}>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '1fr',
                gap: '24px',
              }}
            >
              <ComboBox
                options={projectOption}
                labelText={'Project Name'}
                placeholder="Type of search..."
                value={
                  formik.values.project_name
                    ? {
                        label: formik.values.project_name,
                        value: formik.values.project_name,
                      }
                    : null
                }
                clearIcon={true}
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    project_name: val?.value || '',
                  })
                }
                onBlur={() => formik.setTouched({ ...formik.touched, project_name: true })}
              />
              {/* <ComboBox
                options={phasesOption}
                labelText={"Phase"}
                placeholder="Type of search..."
                value={
                  formik.values.phase
                    ? {
                        label: formik.values.phase,
                        value: formik.values.phase,
                      }
                    : null
                }
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    phase: val?.value || "",
                  })
                }
                onBlur={() =>
                  formik.setTouched({ ...formik.touched, phase: true })
                }
              /> */}
              <TextInputField
                className={styles.entityField}
                name="phase"
                labelText={'Phase'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.phase}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                className={styles.entityField}
                name="value"
                labelText={'Value'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.value}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                className={styles.entityField}
                name="main_description"
                labelText={'Main Description'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.main_description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                className={styles.entityField}
                name="details_description"
                labelText={'Details Description'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.details_description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
            <div>
              {editIndex !== null ? (
                <Button className={styles.addProjectButton} disabled={!formik?.dirty} type="submit">
                  {'Update'}
                </Button>
              ) : (
                <Button className={styles.addProjectButton} disabled={!formik?.dirty} color="secondary" type="submit">
                  {'Add Project'}
                </Button>
              )}
            </div>
          </form>
        )}
        {projectToBeCompletedIns?.length >= 1 && (
          // <Table data={projectToBeCompletedIns} columns={columns} />
          <TanStackTable rows={sortData(projectToBeCompletedIns, 'project_name') as any} columns={columns} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddProjectToBeCompletedIn

import React, { useState, useCallback } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import * as XLSX from 'xlsx'
import styles from './Upload.module.scss'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import { showCustomToast } from '@/src/component/toast/ToastManager'
import useBatchUpload from '@/src/redux/batchUpload/useBatchUpload'

const ACCEPTED_FILE_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
]
const categories = ['project_status', 'key_achievements_plan', 'key_risks', 'project_management']
const categoriesLable = ['project status', 'key achievements plan', 'key risks', `Project Management`]

const Upload: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [excelData, setExcelData] = useState<Record<string, any[][]>>({})
  const [files, setFiles] = useState<Record<string, File[]>>({})
  const [fileName, setFileName] = useState<Record<string, string | null>>({})
  const { batchUploadApi } = useBatchUpload()
  const router = useRouter()

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>, categoryKey: string) => {
    const selectedFiles = Array.from(e.target.files || [])
    if (selectedFiles.length > 0) {
      setFiles((prev) => ({ ...prev, [categoryKey]: selectedFiles }))
      setFileName((prev) => ({ ...prev, [categoryKey]: selectedFiles[0].name }))
      processFiles(selectedFiles, categoryKey)
    }
  }, [])

  const onDropHandler = useCallback((e: React.DragEvent<HTMLDivElement>, categoryKey: string) => {
    e.preventDefault()
    const droppedFiles = Array.from(e.dataTransfer.files || [])
    if (droppedFiles.length > 0) {
      setFiles((prev) => ({ ...prev, [categoryKey]: droppedFiles }))
      setFileName((prev) => ({ ...prev, [categoryKey]: droppedFiles[0].name }))
      processFiles(droppedFiles, categoryKey)
    }
  }, [])

  const processFiles = (files: File[], categoryKey: string) => {
    const [excelFiles, otherFiles] = files.reduce(
      ([excelFiles, otherFiles], file) => {
        if (ACCEPTED_FILE_TYPES.includes(file.type)) {
          excelFiles.push(file)
        } else {
          otherFiles.push(file)
        }
        return [excelFiles, otherFiles]
      },
      [[], []] as [File[], File[]],
    )

    if (excelFiles.length > 0) {
      readExcelFile(excelFiles[0], categoryKey)
    }
  }

  const readExcelFile = (file: File, categoryKey: string) => {
    const reader = new FileReader()

    reader.onload = (event) => {
      const binaryStr = event.target?.result as string
      const workbook = XLSX.read(binaryStr, { type: 'binary' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json<any[]>(worksheet, { header: 1 })

      setExcelData((prev) => ({ ...prev, [categoryKey]: jsonData as any[][] }))
    }

    reader.readAsBinaryString(file)
  }

  const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }, [])

  const handleFileUpload = useCallback(
    async (categoryKey: string) => {
      if (!files[categoryKey] || files[categoryKey].length === 0) return
      setLoading(true)
      const formData = new FormData()
      files[categoryKey].forEach((file) => formData.append('files', file))
      const res: Record<string, any> = await batchUploadApi({ formData, categoryKey })
      if (res?.payload?.success) {
        showCustomToast(res.payload.message, res.payload.success ? 'success' : 'error', 5000)
        setFiles((prev) => ({ ...prev, [categoryKey]: [] }))
        setExcelData((prev) => ({ ...prev, [categoryKey]: [] }))
        setFileName((prev) => ({ ...prev, [categoryKey]: null }))
        setTimeout(() => {
          router.reload()
        }, 2000)
      } else {
        const message = res?.payload?.response?.data?.message
        showCustomToast(message, res.payload.success ? 'success' : 'error', 5000)
      }
      setLoading(false)
    },
    [files, batchUploadApi],
  )

  return (
    <div className={styles.batchUpload}>
      <Head>
        <title>Batch Upload</title>
        <meta name="description" content="Batch Upload" />
      </Head>
      {loading ? (
        <Loader />
      ) : (
        <div className={styles.uploadContainer}>
          {categories.map((category, index) => (
            <div className={styles.categoryBox} key={category}>
              <p>Upload Files for {categoriesLable[index]}</p>
              <div className={styles.dropZone} onDrop={(e) => onDropHandler(e, category)} onDragOver={onDragOver}>
                <label className={styles.customFileInput}>
                  <input
                    type="file"
                    accept=".xlsx, .xls
                    "
                    multiple
                    onChange={(e) => handleFileChange(e, category)}
                    className={styles.fileInput}
                  />
                  <span>Select Files</span>
                </label>
                Drag and drop files here
              </div>

              {excelData[category]?.length > 0 && (
                <div className={styles.filePreview}>
                  <p>
                    {fileName[category]
                      ? `Excel file "${fileName[category]}" is loaded. Click "Upload Files" to submit the files.`
                      : 'Excel file is loaded. Click "Upload Files" to submit the files.'}
                  </p>
                </div>
              )}
              <Button onClick={() => handleFileUpload(category)} className={styles.uploadButton}>
                Upload Files
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Upload

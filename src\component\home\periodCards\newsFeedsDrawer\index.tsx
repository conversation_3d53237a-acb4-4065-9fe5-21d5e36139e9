import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import { IAddEntityProps } from './interface'
import styles from './newsFeedsDrawer.module.scss'
import Button from '../../../shared/button'
import Typography<PERSON>ield from '../../../shared/typography'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Loader from '@/src/component/shared/loader'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextEditor from '@/src/component/shared/textEditor'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useGetNewsFeed, useCreateNewsFeed, useDeleteNewsFeed, useUpdateNewsFeed } from '@/src/hooks/useNewsFeed'

import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const NewsFeedsDrawer: React.FC<IAddEntityProps> = ({ onClose }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()

  const { mutate: addNewsFeed } = useCreateNewsFeed()
  const { mutate: updateNewsFeed } = useUpdateNewsFeed()
  const { mutate: deleteNewsFeed } = useDeleteNewsFeed()
  const { newsFeed, isLoading: isNewsFeedLoading } = useGetNewsFeed(currentPeriod)

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  useEffect(() => {
    setIsLoading(isNewsFeedLoading)
  }, [isNewsFeedLoading])

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
      setEditIndex(null)
      setDeleteModel(null)
      setTimeout(() => {
        setIsLoading(false)
      }, 1000)
    },
    onError: (err: any) => {
      setIsLoading(false)
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { feed: '' },
    onSubmit: async (values) => {
      if (!isEditForUser) {
        return toast(`The current reporting period is locked`, {
          icon: <WarningAmberOutlined />,
        })
      }
      setIsLoading(true)
      const payload = editIndex
        ? { id: editIndex, feed: values.feed, period: currentPeriod }
        : { feed: values.feed, period: currentPeriod }
      if (editIndex) {
        updateNewsFeed(payload, handleMutationCallbacks('Record successfully updated', 'Failed to update record'))
      } else {
        addNewsFeed(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleEditButtonClick = (id: number) => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    const editNewsFeed: any | undefined = newsFeed.find((feed: any) => feed.id === id)
    if (editNewsFeed) {
      formik.setValues({ feed: editNewsFeed.feed })
      setEditIndex(id)
    }
  }

  const handleDeleteButtonClick = (id: number) => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    setDeleteModel(id)
  }

  const handleDeleteNewsFeed = async (id: number) => {
    setIsLoading(true)
    deleteNewsFeed(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'feed',
      header: 'Feed',
      flex: 1,
      cell: ({ row }: any) => (
        <div className={styles.feedContainer} dangerouslySetInnerHTML={{ __html: row.original.feed }} />
      ),
    },
    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }: any) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
          <DeleteIcon
            className={styles.deleteRowIcon}
            onClick={() => {
              handleDeleteButtonClick(row.id)
            }}
          />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'News Feed'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextEditor
              className={styles.newsFeedField}
              name="feed"
              value={formik.values.feed}
              onChange={(content: any) => formik.setFieldValue('feed', content)}
            />
          </div>
          <div>
            <Button
              className={styles.addNewsFeedButton}
              color={'primary'}
              type="submit"
              disabled={
                formik.isSubmitting || isLoading || formik.values.feed === '<p><br></p>' || !formik.values.feed.trim()
              }
            >
              {editIndex ? '+ Update News Feed' : '+ Save News Feed'}
            </Button>
          </div>
        </form>

        {isLoading || isNewsFeedLoading ? (
          <div className={styles.loaderContainer}>
            <Loader />
          </div>
        ) : (
          <TanStackTable rows={sortData(newsFeed, 'index') as any as any} columns={columns} isOverflow={true} />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteNewsFeed(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default NewsFeedsDrawer

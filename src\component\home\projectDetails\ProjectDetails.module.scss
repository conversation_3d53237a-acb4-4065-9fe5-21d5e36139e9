@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.cardContainer {
  padding: 12px 15px;
  background-color: rgba(201, 213, 0, 0.1);
  border-radius: 10px;
  margin-top: 12px;
  position: relative;

  .cardBackground {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: -1;
  }
}

.details {
  display: flex;
  flex-direction: column;
  align-items: center; // Mobile default
  gap: 22px;

  @include respond-to('tablet') {
    align-items: flex-start;
  }

  @include respond-to('laptop') {
    width: -webkit-fill-available;
  }

  @include respond-to('desktop') {
    width: unset;
  }

  .cardsWrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
    // @include respond-to('mobile') {
    //   display: grid;
    //   grid-template-columns: 1fr 1fr;
    // }

    @include respond-to('tablet') {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
}

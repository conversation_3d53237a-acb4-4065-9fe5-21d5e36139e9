import { NextRouter } from 'next/router' // Assuming Next.js router
import { Routes } from '@/src/constant/enum'
import { IStatus } from '@/src/redux/status/interface'

export const redirectToStatus = (
  phaseId: number[],
  stageStatus: string,
  subStage: string | null,
  statuses: IStatus[],
  router: NextRouter,
): void => {
  let redirectStatus: IStatus | undefined
  // If subStage is provided, filter by subStage as well
  if (subStage) {
    redirectStatus = statuses.find((item) => {
      return (
        !!item?.LookupProjectToPhase.find((item) => phaseId.find((id) => id === item.id)) &&
        item?.MasterProjectStageStatus?.project_stage_status === stageStatus &&
        // TODO : test
        item?.MasterProjectSubStage?.project_sub_stage === subStage
      )
    })
  } else {
    redirectStatus = statuses.find((item) => {
      return (
        !!item?.LookupProjectToPhase.find((fi) => phaseId?.find((id) => id === fi.id)) &&
        item?.MasterProjectStageStatus?.project_stage_status === stageStatus
      )
    })
  }
  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}

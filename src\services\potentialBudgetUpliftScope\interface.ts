export interface IPotentialBudgetUpliftScopePayload {
  period: string
  project_name: string
  current_scope: string
  amended_scope: string
  cost_impact: string
}

export interface IUpdatePotentialBudgetUpliftScope extends IPotentialBudgetUpliftScopePayload {
  id: number
  period: string
  project_name: string
}

export interface IPotentialBudgetUpliftScope {
  id: number
  period: string
  project_name: string
  current_scope: string
  amended_scope: string
  cost_impact: string
}

export interface IGetPotentialBudgetUpliftScope {
  data: IPotentialBudgetUpliftScope[]
  message: string
  success: true
}

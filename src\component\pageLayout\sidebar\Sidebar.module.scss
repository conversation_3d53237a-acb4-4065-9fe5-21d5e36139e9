@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.aldarLogo {
  padding: 13px 7px 20px;
  width: 36px;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 30px;
  img {
    background-color: $WHITE;
  }
}
.divider {
  background-color: $DIVIDER;
}
.header {
  display: flex;
  height: 50px;
  width: 100%;
  border-bottom: 1px solid $LIGHT_200;

  .headerContains {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

    .projectButton {
      margin: 7px 30px 7px 0;
    }
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    line-height: 23px;
    // letter-spacing: -0.04em;
    text-align: left;
    padding: 13px 0 14px 24px;
  }
}

.sidebar {
  z-index: 4;
  position: relative;
  height: calc(100% - 40px);
  width: 50px;
  border-right: 1px solid $LIGHT_200;
  padding: 04px;
  background-color: #d9d9d9;
  margin: 20px;
  border-radius: 08px;
  .logos {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: calc(100vh - 190px);
    .upIcon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 25px;
      align-items: center;
      margin-top: 20px;
      // padding: 10px 13px 0px;
    }
    .downIcon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 20px;
      align-items: center;
      padding: 10px 13px 0px;
    }
  }
}
.content {
  width: 100%;
  height: 100%;
}

.pointer {
  cursor: pointer;
  height: 30px !important;
  width: 30px !important;
}

.myComponent {
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.myComponentShow {
  z-index: 3;
  height: 100%;
  width: 100%;
  opacity: 1;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  .isOpen {
    opacity: 1 !important;
    width: 'auto';

    @include respond-to('tablet') {
      width: 800px;
    }
  }
}
.drawerContent {
  height: 100%;
  width: 100%;
}
.sidebarIndex {
  z-index: 4 !important;
}
.cursorPointer {
  cursor: pointer;
}
.selectContain {
  border-radius: 50%;
  display: flex;
  padding: 2px;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 36px;
  background-color: $DARK;
}
.contain {
  border-radius: 7px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  width: 40px;
}
.loader {
  height: 100vh;
  width: 100vw;
  cursor: progress;
}

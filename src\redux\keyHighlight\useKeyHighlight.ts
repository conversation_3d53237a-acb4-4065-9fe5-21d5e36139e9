import { useDispatch, useSelector } from 'react-redux'
import {
  getK<PERSON><PERSON>ighlights,
  add<PERSON><PERSON><PERSON>ighlight,
  update<PERSON><PERSON><PERSON>ighlight,
  deleteKeyHighlight,
  updateHighlight,
  updateIsUpdateHighlight,
} from '.'
import { IKeyHighlights } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useKeyHighlights = () => {
  const dispatch: AppDispatch = useDispatch()

  const getKeyHighlightStatus = useSelector((state: RootState) => state.keyHighlight.getKeyHighlightStatus)
  const keyHighlights = useSelector((state: RootState) => state.keyHighlight.keyHighlights)
  const highlights = useSelector((state: RootState) => state.keyHighlight.highlight)
  const isUpdateHighlight = useSelector((state: RootState) => state.keyHighlight.isUpdateHighlight)

  const getKeyHighlightsApi = (data: { period: string }) => dispatch(getKeyHighlights(data))
  const addKeyHighlightApi = (data: IKeyHighlights) => dispatch(addKeyHighlight(data))
  const updateKeyHighlightApi = (data: { id: number; data: IKeyHighlights }) => dispatch(updateKeyHighlight(data))
  const deleteKeyHighlightApi = (data: number) => dispatch(deleteKeyHighlight(data))
  const setHighlight = (data: any) => dispatch(updateHighlight(data))
  const setIsUpdateHighlight = (data: boolean) => dispatch(updateIsUpdateHighlight(data))

  return {
    getKeyHighlightStatus,
    keyHighlights,
    highlights,
    getKeyHighlightsApi,
    addKeyHighlightApi,
    updateKeyHighlightApi,
    deleteKeyHighlightApi,
    setHighlight,
    isUpdateHighlight,
    setIsUpdateHighlight,
  }
}
export default useKeyHighlights

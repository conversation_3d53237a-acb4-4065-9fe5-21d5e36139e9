import React, { useEffect, useMemo, useState } from 'react'
import { RestartAlt } from '@mui/icons-material'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import * as Yup from 'yup'
import styles from './StageForm.module.scss'
import { handleStageStatusSubmit } from '../helper'
import { IStageForm, IStageFormField } from '../interface'
import {
  cleanNumber,
  findMatchingRecordByKey,
  getPhaseWeightageTotalAndRequired,
  getDesignStageWeightageTotalAndRequired,
  filterMatchingRecordByKey,
  numberWithPrecision,
} from '../validationError/weightageValidation'
import ConfirmationModal from '@/src/component/confirmationModal'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import MultiAutoSelect from '@/src/component/shared/multiAutoSelect'
import MultiSearchAutoSelect from '@/src/component/shared/multiAutoSelect/multiAutoSelect'
import TextInput<PERSON>ield from '@/src/component/shared/textInputField'
import { MULTI_SELECT_SEPARATOR, PREDECESSOR_SUCCESSOR_SEPARATOR } from '@/src/constant/stageStatus'
import { useGetProjectToPhase } from '@/src/hooks/useMasterProjectToPhase'
import { useGetProjectToProjectPhaseCategories } from '@/src/hooks/useMasterProjectToProjectPhaseCategory'
import { useGetProjectPhaseCategories } from '@/src/hooks/useProjectPhaseCategory'
import { useGetProjectStageStatuses } from '@/src/hooks/useProjectStageStatus'
import { useGetProjectSubStage } from '@/src/hooks/useProjectSubStage'
import useCategory from '@/src/redux/category/useCategory'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import {
  ExtraStatusFields,
  ILookupProjectStatusPredecessor,
  ILookupProjectStatusSuccessor,
  ILookupProjectToPhase,
  ILookupProjectToProjectPhaseCategory,
  IStatus,
} from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import {
  convertMultiSelectOption,
  getUniqueValues,
  getUniqueValuesFromArray,
  getValue,
  getValueForMultiSelectOption,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
  setMultiSelectedValueToWildCard,
} from '@/src/utils/arrayUtils'
import { isDateBefore, isValidYMDDate } from '@/src/utils/dateUtils'
import {
  predecessorSuccessorOptionMapping,
  getStageOrderIndex,
  predefinedStageOrder,
  sortStatusesPredecessorSuccessor,
} from '@/src/utils/predecessorSuccessor'
import { errorToast } from '@/src/utils/toastUtils'

const StageForm: React.FC<IStageForm> = ({
  editData,
  setEditData,
  statusData,
  setLoading,
  edit,
  setEdit,
  isStageVisited,
  setIsStageVisited,
  gridFilters,
  setGridFilters,
}) => {
  const router = useRouter()
  const projectName = router.query.slug as string
  const [masterPhase, setMasterPhase] = useState<(IStatus & ExtraStatusFields)[]>([])
  const { getMasterPhaseCategoryApi, uniquePhaseCategories, getMasterPhaseApi, localPhase } = usePhase()
  const { localCategory } = useCategory()
  const { currentPeriod } = useMasterPeriod()
  const { getStatusApi, addStatusApi, updateTableStatusApi, updateStatusApi, statuses } = useStatus()
  const { projectToProjectPhaseCategories, refetch: refetchProjectToProjectPhaseCategories } =
    useGetProjectToProjectPhaseCategories(currentPeriod, projectName)
  const { projectToPhase, refetch: refetchProjectToPhase } = useGetProjectToPhase(currentPeriod, projectName)
  const { projectStageStatuses } = useGetProjectStageStatuses()
  const { projectSubStages } = useGetProjectSubStage()
  const { projectPhaseCategories } = useGetProjectPhaseCategories()
  const [isConfirmationModel, setIsConfirmationModel] = useState<boolean>(false)
  const [confirmationMessage, setConfirmationMessage] = useState<string>('')
  const [submittedValue, setSubmittedValue] = useState()
  const [filterOptions, setFilterOptions] = useState<any>({
    project_to_project_phase_ids: '',
    stage: '',
    subStage: '',
    predecessor: '',
    successor: '',
  })
  const [isPhaseWeightageDisabled, setIsPhaseWeightageDisabled] = useState(false)

  const hasFilter = useMemo(
    () => gridFilters?.some((item: { colId: string; values: any }) => item.values.length > 0),
    [gridFilters],
  )
  const weightageDecimal = 10
  useEffect(() => {
    // getMasterProjectStageApi()
    // currentPeriod && getStatusApi({ period: currentPeriod });
  }, [])

  useEffect(() => {
    // getMasterPhaseCategoryApi({ period: currentPeriod, project_name: projectName })
    // getMasterPhaseApi()
    // getMasterProjectStageApi()
    // getMasterProjectSubStageApi()
    // getProjectPhaseCategoriesApi()
  }, [])

  useEffect(() => {
    const phase = statuses
      .filter((item: any) => {
        return item.project_name === projectName
      })
      ?.map((item) => ({
        ...item,
        categoryIDs: item?.LookupProjectToProjectPhaseCategory
          ? setMultiSelectedValueToWildCard(item.LookupProjectToProjectPhaseCategory.map((cat) => cat.id))
          : '',
        phaseIDs: item?.LookupProjectToPhase
          ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
          : '',
        statusID: item?.MasterProjectStageStatus?.id?.toString() || '',
      }))
    setMasterPhase(phase)
  }, [statuses])

  const validationSchema = Yup.object().shape({
    designStageWeightage: Yup.number()
      .max(100, 'Maximum value is 100')
      .test('custom-design-stage-weightage', function (value) {
        const { path, createError } = this
        // Check if stages is 'Design' and designStageWeightage is empty
        if (this.parent.master_project_stage_status_id?.toString() === '3') {
          if (value === undefined || value === null || value.toString() === '') {
            return createError({
              path,
              message: 'Design Stage Weightage is required',
            })
          }
        }

        // Use the new utility for deduplication and total calculation
        const { total, required } = getDesignStageWeightageTotalAndRequired(
          masterPhase,
          {
            categoryIDs: this.parent.phase ? setMultiSelectedValueToWildCard(this.parent.phase) : '',
            phaseIDs: this.parent.projectPhaseCategory
              ? setMultiSelectedValueToWildCard(this.parent.projectPhaseCategory)
              : '',
            statusID: this.parent.master_project_stage_status_id?.toString(),
            id: editData === null ? undefined : editData,
          },
          {
            phaseKey: 'phaseIDs',
            categoryKey: 'categoryIDs',
            stageStatusKey: 'statusID',
            weightageKey: 'design_stage_weightage',
          },
        )

        if (Number(value) === 0) return true

        if (Number(total) + Number(value) > 100) {
          const errorMessage =
            Number(required) > 0
              ? `Design Stage weightage must not exceed 100 (Remaining value is: ${required})`
              : `Design Stage weightage must not exceed 100 (Remaining value is: 0)`

          return createError({
            path,
            message: errorMessage,
          })
        }

        // If validation passes, return true
        return true
      }),

    phaseWeightage: Yup.number()
      .max(100, 'Maximum value is 100')
      .required('Phase Weightage is required')
      .test('custom-phase-weightage', function (value) {
        const { path, createError } = this
        //* Use the new utility for deduplication and total calculation
        const { total, required } = getPhaseWeightageTotalAndRequired(
          masterPhase,
          {
            categoryIDs: this.parent.phase ? setMultiSelectedValueToWildCard(this.parent.phase) : '',
            phaseIDs: this.parent.projectPhaseCategory
              ? setMultiSelectedValueToWildCard(this.parent.projectPhaseCategory)
              : '',
            statusID: this.parent.master_project_stage_status_id?.toString(),
            id: editData === null ? undefined : editData,
          },
          {
            phaseKey: 'phaseIDs',
            categoryKey: 'categoryIDs',
            stageStatusKey: 'statusID',
            weightageKey: 'phase_weightage',
          },
        )

        if (Number(value) === 0) {
          return true
        }

        if (Number(total) + Number(value) > 100) {
          const errorMessage =
            Number(required) > 0
              ? `Phase weightage across stages must not exceed 100 (Remaining Value is: ${Number(required)})`
              : `Phase weightage across stages must not exceed 100 (Remaining value is: 0)`

          return createError({
            path,
            message: errorMessage,
          })
        }
        // If validation passes, return true
        return true
      }),
  })

  const handleSubmit = async (values: any) => {
    const match = filterMatchingRecordByKey(
      masterPhase,
      {
        categoryIDs: setMultiSelectedValueToWildCard(formik.values.project_to_project_phase_category_ids),
        phaseIDs: setMultiSelectedValueToWildCard(formik.values.project_to_project_phase_ids),
        statusID: formik.values.master_project_stage_status_id?.toString() || '',
      },
      MULTI_SELECT_SEPARATOR,
    )
    const valueChange = match.some((item: any) => {
      return item.phase_weightage !== numberWithPrecision(values?.phaseWeightage)
    })
    if (editData && match.length > 1 && valueChange && values?.stages === 'Design') {
      const phaseDisplay = values.phase ? values.phase?.split(MULTI_SELECT_SEPARATOR)?.join(', ') : '-'
      const categoryDisplay =
        values.projectPhaseCategory && values.projectPhaseCategory.length > 0
          ? values.projectPhaseCategory?.split(MULTI_SELECT_SEPARATOR)?.join(', ')
          : '-'

      setConfirmationMessage(
        `This action will reset all the phase weightages of the selected combination of (${categoryDisplay} / ${phaseDisplay} / ${values.stages}) with ${values.phaseWeightage}. Do you want proceed?`,
      )

      setIsConfirmationModel(true)
      setSubmittedValue(values)
      return
    } else {
      handleConfirm(values)
    }
  }

  const handleConfirm = async (values?: any) => {
    /**
        * TODO : Temp bypass this below validation do not remove below commented
        * =======================================>
      const currentRecord = statuses.find((item: any) => item.id === editData)
        // generate a unique key for each record of statuses
      const statusesKeys = statuses.map((item) => {
        const id = item.id
        const key =
          // (item.project_phase_category ? `${item.project_phase_category}` : '') +
          //  (item.phase ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.phase}` : '') +
          (item.stage_status ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.stage_status}` : '') +
          (item.sub_stage ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.sub_stage}` : '')
        return { id, key }
      })
  
      // predecessor validation
      // find all the records from statusesKeys which are present in values.predecessor
      const predecessorKeys = statusesKeys
        .filter((item) => values?.predecessor?.includes(item.key))
        .map((item) => item.id)
  
      // find all the records from the statuses which are present in predecessorKeys
      const predecessorRecords = statuses.filter((item) => predecessorKeys.includes(item.id))
  
      // filter only those predecessor records that have valid plan dates
      const validPredecessorPlans = predecessorRecords.filter((item) => isValidYMDDate(item.baseline_plan_finish ?? null))
  
      // get the latest (max) baseline plan date from predecessor records
      const maxPredecessorPlan = validPredecessorPlans.length
        ? validPredecessorPlans.reduce((acc, item) =>
            isDateBefore(acc.baseline_plan_finish!, item.baseline_plan_finish!) ? item : acc,
          ).baseline_plan_finish
        : null
  
      // filter only those predecessor records that have valid forecast dates
      const validPredecessorForecasts = predecessorRecords.filter((item) => isValidYMDDate(item.forecast_finish ?? null))
  
      // get the latest (max) forecast finish date from predecessor records
      const maxPredecessorForecast = validPredecessorForecasts.length
        ? validPredecessorForecasts.reduce((acc, item) =>
            isDateBefore(acc.forecast_finish!, item.forecast_finish!) ? item : acc,
          ).forecast_finish
        : null
  
      // block the update if current record's plan/forecast is earlier than any of its predecessors
      if (
        maxPredecessorPlan &&
        currentRecord?.baseline_plan_finish &&
        isDateBefore(currentRecord.baseline_plan_finish, maxPredecessorPlan)
      ) {
        errorToast("Plan finish of current record is earlier than predecessor's plan finish")
        return // plan finish is earlier than predecessor — reject
      }
      if (
        maxPredecessorForecast &&
        currentRecord?.forecast_finish &&
        isDateBefore(currentRecord.forecast_finish, maxPredecessorForecast)
      ) {
        errorToast("Forecast finish of current record is earlier than predecessor's forecast finish")
        return // forecast finish is earlier than predecessor — reject
      }
  
      // successor validation
      // find all the records from statusesKeys which are present in values.successor
      const successorKeys = statusesKeys.filter((item) => values?.successor?.includes(item.key)).map((item) => item.id)
  
      // find all the records from the statuses which are present in successorKeys
      const successorRecords = statuses.filter((item) => successorKeys.includes(item.id))
  
      // filter only those successor records that have valid plan dates
      const validSuccessorPlans = successorRecords.filter((item) => isValidYMDDate(item.baseline_plan_finish ?? null))
  
      // get the latest (max) baseline plan date from successor records
      const maxSuccessorPlan = validSuccessorPlans.length
        ? validSuccessorPlans.reduce((acc, item) =>
            isDateBefore(acc.baseline_plan_finish!, item.baseline_plan_finish!) ? item : acc,
          ).baseline_plan_finish
        : null
  
      // filter only those successor records that have valid forecast dates
      const validSuccessorForecasts = successorRecords.filter((item) => isValidYMDDate(item.forecast_finish ?? null))
  
      // get the latest (max) forecast finish date from successor records
      const maxSuccessorForecast = validSuccessorForecasts.length
        ? validSuccessorForecasts.reduce((acc, item) =>
            isDateBefore(acc.forecast_finish!, item.forecast_finish!) ? item : acc,
          ).forecast_finish
        : null
  
      // block the update if current record's plan/forecast is later than any of its successors
      if (
        maxSuccessorPlan &&
        currentRecord?.baseline_plan_finish &&
        isDateBefore(maxSuccessorPlan, currentRecord.baseline_plan_finish)
      ) {
        errorToast("Plan finish of current record is later than successor's plan finish")
        return // plan finish is later than successor — reject
      }
      if (
        maxSuccessorForecast &&
        currentRecord?.forecast_finish &&
        isDateBefore(maxSuccessorForecast, currentRecord.forecast_finish)
      ) {
        errorToast("Forecast finish of current record is later than successor's forecast finish")
        return // forecast finish is later than successor — reject
      }
      
      * <=======================================
      */

    // updateTableStatusApi || updateStatusApi
    const val = isConfirmationModel ? submittedValue : values
    await handleStageStatusSubmit(
      val,
      editData,
      setEditData,
      currentPeriod,
      router,
      setLoading,
      formik,
      statusData,
      updateStatusApi,
      addStatusApi,
      getStatusApi,
    )
    !isStageVisited && setIsStageVisited && setIsStageVisited(true)
  }

  const handleClose = () => {
    setIsConfirmationModel(false)
    setConfirmationMessage('')
    setEditData(null)
  }

  const initialValues = useMemo(() => {
    const editStatus: any = statuses.find((item: IStatus) => item.id === editData) || {}

    return {
      project_to_project_phase_ids:
        editStatus.LookupProjectToPhase?.map((item: ILookupProjectToPhase) => item?.id) || null,
      project_to_project_phase_category_ids: editStatus?.LookupProjectToProjectPhaseCategory?.map(
        (item: ILookupProjectToProjectPhaseCategory) => item?.id,
      ),
      master_project_stage_status_id: editStatus?.MasterProjectStageStatus?.id || null,
      master_project_sub_stage_id: editStatus?.MasterProjectSubStage?.id || null,
      designStageWeightage: editStatus.design_stage_weightage
        ? cleanNumber(Number(editStatus.design_stage_weightage) * 100).toString()
        : '',
      phaseWeightage: editStatus.phase_weightage
        ? cleanNumber(Number(editStatus.phase_weightage) * 100).toString()
        : '',
      predecessor:
        editStatus.LookupProjectStatusPredecessor?.map(
          (item: ILookupProjectStatusPredecessor) => item?.DestinationProjectStatus?.destination_project_status_id,
        ) || [],
      successor:
        editStatus.LookupProjectStatusSuccessor?.map(
          (item: ILookupProjectStatusSuccessor) => item?.DestinationProjectStatus?.destination_project_status_id,
        ) || [],
    }
  }, [statuses, editData])

  const projectStageStatusesOption: any = useMemo(
    () => prepareDropdownOptions(projectStageStatuses, 'project_stage_status'),
    [projectStageStatuses],
  )

  const predecessorSuccessorOption: any = useMemo(() => {
    const sortedStatuses = sortStatusesPredecessorSuccessor(statuses) //* Sort all statuses based on criteria

    const optionsMap = predecessorSuccessorOptionMapping(sortedStatuses) //* Generat options globally

    return Array.from(optionsMap.values()).map((item) => ({
      id: item.id,
      name: item.name.replaceAll(MULTI_SELECT_SEPARATOR, ', '),
    }))
  }, [statuses])

  const getMultiSelectedValue = (value: string) => {
    if (value?.includes(MULTI_SELECT_SEPARATOR)) {
      return value.split(MULTI_SELECT_SEPARATOR)
    }
    return typeof value === 'string' ? [value] : value
  }

  const subStageOption: any = useMemo(
    () => prepareDropdownOptions(projectSubStages, 'project_sub_stage'),
    [projectSubStages],
  )

  const handleStageChange = (value: string, fieldName: keyof IStageFormField) => {
    formik.setValues({
      ...formik.values,
      [fieldName]: value,
      master_project_sub_stage_id: null,
    })
  }

  const formik = useFormik<IStageFormField>({
    initialValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: handleSubmit,
  })

  const handlePredecessorSuccessor = (value: string[], fieldName: 'predecessor' | 'successor') => {
    formik.setValues({
      ...formik.values,
      [fieldName]: value,
    })
  }

  const handleMultiAutoSelectChange = (
    value: string[],
    fieldName: 'project_to_project_phase_category_ids' | 'project_to_project_phase_ids',
  ) => {
    const ids = value.map((item) => item).filter((id) => id !== null && id)

    if (fieldName === 'project_to_project_phase_ids') {
      let selectedCategories = projectToProjectPhaseCategories
        ?.filter((item) => formik.values.project_to_project_phase_category_ids?.includes(item.id))
        ?.map((item) => item?.MasterProjectPhaseCategory?.id)

      // 1. Get the new valid phases for the selected categories
      const validPhases = projectToPhase
        ?.filter((res) => {
          return res?.id && selectedCategories?.includes(res?.MasterProjectPhaseCategory?.id)
        })
        ?.map((res) => res?.id)

      // 2. Filter the currently selected phases to keep only those that are still valid
      let selectedPhases: number[] = value?.map(Number)
      selectedPhases = selectedPhases?.filter((id: number) => validPhases.includes(id))

      // 3. Update both category and phase in Formik
      formik.setValues({
        ...formik.values,
        project_to_project_phase_ids: selectedPhases,
      })
    } else {
      formik.setValues({
        ...formik.values,
        [fieldName]: Array.from(new Set(ids)).map(Number),
      })
    }
  }

  const sortStageOptions = useMemo(() => {
    const array = projectStageStatusesOption
    return [...array].sort((a, b) => {
      const stageOrderA = getStageOrderIndex(a.label, predefinedStageOrder)
      const stageOrderB = getStageOrderIndex(b.label, predefinedStageOrder)
      if (stageOrderA !== stageOrderB) {
        return stageOrderA - stageOrderB
      }
      return 0
    })
  }, [projectStageStatusesOption])

  const handleWeightage = (value: any, field: string) => {
    // Allow empty string
    if (value === '') {
      formik.setFieldValue(field, '')
      return
    }

    // Limit to 2 decimal places
    const regex = /^\d{0,3}(\.\d{0,2})?$/ //* Allow two only decimal
    if (!regex.test(value)) {
      return // Block invalid decimal input
    }

    // Block values greater than 100
    const numeric = parseFloat(value)
    if (numeric > 100) {
      return
    }

    formik.setFieldValue(field, value)
  }

  const projectCategoryOption: any = useMemo(() => {
    return prepareMultiPhaseCategoryDropdownOptions(
      projectToProjectPhaseCategories,
      'MasterProjectPhaseCategory',
      'project_phase_category',
    )
  }, [statuses])

  // const projectPhaseOption: any = useMemo(() => {
  //   return prepareDropdownOptions(projectToPhase, 'phase')
  // }, [statuses, localPhase])

  const projectPhaseOption = useMemo(() => {
    let selectedCategories = projectToProjectPhaseCategories
      ?.filter((item) => formik.values.project_to_project_phase_category_ids?.includes(item.id))
      ?.map((item) => item?.MasterProjectPhaseCategory?.id)

    return selectedCategories.length > 0
      ? projectToPhase
          ?.filter((res) => res?.id && selectedCategories?.includes(res?.MasterProjectPhaseCategory?.id))
          ?.map((res) => ({ value: res?.id, label: res?.phase }))
      : []
  }, [projectToProjectPhaseCategories, formik])

  useEffect(() => {
    if (!editData) {
      const valueToMatch = {
        categoryIDs: setMultiSelectedValueToWildCard(formik.values.project_to_project_phase_category_ids),
        phaseIDs: setMultiSelectedValueToWildCard(formik.values.project_to_project_phase_ids),
        statusID: formik.values.master_project_stage_status_id?.toString() || '',
      }
      const match = findMatchingRecordByKey(masterPhase, valueToMatch, MULTI_SELECT_SEPARATOR)

      if (match) {
        setIsPhaseWeightageDisabled(true)
        formik.setFieldValue('phaseWeightage', (Number(cleanNumber(match.phase_weightage || '0')) * 100).toString())
      } else {
        setIsPhaseWeightageDisabled(false)
        formik.setFieldValue('phaseWeightage', '')
      }
    } else {
      setIsPhaseWeightageDisabled(false)
    }
  }, [
    formik.values.project_to_project_phase_category_ids,
    formik.values.project_to_project_phase_ids,
    formik.values.master_project_stage_status_id,
    masterPhase,
    editData,
  ])

  return (
    <>
      <div>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              zIndex: '10000000',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '24px',
              // maxWidth: '980px',
              maxWidth: '1400px',
            }}
          >
            {/* Project Phase Category */}
            <MultiSearchAutoSelect
              labelText="Project Phase Category *"
              placeholder={
                formik.values.project_to_project_phase_category_ids?.length > 0
                  ? ''
                  : 'Select Project Phase Category...'
              }
              isSubOption={false}
              isSx={false}
              clearIcon={true}
              options={convertMultiSelectOption(projectCategoryOption).filter(
                (item: any) => item.id !== null && item.name !== null,
              )}
              // options={projectCategoryOption}
              value={getValueForMultiSelectOption(
                convertMultiSelectOption(projectCategoryOption),
                formik.values.project_to_project_phase_category_ids,
              )}
              // value={getMultiSelectedValue(formik.values.project_to_project_phase_category_ids) as unknown as string[]}
              handleSelectedOption={(val) => handleMultiAutoSelectChange(val, 'project_to_project_phase_category_ids')}
              className={styles.multiSelect}
              inputValue={filterOptions?.project_to_project_phase_category_ids}
              setInputValue={(val: any) =>
                setFilterOptions({ ...filterOptions, project_to_project_phase_category_ids: val })
              }
            />
            {/* Phase/Package */}
            <MultiSearchAutoSelect
              labelText="Phase/Package"
              placeholder={formik.values.project_to_project_phase_ids?.length > 0 ? '' : 'Select phase...'}
              isSubOption={false}
              isSx={false}
              clearIcon={true}
              options={convertMultiSelectOption(projectPhaseOption).filter(
                (item: any) => item.id !== null && !!item.name,
              )}
              // options={projectPhaseOption}
              value={getValueForMultiSelectOption(
                convertMultiSelectOption(projectPhaseOption),
                formik.values.project_to_project_phase_ids,
              )}
              // value={getMultiSelectedValue(formik.values.phase) as unknown as string[]}
              handleSelectedOption={(val) => handleMultiAutoSelectChange(val, 'project_to_project_phase_ids')}
              className={styles.multiSelect}
              inputValue={filterOptions?.phase}
              setInputValue={(val: any) => setFilterOptions({ ...filterOptions, project_to_project_phase_ids: val })}
            />
            {/* Stages */}
            <ComboBox
              labelText="Stages *"
              placeholder="Select stage..."
              className={styles.selectField}
              focusCustomClass={styles.focusClass}
              options={sortStageOptions}
              value={
                formik.values.master_project_stage_status_id
                  ? getValue(
                      getUniqueValuesFromArray(projectStageStatusesOption),
                      formik.values.master_project_stage_status_id,
                    )
                  : null
              }
              clearIcon={true}
              onChange={(val) => handleStageChange(val?.value || '', 'master_project_stage_status_id')}
              isCustomSorting={true}
            />
            {/* Sub Stage */}
            {getValue(
              getUniqueValuesFromArray(projectStageStatusesOption),
              formik.values.master_project_stage_status_id,
            )?.label === 'Design' && (
              <ComboBox
                labelText="Sub Stage *"
                placeholder="Select sub stage..."
                className={styles.selectField}
                focusCustomClass={styles.focusClass}
                options={getUniqueValuesFromArray(subStageOption)}
                value={
                  formik.values.master_project_sub_stage_id
                    ? getValue(getUniqueValuesFromArray(subStageOption), formik.values.master_project_sub_stage_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    master_project_sub_stage_id: val?.value || null,
                  })
                }
              />
            )}
            {getValue(
              getUniqueValuesFromArray(projectStageStatusesOption),
              formik.values.master_project_stage_status_id,
            )?.label === 'Design' && (
              <TextInputField
                labelText="Design Stage Weightage *"
                placeholder="Type something ..."
                type="number"
                className={styles.phaseField}
                name="designStageWeightage"
                variant="outlined"
                value={formik.values.designStageWeightage}
                onChange={(e) => {
                  const value = e.target.value
                  handleWeightage(value, 'designStageWeightage')
                }}
                error={formik.touched.designStageWeightage && formik.errors.designStageWeightage ? true : false}
                helperText={formik.touched.designStageWeightage && formik.errors.designStageWeightage}
              />
            )}
            {/* Phase Weightage */}
            <TextInputField
              labelText="Phase Weightage *"
              placeholder="Type something ..."
              type="number"
              className={styles.phaseField}
              name="phaseWeightage"
              variant="outlined"
              value={formik.values.phaseWeightage}
              onChange={(e) => {
                const value = e.target.value
                handleWeightage(value, 'phaseWeightage')
              }}
              error={formik.touched.phaseWeightage && formik.errors.phaseWeightage ? true : false}
              helperText={formik.touched.phaseWeightage && formik.errors.phaseWeightage}
              disabled={isPhaseWeightageDisabled}
            />
            {/* Predecessor */}
            <MultiSearchAutoSelect
              isSubOption={false}
              isSx={false}
              isSort={false}
              clearIcon={true}
              labelText="Predecessor"
              placeholder={formik.values.predecessor?.length > 0 ? '' : 'Select Predecessor'}
              options={predecessorSuccessorOption}
              value={getValueForMultiSelectOption(predecessorSuccessorOption, formik.values.predecessor)}
              handleSelectedOption={(val) => handlePredecessorSuccessor(val, 'predecessor')}
              className={styles.multiSelect}
              inputValue={filterOptions?.predecessor}
              setInputValue={(val: any) => setFilterOptions({ ...filterOptions, predecessor: val })}
            />
            {/* Successor */}
            <MultiSearchAutoSelect
              isSubOption={false}
              isSx={false}
              isSort={false}
              labelText="Successor"
              placeholder={formik.values.successor?.length > 0 ? '' : 'Select Successor'}
              options={predecessorSuccessorOption}
              clearIcon={true}
              value={getValueForMultiSelectOption(predecessorSuccessorOption, formik.values.successor)}
              handleSelectedOption={(val) => handlePredecessorSuccessor(val, 'successor')}
              className={styles.multiSelect}
              inputValue={filterOptions?.successor}
              setInputValue={(val: any) => setFilterOptions({ ...filterOptions, successor: val })}
            />
          </div>
          <div className={styles.buttons}>
            <Button
              type="submit"
              className={styles.addProjectButton}
              disabled={
                formik.values?.project_to_project_phase_category_ids?.length == 0 ||
                formik.values.phaseWeightage === null ||
                (getValue(
                  getUniqueValuesFromArray(projectStageStatusesOption),
                  formik.values.master_project_stage_status_id,
                )?.label === 'Design' &&
                  !formik.values.master_project_sub_stage_id) ||
                formik.values?.project_to_project_phase_category_ids?.length == 0
              }
              // disabled={
              //   formik.values.phaseWeightage === '' || formik.values.phaseWeightage === null || !formik.values.stages
              // }
            >
              {editData ? '+ Update Stage' : '+ Add Stage'}
            </Button>
            <div className={styles.tableButtons}>
              {hasFilter ? (
                <Button endIcon={<RestartAlt />} variant="contained" onClick={() => setGridFilters([])}>
                  Reset Filter
                </Button>
              ) : null}
              <Button variant={'contained'} onClick={() => setEdit(!edit)}>
                Edit Table
              </Button>
            </div>
            {/* <Button
              disabled={loading}
              onClick={(e) => {
                onValidate()
              }}
            >
              Validate
            </Button> */}
          </div>
        </form>
        <ConfirmationModal
          open={isConfirmationModel}
          message={confirmationMessage}
          handleConfirm={handleConfirm}
          onClose={handleClose}
        />
      </div>
    </>
  )
}

export default StageForm

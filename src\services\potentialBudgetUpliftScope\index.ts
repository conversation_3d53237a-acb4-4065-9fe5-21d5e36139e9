import { IPotentialBudgetUpliftScopePayload, IUpdatePotentialBudgetUpliftScope } from './interface'
import { api } from '@/src/api/axios'

export const POTENTIAL_BUDGET_UPLIFT_SCOPE_URL = `/potential-budget-uplift-scope`

// Get Potential Budget Uplift Scope data
export const getPotentialBudgetUpliftScope = async ({
  period,
  project_name,
}: {
  period: string
  project_name: string
}) => {
  const params = { period: period, project_name: project_name }
  const response = await api.get(`${POTENTIAL_BUDGET_UPLIFT_SCOPE_URL}`, { params })
  return response?.data
}

export const deletePotentialBudgetUpliftScope = async (data: number) => {
  const response = await api.delete(`${POTENTIAL_BUDGET_UPLIFT_SCOPE_URL}/${data}`)
  return response?.data
}

// Create Potential Budget Uplift Scope entry
export const createPotentialBudgetUpliftScope = async (data: IPotentialBudgetUpliftScopePayload) => {
  const response = await api.post(`${POTENTIAL_BUDGET_UPLIFT_SCOPE_URL}`, data)
  return response
}

// Update Potential Budget Uplift Scope entry
export const updatePotentialBudgetUpliftScope = async (data: IUpdatePotentialBudgetUpliftScope): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${POTENTIAL_BUDGET_UPLIFT_SCOPE_URL}/${data.id}`, { ...rest })
  return response
}

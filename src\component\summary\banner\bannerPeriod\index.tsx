import React from 'react'
import { Tooltip } from '@mui/material'
import styles from './BannerPeriod.module.scss'
import TypographyField from '@/src/component/shared/typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { formatDateString } from '@/src/utils/dateUtils'

const BannerPeriod = () => {
  const { currentPeriod } = useMasterPeriod()

  return (
    <>
      {currentPeriod && (
        <Tooltip
          title="Current Period"
          componentsProps={{
            tooltip: {
              sx: { fontFamily: 'poppins' },
            },
          }}
          arrow
        >
          <div className={styles.iconWrapper}>
            <CalendarIcon color="#ffffff" />
            <div className={styles.periodContainer}>
              <TypographyField
                variant="thin"
                style={{ color: '#b0b0b0', fontSize: '10px', lineHeight: '10px' }}
                text={'Reporting Period'}
              />
              <TypographyField
                variant="caption"
                style={{ color: '#ffffff', fontSize: '14px', lineHeight: '10px' }}
                text={currentPeriod ? formatDateString(currentPeriod) : ''}
              />
            </div>
          </div>
        </Tooltip>
      )}
    </>
  )
}

export default BannerPeriod

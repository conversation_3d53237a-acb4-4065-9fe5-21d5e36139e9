import { useDispatch, useSelector } from 'react-redux'
import {
  getKeyAchievements,
  addKeyAchievements,
  updateKeyAchievements,
  deleteKeyAchievements,
  updateFormData,
  keyAchievementSort,
} from '.'
import { IKeyAchievement } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useKeyAchievement = () => {
  const dispatch: AppDispatch = useDispatch()

  const getKeyAchievementStatus = useSelector((state: RootState) => state.keyAchievement.getKeyAchievementStatus)
  const keyAchievements = useSelector((state: RootState) => state.keyAchievement.keyAchievements)
  const formData = useSelector((state: RootState) => state.keyAchievement.formData)

  const getKeyAchievementsApi = (data: { period: string; project_name?: string }) => dispatch(getKeyAchievements(data))
  const addKeyAchievementsApi = (data: IKeyAchievement) => dispatch(addKeyAchievements(data))
  const keyAchievementSortApi = (data: any) => dispatch(keyAchievementSort(data))
  const updateKeyAchievementsApi = (data: { id?: number; data: IKeyAchievement }) =>
    dispatch(updateKeyAchievements(data))
  const deleteKeyAchievementsApi = (data: number) => dispatch(deleteKeyAchievements(data))

  const setFormData = (data: any) => dispatch(updateFormData(data))

  return {
    getKeyAchievementStatus,
    keyAchievements,
    keyAchievementSortApi,
    getKeyAchievementsApi,
    addKeyAchievementsApi,
    updateKeyAchievementsApi,
    deleteKeyAchievementsApi,
    setFormData,
    formData,
  }
}
export default useKeyAchievement

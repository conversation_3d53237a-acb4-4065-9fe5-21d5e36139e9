@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.container {
  display: flex;
  height: 100%;
  width: 100%;
}

.projectLayout {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.pageContent {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
}

.loader {
  height: 100vh;
  width: 100vw;
  cursor: progress;
}

.children {
  overflow: auto;
  width: 100%;
  position: relative;

  @include respond-to('mobile') {
    height: 100vh;
  }
  @include respond-to('tablet') {
    height: 100vh;
  }
  @include respond-to('laptop') {
    height: calc(100vh - 2.4375rem); // 39px → 2.4375rem
    top: 1.25rem; // 20px → 1.25rem
  }

  &::-webkit-scrollbar {
    width: 0rem;
  }

  &::-webkit-scrollbar-track {
    margin-top: 2.5rem; // 40px → 2.5rem
    border-radius: 0.3125rem; // 5px → 0.3125rem
    width: 0.25rem; // 4px → 0.25rem
  }

  &::-webkit-scrollbar-thumb {
    background: #dddddd;
    border-radius: 0.3125rem; // 5px → 0.3125rem
    width: 0.25rem; // 4px → 0.25rem
  }
}

.menuIcon {
  margin: 10px 10px 0 30px;
  padding: 5px 5px;
  z-index: 1000; // Ensure it stays above other elements
  cursor: pointer;
  background: transparent; // Prevents it from taking extra space
  border-radius: 50%; // Makes it look better visually

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }
}

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterLocation,
  deleteMasterLocation,
  getMasterLocations,
  updateMasterLocation,
} from '../services/locations'
import { IGetMasterLocationsResponse, IUpdateLocation } from '../services/locations/interface'

export const QUERY_LOCATION = 'locations'

/**
 * Hook to fetch Locations
 * @param enabled - Enables or disables the query
 */
export const useGetLocations = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterLocationsResponse>({
    queryKey: [QUERY_LOCATION],
    queryFn: getMasterLocations,
    enabled,
  })

  return {
    locations: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Locations
 */
export const useCreateLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_LOCATION] })
    },
  })
}

/**
 * Hook to delete a Locations
 */
export const useDeleteLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_LOCATION] })
    },
  })
}

/**
 * Hook to update a Locations
 */
export const useUpdateLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterLocation,
    onMutate: async (updatedData: IUpdateLocation) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_LOCATION] })
      const previousData = queryClient.getQueryData<IGetMasterLocationsResponse>([QUERY_LOCATION])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterLocationsResponse>([QUERY_LOCATION], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, location: updatedData.location } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_LOCATION], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_LOCATION] })
    },
  })
}

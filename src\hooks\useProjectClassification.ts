import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterProjectClassification,
  deleteMasterProjectClassification,
  getMasterProjectClassifications,
  updateMasterProjectClassification,
} from '../services/projectClassification'
import {
  IGetMasterProjectClassificationResponse,
  IUpdateProjectClassification,
} from '../services/projectClassification/interface'

export const QUERY_PROJECT_CLASSIFICATION = 'project-classification'

/**
 * Hook to fetch Project Classifications
 * @param enabled - Enables or disables the query
 */
export const useGetProjectClassifications = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterProjectClassificationResponse>({
    queryKey: [QUERY_PROJECT_CLASSIFICATION],
    queryFn: getMasterProjectClassifications,
    enabled,
  })

  return {
    projectClassifications: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project Classifications
 *  */
export const useCreateProjectClassifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterProjectClassification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_CLASSIFICATION] })
    },
  })
}

/**
 * Hook to delete a Project Classifications
 * */
export const useDeleteProjectClassifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterProjectClassification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_CLASSIFICATION] })
    },
  })
}

/**
 * Hook to update a Project Classifications
 * */
export const useUpdateProjectClassifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterProjectClassification,
    onMutate: async (updatedData: IUpdateProjectClassification) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_CLASSIFICATION] })
      const previousData = queryClient.getQueryData<IGetMasterProjectClassificationResponse>([
        QUERY_PROJECT_CLASSIFICATION,
      ])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterProjectClassificationResponse>([QUERY_PROJECT_CLASSIFICATION], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, project_classification: updatedData.project_classification }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_CLASSIFICATION], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_CLASSIFICATION] })
    },
  })
}

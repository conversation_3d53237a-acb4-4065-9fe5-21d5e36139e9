import React from 'react'
import AddConsultants from './consultants'
import AddContractors from './contractors'
import AddContractType from './contractType'
import AddControlManagers from './controlManager'
import AddDesignManagers from './designManager'
import Director from './director'
import AddExecutiveDirectors from './executiveDirector'
import AddMilestoneNumber from './milestoneNumber'
import AddPmcConsultant from './pmcConsultant'
import AddPortfolioManagers from './portfolioManager'
import AddProcurementManagement from './procurementManagement'
import styles from './projectManagement.module.scss'
import ProjectOwnerCard from './projectOwnerCard'
import AddRiskCategory from './riskCategory'
import AddRiskDiscipline from './riskDiscipline'
import AddRiskOwner from './riskOwner'
import Svp from './svp'
import Drawer from '../../shared/drawer'
import TypographyField from '../../shared/typography'
import HandoverStep from '../handover/handoverStep'
import ImageCard from '../imageCard'
import { DrawerStates } from '../interface'
import useModuleCount from '@/src/redux/moduleCount/useModuleCount'

// Importing all drawer components

interface IProjectPhasesProps {
  drawerStates: DrawerStates
  toggleDrawer: (drawer: keyof DrawerStates) => (open: boolean) => void
}

const componentsMap = {
  handoverStep: HandoverStep,
  svp: Svp,
  director: Director,
  addContractors: AddContractors,
  riskOwner: AddRiskOwner,
  riskCategory: AddRiskCategory,
  riskDiscipline: AddRiskDiscipline,
  addProcurementManagement: AddProcurementManagement,
  addContractType: AddContractType,
  addProjectOwnerCard: ProjectOwnerCard,
  addConsultants: AddConsultants,
  addPmcConsultant: AddPmcConsultant,
  addControlManagers: AddControlManagers,
  addPortFolioManagers: AddPortfolioManagers,
  addExecutiveDirectors: AddExecutiveDirectors,
  addDesignManagers: AddDesignManagers,
}

const imageCardsConfig = [
  {
    key: 'portfolio_managers',
    label: 'Portfolio Manager',
    drawer: 'addPortFolioManagers',
    imageSrc: '/svg/phases.svg',
  },
  { key: 'design_project_manager', label: 'Design Manager', drawer: 'addDesignManagers', imageSrc: '/svg/phases.svg' },
  {
    key: 'design_project_owner',
    label: 'Design Executive Director',
    drawer: 'addProjectOwnerCard',
    imageSrc: '/svg/phases.svg',
  },
  { key: 'control_managers', label: 'Control Manager', drawer: 'addControlManagers', imageSrc: '/svg/consultants.svg' },
  {
    key: 'Procurement_manager',
    label: 'Procurement Manager',
    drawer: 'addProcurementManagement',
    imageSrc: '/svg/phases.svg',
  },
  {
    key: 'executive_directors',
    label: 'Delivery Executive Director',
    drawer: 'addExecutiveDirectors',
    imageSrc: '/svg/phases.svg',
  },
  { key: 'director', label: 'Delivery Director', drawer: 'director', imageSrc: '/svg/phases.svg' },
  { key: 'svp', label: 'Delivery PM', drawer: 'svp', imageSrc: '/svg/phases.svg' },
  {
    key: 'contractors',
    label: 'Contractor',
    drawer: 'addContractors',
    imageSrc: '/svg/contractor.svg',
    imageWidth: 43,
  },
  {
    key: 'consultants',
    label: 'Design Consultant',
    drawer: 'addConsultants',
    imageSrc: '/svg/consultants.svg',
    imageWidth: 43,
  },
  {
    key: 'pmc_consultants',
    label: 'PMC/Supervision Consultant',
    drawer: 'addPmcConsultant',
    imageSrc: '/svg/pmcConsultants.svg',
  },
  { key: 'contract_type', label: 'Contract Type', drawer: 'addContractType', imageSrc: '/svg/phases.svg' },
  { key: 'risk_owner', label: 'Risk Owner', drawer: 'riskOwner', imageSrc: '/svg/phases.svg' },
  { key: 'risk_discipline', label: 'Risk Discipline', drawer: 'riskDiscipline', imageSrc: '/svg/phases.svg' },
  { key: 'risk_category', label: 'Risk Category', drawer: 'riskCategory', imageSrc: '/svg/phases.svg' },
]

const ProjectManagement: React.FC<IProjectPhasesProps> = ({ toggleDrawer, drawerStates }) => {
  const { moduleCounts } = useModuleCount()

  return (
    <div className={styles.cardContainer}>
      {/* Test below code */}
      <TypographyField variant={'subheading'} text={'Project Management'} />
      <div className={styles.cards}>
        {imageCardsConfig.map(({ key, label, drawer, imageSrc, imageWidth }) => (
          <ImageCard
            key={key}
            label={label}
            value={moduleCounts.projectManagement?.[key] || 0}
            imageSrc={imageSrc}
            imageWidth={imageWidth ? imageWidth : 53}
            imageHeight={50}
            onAddButtonClick={() => toggleDrawer(drawer as keyof DrawerStates)(true)}
          />
        ))}
      </div>

      {/* Drawer Rendering */}
      {Object.entries(drawerStates).map(([key, isOpen]) => {
        const Component = componentsMap[key as keyof typeof componentsMap]
        if (!Component) return null // Skip if no matching component

        return (
          <Drawer key={key} anchor="right" open={isOpen} onClose={() => toggleDrawer(key as keyof DrawerStates)(false)}>
            <Component drawerStates={drawerStates} onClose={() => toggleDrawer(key as keyof DrawerStates)(false)} />
          </Drawer>
        )
      })}
    </div>
  )
}

export default ProjectManagement

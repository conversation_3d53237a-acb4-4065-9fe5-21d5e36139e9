import { IConsultantPayload, IUpdateConsultant } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_CONSULTANT_URL = `/master-consultant`

// Get Master Consultant data
export const getMasterConsultants = async () => {
  const response = await api.get(`${MASTER_CONSULTANT_URL}`)
  return response?.data
}

export const deleteMasterConsultant = async (data: number) => {
  const response = await api.delete(`${MASTER_CONSULTANT_URL}/${data}`)
  return response?.data
}

// Create Master Consultant entry
export const createMasterConsultant = async (data: IConsultantPayload) => {
  const response = await api.post(`${MASTER_CONSULTANT_URL}`, data)
  return response
}

// Update Master Consultant entry
export const updateMasterConsultant = async (data: IUpdateConsultant): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_CONSULTANT_URL}/${data.id}`, { ...rest })
  return response
}

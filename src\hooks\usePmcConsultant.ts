import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterPmcConsultant,
  deleteMasterPmcConsultant,
  getMasterPmcConsultant,
  updateMasterPmcConsultant,
} from '../services/pmcConsultant'
import { IGetMasterPmcConsultantResponse, IUpdatePmcConsultant } from '../services/pmcConsultant/interface'

export const QUERY_PMC_CONSULTANT = 'pmc-consultant'

/**
 * Hook to fetch Pmc Consultant
 * @param enabled - Enables or disables the query
 */
export const useGetPmcConsultant = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterPmcConsultantResponse>({
    queryKey: [QUERY_PMC_CONSULTANT],
    queryFn: getMasterPmcConsultant,
    enabled,
  })

  return {
    pmcConsultants: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Pmc Consultant
 *  */
export const useCreatePmcConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterPmcConsultant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PMC_CONSULTANT] })
    },
  })
}

/**
 * Hook to delete a Pmc Consultant
 * */
export const useDeletePmcConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterPmcConsultant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PMC_CONSULTANT] })
    },
  })
}

/**
 * Hook to update a Pmc Consultant
 * */
export const useUpdatePmcConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterPmcConsultant,
    onMutate: async (updatedData: IUpdatePmcConsultant) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PMC_CONSULTANT] })
      const previousData = queryClient.getQueryData<IGetMasterPmcConsultantResponse>([QUERY_PMC_CONSULTANT])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterPmcConsultantResponse>([QUERY_PMC_CONSULTANT], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, pmc_consultant: updatedData.pmc_consultant } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PMC_CONSULTANT], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PMC_CONSULTANT] })
    },
  })
}

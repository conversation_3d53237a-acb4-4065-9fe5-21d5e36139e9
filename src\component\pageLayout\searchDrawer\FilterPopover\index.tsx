import React, { useState, ChangeEvent, FC, useMemo, useEffect } from 'react'
import { Popover, Checkbox, FormControlLabel, Button, TextField } from '@mui/material'
import { format, getDate, getYear, Month, parse } from 'date-fns'
import DateFilter, { YearData } from './dateFilter'
import styles from './FilterPopover.module.scss'
import useSearchDrawer from '@/src/redux/searchDrawer/useSearchDrawer'

export interface FilterOption {
  label: string
  value: string
  subItem?: FilterOption[]
}

interface FilterPopoverProps {
  type?: 'date' | 'other'
  anchorEl: HTMLElement | null
  selectedOptions: FilterOption[]
  setSelectedOptions: React.Dispatch<React.SetStateAction<FilterOption[]>>
  onClose: () => void
  options: FilterOption[]
  onApply: (selected: FilterOption[]) => void
  isBlank?: boolean
  popOverWidth?: string
  filterField?: string
}

const useFilterLogic = (options: FilterOption[], searchTerm: string, type: string | undefined) => {
  const uniqueOptions: { [key: string]: FilterOption } = {}

  options?.forEach((option) => {
    const normalizedValue = String(option.value || '').toLowerCase() // Ensure it's a string before calling toLowerCase()

    if (!uniqueOptions[normalizedValue]) {
      uniqueOptions[normalizedValue] = option
    }
  })

  const filteredOptionsArray = Object.values(uniqueOptions)

  const filterOptions = () => {
    if (type === 'date') {
      const groupedData: YearData[] = []

      filteredOptionsArray.forEach((item) => {
        const parsedDate = parse(item.value, 'dd-MM-yyyy', new Date())

        if (isNaN(parsedDate.getTime())) {
          console.log(`Invalid date format: ${item.value}`)
          groupedData.push({ year: null as any, month: [] })
          return
        }

        const year = getYear(parsedDate)
        const month = format(parsedDate, 'MMMM')
        const date = getDate(parsedDate)

        let yearGroup = groupedData.find((group) => group.year === year)
        if (!yearGroup) {
          yearGroup = { year, month: [] }
          groupedData.push(yearGroup)
        }

        let monthGroup: any = yearGroup?.month?.find((monthItem: any) => monthItem?.monthName === month)
        if (!monthGroup) {
          monthGroup = { monthName: month, date: [] }
          yearGroup.month.push(monthGroup)
        }

        if (!monthGroup.date.includes(date)) {
          monthGroup.date.push(date)
        }
      })

      return groupedData
    }

    return filteredOptionsArray.reduce<FilterOption[]>((acc, option) => {
      const filteredSubItems =
        option.subItem?.filter((sub) => String(sub.label).toLowerCase().includes(searchTerm.toLowerCase())) || []
      if (filteredSubItems.length > 0 || String(option.label).toLowerCase().includes(searchTerm.toLowerCase())) {
        acc.push({
          label: option.label,
          value: option.value,
          ...(filteredSubItems.length > 0 && { subItem: filteredSubItems }),
        })
      }
      return acc
    }, [])
  }

  return filterOptions()
}

const FilterPopover: FC<FilterPopoverProps> = ({
  type,
  anchorEl,
  selectedOptions,
  setSelectedOptions,
  onClose,
  options,
  onApply,
  isBlank = false,
  popOverWidth,
  filterField,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const filteredOptions = useFilterLogic(options, searchTerm, type)
  const { userFilterFields, setUserFilterFieldsApi } = useSearchDrawer()

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value.toLowerCase())
  }

  const isOptionSelected = (value: string, isSubItem: boolean, parentValue?: string) => {
    if (isSubItem && parentValue) {
      const parent = selectedOptions.find((opt) => opt.value === parentValue)
      return parent?.subItem?.some((sub) => sub.value === value) ?? false
    }
    return selectedOptions.some((opt) => opt.value?.toLowerCase() === value?.toLowerCase())
  }

  const handleToggle = (value: string, isSubItem: boolean, parentValue?: string) => {
    setSelectedOptions((prevOptions) => {
      const newOptions = [...prevOptions]

      if (value === 'Blanks') {
        // Toggle the 'Blanks' value independently
        const blankIndex = newOptions.findIndex((opt: any) => opt.value === 'Blanks')
        if (blankIndex !== -1) {
          newOptions.splice(blankIndex, 1) // Remove Blanks if already selected
        } else {
          newOptions.push({ label: 'Blanks', value: 'Blanks' }) // Add Blanks if not selected
        }
      } else if (isSubItem && parentValue) {
        const parentIndex = newOptions.findIndex((opt) => opt.value === parentValue)
        if (parentIndex !== -1) {
          const parent = newOptions[parentIndex]
          const subIndex = parent.subItem?.findIndex((sub) => sub.value === value)

          if (subIndex !== undefined && subIndex !== -1) {
            parent.subItem?.splice(subIndex, 1)
          } else {
            parent.subItem = [...(parent.subItem || []), { label: value, value }]
          }
        } else {
          newOptions.push({ label: parentValue, value: parentValue, subItem: [{ label: value, value }] })
        }
      } else {
        const optionIndex = newOptions.findIndex((opt) => opt.value === value)
        if (optionIndex !== -1) {
          newOptions.splice(optionIndex, 1)
        } else {
          const option = options.find((opt) => opt.value === value)
          if (option && !newOptions.some((opt) => opt.value === value)) {
            newOptions.push({ ...option })
          }
        }
      }

      return newOptions
    })
  }

  const handleSelectAll = (checked: boolean) => {
    const data: any = filteredOptions
    setSelectedOptions(checked ? data : [])
  }

  const handleReset = () => {
    // setSelectedOptions([])
    onApply([])
    // onClose()
    // NOTE :  remove filterField name from userFilterFields
    const filterFields = userFilterFields?.filter((res: any) => res !== filterField)
    setUserFilterFieldsApi([...filterFields])
    setSelectedOptions([...filteredOptions] as FilterOption[])
  }

  const count = useMemo(() => {
    return filteredOptions?.length
  }, [filteredOptions])

  const isYearData = (item: any): item is YearData => {
    return item && typeof item.year === 'number' && Array.isArray(item.month)
  }

  const dateFilterOptions = useMemo(() => {
    return filteredOptions?.filter((res): res is YearData => isYearData(res) && res.year !== null)
  }, [filteredOptions])

  const isBlankDate = useMemo(() => {
    return filteredOptions?.filter((res: any) => res.year === null)
  }, [filteredOptions])

  useEffect(() => {
    setSelectedOptions([...(filteredOptions as FilterOption[])])
  }, [searchTerm])

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={() => {
        onClose()
        setSearchTerm('')
      }}
    >
      <div
        className={styles.filterContainer}
        style={{ '--popover-width': popOverWidth || '300px' } as React.CSSProperties}
      >
        {type !== 'date' && (
          <TextField
            placeholder="Search in filters"
            fullWidth
            margin="dense"
            variant="outlined"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mb: 2 }}
          />
        )}

        <div className={styles.options}>
          {type === 'date' ? (
            <>
              <DateFilter
                filteredOptions={dateFilterOptions as any[]}
                selectedOptions={selectedOptions}
                setSelectedOption={setSelectedOptions}
                onApply={onApply}
              />
              {/* NOTE : uncomment below code for Blank filter for date */}
              {/* {isBlankDate && (
                <div style={{ marginLeft: '50px' }}>
                  <FormControlLabel
                    className={styles.filterBox}
                    control={
                      <Checkbox
                        className={styles.filterOption}
                        checked={isOptionSelected('Blanks', false)}
                        onChange={() => handleToggle('Blanks', false)}
                      />
                    }
                    label={'( Blanks )'}
                  />
                </div>
              )} */}
            </>
          ) : (
            <>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedOptions.length > 0 && selectedOptions.length === filteredOptions.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                }
                label={`Select all (${count})`}
              />
              <div className={styles.opt}>
                {(filteredOptions as FilterOption[]).map((option: any) => (
                  <div key={option.value}>
                    {option.label !== 'Blanks' && (
                      <FormControlLabel
                        className={styles.filterBox}
                        control={
                          <Checkbox
                            className={styles.filterOption}
                            checked={isOptionSelected(option.value, false)}
                            onChange={() => handleToggle(option.value, false)}
                          />
                        }
                        label={option.label}
                        sx={{
                          '.MuiTypography-root': {
                            wordBreak: 'break-all',
                          },
                        }}
                      />
                    )}
                    {option.subItem && (
                      <div className={styles.subs}>
                        {option.subItem.map((subItem: any) => (
                          <FormControlLabel
                            key={subItem.value}
                            control={
                              <Checkbox
                                checked={isOptionSelected(subItem.value, true, option.value)}
                                onChange={() => handleToggle(subItem.value, true, option.value)}
                              />
                            }
                            sx={{
                              '.MuiTypography-root': {
                                wordBreak: 'break-all',
                              },
                            }}
                            label={subItem.label}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {isBlank &&
                  ((searchTerm?.length > 0 && 'Blanks'.toLowerCase().includes(searchTerm.toLowerCase())) ||
                    searchTerm?.length === 0) && (
                    <FormControlLabel
                      className={styles.filterBox}
                      control={
                        <Checkbox
                          className={styles.filterOption}
                          checked={isOptionSelected('Blanks', false)}
                          onChange={() => handleToggle('Blanks', false)}
                        />
                      }
                      label={'( Blanks )'}
                    />
                  )}
              </div>
            </>
          )}
        </div>

        <div className={styles.actionButtons}>
          <Button onClick={handleReset} variant="contained" disabled={selectedOptions?.length === 0}>
            Reset
          </Button>
          <Button
            onClick={() => {
              onApply(selectedOptions)
              onClose()
              setSearchTerm('')
            }}
            disabled={selectedOptions?.length === 0}
          >
            Ok
          </Button>
        </div>
      </div>
    </Popover>
  )
}

export default FilterPopover

import { useDispatch, useSelector } from 'react-redux'
import { addRole, deleteRole, getProjectAndEntity, getRole, getViewPermissions, updateRole } from '.'
import { IRole } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useRole = () => {
  const dispatch: AppDispatch = useDispatch()

  const getRoleStatus = useSelector((state: RootState) => state.role.getRoleStatus)
  const roles = useSelector((state: RootState) => state.role.roles)
  const permissions = useSelector((state: RootState) => state.role.permissions)
  const projectAndEntities = useSelector((state: RootState) => state.role.projectAndEntities)

  const getRoleApi = () => dispatch(getRole())
  const addRoleApi = (data: IRole) => dispatch(addRole(data))
  const updateRoleApi = (data: IRole) => dispatch(updateRole(data))
  const deleteRoleApi = (data: number) => dispatch(deleteRole(data))
  const getViewPermissionsApi = () => dispatch(getViewPermissions())
  const getProjectAndEntityApi = (data: any) => dispatch(getProjectAndEntity(data))

  return {
    permissions,
    getRoleStatus,
    roles,
    projectAndEntities,
    getRoleApi,
    addRoleApi,
    updateRoleApi,
    deleteRoleApi,
    getViewPermissionsApi,
    getProjectAndEntityApi,
  }
}

export default useRole

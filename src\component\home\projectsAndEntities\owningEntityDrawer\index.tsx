import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import { IAddEntityProps, IEntity } from './interface'
import styles from './OwningEntityDrawer.module.scss'
import ConfirmDeleteModal from '../../../confirmDeleteModal'
import Button from '../../../shared/button'
import Loader from '../../../shared/loader'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import TextInputField from '../../../shared/textInputField'
import TypographyField from '../../../shared/typography'
import DeleteIcon from '../../../svgImages/deleteIcon'
import EditIcon from '../../../svgImages/editIcon'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import {
  useCreateOwningEntity,
  useDeleteOwningEntity,
  useGetOwningEntity,
  useUpdateOwningEntity,
} from '@/src/hooks/useOwningEntity'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const OwningEntityDrawer: React.FC<IAddEntityProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addEntity } = drawerStates
  //REACT-QUERY
  const { owningEntities, isLoading, isSuccess } = useGetOwningEntity(addEntity)
  // MUTATIONS
  const { mutate: addOwningEntity } = useCreateOwningEntity()
  const { mutate: deleteOwningEntity } = useDeleteOwningEntity()
  const { mutate: updateOwningEntity } = useUpdateOwningEntity()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { entityName: '' },
    onSubmit: async (values) => {
      const payload = { owning_entity: values.entityName }
      if (editIndex !== null) {
        updateOwningEntity(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addOwningEntity(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteOwningEntity(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editEntity: IEntity | undefined = owningEntities.find((entity: any) => entity.id === id)
    if (editEntity) {
      formik.setValues({ entityName: editEntity.owning_entity })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'owning_entity',
      header: 'Entity Name',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: ({ row }: any) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Entities'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {isSuccess && (
          <form className={styles.form} onSubmit={formik.handleSubmit}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
              <TextInputField
                className={styles.entityField}
                name="entityName"
                labelText={'Entity Name'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.entityName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
            <div>
              {editIndex !== null ? (
                <Button
                  className={styles.addProjectButton}
                  type="submit"
                  disabled={formik.isSubmitting || !formik.values.entityName?.trim()}
                >
                  {'Update'}
                </Button>
              ) : (
                <Button
                  className={styles.addProjectButton}
                  color="secondary"
                  type="submit"
                  disabled={formik.isSubmitting || !formik.values.entityName?.trim()}
                >
                  {'+ Add Entity'}
                </Button>
              )}
            </div>
          </form>
        )}
        {owningEntities?.length >= 1 ? (
          isLoading ? (
            <Loader />
          ) : (
            <TanStackTable
              rows={sortData(owningEntities, 'owning_entity') as any}
              columns={columns}
              isOverflow={true}
            />
          )
        ) : (
          ''
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default OwningEntityDrawer

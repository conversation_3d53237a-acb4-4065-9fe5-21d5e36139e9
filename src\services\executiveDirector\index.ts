import { IExecutiveDirectorPayload, IUpdateExecutiveDirector } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_EXECUTIVE_DIRECTOR_URL = `/master-executive-director`

// Get Master Executive Manager data
export const getMasterExecutiveDirectors = async () => {
  const response = await api.get(`${MASTER_EXECUTIVE_DIRECTOR_URL}`)
  return response?.data
}
export const deleteMasterExecutiveDirector = async (data: number) => {
  const response = await api.delete(`${MASTER_EXECUTIVE_DIRECTOR_URL}/${data}`)
  return response?.data
}

// Create Master Executive Manager entry
export const createMasterExecutiveDirector = async (data: IExecutiveDirectorPayload) => {
  const response = await api.post(`${MASTER_EXECUTIVE_DIRECTOR_URL}`, data)
  return response
}

// Update Master Executive Manager entry
export const updateMasterExecutiveDirector = async (data: IUpdateExecutiveDirector): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_EXECUTIVE_DIRECTOR_URL}/${data.id}`, { ...rest })
  return response
}

import { combineReducers, configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist'
import storage from 'redux-persist/lib/storage/session'
import areaOfConcernReducer from '@/src/redux/areaOfConcern'
import authorityApprovalReducer from '@/src/redux/authorityApproval'
import authorizationReducer from '@/src/redux/authorization'
import avatarReducer from '@/src/redux/avatars'
import batchUploadReducer from '@/src/redux/batchUpload'
import categoryReducer from '@/src/redux/category'
import commercialReducer from '@/src/redux/commercial'
import contractorAndVosReducer from '@/src/redux/contractorAndVos'
import userReducer from '@/src/redux/createUser'
import currentPeriodReducer from '@/src/redux/currentPeriod'
import dataEntryScreenReducer from '@/src/redux/dataEntryScreen'
import dataManagementReducer from '@/src/redux/dataManagement'
import developerReducer from '@/src/redux/developers'
import entityKeyAchievementReducer from '@/src/redux/entityKeyAchievement'
import governanceReducer from '@/src/redux/governance'
import keyAchievementReducer from '@/src/redux/keyAchievement'
import keyHighlightReducer from '@/src/redux/keyHighlight'
import lastUpdatedReducer from '@/src/redux/lastUpdated'
import masterPeriodsReducer from '@/src/redux/masterPeriods'
import moduleCountReducer from '@/src/redux/moduleCount'
import phaseReducer from '@/src/redux/phase'
import projectKpiReducer from '@/src/redux/projectKpi'
import projectManagementReducer from '@/src/redux/projectManagement'
import projectSummaryReducer from '@/src/redux/projectSummary'
import projectToBeComplatedInReducer from '@/src/redux/projectToBeComplatedIn'
import ratingReducer from '@/src/redux/rating'
import roleReducer from '@/src/redux/role'
import filtersReducer from '@/src/redux/searchDrawer'
import spaReducer from '@/src/redux/spaMileston'
import statusReducer from '@/src/redux/status'
import subOwningEntityReducer from '@/src/redux/subOwningEntity'
import subStatusesReducer from '@/src/redux/subStatuses'
import sustainabilityReducer from '@/src/redux/sustainability'
import tableReducer from '@/src/redux/table'
import uploadPictureReducer from '@/src/redux/uploadPicture'

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['projectSummary', 'status', 'masterPeriod', 'currentPeriodData'],
}

const rootReducer = combineReducers({
  table: tableReducer,
  // project: projectReducer,
  developer: developerReducer,
  rating: ratingReducer,
  projectSummary: projectSummaryReducer,
  subStatuses: subStatusesReducer,
  subOwningEntity: subOwningEntityReducer,
  sustainability: sustainabilityReducer,
  status: statusReducer,
  commercial: commercialReducer,
  governance: governanceReducer,
  areaOfConcern: areaOfConcernReducer,
  keyAchievement: keyAchievementReducer,
  projectManagement: projectManagementReducer,
  keyHighlight: keyHighlightReducer,
  projectKpi: projectKpiReducer,
  summaryTab: dataEntryScreenReducer,
  moduleCount: moduleCountReducer,
  phase: phaseReducer,
  category: categoryReducer,
  masterPeriod: masterPeriodsReducer,
  contractorAndVos: contractorAndVosReducer,
  role: roleReducer,
  user: userReducer,
  spa: spaReducer,
  entityKeyAchievement: entityKeyAchievementReducer,
  projectToBeComplatedIn: projectToBeComplatedInReducer,
  authorization: authorizationReducer,
  database: dataManagementReducer,
  authorityApproval: authorityApprovalReducer,
  file: uploadPictureReducer,
  filters: filtersReducer,
  lastUpdated: lastUpdatedReducer,
  batchUpload: batchUploadReducer,
  avatars: avatarReducer,
  currentPeriodData: currentPeriodReducer,
})

const persistedReducer = persistReducer(persistConfig, rootReducer)

const store = configureStore({
  reducer: persistedReducer,
  devTools: true,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
})

export type RootState = ReturnType<typeof rootReducer>
export type AppDispatch = typeof store.dispatch
export const persister = persistStore(store)
export default store

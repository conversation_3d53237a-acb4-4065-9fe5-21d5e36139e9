import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createDirector, deleteDirector, getDirectors, updateDirector } from '../services/director'
import { IGetDirectorResponse, IUpdateDirector } from '../services/director/interface'

export const QUERY_DIRECTORS = 'directors'

/**
 * Hook to fetch directors
 * @param enabled - Enables or disables the query
 */
export const useGetDirectors = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetDirectorResponse>({
    queryKey: [QUERY_DIRECTORS],
    queryFn: getDirectors,
    enabled,
  })

  return {
    directors: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a directors
 */
export const useCreateDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createDirector,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DIRECTORS] })
    },
  })
}

/**
 * Hook to delete a directors
 */
export const useDeleteDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteDirector,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DIRECTORS] })
    },
  })
}

/**
 * Hook to update a directors
 */
export const useUpdateDirector = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateDirector,
    onMutate: async (updatedData: IUpdateDirector) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_DIRECTORS] })
      const previousData = queryClient.getQueryData<IGetDirectorResponse>([QUERY_DIRECTORS])
      // Optimistically update the cache
      queryClient.setQueryData<IGetDirectorResponse>([QUERY_DIRECTORS], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, director: updatedData.director } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_DIRECTORS], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DIRECTORS] })
    },
  })
}

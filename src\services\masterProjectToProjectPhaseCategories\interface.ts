import { ILookupProjectToPhase } from '@/src/redux/status/interface'
export interface IProjectPhaseCategoryPayload {
  project_name: string
  period: string
  master_project_phase_category_id: number | null
}

export interface IUpdateProjectPhaseCategory extends IProjectPhaseCategoryPayload {
  id: number
}

export interface IProjectPhaseCategory {
  id: number
  project_phase_category: string
}

export interface IGetProjectPhaseCategoryResponse {
  data: ILookupProjectToPhase[]
  message: string
  success: true
}

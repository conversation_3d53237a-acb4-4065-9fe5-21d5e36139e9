import { useDispatch, useSelector } from 'react-redux'
import {
  addMasterPeriods,
  finalFreezePeriod,
  getAllPeriod,
  getCurrentPeriod,
  unFreezePeriod,
  userFreezePeriod,
} from '.'
import { setIsPeriodChangeFromProjectList, setPeriod } from '../currentPeriod'
import { byPassSessionForCurrentPeriodData } from '../currentPeriod/helper'
import { StatusEnum } from '../types'
import { AppDispatch, RootState } from '@/src/redux/store'

const useMasterPeriod = () => {
  const dispatch: AppDispatch = useDispatch()

  const loading = useSelector(
    (state: RootState) =>
      state.masterPeriod.finalFreezePeriodStatus === StatusEnum.Pending ||
      state.masterPeriod.unFreezePeriodStates === StatusEnum.Pending ||
      state.masterPeriod.userFreezePeriodStates === StatusEnum.Pending,
  )

  const getMasterPeriodStates = useSelector((state: RootState) => state.masterPeriod.getMasterPeriodStates)
  const mainPeriod = useSelector((state: RootState) => state.masterPeriod.mainPeriod)
  const allPeriods = useSelector((state: RootState) => state.masterPeriod.allPeriods)
  const isPeriodChangeFromProjectList = useSelector(
    (state: RootState) => state.currentPeriodData.isPeriodChangeFromProjectList,
  )

  const freezeType = useSelector((state: RootState) => state.masterPeriod.period?.freeze_type)
  const currentPeriod = useSelector((state: RootState) => state.currentPeriodData.currentPeriod)

  const getMasterPeriodsApi = () => dispatch(getCurrentPeriod())
  const getAllPeriodApi = () => dispatch(getAllPeriod())
  const addMasterPeriodsApi = (data: { period: string }) => dispatch(addMasterPeriods(data))
  const userFreezePeriodApi = () => dispatch(userFreezePeriod())
  const finalFreezePeriodApi = () => dispatch(finalFreezePeriod())
  const unFreezePeriodApi = () => dispatch(unFreezePeriod())
  const setPeriodApi = (data: string) => {
    dispatch(setPeriod(data))
    byPassSessionForCurrentPeriodData({ currentPeriod: data })
  }
  const setIsPeriodChangeFromProjectListApi = (data: boolean) => dispatch(setIsPeriodChangeFromProjectList(data))

  return {
    getMasterPeriodStates,
    currentPeriod,
    freezeType,
    getAllPeriodApi,
    setPeriodApi,
    mainPeriod,
    getMasterPeriodsApi,
    addMasterPeriodsApi,
    userFreezePeriodApi,
    finalFreezePeriodApi,
    unFreezePeriodApi,
    loading,
    allPeriods,
    isPeriodChangeFromProjectList,
    setIsPeriodChangeFromProjectListApi,
  }
}
export default useMasterPeriod

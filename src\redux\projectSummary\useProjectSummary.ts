import { useDispatch, useSelector } from 'react-redux'
import {
  setColumnsWidth,
  setColumnVisibilities,
  setFilterData,
  setLocalFilteredData,
  setProjectFilter,
  setProjectFilterValue,
  setRecentProjectData,
} from '.'
import { IRecentProject } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useProjectSummary = () => {
  const dispatch: AppDispatch = useDispatch()

  const filterData = useSelector((state: RootState) => state.projectSummary.filterData)
  const columnsWidth = useSelector((state: RootState) => state.projectSummary.columnsWidth)
  const projectFilter = useSelector((state: RootState) => {
    return state.projectSummary.projectFilter
  })
  const projectFilterValue = useSelector((state: RootState) => state.projectSummary.projectFilterValue)
  const localFilteredData = useSelector((state: RootState) => state.projectSummary.localFilteredData)
  const columnVisibilities = useSelector((state: RootState) => state.projectSummary.columnVisibilities)
  const recentProject = useSelector((state: RootState) => state.projectSummary.recentProject)

  const setFilterDataApi = (data: any) => dispatch(setFilterData(data))
  const setProjectFilterApi = (data: any) => dispatch(setProjectFilter(data))
  const setRecentProject = (data: IRecentProject) => dispatch(setRecentProjectData(data))
  const setProjectFilterValueApi = (data: any) => dispatch(setProjectFilterValue(data))
  const setLocalFilteredDataApi = (data: any) => dispatch(setLocalFilteredData(data))
  const setColumnVisibilitiesApi = (data: any) => dispatch(setColumnVisibilities(data))
  const setColumnsWidthApi = (data: any) => dispatch(setColumnsWidth(data))

  return {
    columnsWidth,
    filterData,
    localFilteredData,
    recentProject,
    projectFilter,
    projectFilterValue,
    columnVisibilities,

    setColumnsWidthApi,
    setColumnVisibilitiesApi,
    setLocalFilteredDataApi,
    setFilterDataApi,
    setRecentProject,
    setProjectFilterApi,
    setProjectFilterValueApi,
  }
}
export default useProjectSummary

import React, { useMemo, useState } from 'react'
import { format, parseISO } from 'date-fns'
import CellDatePicker from './CellDatepicker'
import ConfirmValueChangeModal from './confirmValueChangeModal'
import styles from './EditableCell.module.scss'
import ComboBox from '../../combobox'
import MultiAutoSelect from '../../multiAutoSelect'
import MultiSearchAutoSelect from '../../multiAutoSelect/multiAutoSelect'
import NumberInputField from '../../numberInputField'
import Textarea from '../../textArea'
import TextInputField from '../../textInputField'
import RichTextEditor from '@/src/component/richTextEditor'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { getValue, dateNonRequiredFields } from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate } from '@/src/utils/dateUtils'
import { isLooseValueChanged } from '@/src/utils/stringUtils'

interface EditableCellProps {
  value: string | number | null
  defaultValue: string | number | null
  column: any
  onBlur?: () => void
  onDatePickerBlur?: (value: any) => void
  cell: any
  setValue: (value: any) => void
  setActiveEditableCell?: Function
}

const EditableCell: React.FC<EditableCellProps> = ({
  value,
  defaultValue,
  column,
  onBlur,
  onDatePickerBlur,
  setValue,
  cell,
  setActiveEditableCell,
}) => {
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [filterOptions, setFilterOptions] = useState<any>({
    [column.columnDef.accessorKey]: '',
  })

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value)
  }

  const handleFieldBlur = () => {
    const confirmationRequired =
      typeof column.columnDef.isSaveConfirmationRequired === 'function'
        ? column.columnDef.isSaveConfirmationRequired(cell, value)
        : column.columnDef.isSaveConfirmationRequired
    if (confirmationRequired) {
      const isChanged = isLooseValueChanged(value, defaultValue)
      isChanged ? setIsConfirmation(true) : resetToDefaultValue()
    } else {
      onBlur?.()
    }
  }

  const formattedDateValue = useMemo(() => {
    return cell?.column?.columnDef.editableType === 'date' && value
      ? convertDDMMYYYYToLongDate(value as string) || 'invalid date'
      : 'invalid date'
  }, [value])

  const resetToDefaultValue = () => {
    const { editableType } = cell?.column?.columnDef
    setValue(defaultValue)
    setIsConfirmation(false)
    setActiveEditableCell && setActiveEditableCell(null)
    // setValue(editableType === 'date' ? null : (defaultValue ?? null))
  }

  const renderEditableInput = () => {
    switch (cell?.column?.columnDef.editableType) {
      case 'date':
        return (
          <CellDatePicker
            placeholder="dd-MM-yyyy"
            className={styles.dataPickerInput}
            value={formattedDateValue}
            onChange={(val) => {
              if (val == 'Invalid Date' || val === null || !val) return setValue(null)
              const formattedDate = format(parseISO(val.toISOString()), 'dd-MM-yyyy')
              setValue(formattedDate)
            }}
            onAccept={(val) => {
              if (val == 'Invalid Date' || val === null || !val) {
                if (
                  column?.columnDef?.require ||
                  dateNonRequiredFields(column?.columnDef.accessorKey, cell?.column.id?.toString())
                ) {
                  onBlur && onBlur()
                }
                setSelectedDate(null)
              }
              const formattedDate =
                val == 'Invalid Date' ? null : val ? format(parseISO(val.toISOString()), 'dd-MM-yyyy') : null
              setSelectedDate(formattedDate)
              if (column.columnDef.isSaveConfirmationRequired) {
                const isChanged = isLooseValueChanged(formattedDate, defaultValue)
                isChanged ? setIsConfirmation(true) : resetToDefaultValue()
              } else {
                onBlur?.()
              }
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'transparent',
                width: cell?.column?.getSize() - 20,
                textAlign: cell?.column?.columnDef?.align || 'left',
                flex: cell?.column?.columnDef.flex,
                cursor: cell?.column?.columnDef.cursor,
                borderTop: 0,
                borderRight: 0,
                borderLeft: 0,
                borderRadius: 0,
              },
              '& .MuiOutlinedInput-input': {
                padding: '6px 0px 6px 0px',
                fontWeight: '400',
                fontSize: '12px',
                lineHeight: '18px',
              },
              '& .Mui-focused .MuiOutlinedInput-root': {
                backgroundColor: '#f4f4fc',
              },
              '& .Mui-error': {
                borderBottom: '1px solid #ffffff',
              },
            }}
          />
        )
      case 'number':
        return (
          <NumberInputField
            minusValue={true}
            isUpAndDowns={false}
            className={`${styles.numberField} ${cell?.column?.columnDef?.align === 'left' && styles.center}`}
            value={value}
            onChange={setValue}
            onBlur={handleFieldBlur}
            sx={{
              '& .MuiOutlinedInput-input': {
                width: cell?.column?.getSize(),
                textAlign: cell?.column?.columnDef?.align || 'left',
                flex: cell?.column?.columnDef.flex,
                cursor: cell?.column?.columnDef.cursor,
              },
            }}
          />
        )
      case 'text':
        return (
          <TextInputField
            value={value}
            className={styles.textField}
            onChange={handleTextChange}
            onBlur={handleFieldBlur}
            onKeyDown={(event) => event.key === 'Enter' && handleFieldBlur()}
            variant={'outlined'}
            InputProps={{
              sx: {
                '& .MuiOutlinedInput-input': {
                  width: cell?.column?.getSize(),
                  textAlign: cell?.column?.columnDef?.align || 'left',
                  flex: cell?.column?.columnDef.flex,
                  cursor: cell?.column?.columnDef.cursor,
                },
              },
            }}
          />
        )
      case 'textArea':
        return (
          <Textarea
            className={styles.textArea}
            value={value as string}
            onChange={(e) => handleTextChange(e as any)}
            onBlur={handleFieldBlur}
          />
        )
      case 'richTextEditor':
        return (
          <RichTextEditor
            value={value as string}
            handleChange={(val) => setValue(val)}
            isEdit={true}
            onBlur={handleFieldBlur}
          />
        )
      case 'dropDown':
        return (
          <ComboBox
            className={styles.comboBox}
            value={getValue(cell?.column?.columnDef?.editOption, value) || { label: value as string, value }}
            onChange={(val) => setValue(val?.value)}
            onBlur={handleFieldBlur}
            options={cell?.column?.columnDef?.editableType ? cell?.column?.columnDef?.editOption : []}
          />
        )
      case 'multiDropDown':
        let options = cell?.column?.columnDef?.editOption
        options = options?.map((item: any) => {
          return {
            name: item,
            id: item,
          }
        })
        let displayValue = value
          ?.toString()
          .split(MULTI_SELECT_SEPARATOR)
          ?.filter((res) => res)
        if (!displayValue) {
          return null
        }
        return (
          <MultiSearchAutoSelect
            value={displayValue}
            handleSelectedOption={(val) => setValue(val.join(MULTI_SELECT_SEPARATOR))}
            options={options}
            isSubOption={false}
            onBlur={handleFieldBlur}
            isSx={false}
            inputValue={filterOptions[column.columnDef.accessorKey]}
            setInputValue={(val: any) => setFilterOptions({ ...filterOptions, [column.columnDef.accessorKey]: val })}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      {renderEditableInput()}
      <ConfirmValueChangeModal
        open={column.columnDef.isSaveConfirmationRequired && isConfirmation}
        header={column.columnDef.header}
        value={cell?.column?.columnDef.editableType === 'date' ? selectedDate : value}
        message={
          cell?.column?.columnDef?.validationMessage ? cell?.column?.columnDef?.validationMessage(cell, value) : ''
        }
        onClose={resetToDefaultValue}
        handleConfirm={() => {
          cell?.column?.columnDef.editableType === 'date' ? onDatePickerBlur?.(selectedDate) : onBlur?.()
        }}
      />
    </>
  )
}

export default EditableCell

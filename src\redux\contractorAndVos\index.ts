import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetContractorAndVosStates, IContractorAndVosStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IContractorAndVosStates = {
  getContractorAndVosStatus: StatusEnum.Idle,
  addContractorAndVosStatus: StatusEnum.Idle,
  deleteContractorAndVosStatus: StatusEnum.Idle,
  updateContractorAndVosStatus: StatusEnum.Idle,
  contractorAndVoses: [],
  contractorAndVo: {},
}

export const getContractorAndVos = createAsyncThunk(
  '/get-contract-vo-status',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGet<PERSON>o<PERSON><PERSON>(
        `/contract-vo-status?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addContractorAndVos = createAsyncThunk('/add-contract-vo-status', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/contract-vo-status`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateContractorAndVos = createAsyncThunk(
  '/update-contract-vo-status',
  async (data: { id: number; data: any }, thunkAPI) => {
    const contract = data.data
    delete contract.id
    try {
      const response = await ApiPutNoAuth(`/contract-vo-status/${data.id}`, contract)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteContractorAndVos = createAsyncThunk('/delete-contract-vo-status', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/contract-vo-status/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const contractorAndVosSlice = createSlice({
  name: 'contractorAndVos',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getContractorAndVos
    builder.addCase(getContractorAndVos.pending, (state) => {
      state.getContractorAndVosStatus = StatusEnum.Pending
    })
    builder.addCase(getContractorAndVos.fulfilled, (state, action) => {
      state.getContractorAndVosStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetContractorAndVosStates
      state.contractorAndVoses = actionPayload.data
    })
    builder.addCase(getContractorAndVos.rejected, (state) => {
      state.getContractorAndVosStatus = StatusEnum.Failed
    })
    //addContractorAndVos
    builder.addCase(addContractorAndVos.pending, (state) => {
      state.addContractorAndVosStatus = StatusEnum.Pending
    })
    builder.addCase(addContractorAndVos.fulfilled, (state, action) => {
      state.addContractorAndVosStatus = StatusEnum.Success
    })
    builder.addCase(addContractorAndVos.rejected, (state) => {
      state.addContractorAndVosStatus = StatusEnum.Failed
    })
    //updateContractorAndVos
    builder.addCase(updateContractorAndVos.pending, (state) => {
      state.updateContractorAndVosStatus = StatusEnum.Pending
    })
    builder.addCase(updateContractorAndVos.fulfilled, (state, action) => {
      state.updateContractorAndVosStatus = StatusEnum.Success
    })
    builder.addCase(updateContractorAndVos.rejected, (state) => {
      state.updateContractorAndVosStatus = StatusEnum.Failed
    })
    //deleteContractorAndVos
    builder.addCase(deleteContractorAndVos.pending, (state) => {
      state.deleteContractorAndVosStatus = StatusEnum.Pending
    })
    builder.addCase(deleteContractorAndVos.fulfilled, (state, action) => {
      state.deleteContractorAndVosStatus = StatusEnum.Success
    })
    builder.addCase(deleteContractorAndVos.rejected, (state) => {
      state.deleteContractorAndVosStatus = StatusEnum.Failed
    })
  },
})

export const {} = contractorAndVosSlice.actions

// Export the reducer
export default contractorAndVosSlice.reducer

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './RiskDiscipline.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateRiskDiscipline,
  useDeleteRiskDiscipline,
  useGetRiskDiscipline,
  useUpdateRiskDiscipline,
} from '@/src/hooks/useRiskDiscipline'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddRiskDiscipline: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { riskDiscipline } = drawerStates
  //REACT-QUERY
  const { riskDisciplines } = useGetRiskDiscipline(riskDiscipline)
  // MUTATIONS
  const { mutate: addRiskDiscipline } = useCreateRiskDiscipline()
  const { mutate: deleteRiskDiscipline } = useDeleteRiskDiscipline()
  const { mutate: updateRiskDiscipline } = useUpdateRiskDiscipline()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { riskDiscipline: '' },
    onSubmit: async (values) => {
      const payload = { risk_discipline: values.riskDiscipline }
      if (editIndex !== null) {
        updateRiskDiscipline(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addRiskDiscipline(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteRiskDiscipline = async (id: number) => {
    deleteRiskDiscipline(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editRiskDiscipline = riskDisciplines.find((riskDiscipline) => riskDiscipline.id === id)
    if (editRiskDiscipline) {
      formik.setValues({ riskDiscipline: editRiskDiscipline.risk_discipline })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'risk_discipline',
      header: 'Risk Discipline',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: any) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.row.id)} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Risk Discipline'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="riskDiscipline"
              labelText={'Risk Discipline'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.riskDiscipline}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskDiscipline?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskDiscipline?.trim()}
              >
                {'+ Add Risk Discipline'}
              </Button>
            )}
          </div>
        </form>
        {riskDisciplines.length >= 1 ? (
          // <Table data={riskDisciplines} columns={columns} />
          <TanStackTable
            rows={sortData(riskDisciplines, 'risk_discipline') as any}
            columns={columns}
            isOverflow={true}
          />
        ) : (
          ''
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteRiskDiscipline(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddRiskDiscipline

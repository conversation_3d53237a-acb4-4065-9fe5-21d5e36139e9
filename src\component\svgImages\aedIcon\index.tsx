import React, { ReactElement, forwardRef } from 'react'

interface AedIconProps {
  className?: string
  onClick?: (e: any) => void
}

const AedIcon: React.FC<AedIconProps> = forwardRef<SVGSVGElement, AedIconProps>((props, ref): ReactElement => {
  return (
    <svg width="23" height="18" viewBox="0 0 23 9" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        d="M6.00667 7.14H2.35867L1.68667 9H0.534672L3.55867 0.684H4.81867L7.83067 9H6.67867L6.00667 7.14ZM5.69467 6.252L4.18267 2.028L2.67067 6.252H5.69467ZM10.2406 1.524V4.32H13.2886V5.22H10.2406V8.1H13.6486V9H9.14861V0.624H13.6486V1.524H10.2406ZM17.905 0.636C18.817 0.636 19.605 0.808 20.269 1.152C20.941 1.488 21.453 1.972 21.805 2.604C22.165 3.236 22.345 3.98 22.345 4.836C22.345 5.692 22.165 6.436 21.805 7.068C21.453 7.692 20.941 8.172 20.269 8.508C19.605 8.836 18.817 9 17.905 9H15.301V0.636H17.905ZM17.905 8.1C18.985 8.1 19.809 7.816 20.377 7.248C20.945 6.672 21.229 5.868 21.229 4.836C21.229 3.796 20.941 2.984 20.365 2.4C19.797 1.816 18.977 1.524 17.905 1.524H16.393V8.1H17.905Z"
        fill="black"
      />
    </svg>
  )
})
AedIcon.displayName = 'AedIcon'

export default AedIcon

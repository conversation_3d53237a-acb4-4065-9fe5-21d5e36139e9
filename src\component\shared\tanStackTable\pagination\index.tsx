import React, { useEffect, useState } from 'react'
import {
  FirstPage as FirstPageIcon,
  LastPage as LastPageIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material'
import { Pagination, Typography } from '@mui/material'
import styles from './Pagination.module.scss'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'

interface PaginationProps {
  table: any
}

const PaginationComponent: React.FC<PaginationProps> = ({ table }) => {
  const [inputPage, setInputPage] = useState(table.getState().pagination.pageIndex + 1)
  const pageIndex = table.getState().pagination.pageIndex
  const pageCount = table.getPageCount()
  const rowCount = table.getRowCount()
  const breakpoint = useBreakpoint() // Get current breakpoint

  useEffect(() => {
    setInputPage(pageIndex + 1)
  }, [pageIndex])

  return (
    <div className={styles.paginationContainer}>
      {/* Page Info */}
      <Typography variant="body2" className={styles.pageInfo}>
        Page <strong>{pageIndex + 1}</strong> of {pageCount.toLocaleString()}
      </Typography>

      {/* Pagination Controls */}
      <div className={styles.summaryContainer}>
        <div className={styles.paginationControls}>
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className={styles.pageButton}
          >
            <FirstPageIcon fontSize={breakpoint === 'mobile' ? 'small' : 'medium'} />
          </button>

          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className={styles.pageButton}
          >
            <NavigateBeforeIcon fontSize={breakpoint === 'mobile' ? 'small' : 'medium'} />
          </button>

          <Pagination
            count={pageCount}
            hideNextButton={true}
            hidePrevButton={true}
            page={pageIndex + 1}
            onChange={(_, value) => table.setPageIndex(value - 1)}
            size={breakpoint === 'mobile' ? 'small' : 'medium'}
            shape="rounded"
            className={styles.pagination}
          />

          <button onClick={() => table.nextPage()} disabled={!table.getCanNextPage()} className={styles.pageButton}>
            <NavigateNextIcon fontSize={breakpoint === 'mobile' ? 'small' : 'medium'} />
          </button>

          <button
            onClick={() => table.setPageIndex(pageCount - 1)}
            disabled={!table.getCanNextPage()}
            className={styles.pageButton}
          >
            <LastPageIcon fontSize={breakpoint === 'mobile' ? 'small' : 'medium'} />
          </button>
        </div>
        {/* Summary */}
        <Typography variant="body2" className={styles.summary}>
          Showing {table.getRowModel().rows.length.toLocaleString()} of {rowCount.toLocaleString()} Rows
        </Typography>
      </div>
    </div>
  )
}

export default PaginationComponent

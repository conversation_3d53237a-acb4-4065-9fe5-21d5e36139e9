import React, { useEffect, useRef, useState, useCallback, memo } from 'react'
import { Divider, Popover, Tooltip } from '@mui/material'
import Image from 'next/image'
import styles from './AvatarPicker.module.scss'
import ManagerIcon from '../../svgImages/managerIcon'
import { AvatarItem, IAvatar } from '@/src/redux/avatars/interface'
import useAvatar from '@/src/redux/avatars/useAvatar'

interface IAvatarPickerProps {
  url: string[]
  handleSelectAvatar: (args: string[]) => void
  disabled?: boolean
  multiSelect?: boolean
  isManager?: boolean
}

// Memoized individual avatar component to prevent re-renders
const AvatarImage = memo(
  ({ src, onClick, isActive, size = 45 }: { src: string; onClick?: () => void; isActive?: boolean; size?: number }) => (
    <div className={isActive ? styles.imgContainer : ''}>
      <Image
        src={src}
        height={size}
        width={size}
        style={{ borderRadius: '25px' }}
        className={isActive ? styles.avatarBorder : ''}
        onClick={onClick}
        alt="Avatar"
      />
    </div>
  ),
)

AvatarImage.displayName = 'AvatarImage'

// Memoized avatar category component
const CategoryAvatar = memo(
  ({ category, isActive, onSelect }: { category: IAvatar; isActive: boolean; onSelect: () => void }) => (
    <div className={styles.childAvatar}>
      <Tooltip title={category?.categoryName} arrow>
        <div className={`${styles.imgContainer} ${isActive ? styles.avatarBorder : ''}`}>
          <Image
            height={45}
            width={45}
            src={category.avatars[0]?.url}
            onClick={onSelect}
            alt={category.categoryName || 'Category'}
          />
        </div>
      </Tooltip>
    </div>
  ),
)

CategoryAvatar.displayName = 'CategoryAvatar'

// Memoized avatar grid item component
const AvatarGridItem = memo(
  ({
    avatar,
    onSelect,
    formatString,
  }: {
    avatar: AvatarItem
    onSelect: () => void
    formatString: (str: string) => string
  }) => (
    <div className={styles.childAvatar}>
      <Tooltip title={formatString(avatar.name)} arrow>
        <Image
          height={45}
          width={45}
          src={avatar?.url}
          className={styles.avatarItem}
          onClick={onSelect}
          alt={formatString(avatar.name) || 'Avatar'}
        />
      </Tooltip>
    </div>
  ),
)

AvatarGridItem.displayName = 'AvatarGridItem'

const AvatarPicker: React.FC<IAvatarPickerProps> = ({
  url,
  handleSelectAvatar,
  disabled = false,
  multiSelect = false,
  isManager = false,
}) => {
  const { allAvatars } = useAvatar()
  const [categories, setCategories] = useState<IAvatar[]>([])
  const [filteredAvatars, setFilteredAvatars] = useState<AvatarItem[]>([])
  const [activeCategory, setActiveCategory] = useState<IAvatar | null>(null)
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)

  // Use useRef to track selected avatars to avoid unnecessary re-renders
  const selectedAvatarsRef = useRef<string[]>(url?.length ? [...url] : [])
  const [selectedAvatars, setSelectedAvatars] = useState<string[]>(selectedAvatarsRef.current)

  // Use useCallback for all event handlers to prevent re-creation on every render
  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (disabled) return
      setAnchorEl(event.currentTarget)

      // Only fetch avatars if needed
      if (categories.length === 0) {
        setCategories(allAvatars || [])
        setActiveCategory(allAvatars[0] || null)
      }
    },
    [disabled, allAvatars, categories.length],
  )

  const handleClose = useCallback(() => {
    setAnchorEl(null)
  }, [])

  const handleAvatarSelect = useCallback(
    (avatarUrl: string) => {
      let updatedAvatars = [...selectedAvatarsRef.current]

      if (multiSelect) {
        if (updatedAvatars.includes(avatarUrl)) {
          updatedAvatars = updatedAvatars.filter((av) => av !== avatarUrl)
        } else {
          updatedAvatars.push(avatarUrl)
        }
      } else {
        updatedAvatars = [avatarUrl]
      }

      selectedAvatarsRef.current = updatedAvatars
      setSelectedAvatars([...updatedAvatars])
      handleSelectAvatar(updatedAvatars)

      if (!multiSelect) {
        handleClose()
      }
    },
    [multiSelect, handleSelectAvatar, handleClose],
  )

  // Update filtered avatars when active category changes
  useEffect(() => {
    if (activeCategory && activeCategory.avatars) {
      setFilteredAvatars(activeCategory.avatars)
    }
  }, [activeCategory])

  // Update selectedAvatars when url prop changes
  useEffect(() => {
    if (url && JSON.stringify(url) !== JSON.stringify(selectedAvatarsRef.current)) {
      selectedAvatarsRef.current = [...url]
      setSelectedAvatars([...url])
    }
  }, [url])

  const formatString = useCallback((str: string): string => {
    if (!str) return ''
    return str.replace(/_/g, ' ')
  }, [])

  const handleCategorySelect = useCallback((category: IAvatar) => {
    setActiveCategory(category)
  }, [])

  const open = Boolean(anchorEl)

  return (
    <div className={styles.avatarPicker}>
      <div className={`${styles.avatarStack} ${!disabled ? styles.activeAvatar1 : ''}`} onClick={handleClick}>
        {selectedAvatars.length === 0 && <ManagerIcon color="#ffffff" />}

        {selectedAvatars.slice(0, 3).map((avatar, index) => (
          <div
            key={`avatar-${index}`}
            style={{
              zIndex: 3 - index,
              borderRadius: '25px',
              height: '45px',
              width: '45px',
              marginLeft: index > 0 ? -10 : 0,
            }}
          >
            {avatar ? (
              <Image src={avatar} height={45} width={45} style={{ borderRadius: '25px' }} alt="Avatar" />
            ) : (
              <ManagerIcon color="#ffffff" />
            )}
          </div>
        ))}

        {selectedAvatars.length > 3 && <span className={styles.avatarLength}>{`+${selectedAvatars.length - 3}`}</span>}
      </div>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        disableRestoreFocus
      >
        <div className={styles.avatarPopup}>
          <div className={styles.avatarGrid}>
            {filteredAvatars.map((avatar, index) => (
              <AvatarGridItem
                key={`${avatar.name}-${index}`}
                avatar={avatar}
                onSelect={() => handleAvatarSelect(avatar.url)}
                formatString={formatString}
              />
            ))}
          </div>

          <Divider sx={{ marginTop: '10px' }} />

          <div className={styles.avatarCategories}>
            {categories.map((category, i) => (
              <CategoryAvatar
                key={`category-${i}`}
                category={category}
                isActive={activeCategory?.categoryName === category.categoryName}
                onSelect={() => handleCategorySelect(category)}
              />
            ))}
          </div>
        </div>
      </Popover>
    </div>
  )
}

export default memo(AvatarPicker)

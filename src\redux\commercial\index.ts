import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetCommercialsResponse, IConsultantStatus, ICommercial } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IConsultantStatus = {
  getCommercialStatus: StatusEnum.Idle,
  addCommercialStatus: StatusEnum.Idle,
  deleteCommercialStatus: StatusEnum.Idle,
  updateCommercialStatus: StatusEnum.Idle,
  commercials: [],
  commercial: {
    period: '',
    project_name: '',
    description: '',
    budget: 0,
    committed_cost: 0,
    vowd: 0,
    paid_amount: 0,
    last_updated: new Date(),
    construction_budget: 0,
    supervision_budget: 0,
    management_fees: 0,
    contingency: 0,
    year_planned_vowd: 0,
    year_forecast_vowd: 0,
  },
}

export const getCommercial = createAsyncThunk(
  '/get-commercial-status',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(
        `/commercial-status?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addCommercial = createAsyncThunk('/add-commercial-status', async (data: ICommercial, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/commercial-status`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateCommercial = createAsyncThunk(
  '/update-commercial-status',
  async ({ id, data }: { id: number; data: ICommercial }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/commercial-status/${id}`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteCommercial = createAsyncThunk('/delete-commercial-status', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/commercial-status/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const commercialSlice = createSlice({
  name: 'commercial',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getCommercial
    builder.addCase(getCommercial.pending, (state) => {
      state.getCommercialStatus = StatusEnum.Pending
    })
    builder.addCase(getCommercial.fulfilled, (state, action) => {
      state.getCommercialStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetCommercialsResponse
      state.commercials = actionPayload.data
    })
    builder.addCase(getCommercial.rejected, (state) => {
      state.getCommercialStatus = StatusEnum.Failed
    })
    //addCommercial
    builder.addCase(addCommercial.pending, (state) => {
      state.addCommercialStatus = StatusEnum.Pending
    })
    builder.addCase(addCommercial.fulfilled, (state, action) => {
      state.addCommercialStatus = StatusEnum.Success
    })
    builder.addCase(addCommercial.rejected, (state) => {
      state.addCommercialStatus = StatusEnum.Failed
    })
    //updateCommercial
    builder.addCase(updateCommercial.pending, (state) => {
      state.updateCommercialStatus = StatusEnum.Pending
    })
    builder.addCase(updateCommercial.fulfilled, (state, action) => {
      state.updateCommercialStatus = StatusEnum.Success
    })
    builder.addCase(updateCommercial.rejected, (state) => {
      state.updateCommercialStatus = StatusEnum.Failed
    })
    //deleteCommercial
    builder.addCase(deleteCommercial.pending, (state) => {
      state.deleteCommercialStatus = StatusEnum.Pending
    })
    builder.addCase(deleteCommercial.fulfilled, (state, action) => {
      state.deleteCommercialStatus = StatusEnum.Success
    })
    builder.addCase(deleteCommercial.rejected, (state) => {
      state.deleteCommercialStatus = StatusEnum.Failed
    })
  },
})

export const {} = commercialSlice.actions

// Export the reducer
export default commercialSlice.reducer

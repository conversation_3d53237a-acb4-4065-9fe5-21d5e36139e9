import { ReactElement } from 'react'
import { Checkbox as <PERSON>iCheck<PERSON>, CheckboxProps } from '@mui/material'
import CheckboxIcon from '@/src/component/svgImages/checkBoxIcon'
import CheckBoxSuccessIcon from '@/src/component/svgImages/checkBoxSuccessIcon'

const Checkbox: React.FC<CheckboxProps> = ({
  icon,
  checkedIcon,
  checked,
  onChange,
  ...rest
}: CheckboxProps): ReactElement => {
  return (
    <MuiCheckbox
      {...rest}
      checked={checked} // Pass checked state
      onChange={onChange} // Pass onChange handler
      icon={icon ? icon : <CheckboxIcon />}
      checkedIcon={checkedIcon ? checkedIcon : <CheckBoxSuccessIcon />}
      sx={{ '&:hover': { backgroundColor: 'white' }, '& .MuiTouchRipple-root': { display: 'none' } }}
    />
  )
}

export default Checkbox

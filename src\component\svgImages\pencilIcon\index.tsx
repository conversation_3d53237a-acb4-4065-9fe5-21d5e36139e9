import React, { ReactElement, forwardRef } from 'react'

interface PencilIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const PencilIcon: React.FC<PencilIconProps> = forwardRef<SVGSVGElement, PencilIconProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} ref={ref}>
      <g clipPath="url(#clip0_1828_45509)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.83841 1.74828C11.0575 0.529218 13.034 0.529218 14.253 1.74828C15.4721 2.96735 15.4721 4.94384 14.253 6.16291L7.9295 12.4864C7.56822 12.8477 7.35555 13.0604 7.11895 13.245C6.84009 13.4625 6.53835 13.649 6.21909 13.8011C5.94823 13.9302 5.66288 14.0253 5.17814 14.1869L2.95739 14.9271L2.42267 15.1054C1.98896 15.2499 1.5108 15.137 1.18753 14.8138C0.864265 14.4905 0.751386 14.0124 0.895955 13.5786L1.81444 10.8232C1.976 10.3384 2.07111 10.0531 2.2002 9.78222C2.35235 9.46297 2.53883 9.16123 2.75634 8.88236C2.94088 8.64576 3.15358 8.43309 3.5149 8.07179L9.83841 1.74828ZM2.93432 13.8807L4.82867 13.2493C5.35608 13.0735 5.58017 12.9979 5.78887 12.8984C6.04257 12.7775 6.28233 12.6293 6.50393 12.4565C6.68623 12.3143 6.85407 12.1477 7.24718 11.7545L12.2935 6.70823C11.7677 6.52279 11.0904 6.17907 10.4563 5.54499C9.82225 4.91091 9.47852 4.23358 9.29309 3.70782L4.24677 8.75414C3.85366 9.14725 3.68704 9.31508 3.54485 9.49739C3.37201 9.71898 3.22383 9.95875 3.10292 10.2124C3.00346 10.4212 2.92785 10.6452 2.75205 11.1726L2.1206 13.067L2.93432 13.8807ZM10.1042 2.89668C10.127 3.01332 10.1656 3.17188 10.2299 3.35723C10.3746 3.77453 10.6481 4.32257 11.1634 4.83788C11.6787 5.35319 12.2268 5.62667 12.6441 5.77145C12.8294 5.83576 12.988 5.87429 13.1046 5.89709L13.5459 5.4558C14.3745 4.62726 14.3745 3.28393 13.5459 2.45539C12.7174 1.62685 11.3741 1.62685 10.5455 2.45539L10.1042 2.89668Z"
          fill={props.color ? props.color : '#808080'}
        />
      </g>
      <defs>
        <clipPath id="clip0_1828_45509">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
})
PencilIcon.displayName = 'PencilIcon'

export default PencilIcon

import React, { useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { format, parseISO } from 'date-fns'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import * as Yup from 'yup'
import styles from './ContractAndVos.module.scss'
import ContractFields from './contractFields'
import DropDownIcon from '../../../svgImages/dropDownIcon'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TypographyField from '@/src/component/shared/typography'
import CancelRoundedIcon from '@/src/component/svgImages/cancelRoundedIcon'
import CommercialsDeleteIcon from '@/src/component/svgImages/commercialsDeleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { showCustomToast } from '@/src/component/toast/ToastManager'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useContractorAndVos from '@/src/redux/contractorAndVos/useContractorAndVos'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { getProjects } from '@/src/services/projects'
import { DDMMYYYYformate, getLastUpdatedTime } from '@/src/utils/dateUtils'
import { errorToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const ContractAndVos = ({
  contract,
  id,
  contractVosAccordion,
  setContractVosAccordion,
  index,
  contracts,
  setContracts,
  phase,
}: any) => {
  const { getContractorAndVosApi, updateContractorAndVosApi, addContractorAndVosApi, deleteContractorAndVosApi } =
    useContractorAndVos()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const router = useRouter()
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)

  //REACT_QUERYs
  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    select: (response) => response.data, // This extracts 'data' directly
  })

  const initialValuesMap = useMemo(() => {
    return contract[1].map((contract: any) => ({
      project_to_project_phase_ids: contract?.LookupProjectToPhase?.map((item: any) => item.id),
      description: contract?.description ?? null,
      master_contract_type_id: contract?.MasterContractType?.id ?? null,
      master_contractor_id: contract?.MasterContractor?.id ?? null,
      original_contract_value: contract?.original_contract_value ?? null,
      approved_vo_value: contract?.approved_vo_value ?? 0,
      revised_contract_value: contract?.revised_contract_value ?? null,
      pvr_value: contract?.pvr_value ?? 0,
      forecasted_contract_value: contract?.forecasted_contract_value ?? null,
      paid_amount: contract?.paid_amount ?? 0,
      id: contract?.id ?? null,
      contract_signature: contract?.contract_signature ?? null,
      bonds: contract?.bonds ?? null,
      insurance: contract?.insurance ?? null,
      advanced_payment: contract?.advanced_payment ?? null,
    }))
  }, [contract])

  const handleSubmit = async (values: any, contractItem: any) => {
    const todayDate = new Date().toISOString()
    const date = format(parseISO(todayDate), 'dd-MM-yyyy')
    const last_updated = DDMMYYYYformate(date) //issue
    if (contractItem?.id) {
      const editableContractItem = { ...contractItem }
      delete editableContractItem.id
      delete values.revised_contract_value
      delete values.forecasted_contract_value
      const payload = {
        id: contractItem?.id,
        data: {
          ...values,
          approved_vo_value: values?.approved_vo_value ? values?.approved_vo_value : null,
          master_contract_type_id: values?.master_contract_type_id ? values?.master_contract_type_id : null,
          master_contractor_id: values?.master_contractor_id ? values?.master_contractor_id : null,
          paid_amount: values?.paid_amount ? values?.paid_amount : null,
          description: values?.description ? values?.description : null,
          // forecasted_contract_value: values?.forecasted_contract_value
          //   ? values?.forecasted_contract_value
          //   : null,
          original_contract_value: values?.original_contract_value ? values?.original_contract_value : null,
          project_to_project_phase_ids: values?.project_to_project_phase_ids
            ? values?.project_to_project_phase_ids
            : null,
          pvr_value: values?.pvr_value ? values?.pvr_value : null,
          // revised_contract_value: values?.revised_contract_value ? values?.revised_contract_value : null,
          last_updated: last_updated,
          project_name: router.query.slug,
          period: currentPeriod,
        },
      }
      const res: Record<string, any> = await updateContractorAndVosApi(payload)
      if (res.payload.success)
        getContractorAndVosApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      else {
        errorToast(res.payload?.response?.data?.message || 'Something went wrong!')
      }
      setEditIndex(null)
    } else {
      delete values.id
      delete values.revised_contract_value
      delete values.forecasted_contract_value
      const res: Record<string, any> = await addContractorAndVosApi({
        // ...values,
        approved_vo_value: values?.approved_vo_value ? values?.approved_vo_value : null,
        master_contract_type_id: values?.master_contract_type_id ? values?.master_contract_type_id : null,
        master_contractor_id: values?.master_contractor_id ? values?.master_contractor_id : null,
        paid_amount: values?.paid_amount ? values?.paid_amount : null,
        description: values?.description ? values?.description : null,
        // forecasted_contract_value: values?.forecasted_contract_value
        // ? values?.forecasted_contract_value
        // : null,
        original_contract_value: values?.original_contract_value ? values?.original_contract_value : null,
        project_to_project_phase_ids: values?.project_to_project_phase_ids
          ? values?.project_to_project_phase_ids
          : null,
        pvr_value: values?.pvr_value ? values?.pvr_value : null,
        // revised_contract_value: values?.revised_contract_value ? values?.revised_contract_value : null,
        last_updated: last_updated,
        project_name: router.query.slug,
        period: currentPeriod,
      })
      if (res.payload.success)
        getContractorAndVosApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      else {
        const errorMessage = res?.payload?.response?.data?.message || res?.payload?.message || 'Something went wrong!'
        errorToast(errorMessage || 'Something went wrong!')
      }
    }
    formik.resetForm()
  }

  const validationSchema = Yup.object().shape({
    original_contract_value: Yup.number().typeError('Please enter a valid number'),
    approved_vo_value: Yup.number().typeError('Please enter a valid number'),
    // revised_contract_value: Yup.number().typeError('Please enter a valid number'),
    pvr_value: Yup.number().typeError('Please enter a valid number'),
    // forecasted_contract_value: Yup.number().typeError('Please enter a valid number'),
  })

  const formik = useFormik({
    initialValues: initialValuesMap[editIndex ?? 0],
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      if (editIndex !== null) {
        handleSubmit(values, contract[1][editIndex])
      }
    },
  })

  const handleDelete = async () => {
    if (deleteIndex === null) return
    const contractItem = contract[1][deleteIndex]
    if (contractItem?.id) {
      const res: Record<string, any> = await deleteContractorAndVosApi(contractItem?.id)
      if (!res.payload.success) {
        setShowDeleteConfirmation(false)
        errorToast(res.payload?.response?.data?.message || 'Something went wrong!')
        return
      }
      getContractorAndVosApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    } else {
      const newContractList = [...contracts]
      const newDataList = newContractList.slice(0, -1)
      setContracts(newDataList)
    }
    setShowDeleteConfirmation(false)
    setDeleteIndex(null)
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  //USE_FOR: Toggles the accordion state for a specific item based on its index.
  const handleAccordion = () => {
    const isAccordion = contractVosAccordion.find((item: any) => item?.index === index)

    if (!isAccordion) {
      setContractVosAccordion((prev: any) => [...prev, { index, isAccordion: true }])
    } else {
      const updated = contractVosAccordion.map((item: any) => {
        if (item.index === index) {
          return { ...item, isAccordion: !item.isAccordion }
        }
        return item
      })
      setContractVosAccordion(updated)
    }
  }

  //USE_FOR: Checks whether the current accordion is expanded or collapsed.
  const isAccordion = useMemo(
    () => contractVosAccordion?.some((item: any) => item.index === index && item.isAccordion === true),
    [contractVosAccordion, index],
  )

  const title = useMemo(() => {
    if (!Array.isArray(contract[1])) return '-'

    const phases = contract[1]
      .flatMap((item: any) =>
        Array.isArray(item?.LookupProjectToPhase) ? item.LookupProjectToPhase.map((res: any) => res?.phase) : [],
      )
      .filter((phase: any) => phase) // remove falsy values
      .filter((value, index, self) => self.indexOf(value) === index) // remove duplicates
      .join(', ')

    return phases || '-'
  }, [contract])

  return (
    <div className={styles.container} id={id}>
      <Accordion expanded={isAccordion} onChange={handleAccordion} classes={{ root: styles.accordion }}>
        <AccordionSummary
          expandIcon={<DropDownIcon className={styles.expandIcon} />}
          className={styles.summary}
          classes={{ content: styles.content }}
        >
          <div className={styles.summaryTitle}>
            <div className={styles.header}>
              {title} - {contract[0]}
            </div>
          </div>
        </AccordionSummary>
        {contract[1]?.map((contractItem: any, idx: number) => {
          const isEditing = editIndex === idx

          return (
            <AccordionDetails key={idx} className={styles.details}>
              <div className={styles.summaryTitle}>
                <div className={styles.crudIcon}>
                  <div className={styles.action}>
                    {isEditing ? (
                      <>
                        <Button
                          type="button"
                          startIcon={<CancelRoundedIcon />}
                          className={styles.addProjectButton}
                          color="secondary"
                          onClick={(e) => {
                            e.stopPropagation()
                            setEditIndex(null)
                          }}
                        >
                          Discard
                        </Button>
                        <Button
                          className={styles.btnOfAction}
                          startIcon={<SaveIcon />}
                          variant="contained"
                          onClick={(e) => {
                            e.stopPropagation()
                            formik.handleSubmit()
                          }}
                          disabled={!formik.dirty}
                        >
                          Save
                        </Button>
                      </>
                    ) : (
                      <Button
                        className={styles.btnOfAction}
                        startIcon={<EditIcon fill="#FFFFFF" />}
                        variant="contained"
                        onClick={(e) => {
                          e.stopPropagation()
                          if (!isEditForUser) {
                            toast(`The current reporting period is locked`, {
                              icon: <WarningAmberOutlined />,
                            })
                          } else {
                            setEditIndex(idx)
                            // set formik values based on this contractItem
                            formik.setValues(initialValuesMap[idx])
                          }
                        }}
                      >
                        Edit
                      </Button>
                    )}
                    <CommercialsDeleteIcon
                      className={styles.deleteIcon}
                      onClick={(e) => {
                        e.stopPropagation()
                        if (!isEditForUser) {
                          toast(`The current reporting period is locked`, {
                            icon: <WarningAmberOutlined />,
                          })
                        } else {
                          setDeleteIndex(idx)
                          setShowDeleteConfirmation(true)
                        }
                      }}
                    />
                  </div>

                  <div className={styles.timerIcon}>
                    <TypographyField
                      variant="bodyBold"
                      className={styles.lastUpdatedText}
                      text={contractItem.updated_by || ''}
                    />
                    <TypographyField
                      variant="thin"
                      className={styles.lastUpdatedText}
                      text={contractItem?.last_updated ? getLastUpdatedTime(contractItem?.last_updated) : ''}
                    />
                  </div>
                </div>
              </div>

              {isEditing ? (
                <ContractFields formik={formik} isEditMode />
              ) : (
                <ContractFields
                  formik={{ values: initialValuesMap[idx], getFieldProps: () => ({}) }}
                  isEditMode={false}
                />
              )}
            </AccordionDetails>
          )
        })}
      </Accordion>
      <ConfirmDeleteModal
        open={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        handleConfirm={() => handleDelete()}
      />
    </div>
  )
}

export default ContractAndVos

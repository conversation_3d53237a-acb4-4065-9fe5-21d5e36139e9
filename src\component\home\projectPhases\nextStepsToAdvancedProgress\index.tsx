import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './NextStepsToAdvancedProgress.module.scss'
import { DrawerStates } from '../../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateNstAdvancedProgresses,
  useDeleteNstAdvancedProgresses,
  useGetNstAdvancedProgresses,
  useUpdateNstAdvancedProgresses,
} from '@/src/hooks/useNextStepsToAdvancedProgress'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const NextStepsToAdvancedProgress: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({
  drawerStates,
  onClose,
}) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { nextStepsToAdvancedProgress } = drawerStates
  //REACT-QUERY
  const { nextStepsToAdvancedProgresses } = useGetNstAdvancedProgresses(nextStepsToAdvancedProgress)
  // MUTATIONS
  const { mutate: addNstAdvancedProgresses } = useCreateNstAdvancedProgresses()
  const { mutate: deleteNstAdvancedProgresses } = useDeleteNstAdvancedProgresses()
  const { mutate: updateNstAdvancedProgresses } = useUpdateNstAdvancedProgresses()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: {
      nextStepsToAdvanceProgress: '',
      nextStepsToAdvanceProgressCode: '',
    },
    onSubmit: async (values) => {
      const payload = {
        next_steps_to_advance_progress: values.nextStepsToAdvanceProgress,
        next_steps_to_advance_progress_code: values.nextStepsToAdvanceProgressCode,
      }
      if (editIndex !== null) {
        updateNstAdvancedProgresses(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addNstAdvancedProgresses(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteNstAdvancedProgresses(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editProgress = nextStepsToAdvancedProgresses.find((progress) => progress.id === id)
    formik.setValues({
      nextStepsToAdvanceProgress: editProgress?.next_steps_to_advance_progress || '',
      nextStepsToAdvanceProgressCode: editProgress?.next_steps_to_advance_progress_code || '',
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    { accessorKey: 'next_steps_to_advance_progress_code', header: 'Code', flex: 1 },
    { accessorKey: 'next_steps_to_advance_progress', header: 'Next Steps to Advance Progress', flex: 1 },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.row?.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text="Add Next Steps to Advance Progress"
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div className={styles.formGroup}>
            <TextInputField
              className={styles.field}
              name="nextStepsToAdvanceProgress"
              labelText="Next Steps to Advance Progress"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.nextStepsToAdvanceProgress}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.field}
              name="nextStepsToAdvanceProgressCode"
              labelText="Next Steps to Advance Progress Code"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.nextStepsToAdvanceProgressCode}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <Button
            className={styles.submitButton}
            type="submit"
            disabled={
              (!formik.values.nextStepsToAdvanceProgress && !formik.values.nextStepsToAdvanceProgressCode) ||
              formik.isSubmitting
            }
          >
            {editIndex !== null ? '+ Update Next Steps to Advance Progress' : '+ Add Next Steps to Advance Progress'}
          </Button>
        </form>

        {nextStepsToAdvancedProgresses?.length > 0 && (
          <TanStackTable
            rows={sortData(nextStepsToAdvancedProgresses, 'next_steps_to_advance_progress_code') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default NextStepsToAdvancedProgress

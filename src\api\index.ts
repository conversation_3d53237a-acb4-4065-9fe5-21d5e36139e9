import axios from 'axios'
import { IHeaderProps } from './interface'
import { StorageKeys } from '../constant/enum'
import { removeLocalStorageItem } from '../utils/storageUtils'

export const BaseURL = process.env.NEXT_PUBLIC_API_URL
// export const BaseURL = '/api/';

// To handle session expiration uniformly across all API requests, implement a mechanism in your common API utility files to manage HTTP 401 status codes. This approach ensures that session expiration is consistently addressed throughout your application.
const defaultHeaders: IHeaderProps = {
  isAuth: true,
  AdditionalParams: {},
  isJsonRequest: true,
  api_key: true,
  isFormData: false,
}

export const ApiPostNoAuth = (type: string, userData?: any, AdditionalHeader?: any) => {
  return new Promise((resolve, reject) => {
    axios
      .post(
        BaseURL + type,
        userData,
        getHttpOptions({
          ...defaultHeaders,
          ...AdditionalHeader,
          isAuth: false,
        }),
      )
      .then((response: any) => {
        if (response.data.statusCode === 400) {
          reject({ status: 400, message: response.data.body })
        } else {
          resolve(response.data)
        }
      })
      .catch((error: any) => {
        if (error?.response?.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.pathname = '/login'
          return
        }
        reject(error)
      })
  })
}

export const ApiPutNoAuth = (type: string, userData: any, AdditionalHeader?: any) => {
  return new Promise((resolve, reject) => {
    axios
      .put(BaseURL + type, userData, getHttpOptions({ ...defaultHeaders, ...AdditionalHeader, isAuth: false }))
      .then((response: any) => {
        resolve(response.data)
      })
      .catch((error: any) => {
        if (error?.response?.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.pathname = '/login'
          return
        } else if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiGetNoAuth = (type: string) => {
  return new Promise((resolve, reject) => {
    axios
      .get(BaseURL + type, getHttpOptions({ ...defaultHeaders, isAuth: false }))
      .then((response: any) => {
        resolve(response.data)
      })
      .catch((error: any) => {
        if (error?.response?.status === 401) {
          console.log('unauthorized error------: ', error)
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          if (error?.response?.data?.exitCode === 138) {
            reject(error?.response?.data)
          } else {
            window.location.pathname = '/login'
          }
        } else if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiDeleteNoAuth = (type: string) => {
  return new Promise((resolve, reject) => {
    axios
      .delete(BaseURL + type, getHttpOptions({ ...defaultHeaders, isAuth: false }))
      .then((response: any) => {
        resolve(response.data)
      })
      .catch((error) => {
        if (error?.response?.status === 401) {
          window.location.pathname = '/login'
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        } else if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiGet = (type: string) => {
  return new Promise((resolve, reject) => {
    axios
      .get(BaseURL + type, getHttpOptions())
      .then((response: any) => resolve(response.data))
      .catch((error: any) => {
        if (error.response?.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        }
        if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiPost = (type: string, userData?: any, AdditionalHeader?: any) => {
  return new Promise((resolve, reject) => {
    axios
      .post(BaseURL + type, userData, {
        ...getHttpOptions({
          ...defaultHeaders,
          ...AdditionalHeader,
        }),
      })
      .then((response: any) => resolve(response.data))
      .catch((error: any) => {
        if (error.response?.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        }
        if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiPut = (type: string, userData: any) => {
  return new Promise((resolve, reject) => {
    axios
      .put(BaseURL + type, userData, getHttpOptions())
      .then((response: any) => resolve(response.data))
      .catch((error: any) => {
        if (error.response.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        }
        if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiPatch = (type: string, userData: any) => {
  return new Promise((resolve, reject) => {
    axios
      .patch(BaseURL + type, userData, getHttpOptions())
      .then((response: any) => resolve(response.data))
      .catch((error: any) => {
        if (error.response.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        }
        if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiDelete = (type: string) => {
  return new Promise((resolve, reject) => {
    axios
      .delete(BaseURL + type, getHttpOptions())
      .then((response: any) => resolve(response.data))
      .catch((error) => {
        if (error.response.status === 401) {
          removeLocalStorageItem(StorageKeys.IS_LOGIN)
          window.location.reload()
        }
        if (error?.response?.data?.error) {
          reject(error.response.data.error)
        } else {
          reject(error)
        }
      })
  })
}

export const ApiGetBuffer = (url: string) => {
  return new Promise((resolve, reject) => {
    fetch(url, { method: 'GET', mode: 'no-cors' })
      .then((response) => {
        if (response.ok) {
          return response.arrayBuffer()
        } else {
          resolve(null)
        }
      })
      .then((buffer) => resolve(buffer))
      .catch((error) => reject(error))
  })
}

export const getHttpOptions = (options = defaultHeaders) => {
  let headers: any = {}
  if (options?.isAuth) {
    if (localStorage.getItem('token')) {
      headers['Authorization'] = 'Bearer ' + localStorage.getItem('token')
    }
  }
  if (options?.isJsonRequest && !options?.isFormData) {
    headers['Content-Type'] = 'application/json'
  }
  if (options?.isFormData) {
    headers['Content-Type'] = 'multipart/form-data'
  }
  if (options?.AdditionalParams) {
    headers = { ...headers, ...options.AdditionalParams }
  }
  headers['ngrok-skip-browser-warning'] = true //For ng-rock only
  return { headers }
}

import { FormikProps } from 'formik'

export interface ImageCardProps {
  label: string
  value: any
  formik?: FormikProps<any>
  fieldName: string
  isEditable?: boolean
  summaryEdit: any
  imageSrc?: string
  imageHeight: number | `${number}` | undefined
  imageWidth: number | `${number}` | undefined
  isFormattedNumber?: any
  isPercentage?: boolean
  className?: any
  onChange: (value: any) => void
}

import React, { useEffect, useState } from 'react'
import { motion, useTransform, useMotionValue, useSpring } from 'framer-motion'
import ReactDOM from 'react-dom'
import styles from './AnimatedTooltip.module.scss'

interface ITooltipProps {
  text: string
  children: React.ReactNode
  bgColor?: string
  textColor?: string
}

const TooltipPortal = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    return () => setMounted(false)
  }, [])

  if (!mounted) return null

  return ReactDOM.createPortal(children, document.body)
}

const AnimatedTooltip: React.FC<ITooltipProps> = ({
  text,
  children,
  bgColor = '#ffffff', // Default white background
  textColor = '#000000', // Default black text
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })
  const x = useMotionValue(0)
  const springConfig = { stiffness: 100, damping: 5 }
  const rotate = useSpring(useTransform(x, [-100, 100], [-45, 45]), springConfig)
  const translateX = useSpring(useTransform(x, [-100, 100], [-50, 50]), springConfig)

  const handleMouseEnter = (event: React.MouseEvent<HTMLDivElement>) => {
    setIsVisible(true)
    // Calculate and set the tooltip position
    if (event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect()
      setTooltipPosition({
        top: rect.top - 5,
        left: rect.left + rect.width / 2,
      })
    }
  }

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const halfWidth = event.currentTarget.offsetWidth / 2
    x.set(event.nativeEvent.offsetX - halfWidth) // set the x value, which is then used in transform and rotate
  }

  const handleMouseLeave = () => {
    setIsVisible(false)
  }

  return (
    <div onMouseEnter={handleMouseEnter} onMouseMove={handleMouseMove} onMouseLeave={handleMouseLeave}>
      {children}
      {isVisible && (
        <TooltipPortal>
          <div
            className={styles.portalTooltip}
            style={{
              position: 'fixed',
              top: `${tooltipPosition.top}px`,
              left: `${tooltipPosition.left}px`,
              transform: 'translate(-50%, -100%)',
              zIndex: 9999,
            }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.6 }}
              animate={{ opacity: 1, y: 0, scale: 1, transition: { type: 'spring', stiffness: 260, damping: 10 } }}
              exit={{ opacity: 0, y: 20, scale: 0.6 }}
              style={{
                translateX,
                rotate,
                whiteSpace: 'nowrap',
                backgroundColor: bgColor, // Apply dynamic background color
              }}
              className={styles.tooltip}
            >
              <div className={styles.tooltipGradient1}></div>
              <div className={styles.tooltipGradient2}></div>
              <div
                style={{
                  color: textColor, // Apply dynamic text color
                }}
                className={styles.tooltipText}
              >
                {text}
              </div>
            </motion.div>
          </div>
        </TooltipPortal>
      )}
    </div>
  )
}

export default AnimatedTooltip

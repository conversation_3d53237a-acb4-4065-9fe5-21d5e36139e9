import React, { Suspense, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Toaster } from 'sonner'
import styles from './Layout.module.scss'
import PageLayout from '../component/pageLayout'
import Loader from '../component/shared/loader'
import { Routes, StorageKeys } from '../constant/enum'
import { getLocalStorageItem } from '../utils/storageUtils'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }: LayoutProps) => {
  const router = useRouter()

  const shouldRenderPageLayout = () => {
    const currentPath = router.pathname
    const excludedPaths = [Routes.LOGIN, Routes.REDIRECT]
    return !excludedPaths.includes(currentPath)
  }

  useEffect(() => {
    const currentPath = router.pathname
    if (currentPath === Routes.LOGIN) {
      const loginStatus = getLocalStorageItem(StorageKeys.IS_LOGIN)
      if (loginStatus) {
        router.push(`${Routes.HOME}`)
      }
    }
  }, [router])

  return (
    <div className={styles.layoutContainer}>
      <Suspense fallback={<Loader />}>
        <Toaster position="bottom-right" duration={5000} />
        {shouldRenderPageLayout() ? <PageLayout>{children}</PageLayout> : <>{children}</>}
      </Suspense>
    </div>
  )
}

export default Layout

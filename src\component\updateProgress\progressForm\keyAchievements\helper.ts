export const fetchKeyAchievements = async (getKeyAchievementsApi: any, currentPeriod: any, projectName: any) => {
  const response = await getKeyAchievementsApi({ period: currentPeriod, project_name: projectName as string })
  return response.payload.success ? response : null
}

export const isValueBetween = (value: any, min: any, max: any) => {
  return Number(value) >= Number(min) && Number(value) <= Number(max)
}

export const reorderKeyAchievements = (keyAchievements: any, dragItem: any, dropItem: any) => {
  const min = Math.min(dragItem.start_date, dropItem.start_date)
  const max = Math.max(dragItem.start_date, dropItem.start_date)

  const filteredAndSortedData = keyAchievements
    .filter((item: any) => isValueBetween(Number(item.start_date), min, max))
    .sort((a: any, b: any) => Number(a.start_date) - Number(b.start_date))

  const findFirstValue = filteredAndSortedData.find((item: any) => item.start_date === dragItem.start_date)
  const findFirstPlace = filteredAndSortedData.findIndex((item: any) => item.start_date === dropItem.start_date)

  let arrangedData = [...filteredAndSortedData]

  if (findFirstValue && findFirstPlace !== -1) {
    arrangedData = arrangedData.filter((item) => item?.start_date !== dragItem?.start_date)
    arrangedData.splice(findFirstPlace, 0, findFirstValue)
  }

  return arrangedData.map((item, index) => ({
    ...item,
    start_date: min + index,
  }))
}

export const createPostAndUpdateKeyAchievementPayload = (
  values: any,
  projectName: any,
  todayDate: Date,
  sort: any = null,
  currentPeriod: any,
) => {
  return {
    // phase: values?.description || null,
    lookup_project_to_phase_id: values?.lookup_project_to_phase_id || null,
    key_achievments_delim: values?.key_achivements_past_week || null,
    next_plan_delim: values?.plans_next_week || null,
    period: currentPeriod,
    project_name: projectName,
    last_updated: todayDate,
    master_project_stage_status_id: values?.master_project_stage_status_id || null,
    master_project_sub_stage_id: values?.master_project_sub_stage_id || null,
    ...(sort !== null && { start_date: sort.toString() }), // Add start_date only if sort is provided
  }
}

export const getKeyAchievementsTableData = (array: any) => {
  return array?.map((data: any) => {
    return {
      ...data,
      stage_status: data.MasterProjectStageStatus?.project_stage_status || null,
      sub_stage: data.MasterProjectSubStage?.project_sub_stage || null,
    }
  })
}

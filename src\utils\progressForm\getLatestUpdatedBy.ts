import { findLatestDate } from '@/src/utils/dateUtils'

interface DataItem {
  last_updated?: Date
  updated_by?: string
  [key: string]: any // Allow unknown properties
}

export const getLatestUpdatedBy = (dates: string[], data: DataItem[]): string => {
  const latestDate = findLatestDate(dates) as string
  if (!latestDate) {
    return ''
  }

  const findLatestUpdatedData = data.find((item: any) => {
    if (!item.last_updated) return false

    const itemDate = new Date(item.last_updated)

    if (isNaN(itemDate.getTime())) {
      console.error(`Invalid date format for item: ${item.last_updated}`)
      return false
    }

    return itemDate.toISOString() === new Date(latestDate).toISOString()
  })

  if (!findLatestUpdatedData) {
    return ''
  }

  return findLatestUpdatedData.updated_by || ''
}

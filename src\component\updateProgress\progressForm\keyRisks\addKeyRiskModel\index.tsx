import React, { useMemo, useState } from 'react'
import { Modal, Box } from '@mui/material'
import { format } from 'date-fns'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './AddKeyRiskModel.module.scss'
import ValidationModel from '../../validationModel'
import { RATING } from '../constant'
import { KeyRiskValidation } from '../validation'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import DatePicker from '@/src/component/shared/dataPicker'
import PulseModel from '@/src/component/shared/pulseModel'
import Textarea from '@/src/component/shared/textArea'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import { showCustomToast } from '@/src/component/toast/ToastManager'
import { useGetProjectToPhase } from '@/src/hooks/useMasterProjectToPhase'
import { useOptions } from '@/src/hooks/useOptions'
import { useGetRiskCategory } from '@/src/hooks/useRiskCategory'
import { useGetRiskDiscipline } from '@/src/hooks/useRiskDiscipline'
import { useGetRiskOwner } from '@/src/hooks/useRiskOwner'
import { IAreaOfConcern, StageStatus, SubStage } from '@/src/redux/areaOfConcern/interface'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { IProjects } from '@/src/services/projects/interface'
import {
  getValue,
  getValueWithConvertValuesToCommaSeparated,
  prepareDropdownOptions,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate, isoToYYYYMMDD, payloadDateFormate } from '@/src/utils/dateUtils'
import { predefinedStageOrder, getStageOrderIndex } from '@/src/utils/predecessorSuccessor'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'

interface ModalProps {
  open: boolean
  onClose?: () => void
  onConfirmOpen?: () => void
  project?: IProjects
  editKeyRisksIndex?: number | null
  edit: boolean
  filteredAreaOfConcern: IAreaOfConcern[]
}

const AddKeyRiskModel: React.FC<ModalProps> = ({
  open,
  onClose,
  project,
  editKeyRisksIndex,
  edit,
  filteredAreaOfConcern,
}) => {
  const router = useRouter()
  const [disable, setDisable] = useState(false)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])
  const { getAreaOfConcernsApi, masterRating, areaOfConcerns, updateAreaOfConcernApi, addAreaOfConcernApi } =
    useAreaOfConcern()
  const { statuses } = useStatus()
  const { currentPeriod } = useMasterPeriod()
  const { riskCategories } = useGetRiskCategory()
  const { riskOwners } = useGetRiskOwner()
  const { riskDisciplines } = useGetRiskDiscipline()
  const [isStatusChanged, setIsStatusChanged] = useState(false)
  const { currentUser } = useAuthorization()
  // const [phaseWithStageStatus, setPhaseWithStageStatus] = useState<any[]>([])
  // const [phaseOptions, setPhaseOptions] = useState<any[]>([])

  const currentStatus = statuses.find((item: IStatus) => `${item.id}` === router.query.slug)
  const phaseNotPresent = !currentStatus?.LookupProjectToPhase?.length

  // const fetchData = async () => {
  //   try {
  //     // await getRiskCategoryApi()
  //     // await getRiskOwnerApi()
  //     // await getRiskDisciplineApi()
  //   } catch (error) {
  //     console.error('Error fetching data:', error)
  //   }
  // }

  // useEffect(() => {
  //   fetchData()
  // }, [])

  const riskCategoriesOption = useMemo(() => {
    return prepareDropdownOptions(riskCategories, 'risk_category')
  }, [riskCategories])

  const riskOwnersOption = useMemo(() => {
    return prepareDropdownOptions(riskOwners, 'risk_owner')
  }, [riskOwners])

  const riskDisciplinesOptions = useMemo(() => {
    return prepareDropdownOptions(riskDisciplines, 'risk_discipline')
  }, [riskDisciplines])

  const areaOfConcern: any = useMemo(() => {
    return areaOfConcerns.find((item: any) => item.id === editKeyRisksIndex)
  }, [areaOfConcerns, editKeyRisksIndex])

  const initialValues = useMemo(() => {
    return {
      lookup_project_to_phase_id: editKeyRisksIndex ? areaOfConcern?.LookupProjectToPhase?.id : null,
      // stage_status: (editKeyRisksIndex && areaOfConcern.MasterProjectStageStatus?.stage_status) || null,
      // sub_stage: (editKeyRisksIndex && areaOfConcern.MasterProjectStageStatus?.sub_stage) || null,

      MasterProjectStageStatus: (editKeyRisksIndex && areaOfConcern.MasterProjectStageStatus) || null,
      MasterProjectSubStage: (editKeyRisksIndex && areaOfConcern.MasterProjectSubStage) || null,
      master_project_stage_status_id: (editKeyRisksIndex && areaOfConcern.MasterProjectStageStatus?.id) || null,
      master_project_sub_stage_id: (editKeyRisksIndex && areaOfConcern.MasterProjectSubStage?.id) || null,

      description: editKeyRisksIndex ? areaOfConcern?.description : null,
      migrationMeasures: editKeyRisksIndex ? areaOfConcern?.mitigation_measures : null,
      forecastedClosureDate: editKeyRisksIndex ? areaOfConcern?.forecasted_closure_date : null, //TODO : Confirm
      forecastedClosureDateNote: editKeyRisksIndex ? areaOfConcern?.forecast_closure_date_note : null,
      master_rating_id: editKeyRisksIndex ? areaOfConcern?.master_rating_id : null,
      master_risk_category_id: editKeyRisksIndex ? areaOfConcern?.master_risk_category_id : null,
      master_risk_discipline_id: editKeyRisksIndex ? areaOfConcern?.master_risk_discipline_id : null,
      master_risk_owner_id: editKeyRisksIndex ? areaOfConcern?.master_risk_owner_id : null,
      riskImpact: editKeyRisksIndex ? areaOfConcern?.risk_impact : null,
      rating: editKeyRisksIndex ? areaOfConcern?.rating : null,
      risk_probability: editKeyRisksIndex ? areaOfConcern?.risk_probability : null,
    }
  }, [areaOfConcern, editKeyRisksIndex])

  const handleSubmit = async (values: any) => {
    const messages = []
    const isForecastedValidDate = await KeyRiskValidation.ForecastedClosureDate(values)
    if (isForecastedValidDate) {
      messages.push("Forecasted Closure Date must be greater than today if the risk rating is not 'Closed.'")
    }
    setIsValidationModel(isForecastedValidDate)
    setValidationMessage(messages)
    if (messages?.length) {
      setDisable(false)
      return
    }
    const todayDate = new Date().toISOString()
    const last_updated = todayDate

    if (editKeyRisksIndex) {
      const forecasted_closure_date = payloadDateFormate(values?.forecastedClosureDate) || null

      const payload: any = {
        lookup_project_to_phase_id: values?.lookup_project_to_phase_id || null,
        description: values?.description || null,
        master_risk_category_id: values?.master_risk_category_id || null,
        master_project_stage_status_id: values?.master_project_stage_status_id || null,
        master_project_sub_stage_id: values?.master_project_sub_stage_id || null,
        master_risk_discipline_id: values?.master_risk_discipline_id || null,
        risk_impact: values?.riskImpact || null,
        master_risk_owner_id: values?.master_risk_owner_id || null,
        mitigation_measures: values?.migrationMeasures || null,
        forecasted_closure_date: forecasted_closure_date ? forecasted_closure_date : null, //TODO : Confim this
        forecast_closure_date_note: values?.forecastedClosureDateNote || null,
        // rating: values?.rating || null,
        risk_probability: values?.risk_probability || null,
        master_rating_id: values?.master_rating_id || null,
        // rating: values?.rating || null,
        period: payloadDateFormate(project?.period?.toString() ?? ''),
        project_name: project?.project_name,
        last_updated,
      }
      const response: Record<string, any> = await updateAreaOfConcernApi({
        id: editKeyRisksIndex,
        data: payload,
      })
      if (response.payload.success === true) {
        setDisable(false)
        currentPeriod && getAreaOfConcernsApi({ period: currentPeriod })
        formik.resetForm()
        onClose && onClose()
      } else {
        setDisable(false)
        showCustomToast('Error', 'error')
      }
    } else {
      const payload: any = {
        lookup_project_to_phase_id: values?.lookup_project_to_phase_id || null,
        description: values?.description || null,
        master_project_stage_status_id: values?.master_project_stage_status_id || null,
        master_project_sub_stage_id: values?.master_project_sub_stage_id || null,
        master_risk_category_id: values?.master_risk_category_id || null,
        master_risk_discipline_id: values?.master_risk_discipline_id || null,
        risk_impact: values?.riskImpact || null,
        master_risk_owner_id: values?.master_risk_owner_id || null,
        mitigation_measures: values?.migrationMeasures || null,
        forecasted_closure_date: payloadDateFormate(values?.forecastedClosureDate) || null,
        forecast_closure_date_note: values?.forecastedClosureDateNote || null,
        // rating: values?.rating || null,
        risk_probability: values?.risk_probability || null,
        master_rating_id: values?.master_rating_id || null,
        // rating: values?.rating || null,
        period: currentPeriod,
        last_updated,
        project_name: project?.project_name,
        key_risk_sorting_order: filteredAreaOfConcern.length
          ? Number(filteredAreaOfConcern[filteredAreaOfConcern.length - 1].key_risk_sorting_order) + 1
          : 1,
      }
      const response: Record<string, any> = await addAreaOfConcernApi(payload)
      if (response.payload.success === true) {
        setDisable(false)
        currentPeriod && getAreaOfConcernsApi({ period: currentPeriod })
        formik.resetForm()
        onClose && onClose() // Fixed this line
      } else {
        setDisable(false)
        showCustomToast('Error', 'error')
      }
    }
  }

  const formik: any = useFormik({
    initialValues,
    enableReinitialize: true,
    onSubmit: (values) => {
      setDisable(true)
      handleSubmit(values)
      // formik.resetForm();
    },
  })

  // Update form values and handle status change if `isStatusChanged` is true
  const handleStatusUpdate = (phaseStatusOptions: any[]) => {
    formik.setFieldValue('stage_status', phaseStatusOptions?.length > 0 ? phaseStatusOptions[0]?.stage_status : null)
    formik.setFieldValue('sub_stage', phaseStatusOptions?.length > 0 ? phaseStatusOptions[0]?.sub_stage : null)
    setIsStatusChanged(false)
  }

  // TODO: Comment old logic
  // const { phases, stage_status, sub_stage } = useOptions(
  //   statuses,
  //   formik?.values?.phase,
  //   currentUser,
  //   handleStatusUpdate,
  //   isStatusChanged,
  // )

  const getPhaseStageAssociations = (records: any[]) => {
    const phaseStageMap = new Map()

    records.forEach((record: any) => {
      const phaseArray = record.phases // now an array of objects
      const stageStatusValue = record.stage_status

      const subStageValue =
        typeof record.sub_stage?.project_sub_stage === 'string' && record.sub_stage?.project_sub_stage.trim() !== ''
          ? record.sub_stage
          : null

      // Skip invalid stage_status
      if (
        !stageStatusValue ||
        typeof stageStatusValue?.project_stage_status !== 'string' ||
        stageStatusValue?.project_stage_status.trim() === ''
      ) {
        return
      }

      const stageInfo = {
        stageStatus: stageStatusValue,
        subStage: subStageValue,
      }

      const stageInfoString = JSON.stringify(stageInfo)

      // Process phase array of objects
      if (Array.isArray(phaseArray) && phaseArray.length > 0) {
        phaseArray.forEach((phaseObj: any) => {
          if (!phaseObj?.phase) return // skip if missing phase name

          const phaseKey = JSON.stringify(phaseObj) // use entire object as key

          if (!phaseStageMap.has(phaseKey)) {
            phaseStageMap.set(phaseKey, new Set())
          }

          phaseStageMap.get(phaseKey).add(stageInfoString)
        })
      }
    })

    // Convert Map to desired result
    const result: Array<{ phases: any; stages: any[] }> = []
    for (const [phaseKey, stagesSet] of phaseStageMap.entries()) {
      result.push({
        phases: JSON.parse(phaseKey),
        stages: Array.from(stagesSet).map((s: any) => JSON.parse(s)),
      })
    }

    return result
  }

  const { phaseWithStageStatus, phaseOptions } = useMemo(() => {
    const statusesByPermission = statuses.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )
    const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
      statusesByPermission.map((status) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const phaseWithStageStatuses = sortedStatusesByPermission.map((item: any) => {
      return {
        phases: item.LookupProjectToPhase,
        stage_status: item.MasterProjectStageStatus,
        sub_stage: item.MasterProjectSubStage,
      }
    })

    const phaseStageAssociations: any = getPhaseStageAssociations(phaseWithStageStatuses)

    const uniquePhases =
      phaseStageAssociations?.length > 0
        ? phaseStageAssociations?.map((item: any) => {
            return item?.phases
          })
        : []

    const phaseOptions =
      uniquePhases?.length > 0
        ? uniquePhases?.map((phase: any) => {
            return {
              label: phase?.phase,
              value: phase?.id,
            }
          })
        : []

    return { phaseWithStageStatus: phaseStageAssociations, phaseOptions }
  }, [formik.values?.lookup_project_to_phase_id])

  const { stageStatusOptions, subStageOptions } = useMemo(() => {
    let phase = getValue(phaseOptions, formik.values.lookup_project_to_phase_id)

    const filteredPhaseWithStageStatus =
      phaseWithStageStatus?.length > 0
        ? phaseWithStageStatus?.find((item: any) => {
            return item?.phases?.id === phase?.value
          })
        : undefined // Changed to undefined for clarity when not found

    // Use Sets to collect unique values
    const uniqueStageStatuses = new Set<StageStatus>()
    const uniqueSubStages = new Set<SubStage>()

    if (filteredPhaseWithStageStatus?.stages) {
      filteredPhaseWithStageStatus.stages.forEach((item: any) => {
        if (item?.stageStatus !== null && item?.stageStatus !== undefined) {
          uniqueStageStatuses.add(item.stageStatus)
        }
        if (item?.subStage !== null && item?.subStage !== undefined) {
          uniqueSubStages.add(item.subStage)
        }
      })
    }

    // Convert Sets back to arrays of objects with 'label' and 'value'
    const stageStatusOptions = Array.from(uniqueStageStatuses).map((status) => {
      return {
        label: status.project_stage_status,
        value: status.id,
      }
    })

    const subStageOptions = Array.from(uniqueSubStages).map((subStage) => {
      return {
        label: subStage.project_sub_stage,
        value: subStage.id,
      }
    })

    return { stageStatusOptions, subStageOptions }
  }, [phaseWithStageStatus, formik.values?.lookup_project_to_phase_id])

  // TODO: Comment old logic
  // useMemo(() => {
  //   const uniquePhases = getUniqueValuesById(phases)

  //   return uniquePhases?.length > 0
  //     ? uniquePhases?.map((item: any) => {
  //         return {
  //           label: item?.phase,
  //           value: item?.id,
  //         }
  //       })
  //     : []
  // }, [formik.values?.lookup_project_to_phase_id])

  // This other function is clean way to check if any field is empty
  const isAnyFieldEmpty = useMemo(() => {
    const values = formik.values
    // Fields that are always required based on your JSX labels with '*'
    const requiredFields = [
      'lookup_project_to_phase_id',
      'master_project_stage_status_id',
      'description',
      'migrationMeasures',
      'forecastedClosureDate',
      'master_rating_id',
    ]

    // Check if any of the always-required fields are empty
    const areRequiredFieldsGenerallyEmpty = requiredFields.some((key) => {
      // Check for null, undefined, or empty string after trimming
      const value = values[key]
      // For comboboxes, a null value might be considered empty
      if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
        return true
      }
      return false
    })

    if (areRequiredFieldsGenerallyEmpty) {
      return true // Found an empty required field
    }

    // Conditional check for 'sub_stage'
    if (getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)?.label === 'Design') {
      // If stage_status is "Design", sub_stage must not be empty
      const isSubStageEmpty =
        values.master_project_sub_stage_id === null || values.master_project_sub_stage_id === undefined
      if (isSubStageEmpty) {
        return true // sub_stage is empty when stage_status is Design
      }
    }

    // All required fields (including conditional ones) are filled
    return false
  }, [formik.values])

  useMemo(() => {
    formik.setFieldValue(
      'lookup_project_to_phase_id',
      phaseOptions?.filter((item: any) => item?.value === formik.values?.lookup_project_to_phase_id)?.length > 0
        ? formik.values?.lookup_project_to_phase_id
        : null,
    )

    if (formik.values?.lookup_project_to_phase_id || formik.values?.lookup_project_to_phase_id === null) {
      formik.setFieldValue('stage_status', null)
    }
  }, [formik.values?.lookup_project_to_phase_id])

  return (
    <Modal
      className={styles.model}
      open={open}
      onClose={() => {
        formik.resetForm()
        onClose && onClose()
      }}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'calc(100% - 4rem)',
          maxWidth: '1288px',
          bgcolor: 'background.paper',
          borderRadius: '12px',
          pt: '20px',
          px: '20px',
          pb: '20px',
          zIndex: 1,
        }}
      >
        <div className={styles.header}>
          <div className={styles.rightSection}>
            <TypographyField variant={'bodySemiBold'} text={'Add Key Risk'} />
            {/* <div className={styles.divider}></div> */}
            {/* <TypographyField sx={"caption"} text={"Infrastructure"} /> */}
          </div>
          {/* <CloseRoundedIcon
            style={{ cursor: 'pointer' }}
            onClick={() => {
              onClose && onClose()
            }}
          /> */}
        </div>
        <form onSubmit={formik.handleSubmit}>
          {/* <Textarea
            labelText="Phase"
            placeholder="Type something ..."
            name="phase"
            value={formik.values.phase}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          /> */}
          <div className={styles.comboBoxContainer}>
            <ComboBox
              options={phaseNotPresent ? [{ label: 'N/A', value: null }] : phaseOptions}
              labelText={`Phase/Package${!phaseNotPresent ? ' *' : ''}`}
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              placeholder="Type of search..."
              value={
                formik.values?.lookup_project_to_phase_id
                  ? getValue(phaseOptions, formik.values.lookup_project_to_phase_id)
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  lookup_project_to_phase_id: val?.value || null,
                  master_project_stage_status_id: null,
                  master_project_sub_stage_id: null,
                })
                setIsStatusChanged(true)
              }}
            />

            <ComboBox
              options={stageStatusOptions}
              labelText={'Stage Status *'}
              placeholder="Type of search..."
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              value={
                formik.values?.master_project_stage_status_id
                  ? getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_stage_status_id: val?.value || null,
                  master_project_sub_stage_id: null,
                })
                // formik.setFieldValue('master_project_sub_stage_id', null)
              }}
              isCustomSorting={true}
            />

            {getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)?.label === 'Design' && (
              <ComboBox
                options={subStageOptions}
                labelText={'Sub Stage *'}
                // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
                placeholder="Type of search..."
                value={
                  formik.values.master_project_sub_stage_id
                    ? getValue(subStageOptions, formik.values.master_project_sub_stage_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) => {
                  formik.setValues({
                    ...formik.values,
                    master_project_sub_stage_id: val?.value || null,
                  })
                }}
              />
            )}
          </div>
          <Textarea
            labelText="Description *"
            placeholder="Type something ..."
            name="description"
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          <Textarea
            labelText="Mitigation Measures *"
            placeholder="Type something ..."
            name="migrationMeasures"
            value={formik.values.migrationMeasures}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
              gap: '24px',
              marginTop: '16px',
            }}
          >
            <DatePicker
              name="forecastedClosureDate"
              labelText="Forecasted Closure Date *"
              placeholder="DD-MM-YYYY"
              minDate={new Date()}
              value={convertDDMMYYYYToLongDate(formik.values.forecastedClosureDate) || null}
              onChange={(value) => {
                if (!value || isNaN(new Date(value).getTime())) {
                  formik.setFieldValue('forecastedClosureDate', null)
                  return
                }

                const formattedDate = format(new Date(value), 'dd-MM-yyyy')
                formik.setFieldValue('forecastedClosureDate', formattedDate)
              }}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              name="forecastedClosureDateNote"
              placeholder="Type something..."
              variant={'outlined'}
              labelText={'Forecasted Closure Date Note'}
              value={formik.values.forecastedClosureDateNote}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <div>
              <div className={styles.labelOfRating}>Rating *</div>
              <div className={styles.buttonGroup}>
                {masterRating?.map((mr: { id: number; rating: string }) => (
                  <div
                    className={`${styles.button} ${formik.values.master_rating_id === mr.id ? styles[`active${mr.rating !== 'Closed' ? mr.rating : ''}`] : ''}`}
                    onClick={() => formik.setFieldValue('master_rating_id', mr.id)}
                  >
                    {mr.rating}
                  </div>
                ))}
              </div>
            </div>
            <div>
              <div className={styles.labelOfRating}>Probability</div>
              <div className={styles.buttonGroup}>
                {masterRating?.map((mr: { id: number; rating: string }) => (
                  <div
                    className={`${styles.button} ${formik.values.risk_probability === mr.rating ? styles[`active${mr.rating !== 'Closed' ? mr.rating : ''}`] : ''}`}
                    onClick={() => formik.setFieldValue('risk_probability', mr.rating)}
                  >
                    {mr.rating}
                  </div>
                ))}

                {/* <div
                  className={`${styles.button} ${formik.values.risk_probability === RATING.LOW ? styles.activeLow : ''}`}
                  onClick={() => formik.setFieldValue('risk_probability', RATING.LOW)}
                >
                  Low
                </div>
                <div
                  className={`${styles.button} ${formik.values.risk_probability === RATING.MEDIUM ? styles.activeMedium : ''}`}
                  onClick={() => formik.setFieldValue('risk_probability', RATING.MEDIUM)}
                >
                  Medium
                </div>
                <div
                  className={`${styles.button} ${formik.values.risk_probability === RATING.HIGH ? styles.activeHigh : ''}`}
                  onClick={() => formik.setFieldValue('risk_probability', RATING.HIGH)}
                >
                  High
                </div>
                <div
                  className={`${styles.button} ${formik.values.risk_probability === RATING.CLOSED ? styles.active : ''}`}
                  onClick={() => formik.setFieldValue('risk_probability', RATING.CLOSED)}
                >
                  Closed
                </div> */}
              </div>
            </div>
            <div>
              <ComboBox
                options={riskCategoriesOption}
                labelText={'Risk Category'}
                placeholder="Type of search..."
                value={
                  formik.values.master_risk_category_id
                    ? getValue(riskCategoriesOption, formik.values.master_risk_category_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    master_risk_category_id: val?.value || null,
                  })
                }
              />
            </div>
            <div>
              <ComboBox
                options={riskDisciplinesOptions}
                labelText={'Risk Discipline'}
                placeholder="Type of search..."
                value={
                  formik.values.master_risk_discipline_id
                    ? getValue(riskDisciplinesOptions, formik.values.master_risk_discipline_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    master_risk_discipline_id: val?.value || null,
                  })
                }
              />
            </div>
            <div>
              <ComboBox
                // options={["RJ1", "RJ2", "RJ3", "RJ4", "RJ5", "RJ6", "RJ7"]}
                options={riskOwnersOption}
                labelText={'Risk Owner'}
                placeholder="Type of search..."
                value={
                  formik.values.master_risk_owner_id
                    ? getValue(riskOwnersOption, formik.values.master_risk_owner_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    master_risk_owner_id: val?.value || null,
                  })
                }
              />
            </div>
            <div>
              <TextInputField
                name="riskImpact"
                placeholder="Type something..."
                variant={'outlined'}
                labelText={'Risk Impact'}
                value={formik.values.riskImpact}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
          </div>

          <div className={styles.buttons}>
            <Button
              color="secondary"
              onClick={() => {
                onClose && onClose()
                formik.resetForm()
              }}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={disable || isAnyFieldEmpty}>
              {`${editKeyRisksIndex ? 'Edit Key Risk' : 'Add Key Risk'}`}
            </Button>
          </div>
        </form>
        <PulseModel
          closable={false}
          style={{ width: 'fitContent' }}
          open={isValidationModel}
          onClose={() => setIsValidationModel(false)}
          content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
        />
      </Box>
    </Modal>
  )
}

export default AddKeyRiskModel

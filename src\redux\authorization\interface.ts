import { StatusEnum } from '../types'

export interface IAuthorizationStates {
  authUrlStatus: StatusEnum
  getAccessTokenStatus: StatusEnum
  logInUserStatus: StatusEnum
  currentLogInUserStatus: StatusEnum
  logOutUserStatus: StatusEnum
  currentUser: ICurrentUser
}

export interface ICurrentUser {
  email: string
  id: number
  name: string
  organization: string
  role: IRole
  user_type: 'admin' | 'user' | 'super_admin'
}

export interface IRole {
  id: number
  role_name: string
  permission_type: string
  owning_entities: string[]
  projects: string[]
  view_permissions: string[]
}

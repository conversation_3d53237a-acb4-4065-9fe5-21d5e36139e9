import React, { useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { toast } from 'sonner'
import AddKeyAchievementsModel from './addKeyAchievementsModel'
import { fetchKeyAchievements, getKeyAchievementsTableData, reorderKeyAchievements } from './helper'
import styles from './KeyAchievements.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import DragCell from '@/src/component/customCells/dragCell'
import PulseButton from '@/src/component/shared/button/pulseButton'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { convertValuesToCommaSeparated } from '@/src/helpers/helpers'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IStatus } from '@/src/redux/status/interface'
import { IProjects } from '@/src/services/projects/interface'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'
import { errorToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

interface IKeyAchievementProps {
  project: IProjects
  edit: any
  statuses: IStatus[]
}

const KeyAchievements: React.FC<IKeyAchievementProps> = ({ project, edit, statuses }) => {
  //react-hook
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editKeyAchievementIndex, setEditKeyAchievementIndex] = useState<number | string>()
  const [isModelOpen, setIsModelOpen] = useState(false)
  //custom-hook
  const {
    keyAchievements,
    getKeyAchievementsApi,
    deleteKeyAchievementsApi,
    updateKeyAchievementsApi,
    keyAchievementSortApi,
  } = useKeyAchievement()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const keyAchievement: any = keyAchievements.filter((item) => item.project_name === project?.project_name)

  const handleOpenModel = () => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    } else {
      setIsModelOpen(true)
      setEditKeyAchievementIndex('')
    }
  }

  const handleCloseModel = () => {
    setIsModelOpen(false)
  }

  const handleDelete = async (id: any) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    } else {
      const res: Record<string, any> = await deleteKeyAchievementsApi(id)
      if (res.payload.success) {
        const getResponse = await fetchKeyAchievements(getKeyAchievementsApi, currentPeriod, project?.project_name)
        if (getResponse) {
          setDeleteModel(null)
        }
      }
    }
  }

  const onCellUpdate = async (cell: any, newValue: any, row: any): Promise<boolean> => {
    if (!row) return false

    try {
      const { id, ...rowData } = row
      const payload = {
        ...rowData,
        [cell.columnId]: newValue,
        last_updated: new Date(),
      }
      delete payload.updated_by

      const response: Record<string, any> = await updateKeyAchievementsApi({ id, data: payload })

      if (!response?.payload?.success) {
        errorToast('Failed to update key achievement')
        return false
      }

      const getResponse = await fetchKeyAchievements(getKeyAchievementsApi, currentPeriod, project?.project_name)

      if (!getResponse?.payload?.success) {
        errorToast('Failed to fetch updated key achievements')
        return false
      }

      return true
    } catch (error) {
      console.error('Error while updating key achievement cell:', error)
      errorToast('Unexpected error occurred')
      return false
    }
  }

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    if (!currentUser?.role?.view_permissions?.includes('Key Achievements Sorting')) return
    const dragItem = keyAchievements.find((item: any) => item.id.toString() === dragId.toString())
    const dropItem = keyAchievements.find((item: any) => item.id.toString() === dropId.toString())
    if (!dragItem || !dropItem) return

    const updatedData = reorderKeyAchievements(keyAchievements, dragItem, dropItem)

    const newPayload = updatedData.map((item: any) => ({
      id: item.id.toString(),
      start_date: item.start_date,
    }))

    const res: any = await keyAchievementSortApi({ period: currentPeriod, keyAchievementsPlans: newPayload })
    if (res?.payload.success) {
      await fetchKeyAchievements(getKeyAchievementsApi, currentPeriod, project?.project_name)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'left',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }: { row: any }) => (
        <div className={styles.actionButtons}>
          {edit && (
            <EditIcon
              className={styles.editRowIcon}
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  setIsModelOpen(true)
                  setEditKeyAchievementIndex(row.id)
                }
              }}
            />
          )}
          {edit && (
            <DeleteIcon
              className={styles.editRowIcon}
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  setDeleteModel(row.id)
                }
              }}
            />
          )}
        </div>
      ),
      size: 70,
    },
    { accessorKey: 'start_date', header: 'ID', size: 90, visible: false },
    {
      accessorKey: 'phase',
      header: 'Phase/Package',
      size: 150,
      require: false,
      cell: ({ row }: { row: any }) => {
        return <div>{row.original?.LookupProjectToPhase?.phase || '-'}</div>
      },
    },
    {
      accessorKey: 'stage_status',
      header: 'Stage Status',
      filterType: 'projectStatus',
      size: 150,
      require: false,
    },
    {
      accessorKey: 'sub_stage',
      header: 'Sub Stage',
      size: 150,
      require: false,
    },
    {
      accessorKey: 'key_achievments_delim',
      header: 'Key Achievements (Last Period)',
      flex: 5,
      isEditableCell: edit,
      editableType: 'textArea',
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.key_achievments_delim} isEdit={false} isGrid={true} />
      // },
      cell: ({ row }) => {
        const val = row.original.key_achievments_delim
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      require: true,
    },
    {
      accessorKey: 'next_plan_delim',
      header: 'Plans (Next Period)',
      flex: 5,
      isEditableCell: edit,
      editableType: 'textArea',
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.next_plan_delim} isEdit={false} isGrid={true} />
      // },
      cell: ({ row }) => {
        const val = row.original.next_plan_delim
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      require: true,
    },
  ]

  return (
    <div className={styles.container}>
      {keyAchievement.length >= 1 && (
        <div className={styles.table}>
          <TanStackTable
            rows={getKeyAchievementsTableData(keyAchievement)}
            columns={columns}
            onDragEnd={handleDragAndDrop}
            gridName={'Key Achievements'}
            enableSticky={false}
          />
        </div>
      )}
      {/* <Button disabled={!edit} color="secondary" onClick={handleOpenModel} className={styles.addButton}>
        Add Key Achievements <AddCircleRoundedIcon fontSize="large" />
      </Button> */}
      <PulseButton
        disabled={!edit}
        onClick={() => handleOpenModel()}
        label="Add Key Achievements"
        icon={<AddOutlinedIcon fontSize="large" />}
      />
      {isModelOpen ? (
        <AddKeyAchievementsModel
          open={isModelOpen}
          keyAchievement={keyAchievement}
          onClose={handleCloseModel}
          project={project}
          editKeyAchievementIndex={editKeyAchievementIndex as number}
          statuses={statuses}
        />
      ) : null}
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete(deleteModel as number)}
      />
    </div>
  )
}

export default KeyAchievements

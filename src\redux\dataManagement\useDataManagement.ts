import { useDispatch } from 'react-redux'
import { productionClone, stagingClone } from '.'
import { AppDispatch } from '@/src/redux/store'

const useDatabaseManagement = () => {
  const dispatch: AppDispatch = useDispatch()

  const stagingCloneApi = () => dispatch(stagingClone())
  const productionCloneApi = () => dispatch(productionClone())

  return {
    productionCloneApi,
    stagingCloneApi,
  }
}

export default useDatabaseManagement

import React, { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import styles from './ProjectOwnerCard.module.scss'
import AvatarPicker from '../../../shared/avatarPicker'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateProjectOwner,
  useDeleteProjectOwner,
  useGetProjectOwners,
  useUpdateProjectOwner,
} from '@/src/hooks/useProjectOwner'
import { IProjectOwner } from '@/src/services/projectOwnerCard/interface'
import { extractAvatarUrlForPayload } from '@/src/utils/stringUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/
const phoneNumberRegex = /^[\d+\- ()]{9,15}$/
//   /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/

const validationSchema = Yup.object({
  email: Yup.string().matches(emailRegex, 'Invalid email format').notRequired(),
  phone_number: Yup.string().notRequired().matches(phoneNumberRegex, 'Invalid phone number'),
  // .phone('any', 'Invalid phone number').matches(phoneNumberRegex, 'Phone number must be 10 digits, optionally with country code')
})

const ProjectOwnerCard: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)

  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addProjectOwnerCard } = drawerStates
  const { projectOwners } = useGetProjectOwners(addProjectOwnerCard)
  // MUTATIONS
  const { mutate: addProjectOwner } = useCreateProjectOwner()
  const { mutate: deleteProjectOwner } = useDeleteProjectOwner()
  const { mutate: updateProjectOwner } = useUpdateProjectOwner()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string, setSubmitting?: Function) => ({
    onSuccess: () => {
      successToast(successMsg)
      setEditIndex(null)
      formik.resetForm()
    },
    onError: (err: any) => {
      setSubmitting && setSubmitting(false)
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { design_executive_director: '', email: '', phone_number: '', location: '' },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      const payload = {
        design_executive_director: values.design_executive_director,
        email: values.email,
        phone_number: values.phone_number,
        location: values.location,
      }
      if (editIndex !== null) {
        updateProjectOwner(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record', setSubmitting),
        )
      } else {
        addProjectOwner(
          payload,
          handleMutationCallbacks('Record successfully added', 'Failed to add record', setSubmitting),
        )
      }
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteProjectOwner(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = projectOwners.find((contractor) => {
      //TODO
      return contractor.id === id
    })

    formik.setValues({
      design_executive_director: editControlManagers?.design_executive_director,
      email: editControlManagers?.email || '',
      phone_number: editControlManagers?.phone_number || '',
      location: editControlManagers?.location || '',
    })
    setEditIndex(id)
  }

  const handleUpdateAvatar = async (id: number, avatar: string) => {
    const editControlManagers: any = projectOwners.find((contractor: IProjectOwner) => {
      return contractor.id === id
    })
    if (editControlManagers) {
      updateProjectOwner(
        {
          id,
          avatar: extractAvatarUrlForPayload(avatar) as string,
          design_executive_director: editControlManagers.design_executive_director,
          email: editControlManagers?.email,
          phone_number: editControlManagers?.phone_number,
          location: editControlManagers?.location,
        },
        handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
      )
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'avatar',
      header: 'Avatar',
      size: 70,
      cell: ({ row }) => {
        const avatar = row?.original?.avatar ? [row?.original?.avatar?.url] : []
        return <AvatarPicker url={avatar} handleSelectAvatar={(val) => handleUpdateAvatar(Number(row?.id), val[0])} />
      },
    },
    {
      accessorKey: 'design_executive_director',
      header: 'Design Executive Director',
      flex: 1,
    },
    {
      accessorKey: 'email',
      header: 'Email',
      // size: 150,
      flex: 1,
    },
    {
      accessorKey: 'phone_number',
      header: 'Phone Number',
      size: 160,
      // flex: 1,
    },
    {
      accessorKey: 'location',
      header: 'Location',
      // size: 150,
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text={'Add Design Executive Director'}
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="design_executive_director"
              labelText={'Design Executive Director'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.design_executive_director}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="email"
              labelText={'Email'}
              error={Boolean(formik.touched.email && formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email && formik.errors.email}
              placeholder="Enter Email Address"
              variant={'outlined'}
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="phone_number"
              labelText={'Phone Number'}
              placeholder="Enter phone number"
              error={Boolean(formik.touched.phone_number && formik.errors.phone_number)}
              helperText={formik.touched.phone_number && formik.errors.phone_number && formik.errors.phone_number}
              variant={'outlined'}
              value={formik.values.phone_number}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="location"
              labelText={'location'}
              placeholder="Enter Location"
              variant={'outlined'}
              value={formik.values.location}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.design_executive_director?.trim()}
              >
                {'Update Design Executive Director'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.design_executive_director?.trim()}
              >
                {'+ Add Design Executive Director'}
              </Button>
            )}
          </div>
        </form>
        {projectOwners?.length >= 1 && (
          // <Table data={projectOwners} columns={columns} />
          <TanStackTable
            rows={sortData(projectOwners, 'design_executive_director') as any}
            columns={columns}
            isOverflow={true}
          />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default ProjectOwnerCard

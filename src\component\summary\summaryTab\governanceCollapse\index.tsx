import React, { useEffect, useMemo } from 'react'
import styles from './GovernanceCollapse.module.scss'
import ImageCard from '../imageCard'
import { ISustainabilityCollapse } from '../interface'

// Utility function to handle numeric validation
const handleNumericChange = (value: string, fieldName: string, formik: any) => {
  const numValue = parseFloat(value)
  if (value === '' || (!isNaN(numValue) && numValue >= 0)) {
    formik.setFieldValue(fieldName, value === '' ? 0 : numValue)
  }
}

const GovernanceCollapse = ({ summaryEdit, formik }: ISustainabilityCollapse) => {
  useEffect(() => {
    if (
      formik?.values?.initiation ||
      formik?.values?.ldc_procurement ||
      formik?.values?.design ||
      formik?.values?.contractor_procurement ||
      formik?.values?.construction ||
      formik?.values?.handover
    ) {
      formik.setValues({ ...formik.values })
    } else if (formik.values.owning_entity === 'Aldar Development') {
      formik.setValues({
        ...formik.values,
        initiation: 0,
        ldc_procurement: 0,
        design: 0,
        contractor_procurement: 0,
        construction: 100,
        handover: 0,
      })
    } else if (formik.values.project_classification) {
      switch (formik.values.project_classification) {
        case 'Design-Bid-Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 3,
            design: 8,
            contractor_procurement: 4,
            construction: 81,
            handover: 2,
          })
          break
        case 'Bid-Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 0,
            design: 0,
            contractor_procurement: 5,
            construction: 91,
            handover: 2,
          })
          break
        case 'Design':
          formik.setValues({
            ...formik.values,
            initiation: 5,
            ldc_procurement: 5,
            design: 90,
            contractor_procurement: 0,
            construction: 0,
            handover: 0,
          })
          break
        case 'Design & Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 0,
            design: 0,
            contractor_procurement: 6,
            construction: 90,
            handover: 2,
          })
          break
        default:
          break
      }
    }
  }, [formik.values.project_classification])

  const total = useMemo(() => {
    const values = [
      formik?.values?.initiation,
      formik?.values?.ldc_procurement,
      formik?.values?.design,
      formik?.values?.contractor_procurement,
      formik?.values?.construction,
      formik?.values?.handover,
    ]
    return values.reduce((acc, val) => acc + (parseFloat(val) || 0), 0)
  }, [formik.values])

  const getDisplayValue = (value: string | number | null | undefined, isEditable: boolean) => {
    if (value === null || value === undefined) {
      return ''
    }
    if (value === '0' || value === 0) {
      return isEditable ? '0' : '0%'
    }
    return value ? `${value}${!isEditable ? '%' : ''}` : ''
  }

  return (
    <div className={styles.cards} onClick={(e) => e.stopPropagation()}>
      <ImageCard
        label="Initiation"
        formik={formik}
        value={getDisplayValue(formik?.values?.initiation, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'initiation', formik)}
        imageWidth={50}
        imageHeight={50}
        fieldName="initiation"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="LDC Procurement"
        formik={formik}
        value={getDisplayValue(formik?.values?.ldc_procurement, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'ldc_procurement', formik)}
        imageWidth={50}
        imageHeight={50}
        fieldName="ldc_procurement"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="Design"
        formik={formik}
        value={getDisplayValue(formik?.values?.design, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'design', formik)}
        imageWidth={50}
        imageHeight={50}
        fieldName="design"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="Contractor Procurement"
        formik={formik}
        value={getDisplayValue(formik?.values?.contractor_procurement, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'contractor_procurement', formik)}
        imageWidth={50}
        imageHeight={50}
        fieldName="contractor_procurement"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="Construction"
        formik={formik}
        value={getDisplayValue(formik?.values?.construction, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'construction', formik)}
        imageWidth={50}
        imageHeight={50}
        fieldName="construction"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="DLP and Project Closeout"
        formik={formik}
        value={getDisplayValue(formik?.values?.handover, summaryEdit)}
        onChange={(value: string) => handleNumericChange(value, 'handover', formik)}
        imageWidth={65}
        imageHeight={50}
        fieldName="handover"
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.highlight : ''}
      />
      <ImageCard
        label="Total"
        formik={formik}
        value={`${total}%`}
        imageWidth={50}
        imageHeight={50}
        fieldName="total"
        summaryEdit={summaryEdit}
        isEditable={false}
        onChange={(e: Event) => e.preventDefault()}
        className={summaryEdit ? styles.highlight : ''}
      />
    </div>
  )
}

export default GovernanceCollapse

import React, { ReactElement, useRef, useState, useEffect } from 'react'
import dynamic from 'next/dynamic'

import 'react-quill/dist/quill.snow.css'
import styles from './TextEditor.module.scss'

// TODO: Remove after confirmation to client for
// TODO: Also remove jodic package if not use.
// Dynamically import JoditEditor for client-side rendering
// const JoditEditor = dynamic(() => import('jodit-react'), {
//   ssr: false,
//   loading: () => <p>Loading...</p>,
// })

const QuillNoSSRWrapper = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Preparing editor...</p>,
})

// TypeScript Interface
interface TextEditorProps {
  labelText?: string
  name?: string
  value?: string
  onChange?: (value: string) => void
  error?: boolean
  helperText?: string
  className?: string
  required?: boolean
  isResizeVertical?: boolean
  placeholder?: string
  editorConfig?: object
}

const TextEditor: React.FC<TextEditorProps> = ({
  labelText = '',
  name = '',
  value = '',
  onChange,
  error = false,
  helperText = '',
  className = '',
  required = false,
  isResizeVertical = true,
  placeholder = 'Start typing...',
  editorConfig = {},
}): ReactElement => {
  const editor = useRef(null)
  const [internalValue, setInternalValue] = useState<string>(value)

  useEffect(() => {
    setInternalValue(value || '')
  }, [value])

  const handleEditorChange = (val: string) => {
    setInternalValue(val)
    onChange?.(val)
  }

  return (
    <div className={`${styles.container} ${isResizeVertical ? styles.vertical : ''} ${className}`}>
      {labelText && (
        <label className={styles.label}>
          {labelText}
          {required && <span>*</span>}
        </label>
      )}
      {/* <JoditEditor
        ref={editor}
        value={internalValue}
        config={{
          readonly: false,
          placeholder,
          ...editorConfig,
        }}
        onChange={handleEditorChange}
        className={`${styles.textArea} ${error ? styles.errorField : ''}`}
      /> */}

      <QuillNoSSRWrapper
        theme="snow"
        value={internalValue}
        onChange={handleEditorChange}
        className={`${styles.textEditor} ${error ? styles.errorField : ''}`}
      />
      {helperText && <span className={`${styles.text} ${error ? styles.error : ''}`}>{helperText}</span>}
    </div>
  )
}

export default TextEditor

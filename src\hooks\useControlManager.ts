import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterControlManager,
  deleteMasterControlManager,
  getMasterControlManagers,
  updateMasterControlManager,
} from '../services/controlManager'
import { IGetMasterControlManagersResponse, IUpdateControlManager } from '../services/controlManager/interface'

export const QUERY_CONTROL_MANAGER = 'control-manager'

/**
 * Hook to fetch Control managers
 * @param enabled - Enables or disables the query
 */
export const useGetControlManagers = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterControlManagersResponse>({
    queryKey: [QUERY_CONTROL_MANAGER],
    queryFn: getMasterControlManagers,
    enabled,
  })

  return {
    controlManagers: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Control manager
 */
export const useCreateControlManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterControlManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTROL_MANAGER] })
    },
  })
}

/**
 * Hook to delete a Control manager
 */
export const useDeleteControlManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterControlManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTROL_MANAGER] })
    },
  })
}

/**
 * Hook to update a Control manager
 */
export const useUpdateControlManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterControlManager,
    onMutate: async (updatedData: IUpdateControlManager) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_CONTROL_MANAGER] })
      const previousData = queryClient.getQueryData<IGetMasterControlManagersResponse>([QUERY_CONTROL_MANAGER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterControlManagersResponse>([QUERY_CONTROL_MANAGER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, control_manager: updatedData.control_manager } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_CONTROL_MANAGER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONTROL_MANAGER] })
    },
  })
}

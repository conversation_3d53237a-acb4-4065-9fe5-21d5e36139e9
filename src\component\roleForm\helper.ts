interface ProjectData {
  owning_entity: string
  projects: string[]
}

interface SeparatedData {
  entities: string[]
  projects: string[]
}

export const separateEntitiesAndProjects = (data: ProjectData[]): SeparatedData => {
  const entities: string[] = []
  const projects: string[] = []

  data.forEach((item) => {
    entities.push(item.owning_entity)
    projects.push(...item.projects)
  })

  return { entities, projects }
}

export const toCapitalizedFormat = (label: string) => {
  return label
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

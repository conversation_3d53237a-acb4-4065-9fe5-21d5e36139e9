import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetProjectKpisResponse, IProjectKpi, IProjectKpiStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IProjectKpiStates = {
  getProjectKpiStates: StatusEnum.Idle,
  addProjectKpiStates: StatusEnum.Idle,
  deleteProjectKpiStates: StatusEnum.Idle,
  updateProjectKpiStates: StatusEnum.Idle,
  projectKpis: [],
  projectKpi: {},
}

export const getProjectKpis = createAsyncThunk(
  '/get-project-kpi',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(
        `/project-kpi?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addProjectKpi = createAsyncThunk('/add-project-kpi', async (data: IProjectKpi, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-kpi`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateProjectKpi = createAsyncThunk(
  '/update-project-kpi',
  async (data: { id: number; data: IProjectKpi }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-kpi/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const sortProjectKpi = createAsyncThunk('/update-project-kpi', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/project-kpi/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteProjectKpi = createAsyncThunk('/delete-project-kpi', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/project-kpi/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const projectKpiSlice = createSlice({
  name: 'Project Kpi',
  initialState,
  reducers: {
    // set selected rows
  },
  extraReducers: (builder) => {
    //getProjectKpis
    builder.addCase(getProjectKpis.pending, (state) => {
      state.getProjectKpiStates = StatusEnum.Pending
    })
    builder.addCase(getProjectKpis.fulfilled, (state, action) => {
      state.getProjectKpiStates = StatusEnum.Success
      const actionPayload = action.payload as IGetProjectKpisResponse
      state.projectKpis = actionPayload.data
    })
    builder.addCase(getProjectKpis.rejected, (state) => {
      state.getProjectKpiStates = StatusEnum.Failed
    })
    //addProjectKpi
    builder.addCase(addProjectKpi.pending, (state) => {
      state.addProjectKpiStates = StatusEnum.Pending
    })
    builder.addCase(addProjectKpi.fulfilled, (state, action) => {
      state.addProjectKpiStates = StatusEnum.Success
    })
    builder.addCase(addProjectKpi.rejected, (state) => {
      state.addProjectKpiStates = StatusEnum.Failed
    })
    //updateProjectKpi
    builder.addCase(updateProjectKpi.pending, (state) => {
      state.updateProjectKpiStates = StatusEnum.Pending
    })
    builder.addCase(updateProjectKpi.fulfilled, (state, action) => {
      state.updateProjectKpiStates = StatusEnum.Success
    })
    builder.addCase(updateProjectKpi.rejected, (state) => {
      state.updateProjectKpiStates = StatusEnum.Failed
    })
    //deleteProjectKpi
    builder.addCase(deleteProjectKpi.pending, (state) => {
      state.deleteProjectKpiStates = StatusEnum.Pending
    })
    builder.addCase(deleteProjectKpi.fulfilled, (state) => {
      state.deleteProjectKpiStates = StatusEnum.Success
    })
    builder.addCase(deleteProjectKpi.rejected, (state) => {
      state.deleteProjectKpiStates = StatusEnum.Failed
    })
  },
})

export const {} = projectKpiSlice.actions

// Export the reducer
export default projectKpiSlice.reducer

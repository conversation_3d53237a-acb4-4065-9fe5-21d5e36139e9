import { IContractorPayload, IUpdateContractor } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_CONTRACTOR_URL = `/master-contractor`

// Get Master Contractor data
export const getMasterContractor = async () => {
  const response = await api.get(`${MASTER_CONTRACTOR_URL}`)
  return response?.data
}

export const deleteContractor = async (data: number) => {
  const response = await api.delete(`${MASTER_CONTRACTOR_URL}/${data}`)
  return response?.data
}

// Create Master Contractor entry
export const createMasterContractor = async (data: IContractorPayload) => {
  const response = await api.post(`${MASTER_CONTRACTOR_URL}`, data)
  return response
}

// Update Master Contractor entry
export const updateMasterContractor = async (data: IUpdateContractor): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_CONTRACTOR_URL}/${data.id}`, { ...rest })
  return response
}

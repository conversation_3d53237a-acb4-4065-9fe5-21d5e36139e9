import { IRiskCategoryPayload, IUpdateRiskCategory } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_RISK_CATEGORY_URL = `/master-risk-category`

// Get Master Risk Category data
export const getRiskCategory = async () => {
  const response = await api.get(`${MASTER_RISK_CATEGORY_URL}`)
  return response?.data
}
export const deleteRiskCategory = async (data: number) => {
  const response = await api.delete(`${MASTER_RISK_CATEGORY_URL}/${data}`)
  return response?.data
}

// Create Master Risk Category entry
export const createRiskCategory = async (data: IRiskCategoryPayload) => {
  const response = await api.post(`${MASTER_RISK_CATEGORY_URL}`, data)
  return response
}

// Update Master Risk Category entry
export const updateRiskCategory = async (data: IUpdateRiskCategory): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_RISK_CATEGORY_URL}/${data.id}`, { ...rest })
  return response
}

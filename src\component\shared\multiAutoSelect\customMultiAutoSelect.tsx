// components/MultiAutoSelect/CustomMultiAutoSelect.tsx
import React, { useMemo } from 'react'
import ClearIcon from '@mui/icons-material/Clear'
import { Avatar, Checkbox } from '@mui/material'
import Autocomplete from '@mui/material/Autocomplete'
import styles from './MultiAutoSelect.module.scss' // Adjust path as needed
import DropDownIcon from '../../svgImages/dropDownIcon' // Adjust path as needed
import TextInput<PERSON>ield from '../textInputField' // Adjust path as needed
import { naturalSort } from '@/src/utils/sortingUtils'

// Interface for the options in the autocomplete
export interface IAutoCompleteOption {
  id: string
  name: string
  avatar?: string
  subOptions?: IAutoCompleteOption[]
}

// Props interface for the MultiAutoSelect component
export interface IAutocompleteSwitch {
  isSubOption?: boolean
  options: IAutoCompleteOption[]
  handleSelectedOption: (option: string[]) => void
  value: string[] | number[]
  labelText?: string
  placeholder?: string
  className?: string
  isSx?: boolean
  disabled?: boolean
  clearIcon?: boolean
  isAvatarOption?: boolean
  isMultiLine?: boolean
  customRenderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => React.ReactNode
  isMultiFilterLabel?: boolean
  multiFilterLabelText?: string
  inputValue?: string // Now directly from props
  setInputValue?: (val: string) => void // Now directly from props
}

// MultiAutoSelect component
const CustomMultiAutoSelect: React.FC<IAutocompleteSwitch> = ({
  isSubOption = true,
  value: valueParent = [],
  isSx = true,
  labelText,
  placeholder,
  options,
  className,
  disabled,
  handleSelectedOption,
  customRenderOption,
  clearIcon,
  isAvatarOption = false,
  isMultiLine,
  isMultiFilterLabel,
  multiFilterLabelText,
  inputValue, // Destructure from props
  setInputValue, // Destructure from props
}) => {
  const val = (valueParent || [])?.map((item: any) => item) as string[]

  const handleChange = (id: string) => {
    const isSelected = val?.includes(id)
    const selectAllId = 'Select All' // Assuming 'Select All' is a special ID

    // Recursive function to get all sub-option IDs
    const getSubOptionIds = (optionId: string, opts: IAutoCompleteOption[]): string[] => {
      const option = opts?.find((o) => o.id === optionId)
      if (!option || !option.subOptions) return []
      return option?.subOptions?.flatMap((sub) => [sub?.id, ...getSubOptionIds(sub?.id, opts)])
    }

    // Calculate all option IDs, including sub-options
    const allOptionIds = options.flatMap((option) => [option?.id, ...getSubOptionIds(option?.id, options)])

    let updatedValues: string[] = []

    if (id === selectAllId) {
      // Handle 'Select All' selection
      updatedValues = val?.length === allOptionIds.length ? [] : allOptionIds // Deselect all : Select all
    } else {
      // Handle individual selection
      updatedValues = isSelected
        ? val?.filter((v) => v !== id) // Deselect
        : [...val, id, ...getSubOptionIds(id, options)] // Select the id and its sub-options

      // Check if 'Select All' needs to be deselected
      if (val?.includes(selectAllId)) {
        if (updatedValues.length !== allOptionIds.length) {
          updatedValues = updatedValues.filter((v) => v !== selectAllId)
        }
      }
    }
    handleSelectedOption(updatedValues)
  }

  const handleClear = () => {
    handleSelectedOption([])
    setInputValue && setInputValue('') // Clear input value when clear icon is clicked
  }

  // Helper function to get option name by ID
  const getOptionNameById = (id: string): string => {
    // Search through options and their suboptions to find the matching ID
    for (const option of options) {
      if (option.id === id) return option.name
      if (option.subOptions) {
        for (const subOption of option.subOptions) {
          if (subOption.id === id) return subOption.name
        }
      }
    }
    return id // Return the ID if no matching option is found
  }

  const getSelectedOptions = () => {
    // Filter top-level options if their ID is selected or any of their sub-options are selected
    return (
      options?.filter(
        (option) =>
          val?.includes(option?.id) ||
          (option.subOptions && option?.subOptions?.some((sub) => val?.includes(sub?.id?.trim()))),
      ) || []
    )
  }

  // This flattens the selected options (including sub-options) for the Autocomplete's `value` prop
  // when `isSubOption` is true.
  const flattenOptions = (opts: IAutoCompleteOption[]): IAutoCompleteOption[] => {
    return opts?.flatMap((option) => [
      { id: option?.id?.trim(), name: option?.name?.trim(), avatar: option?.avatar },
      ...(option.subOptions || []).map((sub) => ({ id: sub?.id?.trim(), name: sub?.name?.trim() })),
    ])
  }

  const renderSelectedValues = () => {
    // Get option names from IDs
    const selectedNames = val?.map((id) => getOptionNameById(id))
    return selectedNames?.length > 0
      ? selectedNames?.length > 1
        ? isMultiFilterLabel // Conditionally display "Multi [Label]"
          ? `Multi ${multiFilterLabelText || 'Selections'}`
          : selectedNames?.join(', ')
        : selectedNames[0]
      : ''
  }

  const renderSubOptions = (subOpts: IAutoCompleteOption[]) => {
    return (
      <div className={styles.subOptions}>
        {subOpts?.map((subOption) => {
          return (
            subOption?.id && (
              <li
                key={subOption?.id}
                onClick={(e) => {
                  e.stopPropagation() // Prevent click event from bubbling up to the parent
                  handleChange(subOption?.id)
                }}
              >
                <Checkbox checked={val?.includes(subOption?.id)} classes={{ root: styles.checkBox }} />
                {subOption?.name}
              </li>
            )
          )
        })}
      </div>
    )
  }

  const defaultRenderOption = (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => (
    <li {...props} onClick={() => handleChange(option.id)} className={styles.optionLi}>
      <div className={styles.options}>
        <Checkbox checked={val?.includes(option.id)} classes={{ root: styles.checkBox }} />
        {isAvatarOption && (
          <Avatar src={option?.avatar} sx={{ width: 24, height: 24, marginRight: 2, marginLeft: 2 }} />
        )}
        <div className={styles.mainOptions}>
          <div className={styles.optionName}>{option.name}</div>
        </div>
      </div>
      {option?.subOptions && renderSubOptions(option.subOptions)}
    </li>
  )

  const selectedOptions = getSelectedOptions()
  // Determine the `value` prop for Autocomplete based on `isSubOption`
  // If `isSubOption` is true, flatten the selected options to include sub-options.
  // Otherwise, use the raw `val` (string[]) directly as Autocomplete's value (requires `isOptionEqualToValue` to handle string comparison correctly if options are objects).
  const autocompleteValue = isSubOption
    ? flattenOptions(selectedOptions)
    : (val.map((id) => options.find((opt) => opt.id === id)).filter(Boolean) as IAutoCompleteOption[]) // Ensure it's an array of IAutoCompleteOption for consistency

  const sortedOptions = useMemo(() => {
    return options?.sort((a, b) => naturalSort(a.name, b.name))
  }, [options])

  return (
    <div className={styles.root}>
      {labelText && <div className={styles.labelText}>{labelText}</div>}
      <Autocomplete
        multiple
        disabled={disabled}
        size="small"
        id="checkboxes-tags-demo"
        options={sortedOptions} // Full list of options for MUI to filter
        inputValue={inputValue} // Controlled input value for filtering
        onInputChange={(_, newInputValue, reason) => {
          // IMPORTANT: Only update inputValue if the reason is NOT 'reset'
          // 'reset' typically happens when an option is selected, and we want to keep the text.
          if (reason !== 'reset') {
            setInputValue && setInputValue(newInputValue)
          }
        }}
        value={autocompleteValue} // The array of selected option objects
        disableCloseOnSelect
        clearIcon={
          clearIcon && val.length > 0 ? <ClearIcon onClick={handleClear} className={styles.clearIcon} /> : null
        }
        getOptionLabel={(option) => option.name} // How MUI gets the label for filtering and display
        renderOption={customRenderOption ?? defaultRenderOption}
        popupIcon={<DropDownIcon className={styles.downArrow} />}
        renderTags={() => (
          <div className={`${isMultiLine ? styles.isMultiLine : ''} ${styles.inputText}`}>{renderSelectedValues()}</div>
        )}
        // `isOptionEqualToValue` is crucial when `value` and `options` contain objects.
        // It tells Autocomplete how to compare an option from `options` with an item in `value`.
        // Ensure both `option` and `valueItem` are treated as IAutoCompleteOption for comparison.
        isOptionEqualToValue={(option, valueItem) => option.id === valueItem?.id}
        classes={{
          input: styles.tagInput,
          popper: styles.popper,
          paper: styles.paper,
          listbox: styles.listBox,
          popupIndicator: styles.popupIndicator,
        }}
        renderInput={(params) => (
          <TextInputField
            variant="outlined"
            {...params}
            className={className}
            placeholder={placeholder}
            sx={
              isSx
                ? {
                    '& .Mui-focused': {
                      backgroundColor: '#f0f8ff !important',
                      borderBottom: '1px solid #2333C2BF',
                      '& .MuiOutlinedInput-input': {
                        backgroundColor: '#f0f8ff',
                        color: 'black !important',
                      },
                    },
                  }
                : undefined
            }
          />
        )}
      />
    </div>
  )
}

export default CustomMultiAutoSelect

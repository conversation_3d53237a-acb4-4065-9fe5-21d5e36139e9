import { Accordion as Mui<PERSON>ccordion, AccordionDetails, AccordionSummary } from '@mui/material'
import styles from './Accordion.module.scss'
import TypographyField from '@/src/component/shared/typography'
import DropDownIcon from '@/src/component/svgImages/dropDownIcon'
import TimerIcon from '@/src/component/svgImages/timerIcon'
import { getLastUpdate, getLastUpdatedTime } from '@/src/utils/dateUtils'

interface IAccordionProps {
  expanded: boolean
  onChange: () => void
  title: string
  lastUpdate?: string | null
  updatedBy?: string | null
  children: React.ReactNode
}

const Accordion: React.FC<IAccordionProps> = ({ expanded, onChange, title, lastUpdate, updatedBy, children }) => {
  return (
    <MuiAccordion expanded={expanded} onChange={onChange} classes={{ root: styles.accordion }}>
      <AccordionSummary
        expandIcon={<DropDownIcon className={styles.expandIcon} />}
        className={styles.summary}
        classes={{ content: styles.content }}
      >
        <div className={styles.header}>{title}</div>
        <div className={styles.timeContainer}>
          <TypographyField variant={'bodyBold'} className={styles.lastUpdatedText} text={updatedBy || ''} />
          <div className={styles.timerIcon}>
            {/* {lastUpdate && <TimerIcon />} */}
            <TypographyField
              variant={'thin'}
              className={styles.lastUpdatedText}
              text={lastUpdate ? `${getLastUpdatedTime(lastUpdate)}` : ''}
            />
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails className={styles.details}>{children}</AccordionDetails>
    </MuiAccordion>
  )
}

export default Accordion

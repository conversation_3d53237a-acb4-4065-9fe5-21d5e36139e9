import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  IGetAreaOfConcernsResponse,
  IAreaOfConcern,
  IAreaOfConcernStatus,
  IGetMastrRatingListResponse,
} from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IAreaOfConcernStatus = {
  getAreaOfConcernStatus: StatusEnum.Idle,
  addAreaOfConcernStatus: StatusEnum.Idle,
  deleteAreaOfConcernStatus: StatusEnum.Idle,
  updateAreaOfConcernStatus: StatusEnum.Idle,
  areaOfConcernsSortStatus: StatusEnum.Idle,
  areaOfConcerns: [],
  masterRatingList: [],
  areaOfConcern: {
    period: '',
    project_name: '',
    description: '',
    mitigation_measures: '',
    forecasted_closure_date: '',
    forecast_closure_date_note: '',
    rating: '',
    last_updated: new Date(),
    key_risk_sorting_order: null,
  },
}

export const getAreaOfConcerns = createAsyncThunk(
  '/get-key-risk',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(
        `/key-risks?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const getMasterRastingList = createAsyncThunk('/get-master-rating', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-rating`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addAreaOfConcern = createAsyncThunk('/add-key-risk', async (data: IAreaOfConcern, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/key-risks`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateAreaOfConcern = createAsyncThunk(
  '/update-key-risk',
  async (data: { id: number; data: IAreaOfConcern }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/key-risks/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteAreaOfConcern = createAsyncThunk('/delete-key-risk', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/key-risks/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const sortAreaOfConcerns = createAsyncThunk('/key-risks/bulk-sort', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/key-risks/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const areaOfConcernSlice = createSlice({
  name: 'areaOfConcern',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getAreaOfConcerns
    builder.addCase(getAreaOfConcerns.pending, (state) => {
      state.getAreaOfConcernStatus = StatusEnum.Pending
    })
    builder.addCase(getAreaOfConcerns.fulfilled, (state, action) => {
      state.getAreaOfConcernStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetAreaOfConcernsResponse
      state.areaOfConcerns = actionPayload.data
    })
    builder.addCase(getAreaOfConcerns.rejected, (state) => {
      state.getAreaOfConcernStatus = StatusEnum.Failed
    })
    //getMasterRastingList
    builder.addCase(getMasterRastingList.pending, (state) => {
      state.getAreaOfConcernStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterRastingList.fulfilled, (state, action) => {
      state.getAreaOfConcernStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMastrRatingListResponse
      state.masterRatingList = actionPayload.data
    })
    builder.addCase(getMasterRastingList.rejected, (state) => {
      state.getAreaOfConcernStatus = StatusEnum.Failed
    })
    //addAreaOfConcern
    builder.addCase(addAreaOfConcern.pending, (state) => {
      state.addAreaOfConcernStatus = StatusEnum.Pending
    })
    builder.addCase(addAreaOfConcern.fulfilled, (state, action) => {
      state.addAreaOfConcernStatus = StatusEnum.Success
    })
    builder.addCase(addAreaOfConcern.rejected, (state) => {
      state.addAreaOfConcernStatus = StatusEnum.Failed
    })
    //updateAreaOfConcern
    builder.addCase(updateAreaOfConcern.pending, (state) => {
      state.updateAreaOfConcernStatus = StatusEnum.Pending
    })
    builder.addCase(updateAreaOfConcern.fulfilled, (state, action) => {
      state.updateAreaOfConcernStatus = StatusEnum.Success
    })
    builder.addCase(updateAreaOfConcern.rejected, (state) => {
      state.updateAreaOfConcernStatus = StatusEnum.Failed
    })
    //deleteAreaOfConcern
    builder.addCase(deleteAreaOfConcern.pending, (state) => {
      state.deleteAreaOfConcernStatus = StatusEnum.Pending
    })
    builder.addCase(deleteAreaOfConcern.fulfilled, (state, action) => {
      state.deleteAreaOfConcernStatus = StatusEnum.Success
    })
    builder.addCase(deleteAreaOfConcern.rejected, (state) => {
      state.deleteAreaOfConcernStatus = StatusEnum.Failed
    })
    //areaOfConcernsSort
    builder.addCase(sortAreaOfConcerns.pending, (state) => {
      state.areaOfConcernsSortStatus = StatusEnum.Pending
    })
    builder.addCase(sortAreaOfConcerns.fulfilled, (state, action) => {
      state.areaOfConcernsSortStatus = StatusEnum.Success
    })
    builder.addCase(sortAreaOfConcerns.rejected, (state) => {
      state.areaOfConcernsSortStatus = StatusEnum.Failed
    })
  },
})

export const {} = areaOfConcernSlice.actions

// Export the reducer
export default areaOfConcernSlice.reducer

import { useDispatch, useSelector } from 'react-redux'
import {
  getProjectManagements,
  addProjectManagements,
  updateProjectManagements,
  deleteProjectManagements,
  uploadDocProjectManagements,
  deleteDocProjectManagements,
  fetchDocument,
} from '.'
import { IDeleteDocProjectManagementPayload, IProjectManagement } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useProjectManagement = () => {
  const dispatch: AppDispatch = useDispatch()

  const getProjectManagementStatus = useSelector(
    (state: RootState) => state.projectManagement.getProjectManagementStatus,
  )

  const projectManagements = useSelector((state: RootState) => state.projectManagement.projectManagements)

  const getProjectManagementsApi = (data: any) => dispatch(getProjectManagements(data))
  const addProjectManagementsApi = (data: IProjectManagement) => dispatch(addProjectManagements(data))
  const updateProjectManagementsApi = (data: { id: number; data: IProjectManagement }) =>
    dispatch(updateProjectManagements(data))
  const uploadDocProjectManagementsApi = (data: { id: number; data: FormData }) =>
    dispatch(uploadDocProjectManagements(data))
  const deleteDocProjectManagementsApi = (data: IDeleteDocProjectManagementPayload) =>
    dispatch(deleteDocProjectManagements(data))
  const deleteProjectManagementsApi = (data: number) => dispatch(deleteProjectManagements(data))
  const getDocumentApi = (data: any) => dispatch(fetchDocument(data))

  return {
    getProjectManagementStatus,
    projectManagements,
    getProjectManagementsApi,
    addProjectManagementsApi,
    updateProjectManagementsApi,
    uploadDocProjectManagementsApi,
    deleteDocProjectManagementsApi,
    deleteProjectManagementsApi,
    getDocumentApi,
  }
}
export default useProjectManagement

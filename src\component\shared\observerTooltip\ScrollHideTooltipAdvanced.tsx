import React, { useState, useEffect, useCallback } from 'react'
import { Tooltip as Mui<PERSON>ooltip, TooltipProps } from '@mui/material'

interface Props extends TooltipProps {
  scrollHideDelay?: number
  enterDelay?: number
  leaveDelay?: number
}

// Alternative version with more granular control
const ScrollHideTooltipAdvanced: React.FC<Props> = (props) => {
  const {
    children,
    title,
    placement = 'top',
    arrow = true,
    enterDelay = 500,
    leaveDelay = 200,
    scrollHideDelay = 0,
    ...otherProps
  } = props

  const [isOpen, setIsOpen] = useState(false)
  const [isHovering, setIsHovering] = useState(false)

  // Scroll handler with custom delay
  const handleScroll = useCallback(() => {
    if (isOpen) {
      if (scrollHideDelay > 0) {
        setTimeout(() => {
          setIsOpen(false)
        }, scrollHideDelay)
      } else {
        setIsOpen(false)
      }
    }
  }, [isOpen, scrollHideDelay])

  useEffect(() => {
    const scrollHandler = () => handleScroll()

    document.addEventListener('scroll', scrollHandler, { passive: true, capture: true })
    document.addEventListener('wheel', scrollHandler, { passive: true, capture: true })

    return () => {
      document.removeEventListener('scroll', scrollHandler, { capture: true })
      document.removeEventListener('wheel', scrollHandler, { capture: true })
    }
  }, [handleScroll])

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true)
    setTimeout(() => {
      if (isHovering) {
        setIsOpen(true)
      }
    }, enterDelay)
  }, [enterDelay, isHovering])

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false)
    setTimeout(() => {
      setIsOpen(false)
    }, leaveDelay)
  }, [leaveDelay])

  return (
    <MuiTooltip
      title={title}
      placement={placement}
      arrow={arrow}
      open={isOpen}
      disableHoverListener={true}
      disableFocusListener={true}
      disableTouchListener={true}
      {...otherProps}
    >
      <span onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave} style={{ display: 'inline-block' }}>
        {children}
      </span>
    </MuiTooltip>
  )
}

export default ScrollHideTooltipAdvanced

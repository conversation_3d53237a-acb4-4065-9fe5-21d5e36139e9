import styles from './Tab.module.scss'
import TypographyField from '../../shared/typography'

interface TabProps {
  name: string
  onSelect: (name: string) => void
  isActive: boolean
  children: string
}
//USE_FOR: Project-details page
const Tab: React.FC<TabProps> = ({ name, onSelect, isActive, children }) => {
  const handleClick = () => {
    onSelect(name)
  }

  return (
    <div
      className={`${styles.tab} ${isActive ? styles.selectedTab : ''}`}
      onClick={(e) => {
        e.stopPropagation()
        handleClick()
      }}
    >
      <TypographyField
        variant={isActive ? 'h2' : 'h7'}
        style={{ color: isActive ? '#444' : '#ffffff' }}
        text={children}
        className={styles.label}
      />
    </div>
  )
}

export default Tab

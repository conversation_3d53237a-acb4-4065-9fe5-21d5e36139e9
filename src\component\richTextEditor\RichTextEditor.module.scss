@import '/styles/color.scss';

.editorContent {
  height: 100%;
  padding-left: 7px;
  // padding-top: 7px;
  > div {
    height: 100%;
    max-height: 85px;
    overflow-y: auto;
    font-size: 12px;
    font-weight: 400;
    line-height: auto;
    :first-child {
      margin-top: 0px;
    }
    p {
      margin-top: 0px;
      margin-bottom: 0px;
    }
  }
  ::-webkit-scrollbar {
    width: 3px;
  }
  ::-webkit-scrollbar-thumb {
    background: #dddddd;
    border-radius: 5px;
  }
}

.disable {
  border: 1px solid #eaeaea !important;
}

.active {
  cursor: text;
  background: #f0f8ff;
  border-bottom: 1px solid rgb(40, 101, 220);
}

.editorContent > div:focus,
.editorContent > div:focus-visible {
  cursor: text;
  outline: none !important; /* Remove focus border */
  border-bottom: 1px solid rgb(40, 101, 220) I !important;
}

.gridCell {
  border: none !important;
}

.gridContainer {
  height: 'auto';
  overflow: 'hidden';
  max-height: '85px';
}

.label {
  font-family: Poppins;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  text-align: left;
  color: $GRAY_500;
}

.labelContainer {
  padding: 0px 5px 0px 8px;
  white-space: nowrap;
  font-size: 12px !important;
  color: #808080;
  display: flex;
  gap: 2px;
  svg {
    margin-top: 1px;
    width: 15px;
    height: 15px;
  }
}

import * as React from 'react'
import { styled } from '@mui/material/styles'
import Switch from '@mui/material/Switch'

const lockIcon = encodeURIComponent(`
<svg
xmlns="http://www.w3.org/2000/svg"
height="16px"
viewBox="0 -960 960 960"
width="16px"
fill="#FFFFFF"
>
<path d="M240-80q-33 0-56.5-23.5T160-160v-400q0-33 23.5-56.5T240-640h40v-80q0-83 58.5-141.5T480-920q83 0 141.5 58.5T680-720v80h40q33 0 56.5 23.5T800-560v400q0 33-23.5 56.5T720-80H240Zm0-80h480v-400H240v400Zm240-120q33 0 56.5-23.5T560-360q0-33-23.5-56.5T480-440q-33 0-56.5 23.5T400-360q0 33 23.5 56.5T480-280ZM360-640h240v-80q0-50-35-85t-85-35q-50 0-85 35t-35 85v80ZM240-160v-400 400Z" />
</svg>
`)

const unLockIcon = encodeURIComponent(`
<svg xmlns="http://www.w3.org/2000/svg"
height="16px"
viewBox="0 0 24 24"
width="16px"
fill="#e8eaed"
><path fill="#000000" d="M12 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m6-5h-1V6c0-2.76-2.24-5-5-5-2.28 0-4.27 1.54-4.84 3.75-.14.54.18 1.08.72 1.22.53.14 1.08-.18 1.22-.72C9.44 3.93 10.63 3 12 3c1.65 0 3 1.35 3 3v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2m0 11c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-8c0-.55.45-1 1-1h10c.55 0 1 .45 1 1z"></path></svg>
`)

const Android12Switch = styled(Switch)(({ theme }) => ({
  padding: 8,
  '& .Mui-checked': {
    color: '#3874CB !important',
    '+ .MuiSwitch-track': {
      backgroundColor: '#9BB9E5 !important',
    },
  },
  '& .MuiSwitch-track': {
    borderRadius: 22 / 2,
    backgroundColor: '#9E9E9E',

    '&::before, &::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      width: 16,
      height: 16,
    },
    '&::before': {
      backgroundImage: `url('data:image/svg+xml;utf8,${lockIcon}')`,
      left: '12px',
    },
    '&::after': {
      backgroundImage: `url('data:image/svg+xml;utf8,${unLockIcon}')`,
      right: '12px',
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: 'none',
    width: 16,
    height: 16,
    margin: 2,
  },
}))

const LockableSwitch = ({ checked, onChange }: any) => {
  return <Android12Switch checked={checked} onChange={onChange} />
}

export default LockableSwitch

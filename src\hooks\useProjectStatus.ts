import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterProjectStatus,
  deleteMasterProjectStatus,
  getMasterProjectStatuses,
  updateMasterProjectStatus,
} from '../services/projectStatus'
import { IGetMasterProjectStatusResponse, IUpdateProjectStatus } from '../services/projectStatus/interface'

export const QUERY_PROJECT_STATUS = 'project-status'

/**
 * Hook to fetch Project Statuses
 * @param enabled - Enables or disables the query
 */
export const useGetProjectStatuses = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterProjectStatusResponse>({
    queryKey: [QUERY_PROJECT_STATUS],
    queryFn: getMasterProjectStatuses,
    enabled,
  })

  return {
    projectStatuses: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project Statuses
 *  */
export const useCreateProjectStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterProjectStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STATUS] })
    },
  })
}

/**
 * Hook to delete a Project Statuses
 * */
export const useDeleteProjectStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterProjectStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STATUS] })
    },
  })
}

/**
 * Hook to update a Project Statuses
 * */
export const useUpdateProjectStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterProjectStatus,
    onMutate: async (updatedData: IUpdateProjectStatus) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_STATUS] })
      const previousData = queryClient.getQueryData<IGetMasterProjectStatusResponse>([QUERY_PROJECT_STATUS])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterProjectStatusResponse>([QUERY_PROJECT_STATUS], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((project) =>
            project.id === updatedData.id ? { ...project, project_status: updatedData.project_status } : project,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_STATUS], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STATUS] })
    },
  })
}

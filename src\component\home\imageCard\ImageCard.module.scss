@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.card {
  padding: 5px 12px;
  background: $WHITE;
  border-radius: 10px;
  min-height: 72px;
  min-width: 172px;
  position: relative;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  color: #808080 !important;
  transition: all 0.3s ease;

  @include respond-to('mobile') {
    min-width: 280px;
  }

  @include respond-to('tablet') {
    min-width: 200px;
  }

  @include respond-to('laptop') {
    min-width: 170px;
  }

  @include respond-to('desktop') {
    min-width: 170px;
  }

  &:hover {
    background-color: #aaaaaa;
    color: white !important;
    cursor: pointer;
    transform: scale(1.05);
    box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.3);

    .label,
    .value {
      color: white !important;
    }

    .cardImage {
      filter: brightness(1.2);
    }
  }

  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    .periodCardImage {
      position: absolute;
      bottom: 0;
      top: 30px;
      right: 0;
      transition: filter 0.3s ease;
    }
    .cardImage {
      position: absolute;
      bottom: -3px;
      right: 0;
      transition: filter 0.3s ease;
    }
  }

  .addButton {
    position: absolute;
    top: 6px;
    right: 6px;
    background-color: $LIGHT_200;
    border-radius: 4px;
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
}

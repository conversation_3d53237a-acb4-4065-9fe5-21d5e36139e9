import { StatusEnum } from '../types'

export interface IRatingState {
  getMasterRatingStatus: StatusEnum
  addMasterRatingStatus: StatusEnum
  deleteMasterRatingStatus: StatusEnum
  updateMasterRatingStatus: StatusEnum
  ratings: IRating[]
  rating: IRating
}
export interface IRating {
  id: number
  rating: string
}

export interface IGetMasterRatingResponse {
  data: IRating[]
  message: string
  success: true
}

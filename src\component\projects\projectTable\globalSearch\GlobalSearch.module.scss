@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.resetFilterContain {
  height: 2.125rem; // 34px → rem
  margin-bottom: 0.625rem; // 10px → rem
  display: flex;
}

.globalSearchContainer {
  margin: 0.625rem; // 10px → rem
  display: flex;
  flex-direction: column;
  gap: 0.625rem; // 10px → rem

  .visibleColumnContainer {
    display: flex;
    gap: 0.625rem; // 10px → rem
    overflow-y: auto;
    padding-bottom: 0.3125rem; // 5px → rem

    ::-webkit-scrollbar {
      width: 0.125rem; // 2px → rem
      height: 0.5rem; // 8px → rem
    }

    ::-webkit-scrollbar-track {
      border-radius: 0.3125rem; // 5px → rem
      width: 0.125rem; // 2px → rem
      height: 0.25rem; // 4px → rem
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 0.3125rem; // 5px → rem
      width: 0.125rem; // 2px → rem
      height: 0.25rem; // 4px → rem
    }
  }
}

.resetButton {
  margin-left: 20px !important;
}

.globalSearchField {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  width: 700px;

  @include respond-to('mobile') {
    width: 200px;
    margin: 0;
  }
  @include respond-to('tablet') {
    width: 500px;
    margin: 0;
  }
  @include respond-to('laptop') {
    width: 700px;
    margin: 0 auto;
  }
}

.chip {
  font-family: 'Poppins' !important;
}

import React, { useEffect, useState } from 'react'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useRouter } from 'next/router'
import styles from './KeyHighlight.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import RichTextEditor from '@/src/component/richTextEditor'
import PulseButton from '@/src/component/shared/button/pulseButton'
import { HighlightField } from '@/src/component/shared/highlightField'
import Loader from '@/src/component/shared/loader'
import Textarea from '@/src/component/shared/textArea'
import useKeyHighlights from '@/src/redux/keyHighlight/useKeyHighlight'
import useStatus from '@/src/redux/status/useStatus'

const KeyHighlight = ({ edit }: { edit: boolean }) => {
  const { setHighlight, highlights, setIsUpdateHighlight } = useKeyHighlights()
  const { statuses } = useStatus()
  const router = useRouter()
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [editText, setEditText] = useState('')
  const [deleteModel, setDeleteModel] = useState<string>('')

  useEffect(() => {
    setLoading(true)
    const status = statuses.find((status) => status.id == router.query.slug)
    if (status) {
      setHighlight((status?.key_highlights?.length && status?.key_highlights?.split(/\s*,\s*/)) || [])
    }
    setLoading(false)
  }, [router.query.slug, statuses])

  const handleChange = (val: any) => {
    setInputValue(val)
  }

  const handleAddHighlight = () => {
    if (inputValue === '') return
    const newHighlights = [...highlights]
    const findEditHighlight = highlights.findIndex((item) => {
      return item === editText
    })

    newHighlights[findEditHighlight] = inputValue
    if (editText) {
      setHighlight([...newHighlights])
      setEditText('')
      setInputValue('')
    } else {
      setHighlight([...highlights, inputValue])
      setInputValue('')
    }
    setIsUpdateHighlight(true)
  }

  const handleDelete = () => {
    const updatedHighlight: any = highlights?.filter((item, itemIndex) => {
      return itemIndex.toString() !== deleteModel
    })
    setHighlight(updatedHighlight)
    setIsUpdateHighlight(true)
    setInputValue('')
    setEditText('')
    setDeleteModel('')
  }

  return (
    <div className={styles.container}>
      {loading ? (
        <Loader />
      ) : (
        <>
          {highlights &&
            highlights?.map((highlight: any, index) => (
              <>
                <div className={styles.heighLightFields}>
                  <HighlightField
                    key={index}
                    text={highlight}
                    isEdit={edit}
                    isDelete={edit}
                    onDeleteClick={() => setDeleteModel(index.toString())}
                    style={index === highlights.length - 1 ? { borderBottom: 'none' } : {}}
                    onEditClick={() => {
                      setInputValue(highlight)
                      setEditText(highlight)
                    }}
                  />
                </div>
              </>
            ))}
        </>
      )}
      {edit ? (
        <>
          <RichTextEditor
            value={inputValue}
            handleChange={(val) => handleChange(val)}
            isEdit={edit}
            className={styles.textEditor}
          />
        </>
      ) : (
        ''
      )}

      <PulseButton
        onClick={() => handleAddHighlight()}
        label={editText ? `Edit Highlight` : `Add Highlights`}
        icon={<AddOutlinedIcon fontSize="large" />}
        disabled={!edit || inputValue === '' ? true : false}
      />
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel('')}
        handleConfirm={() => handleDelete()}
      />
    </div>
  )
}

export default KeyHighlight

import { IProjects } from '@/src/services/projects/interface'

export const MainSearchFilterFields = [
  'categoryValue',
  'entityValue',
  'projectStatus',
  'classificationValue',
  'projectTypeValue',
  'locationValue',
  'contractTypeValue',
  'project_id',
  'cpds',
  'deviationStatusValue',
  'startYear',
  'labelValue',
  'portFolio',
  'designProjectOwner',
  'designManager',
  'procure',
  'control',
  'executive',
  'deliveryDirector',
  'svp',
  'overall_forecasted_finish_date',
]

export const formattedData = (projects: IProjects[]) => {
  const updatedData = projects?.map((res: any) => {
    return {
      ...res,
      contract_type: res?.contract_type?.join(';'),
      entity_category: res?.MasterEntityCategory?.entity_category,
      project_classification: res?.MasterProjectClassification?.project_classification,
      location: res?.Locations?.map((data: any) => data?.location)?.join(';'),
      portfolio_manager: res?.MasterPortfolioManager?.portfolio_manager,
      design_executive_director: res?.DesignExecutiveDirectors?.map(
        (data: any) => data?.design_executive_director,
      )?.join(';'),
      design_project_manager: res?.DesignProjectManagers?.map((data: any) => data?.design_manager)?.join(';'),
      procurement_manager: res?.ProcurementManagers?.map((data: any) => data?.procurement_manager)?.join(';'),
      controls_manager: res?.MasterControlManager?.control_manager,
      executive_director: res?.ExecutiveDirectors?.map((data: any) => data?.executive_director)?.join(';'),
      director: res?.MasterDirector?.director,
      delivery_project_manager: res?.DeliveryProjectManagers?.map((data: any) => data?.delivery_project_manager)?.join(
        ';',
      ),
      overall_forecasted_finish_date: res?.overall_forecasted_finish_date,
    }
  })
  return updatedData
}

export function getUniqueValuesFromArray(arr: string[]): string[] {
  const seen: Set<string> = new Set()
  const result: string[] = []

  arr.forEach((value) => {
    if (!seen.has(value)) {
      seen.add(value)
      result.push(value)
    }
  })

  return result
}

import React, { ReactElement, forwardRef } from 'react'

interface TypologyIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const TypologyIcon: React.FC<TypologyIconProps> = forwardRef<SVGSVGElement, TypologyIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="16"
        height="17"
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.96599 1.66675C3.36701 1.66673 2.86752 1.66671 2.471 1.72002C2.05247 1.77629 1.6746 1.90007 1.37096 2.20372C1.06731 2.50737 0.94353 2.88523 0.887261 3.30376C0.83395 3.70028 0.833966 4.19975 0.833985 4.79873V6.20142C0.833966 6.80039 0.83395 7.29989 0.887261 7.6964C0.94353 8.11493 1.06731 8.4928 1.37096 8.79644C1.6746 9.10009 2.05247 9.22387 2.471 9.28014C2.86752 9.33345 3.36699 9.33344 3.96597 9.33342H4.03532C4.63429 9.33344 5.13379 9.33345 5.53031 9.28014C5.94884 9.22387 6.3267 9.10009 6.63035 8.79644C6.934 8.4928 7.05778 8.11493 7.11405 7.6964C7.16736 7.29989 7.16734 6.80041 7.16732 6.20143V4.79875C7.16734 4.19977 7.16736 3.70028 7.11405 3.30376C7.05778 2.88523 6.934 2.50737 6.63035 2.20372C6.3267 1.90007 5.94884 1.77629 5.53031 1.72002C5.13379 1.66671 4.63432 1.66673 4.03534 1.66675H3.96599ZM2.07806 2.91083C2.16494 2.82395 2.29661 2.75247 2.60425 2.71111C2.92627 2.66781 3.35798 2.66675 4.00065 2.66675C4.64333 2.66675 5.07503 2.66781 5.39706 2.71111C5.7047 2.75247 5.83636 2.82395 5.92324 2.91083C6.01012 2.99771 6.0816 3.12937 6.12296 3.43701C6.16626 3.75904 6.16732 4.19074 6.16732 4.83342V6.16675C6.16732 6.80942 6.16626 7.24113 6.12296 7.56315C6.0816 7.8708 6.01012 8.00246 5.92324 8.08934C5.83636 8.17622 5.7047 8.2477 5.39706 8.28906C5.07503 8.33235 4.64333 8.33342 4.00065 8.33342C3.35798 8.33342 2.92627 8.33235 2.60425 8.28906C2.29661 8.2477 2.16494 8.17622 2.07806 8.08934C1.99119 8.00246 1.9197 7.8708 1.87834 7.56315C1.83505 7.24113 1.83399 6.80942 1.83399 6.16675V4.83342C1.83399 4.19074 1.83505 3.75904 1.87834 3.43701C1.9197 3.12937 1.99119 2.99771 2.07806 2.91083Z"
          fill={props.color ? props.color : '#808080'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.6327 7.66675C11.0337 7.66673 10.5342 7.66671 10.1377 7.72002C9.71914 7.77629 9.34127 7.90007 9.03762 8.20372C8.73398 8.50737 8.6102 8.88523 8.55393 9.30376C8.50062 9.70028 8.50063 10.1998 8.50065 10.7987V12.2014C8.50063 12.8004 8.50062 13.2999 8.55393 13.6964C8.6102 14.1149 8.73398 14.4928 9.03762 14.7964C9.34127 15.1001 9.71914 15.2239 10.1377 15.2801C10.5342 15.3335 11.0336 15.3334 11.6326 15.3334H11.702C12.3009 15.3334 12.8005 15.3335 13.197 15.2801C13.6155 15.2239 13.9934 15.1001 14.297 14.7964C14.6007 14.4928 14.7244 14.1149 14.7807 13.6964C14.834 13.2999 14.834 12.8004 14.834 12.2015V10.7988C14.834 10.1998 14.834 9.70027 14.7807 9.30376C14.7244 8.88523 14.6007 8.50737 14.297 8.20372C13.9934 7.90007 13.6155 7.77629 13.197 7.72002C12.8005 7.66671 12.301 7.66673 11.702 7.66675H11.6327ZM9.74473 8.91083C9.83161 8.82395 9.96327 8.75247 10.2709 8.71111C10.5929 8.66781 11.0246 8.66675 11.6673 8.66675C12.31 8.66675 12.7417 8.66781 13.0637 8.71111C13.3714 8.75247 13.503 8.82395 13.5899 8.91083C13.6768 8.9977 13.7483 9.12937 13.7896 9.43701C13.8329 9.75904 13.834 10.1907 13.834 10.8334V12.1667C13.834 12.8094 13.8329 13.2411 13.7896 13.5632C13.7483 13.8708 13.6768 14.0025 13.5899 14.0893C13.503 14.1762 13.3714 14.2477 13.0637 14.2891C12.7417 14.3324 12.31 14.3334 11.6673 14.3334C11.0246 14.3334 10.5929 14.3324 10.2709 14.2891C9.96327 14.2477 9.83161 14.1762 9.74473 14.0893C9.65785 14.0025 9.58637 13.8708 9.54501 13.5632C9.50171 13.2411 9.50065 12.8094 9.50065 12.1667V10.8334C9.50065 10.1907 9.50171 9.75904 9.54501 9.43701C9.58637 9.12937 9.65785 8.9977 9.74473 8.91083Z"
          fill={props.color ? props.color : '#808080'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.9838 1.66675H12.3508C12.6471 1.66675 12.8945 1.66674 13.0976 1.6806C13.309 1.69502 13.5086 1.72612 13.7022 1.8063C14.1515 1.99238 14.5084 2.34928 14.6944 2.7985C14.7746 2.99209 14.8057 3.19177 14.8201 3.40317C14.834 3.60625 14.834 3.85361 14.834 4.14991V4.18359C14.834 4.47989 14.834 4.72725 14.8201 4.93033C14.8057 5.14173 14.7746 5.34141 14.6944 5.535C14.5084 5.98422 14.1515 6.34112 13.7022 6.5272C13.5086 6.60738 13.309 6.63848 13.0976 6.6529C12.8945 6.66676 12.6471 6.66675 12.3508 6.66675H10.9838C10.6875 6.66675 10.4402 6.66676 10.2371 6.6529C10.0257 6.63848 9.82599 6.60738 9.6324 6.5272C9.18318 6.34112 8.82628 5.98422 8.64021 5.535C8.56002 5.34141 8.52892 5.14173 8.5145 4.93033C8.50064 4.72725 8.50065 4.47989 8.50065 4.18358V4.14992C8.50065 3.85361 8.50064 3.60625 8.5145 3.40317C8.52892 3.19177 8.56002 2.99209 8.64021 2.7985C8.82628 2.34928 9.18318 1.99238 9.6324 1.8063C9.82599 1.72612 10.0257 1.69502 10.2371 1.6806C10.4401 1.66674 10.6875 1.66675 10.9838 1.66675ZM10.3051 2.67828C10.1447 2.68923 10.0665 2.70888 10.0151 2.73018C9.81089 2.81476 9.64867 2.97699 9.56409 3.18118C9.54278 3.23262 9.52313 3.31076 9.51218 3.47124C9.50092 3.63622 9.50065 3.84928 9.50065 4.16675C9.50065 4.48422 9.50092 4.69728 9.51218 4.86226C9.52313 5.02274 9.54278 5.10088 9.56409 5.15232C9.64867 5.35651 9.81089 5.51874 10.0151 5.60332C10.0665 5.62462 10.1447 5.64427 10.3051 5.65522C10.4701 5.66648 10.6832 5.66675 11.0007 5.66675H12.334C12.6515 5.66675 12.8645 5.66648 13.0295 5.65522C13.19 5.64427 13.2681 5.62462 13.3196 5.60332C13.5237 5.51874 13.686 5.35651 13.7706 5.15232C13.7919 5.10088 13.8115 5.02274 13.8225 4.86226C13.8337 4.69728 13.834 4.48422 13.834 4.16675C13.834 3.84928 13.8337 3.63622 13.8225 3.47124C13.8115 3.31076 13.7919 3.23262 13.7706 3.18118C13.686 2.97699 13.5237 2.81476 13.3196 2.73018C13.2681 2.70888 13.19 2.68923 13.0295 2.67828C12.8645 2.66702 12.6515 2.66675 12.334 2.66675H11.0007C10.6832 2.66675 10.4701 2.66702 10.3051 2.67828Z"
          fill={props.color ? props.color : '#808080'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.31715 10.3334C3.02084 10.3334 2.77349 10.3334 2.57041 10.3473C2.35901 10.3617 2.15933 10.3928 1.96573 10.473C1.51652 10.659 1.15961 11.0159 0.973541 11.4652C0.893352 11.6588 0.862257 11.8584 0.847833 12.0698C0.833978 12.2729 0.833981 12.5202 0.833985 12.8165V12.8503C0.833981 13.1465 0.833978 13.3939 0.847833 13.597C0.862257 13.8084 0.893352 14.0081 0.973541 14.2017C1.15961 14.6509 1.51652 15.0078 1.96573 15.1939C2.15933 15.2741 2.35901 15.3051 2.57041 15.3196C2.77348 15.3334 3.02084 15.3334 3.31715 15.3334H4.68416C4.98046 15.3334 5.22782 15.3334 5.4309 15.3196C5.6423 15.3051 5.84198 15.2741 6.03557 15.1939C6.48479 15.0078 6.84169 14.6509 7.02777 14.2017C7.10795 14.0081 7.13905 13.8084 7.15347 13.597C7.16733 13.3939 7.16733 13.1466 7.16732 12.8503V12.8166C7.16733 12.5203 7.16733 12.2729 7.15347 12.0698C7.13905 11.8584 7.10795 11.6588 7.02777 11.4652C6.84169 11.0159 6.48479 10.659 6.03557 10.473C5.84198 10.3928 5.6423 10.3617 5.4309 10.3473C5.22782 10.3334 4.98046 10.3334 4.68415 10.3334H3.31715ZM2.34842 11.3968C2.39985 11.3755 2.478 11.3559 2.63848 11.3449C2.80346 11.3337 3.01652 11.3334 3.33399 11.3334H4.66732C4.98479 11.3334 5.19785 11.3337 5.36283 11.3449C5.52331 11.3559 5.60145 11.3755 5.65289 11.3968C5.85708 11.4814 6.01931 11.6437 6.10389 11.8478C6.12519 11.8993 6.14484 11.9774 6.15579 12.1379C6.16705 12.3029 6.16732 12.5159 6.16732 12.8334C6.16732 13.1509 6.16705 13.3639 6.15579 13.5289C6.14484 13.6894 6.12519 13.7676 6.10389 13.819C6.01931 14.0232 5.85708 14.1854 5.65289 14.27C5.60145 14.2913 5.52331 14.3109 5.36283 14.3219C5.19785 14.3331 4.98479 14.3334 4.66732 14.3334H3.33399C3.01652 14.3334 2.80346 14.3331 2.63848 14.3219C2.478 14.3109 2.39985 14.2913 2.34842 14.27C2.14423 14.1854 1.982 14.0232 1.89742 13.819C1.87611 13.7676 1.85646 13.6894 1.84551 13.5289C1.83426 13.3639 1.83399 13.1509 1.83399 12.8334C1.83399 12.5159 1.83426 12.3029 1.84551 12.1379C1.85646 11.9774 1.87611 11.8993 1.89742 11.8478C1.982 11.6437 2.14423 11.4814 2.34842 11.3968Z"
          fill={props.color ? props.color : '#808080'}
        />
      </svg>
    )
  },
)
TypologyIcon.displayName = 'AccedingIcon'

export default TypologyIcon

import { useDispatch, useSelector } from 'react-redux'
import { addProject<PERSON><PERSON>, deleteProject<PERSON>pi, getProject<PERSON><PERSON>, sortProject<PERSON>pi, updateProject<PERSON><PERSON> } from '.'
import { IProjectKpi } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useProjectKpi = () => {
  const dispatch: AppDispatch = useDispatch()

  const getProjectKpiStates = useSelector((state: RootState) => state.projectKpi.getProjectKpiStates)
  const projectKpis = useSelector((state: RootState) => state.projectKpi.projectKpis)

  const getProjectKpisApi = (data: { period: string; project_name?: string }) => dispatch(getProjectKpis(data))
  const addProjectKpiApi = (data: IProjectKpi) => dispatch(addProjectKpi(data))
  const updateProjectKpiApi = (data: { id: number; data: IProject<PERSON><PERSON> }) => dispatch(updateProjectKpi(data))
  const sortProjectKpiApi = (data: any) => dispatch(sortProjectKpi(data))
  const deleteProjectKpiApi = (data: number) => dispatch(deleteProjectKpi(data))

  return {
    getProjectKpiStates,
    projectKpis,
    getProjectKpisApi,
    addProjectKpiApi,
    updateProjectKpiApi,
    sortProjectKpiApi,
    deleteProjectKpiApi,
  }
}
export default useProjectKpi

import { StatusEnum } from '../types'

export interface IKeyHighlightStates {
  getKeyHighlightStatus: StatusEnum
  addKeyHighlightStatus: StatusEnum
  deleteKeyHighlightStatus: StatusEnum
  updateKeyHighlightStatus: StatusEnum
  keyHighlights: IKeyHigh<PERSON>[]
  keyHighlight: IKey<PERSON><PERSON><PERSON>
  highlight: string[]
  isUpdateHighlight: boolean
}

export interface IKeyHighlights {
  id?: number
  period?: string
  project_name?: string
  phase?: string
  value?: string
  description?: string
  last_updated?: string
}

export interface IGetKeyHighlightsResponse {
  data: I<PERSON>eyHighlights[]
  message: string
  success: boolean
}

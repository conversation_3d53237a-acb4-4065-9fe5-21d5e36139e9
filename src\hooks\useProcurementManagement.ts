import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterProcurementManager,
  deleteProcurementManager,
  getMasterProcurementManager,
  updateMasterProcurementManager,
} from '../services/procurementManagement'
import { IGetMasterProcurementManagement, IUpdateProcureManagement } from '../services/procurementManagement/interface'

export const QUERY_PROCUREMENT_MANAGER = 'procurement-manager'

/**
 * Hook to fetch Procurement managers
 * @param enabled - Enables or disables the query
 */
export const useGetProcurementManagers = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterProcurementManagement>({
    queryKey: [QUERY_PROCUREMENT_MANAGER],
    queryFn: getMasterProcurementManager,
    enabled,
  })

  return {
    procureManagements: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Procurement manager
 */
export const useCreateProcurementManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterProcurementManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROCUREMENT_MANAGER] })
    },
  })
}

/**
 * Hook to delete a Procurement manager
 */
export const useDeleteProcurementManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteProcurementManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROCUREMENT_MANAGER] })
    },
  })
}

/**
 * Hook to update a Procurement manager
 */
export const useUpdateProcurementManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterProcurementManager,
    onMutate: async (updatedData: IUpdateProcureManagement) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROCUREMENT_MANAGER] })
      const previousData = queryClient.getQueryData<IGetMasterProcurementManagement>([QUERY_PROCUREMENT_MANAGER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterProcurementManagement>([QUERY_PROCUREMENT_MANAGER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, procurement_manager: updatedData.procurement_manager }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROCUREMENT_MANAGER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROCUREMENT_MANAGER] })
    },
  })
}

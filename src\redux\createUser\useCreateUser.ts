import { useDispatch, useSelector } from 'react-redux'
import { addUser, deleteUser, getUser, updateUser } from '.'
import { IUser } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useUser = () => {
  const dispatch: AppDispatch = useDispatch()

  const getUserStatus = useSelector((state: RootState) => state.user.getUserStatus)
  const users = useSelector((state: RootState) => state.user.users)

  const getUserApi = () => dispatch(getUser())
  const addUserApi = (data: IUser) => dispatch(addUser(data))
  const updateUserApi = (data: { data: IUser; id: string }) => dispatch(updateUser(data))
  const deleteUserApi = (data: number) => dispatch(deleteUser(data))

  return {
    getUserStatus,
    users,
    getUserApi,
    addUser<PERSON>pi,
    updateUser<PERSON><PERSON>,
    deleteUser<PERSON><PERSON>,
  }
}

export default useUser

import React, { useState } from 'react'
import styles from './HandoverStep.module.scss'
import { DrawerStates } from '../../interface'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TypographyField from '@/src/component/shared/typography'

const handoverSteps = [
  {
    id: 1,
    handoverStep: 'Consultant Snag',
    burnRate: 30,
    contractorRectificationDuration: 7,
    responseDuration: 7,
  },
  {
    id: 2,
    handoverStep: 'Consultant De-snag',
    burnRate: 30,
    contractorRectificationDuration: 14,
    responseDuration: 7,
  },
  {
    id: 3,
    handoverStep: 'CMC Snag',
    burnRate: 30,
    contractorRectificationDuration: 14,
    responseDuration: 7,
  },
  {
    id: 4,
    handoverStep: 'CMC Acceptance',
    burnRate: 30,
    contractorRectificationDuration: 7,
    responseDuration: 7,
  },
  {
    id: 5,
    handoverStep: 'Owner Snag (Home Orientation)',
    burnRate: 30,
    contractorRectificationDuration: 14,
    responseDuration: 7,
  },
  {
    id: 6,
    handoverStep: 'Owner De-snag',
    burnRate: 30,
    contractorRectificationDuration: 7,
    responseDuration: 7,
  },
  {
    id: 7,
    handoverStep: 'Key Handover',
    burnRate: 30,
    contractorRectificationDuration: 0,
    responseDuration: 7,
  },
]

const HandoverStep: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [data, setData] = useState(handoverSteps)

  const handleDelete = (id: number) => {
    setData((prev) => prev.filter((item) => item.id !== id))
    setDeleteModel(null)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'handoverStep',
      header: 'Handover Step',
      size: 250,
    },
    {
      accessorKey: 'burnRate',
      header: 'Burn Rate (Units/Weeks)',
      size: 200,
      isEditableCell: true,
      editableType: 'text',
    },
    {
      accessorKey: 'contractorRectificationDuration',
      header: 'Contractor Rectification Duration (days)',
      size: 250,
      isEditableCell: true,
      editableType: 'text',
    },
    {
      accessorKey: 'responseDuration',
      header: 'Response Duration (days)',
      size: 250,
      isEditableCell: true,
      editableType: 'text',
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text="Handover Step" />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>{data?.length > 0 && <TanStackTable rows={data as any} columns={columns} />}</div>

      {/* Simple Delete Confirmation */}
      {deleteModel !== null && (
        <div className={styles.deleteModal}>
          <p>Are you sure you want to delete this handover step?</p>
          <Button onClick={() => handleDelete(deleteModel)}>Yes</Button>
          <Button onClick={() => setDeleteModel(null)}>No</Button>
        </div>
      )}
    </div>
  )
}

export default HandoverStep

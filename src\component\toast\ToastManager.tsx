// components/ToastManager.tsx
import React, { useState, useCallback, useEffect } from 'react'
import Toast from './index'

interface ToastMessage {
  message: string
  type: 'success' | 'error' | 'info'
  duration?: number // Add duration here
}

let toastListener: ((message: string, type: 'success' | 'error' | 'info', duration?: number) => void) | null = null

const ToastManager: React.FC = () => {
  const [toast, setToast] = useState<ToastMessage | null>(null)

  const showToast = useCallback((message: string, type: 'success' | 'error' | 'info', duration?: number) => {
    setToast({ message, type, duration })
    setTimeout(() => setToast(null), duration)
  }, [])

  useEffect(() => {
    toastListener = showToast
    return () => {
      toastListener = null
    }
  }, [showToast])

  const handleClose = () => {
    setToast(null)
  }

  return toast ? (
    <Toast message={toast.message} type={toast.type} duration={toast.duration} onClose={handleClose} />
  ) : null
}

export default ToastManager

export const showCustomToast = (message: string, type: 'success' | 'error' | 'info', duration?: number) => {
  if (toastListener) {
    toastListener(message, type, duration)
  }
}

import { useDispatch, useSelector } from 'react-redux'
import { getGovernance, deleteGovernance, addGovernance, updateGovernance } from '.'
import { IGovernance } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useGovernance = () => {
  const dispatch: AppDispatch = useDispatch()

  const getGovernanceStatus = useSelector((state: RootState) => state.governance.getGovernanceStatus)
  const governances = useSelector((state: RootState) => state.governance.governances)

  const getGovernanceApi = (data: { period: string; project_name?: string }) => dispatch(getGovernance(data))
  const addGovernanceApi = (data: IGovernance) => dispatch(addGovernance(data))
  const updateGovernanceApi = (data: IGovernance) => dispatch(updateGovernance(data))
  const deleteGovernanceApi = (data: number) => dispatch(deleteGovernance(data))

  return {
    getGovernanceStatus,
    governances,
    getGovernance<PERSON>pi,
    addGovernan<PERSON><PERSON><PERSON>,
    updateGovernance<PERSON><PERSON>,
    deleteGovernance<PERSON><PERSON>,
  }
}
export default useGovernance

@import '/styles/color.scss';

.root {
  width: 100%;
  .autoCompleteRoot > div > div {
    padding-right: 0 !important;
  }
  .tagInput {
    padding: 1px 0 !important;
  }
  .downArrow {
    height: 18px;
    width: 18px;
    fill: $GRAY_500;
  }
  .chip {
    border: 0;
  }
  .inputText {
    font-size: 14px;
    line-height: 18px;
    font-family: Poppins;
    font-weight: 400;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: $BLACK;
  }
}

.popper {
  .paper {
    border-radius: 4px;
    border: 1px solid $WHITE;
    box-shadow: 0px 4px 30px 0px #0000000d;
    margin-top: 4px;
    .listBox {
      margin: 4px 4px 4px 0;
      max-height: 176px;
      overflow: auto;
      padding: 0;
      li {
        padding: 11px 0px 11px 16px;
        border-bottom: 1px solid $LIGHT;
        font-family: <PERSON>pins;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0em;
        text-align: left;
        color: $BLACK;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: transparent;
      }
    }
    .formGroupRoot {
      margin-left: 0;
      width: 100%;
      > span {
        padding: 0;
        min-width: 16px;
      }
      .checkboxLabel {
        padding-left: 4px;
        font-size: 14px;
        line-height: 16px;
        color: $GRAY_500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.dropdown {
  height: 32px !important;
  margin-bottom: 0 !important;
}
.labelText {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #808080;
}

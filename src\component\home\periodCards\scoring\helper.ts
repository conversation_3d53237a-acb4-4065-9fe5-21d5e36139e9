import { OwningEntities } from './constant'
import { IOwningEntity } from '@/src/services/owningEntity/interface'
import { IScoring } from '@/src/services/scoring/interface'

export const generateYearQuarterOptions = (startYear: string, endYear: string) => {
  const result: { label: string; value: string }[] = []
  const quarters = [
    { label: 'Q1', month: '01' },
    { label: 'Q2', month: '04' },
    { label: 'Q3', month: '07' },
    { label: 'Q4', month: '10' },
  ]

  for (let year = parseInt(startYear); year <= parseInt(endYear); year++) {
    quarters.forEach((q) => {
      result.push({
        label: `${year} ${q.label}`,
        value: `${year}-${q.month}-01`,
      })
    })
  }

  return result
}

export const getOverallSore = (res: IScoring) => {
  const schedule = res?.schedule_score ?? 0
  const variation = res?.variation_score ?? 0
  const expenditure = res?.expenditure_score ?? 0
  const overall_score = Number(schedule) + Number(variation) + Number(expenditure)
  return Number(overall_score?.toFixed(2))
}

export const getScoringTableData = (scoreData: IScoring[]) => {
  return scoreData?.map((res) => {
    return { ...res, overall_score: getOverallSore(res) }
  })
}

export const generateArrayBasedOnOwningEntity = (quarter: string, entityArray: IOwningEntity[]) => {
  return entityArray.map((entity) => {
    const match = OwningEntities?.find((e) => e.owning_entity === entity.owning_entity)
    return {
      id: crypto.randomUUID(),
      quarter,
      executive_entity: match ? match.label : null,
      label: entity.owning_entity,
      schedule_score: null,
      variation_score: null,
      expenditure_score: null,
    }
  })
}

import { useState, useEffect, useCallback } from 'react'

// Extendable hook with features for leading/trailing edge debounce and immediate execution
interface UseDebounceOptions {
  immediate?: boolean
  trailing?: boolean
}

const useDebounce = <T>(value: T, delay: number, options: UseDebounceOptions = {}) => {
  const { immediate = false, trailing = true } = options
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const [isLeading, setIsLeading] = useState(true)

  const handler = useCallback(() => {
    setDebouncedValue(value)
  }, [value])

  useEffect(() => {
    if (immediate && isLeading) {
      handler()
      setIsLeading(false)
    }

    const timeout = setTimeout(() => {
      if (trailing) {
        handler()
      }
    }, delay)

    return () => {
      clearTimeout(timeout)
    }
  }, [value, delay, immediate, trailing, handler])

  return debouncedValue
}

export default useDebounce

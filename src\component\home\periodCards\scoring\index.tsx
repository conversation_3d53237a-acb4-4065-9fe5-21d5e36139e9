import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import {
  generateArrayBasedOnOwningEntity,
  generateYearQuarterOptions,
  getOverallSore,
  getScoringTableData,
} from './helper'
import styles from './Scoring.module.scss'
import Button from '../../../shared/button'
import Typography<PERSON>ield from '../../../shared/typography'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import ComboBox from '@/src/component/shared/combobox'
import Loader from '@/src/component/shared/loader'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { numericValueSorting, sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import { canEditUser } from '@/src/helpers/helpers'
import { useGetOwningEntity } from '@/src/hooks/useOwningEntity'
import { useCreateScoring, useDeleteScoring, useGetScoring, useUpdateScoring } from '@/src/hooks/useScoring'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IScoring } from '@/src/services/scoring/interface'
import { replaceNewlinesWithSpace } from '@/src/utils/stringUtils'

const Scoring: React.FC<any> = ({ drawerStates, onClose }) => {
  const { scoring } = drawerStates
  const { owningEntities } = useGetOwningEntity(scoring)
  const [tableData, setTableData] = useState<IScoring[]>([])
  const [edit, setEdit] = useState(false)
  const [isDisableBtn, setIsDisableBtn] = useState(false)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState('')
  const yearQuarterOptions = generateYearQuarterOptions('2023', '2026')

  const formik = useFormik({
    initialValues: { yearQuarter: null, tableData: [] },
    onSubmit: async (values: any) => {
      refetch()
      setEdit(false)
      setIsDisableBtn(false)
      // formik.resetForm()
    },
  })

  //REACT-QUERY
  const { entityScoring, isLoading, isSuccess, refetch } = useGetScoring(false, formik.values.yearQuarter?.value)
  const { mutateAsync: addScoring } = useCreateScoring()
  const { mutate: updateScoring } = useUpdateScoring()
  const { mutateAsync: deleteScoring } = useDeleteScoring()
  const { currentUser } = useAuthorization()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()

  // Memoized Edit Permissions
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const onCellUpdate = async (cell: any, newValue: string, row: any) => {
    // NOTE: Update table data
    const updatedData = tableData?.map((row) => {
      if (row.id === cell.rowId) {
        const updateObj = {
          ...row,
          [cell.columnId]: cell.columnId === 'executive_entity' ? newValue || null : Number(newValue),
        }
        const overall_score = getOverallSore(updateObj)
        // NOTE: uncomment below code if want to error on cell blur
        // if (overall_score > 100 && cell.columnId !== 'executive_entity') {
        //   toast.error(`Overall score cannot exceed 100. Please adjust ${cell.columnId} of ${row?.label} Entity.`)
        //   //   return { ...row, overall_score: getOverallSore(row) }
        // }
        return { ...updateObj, overall_score: overall_score }
      }
      return row
    })
    setTableData([...updatedData])
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'label',
      header: 'Entity',
      editableType: 'text',
      filterType: 'text',
      size: 120,
    },
    {
      accessorKey: 'executive_entity',
      header: 'Executive Entity',
      editableType: 'textArea',
      filterType: 'text',
      isEditableCell: edit,
      onEditCell: (cell, newValue, row) => {
        const sanitizeValue = replaceNewlinesWithSpace(newValue)
        return onCellUpdate(cell, sanitizeValue, row)
      },
      size: 200,
    },
    {
      accessorKey: 'schedule_score',
      header: 'Schedule Score %',
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      size: 180,
      isEditableCell: edit,
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'variation_score',
      header: 'Variation Score %',
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      size: 185,
      isEditableCell: edit,
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'expenditure_score',
      header: 'Expenditure Score %',
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      size: 190,
      isEditableCell: edit,
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'overall_score',
      header: 'Overall Score %',
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      size: 170,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }) => (
        <div className={styles.actionButtons}>
          <DeleteIcon onClick={() => setDeleteModel(row?.original.id)} className={styles.deleteRowIcon} />
        </div>
      ),
      size: 70,
    },
  ]

  useEffect(() => {
    // NOTE: Selected quarter data
    if (isSuccess && entityScoring?.length > 0) {
      const data = getScoringTableData(entityScoring)
      setTableData(data)
      formik.setFieldValue('tableData', data)
    }
    // NOTE : Generate static data based owning entity of master table, when api return empty array for selected quarter
    if (isSuccess && entityScoring?.length === 0) {
      const data = generateArrayBasedOnOwningEntity(formik.values.yearQuarter?.value, owningEntities)
      setTableData([...data])
      formik.setFieldValue('tableData', data)
    }
  }, [entityScoring])

  const handleSave = async () => {
    try {
      const getScoresAbove100 = tableData?.filter((item) => (item.overall_score ?? 0) > 100)
      if (getScoresAbove100?.length > 0) {
        setIsValidationModel(true)
        const allEntity = getScoresAbove100?.map((res) => res?.label)?.join(', ')
        setValidationMessage(`Overall score for ${allEntity} must not be greater than 100.`)
        // NOTE : uncomment below line if table data set to initial values
        // setTableData([...formik.values.tableData])
        return
      }
      const updatedArray =
        entityScoring?.length === 0
          ? tableData?.map(({ id, overall_score, ...rest }) => rest)
          : tableData?.map(({ overall_score, ...rest }) => rest)
      if (entityScoring?.length === 0) {
        await addScoring({ entityScore: [...updatedArray] })
        refetch()
      } else {
        updateScoring({ entityScore: [...updatedArray] })
      }
      setEdit(false)
    } catch (error) {
      console.log('error: ', error)
    }
  }

  const handleCancel = () => {
    setEdit(false)
    // NOTE : setTableData to initialValue
    setTableData([...formik.values.tableData])
  }

  const handleDeleteEntity = async (id: number) => {
    if (entityScoring?.length > 0) {
      await deleteScoring(id)
      refetch()
    } else {
      const filterData = tableData?.filter((res) => res?.id?.toString() !== id?.toString())
      setTableData(filterData)
    }
    setDeleteModel(null)
  }

  const cols = useMemo(() => {
    return isEditForUser ? columns : columns?.filter((res) => res?.accessorKey !== 'action')
  }, [isEditForUser, edit])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Scoring'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ width: '50%' }}>
            <ComboBox
              options={yearQuarterOptions}
              labelText="Quarter"
              placeholder="Select Quarter Year"
              value={formik.values.yearQuarter}
              clearIcon={true}
              onChange={(val) => {
                formik.setFieldValue('yearQuarter', val)
                setIsDisableBtn(true)
              }}
            />
          </div>
          <div className={styles.buttons}>
            <Button
              className={styles.addProjectButton}
              type="submit"
              disabled={formik.isSubmitting || !isDisableBtn || !formik.values.yearQuarter}
            >
              {'Fetch Score Data'}
            </Button>
            {edit && tableData?.length > 0 ? (
              <div className={styles.editBtnContainer}>
                <Button onClick={handleSave}>Save</Button>
                <Button onClick={handleCancel}>Cancel</Button>
              </div>
            ) : (
              <>
                {tableData?.length > 0 && (
                  <Button
                    onClick={() => {
                      if (!isEditForUser) {
                        toast(`The current reporting period is locked`, {
                          icon: <WarningAmberOutlined />,
                        })
                      } else {
                        setEdit(true)
                      }
                    }}
                  >
                    Edit
                  </Button>
                )}
              </>
            )}
          </div>
        </form>
        {isLoading ? (
          <Loader />
        ) : (
          <>
            {tableData?.length >= 1 && (
              <TanStackTable rows={sortData(tableData, 'label')} columns={cols} isOverflow={true} />
            )}
          </>
        )}
      </div>
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={isValidationModel}
        onClose={() => setIsValidationModel(false)}
        content={<ValidationModel messages={[validationMessage]} onClose={() => setIsValidationModel(false)} />}
      />
      {deleteModel && (
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      )}
    </div>
  )
}

export default Scoring

'use client'

import type React from 'react'
import { useState } from 'react'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { Popover } from '@mui/material'
import { naturalSort } from '@/src/utils/sortingUtils'

type CommonPopoverForDisplayMultiSelectProps = {
  content: Array<{ label: string; value: number | string }>
  maxWidth?: number
  placement?: string
  trigger?: 'click' | 'hover'
}

const CommonPopoverForDisplayMultiSelect = ({
  content,
  maxWidth = 400,
  placement = 'right',
  trigger = 'click',
}: CommonPopoverForDisplayMultiSelectProps) => {
  const [anchorEl, setAnchorEl] = useState<SVGSVGElement | null>(null)
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)

  // Click handlers with improved event stopping
  const handleClick = (event: React.MouseEvent<SVGSVGElement>) => {
    event.stopPropagation()
    event.preventDefault()
    event.nativeEvent.stopImmediatePropagation()
    setAnchorEl(event.currentTarget)
  }

  // Improved hover handlers with better event prevention
  const handlePopoverOpen = (event: React.MouseEvent<SVGSVGElement>) => {
    event.stopPropagation()
    event.preventDefault()
    event.nativeEvent.stopImmediatePropagation()
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
    }
    setAnchorEl(event.currentTarget)
  }

  const handlePopoverClose = () => {
    const timeout = setTimeout(() => {
      setAnchorEl(null)
    }, 150)
    setHoverTimeout(timeout)
  }

  const handlePopoverMouseEnter = (event: React.MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
  }

  const handlePopoverMouseLeave = (event: React.MouseEvent) => {
    event.stopPropagation()
    setAnchorEl(null)
  }

  const handleClose = () => {
    setAnchorEl(null)
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
  }

  const handlePopoverClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    event.preventDefault()
    event.nativeEvent.stopImmediatePropagation()
  }

  const open = Boolean(anchorEl)

  const getAnchorOrigin = (): { vertical: 'top' | 'center' | 'bottom'; horizontal: 'left' | 'center' | 'right' } => {
    switch (placement) {
      case 'right':
        return { vertical: 'center', horizontal: 'right' }
      case 'left':
        return { vertical: 'center', horizontal: 'left' }
      case 'top':
        return { vertical: 'top', horizontal: 'center' }
      case 'bottom':
        return { vertical: 'bottom', horizontal: 'center' }
      default:
        return { vertical: 'center', horizontal: 'right' }
    }
  }

  const getTransformOrigin = (): { vertical: 'top' | 'center' | 'bottom'; horizontal: 'left' | 'center' | 'right' } => {
    switch (placement) {
      case 'right':
        return { vertical: 'center', horizontal: 'left' }
      case 'left':
        return { vertical: 'center', horizontal: 'right' }
      case 'top':
        return { vertical: 'bottom', horizontal: 'center' }
      case 'bottom':
        return { vertical: 'top', horizontal: 'center' }
      default:
        return { vertical: 'center', horizontal: 'left' }
    }
  }

  const getIconProps = () => {
    const baseProps = {
      sx: {
        fontSize: '16px',
        color: '#999',
        cursor: 'pointer',
        zIndex: 10002,
      },
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation()
        e.preventDefault()
        e.nativeEvent.stopImmediatePropagation()
      },
    }

    if (trigger === 'hover') {
      return {
        ...baseProps,
        'aria-owns': open ? 'mouse-over-popover' : undefined,
        'aria-haspopup': true,
        onMouseEnter: handlePopoverOpen,
        onMouseLeave: handlePopoverClose,
        'data-popover-trigger': true,
      }
    } else {
      return {
        ...baseProps,
        onClick: handleClick,
        'data-popover-trigger': true,
      }
    }
  }

  const getPopoverProps = () => {
    const baseProps = {
      open,
      anchorEl,
      onClose: handleClose,
      anchorOrigin: getAnchorOrigin(),
      transformOrigin: getTransformOrigin(),
      onClick: handlePopoverClick,
      slotProps: {
        paper: {
          elevation: 8,
          sx: {
            maxWidth: maxWidth,
            minWidth: '150px',
            backgroundColor: '#ffffff',
            border: '1px solid #e0e0e0',
            borderRadius: '6px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            padding: '16px',
            ml: 1,
            maxHeight: '350px',
            overflowY: 'auto',
            zIndex: 10003,
          },
          'data-popover-content': true,
        },
      },
    }

    if (trigger === 'hover') {
      return {
        ...baseProps,
        id: 'mouse-over-popover',
        sx: {
          pointerEvents: 'auto',
          zIndex: 10003,
        },
        disableRestoreFocus: true,
        onMouseEnter: handlePopoverMouseEnter,
        onMouseLeave: handlePopoverMouseLeave,
        container: document.body,
      }
    } else {
      return {
        ...baseProps,
        sx: { zIndex: 10003 },
        container: document.body,
      }
    }
  }

  if (!content || content.length === 0) {
    return null
  }

  return (
    <span
      className="popover-container"
      style={{
        display: 'inline-flex',
        marginLeft: '8px',
        position: 'relative',
        zIndex: 10002,
      }}
      onClick={(e) => {
        e.stopPropagation()
        e.preventDefault()
        e.nativeEvent.stopImmediatePropagation()
      }}
      onMouseDown={(e) => {
        e.stopPropagation()
        e.preventDefault()
        e.nativeEvent.stopImmediatePropagation()
      }}
      onMouseUp={(e) => {
        e.stopPropagation()
        e.preventDefault()
        e.nativeEvent.stopImmediatePropagation()
      }}
      data-popover-wrapper="true"
    >
      <InfoOutlinedIcon {...getIconProps()} />
      <Popover {...getPopoverProps()}>
        <div
          style={{
            color: '#555',
            fontSize: '12px',
            lineHeight: '1.4',
            wordBreak: 'break-word',
          }}
          onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
            e.nativeEvent.stopImmediatePropagation()
          }}
          onMouseDown={(e) => {
            e.stopPropagation()
            e.preventDefault()
            e.nativeEvent.stopImmediatePropagation()
          }}
          onMouseUp={(e) => {
            e.stopPropagation()
            e.preventDefault()
            e.nativeEvent.stopImmediatePropagation()
          }}
        >
          <ul style={{ padding: 0, margin: 0, paddingLeft: '18px' }}>
            {content
              // .sort((a, b) => naturalSort(a.label, b.label))
              .map((item, index) => {
                return (
                  <li style={{ paddingBottom: '3px' }} key={index}>
                    {item?.label || '-'}
                  </li>
                )
              })}
          </ul>
        </div>
      </Popover>
    </span>
  )
}

export default CommonPopoverForDisplayMultiSelect

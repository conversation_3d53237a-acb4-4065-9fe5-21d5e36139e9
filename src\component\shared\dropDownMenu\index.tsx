import * as React from 'react'
import { FormControl, RadioGroup } from '@mui/material'
import Button from '@mui/material/Button'
import FormControlLabel from '@mui/material/FormControlLabel'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import Radio from '@mui/material/Radio'
import { styled } from '@mui/material/styles'
import { ArrowDropDownIcon } from '@mui/x-date-pickers'
import styles from './dropDownMenu.module.scss'
import TextInputField from '../textInputField'

interface DropdownMenuButtonProps {
  buttonLabel?: string
  menuItems?: string[]
  selectedValue?: string // New prop for the selected value
  onChange?: (value: string) => void // Callback for onChange event
}

// const CustomButton = styled(Button)(({ theme }) => ({
//   backgroundColor: theme.palette.primary.main,
//   color: theme.palette.common.white,
//   borderRadius: "8px",
//   textTransform: "none",
//   padding: "6px 10px",
//   fontSize: "14px",
//   "&:hover": {
//     backgroundColor: theme.palette.primary.dark,
//   },
// }));

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderRadius: '8px',
  position: 'relative',
  top: '0',
  margin: '4px 10px',
  minWidth: '152px',
  padding: '0px 10px',
  height: '100%',
  backgroundColor: '#f0f4ff',
  '&:hover': {
    backgroundColor: '#e0eaff',
  },
}))

const DropdownMenuButton: React.FC<DropdownMenuButtonProps> = ({ buttonLabel, menuItems, selectedValue, onChange }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleChange = (item: string) => {
    onChange && onChange(item) // Call parent callback with selected value
    setAnchorEl(null) // Close the menu when an item is selected
  }

  return (
    <div className={styles.dropdownMenuButton}>
      <Button
        className={styles.button}
        id="custom-button"
        aria-controls={open ? 'custom-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        {buttonLabel}
      </Button>

      {/* <TextInputField
        variant="outlined"
        value={selectedValue}
        onClick={handleClick} // Use onClick to open the menu
        onChange={(e) => {}}
        readOnly // Ensure the input is read-only to prevent typing
        InputProps={{
          endAdornment: <ArrowDropDownIcon />, // Use ArrowDropDownIcon as the end icon
        }}
      /> */}
      <Menu
        id="custom-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'custom-button',
        }}
        PaperProps={{
          style: {
            borderRadius: '8px',
            marginTop: '0px',
            marginLeft: '0px',
          },
        }}
      >
        <FormControl component="fieldset">
          <RadioGroup aria-label="menu-items" name="menu-items" value={selectedValue}>
            {menuItems?.map((item, index) => (
              <div key={index} onClick={() => handleChange(item)}>
                <StyledMenuItem>
                  <FormControlLabel
                    value={item}
                    control={<Radio color="primary" />}
                    label={item}
                    style={{ margin: 0 }}
                  />
                </StyledMenuItem>
              </div>
            ))}
          </RadioGroup>
        </FormControl>
      </Menu>
    </div>
  )
}

export default DropdownMenuButton

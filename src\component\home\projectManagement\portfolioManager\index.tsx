import React, { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { IAddPortfolioManagerProps } from './interface'
import styles from './PortfolioManager.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import AvatarPicker from '@/src/component/shared/avatarPicker'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreatePortfolioManager,
  useDeletePortfolioManager,
  useGetPortfolioManagers,
  useUpdatePortfolioManager,
} from '@/src/hooks/usePortfolioManager'
import { IPortfolioManager } from '@/src/services/portfolioManager/interface'
import { extractAvatarUrlForPayload } from '@/src/utils/stringUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/
const phoneNumberRegex = /^[\d+\- ()]{9,15}$/
// /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/

const validationSchema = Yup.object({
  email: Yup.string().matches(emailRegex, 'Invalid email format').email('Invalid email format').notRequired(),
  phone_number: Yup.string().notRequired().matches(phoneNumberRegex, 'Invalid phone number'),
  // .phone('any', 'Invalid phone number').matches(phoneNumberRegex, 'Invalid phone number'),
})

const AddPortfolioManagers: React.FC<IAddPortfolioManagerProps> = ({ drawerStates, onClose }) => {
  const { addPortFolioManagers } = drawerStates
  //STATE
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  //REACT-QUERY
  const { portfolioManagers, isLoading: isFetching } = useGetPortfolioManagers(addPortFolioManagers)
  // MUTATIONS
  const { mutate: addMasterPortfolioManager, isPending: isAdding } = useCreatePortfolioManager()
  const { mutate: deletePortfolioManager } = useDeletePortfolioManager()
  const { mutate: updatePortfolioManager, isPending: isUpdating } = useUpdatePortfolioManager()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string, setSubmitting?: Function) => ({
    onSuccess: () => {
      successToast(successMsg)
      setEditIndex(null)
      formik.resetForm()
    },
    onError: (err: any) => {
      setSubmitting && setSubmitting(false)
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { portfolioManager: '', email: '', phone_number: '', location: '' },
    validationSchema,
    onSubmit: (values, { setSubmitting }) => {
      const payload = {
        portfolio_manager: values.portfolioManager,
        email: values.email,
        phone_number: values.phone_number,
        location: values.location,
      }
      if (editIndex !== null) {
        updatePortfolioManager(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record', setSubmitting),
        )
      } else {
        addMasterPortfolioManager(
          payload,
          handleMutationCallbacks('Record successfully added', 'Failed to add record', setSubmitting),
        )
      }
    },
  })

  const handleDeleteEntity = (id: number) => {
    deletePortfolioManager(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPortfolioManager = portfolioManagers?.find((manager: IPortfolioManager) => manager.id === id)
    if (editPortfolioManager) {
      formik.setValues({
        portfolioManager: editPortfolioManager?.portfolio_manager || '',
        email: editPortfolioManager?.email || '',
        phone_number: editPortfolioManager?.phone_number || '',
        location: editPortfolioManager?.location || '',
      })
      setEditIndex(id)
    }
  }

  const handleUpdateAvatar = async (id: number, avatar: string) => {
    const editPortfolioManager = portfolioManagers?.find((manager: IPortfolioManager) => manager.id === id)
    if (editPortfolioManager) {
      updatePortfolioManager(
        {
          id,
          avatar: extractAvatarUrlForPayload(avatar) as string,
          portfolio_manager: editPortfolioManager.portfolio_manager,
          email: editPortfolioManager?.email,
          phone_number: editPortfolioManager?.phone_number,
          location: editPortfolioManager?.location,
        },
        handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
      )
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'avatar',
      header: 'Avatar',
      size: 70,
      cell: ({ row }) => {
        const avatar = row?.original?.avatar ? [row?.original?.avatar?.url] : []
        return <AvatarPicker url={avatar} handleSelectAvatar={(val) => handleUpdateAvatar(Number(row?.id), val[0])} />
      },
    },
    {
      accessorKey: 'portfolio_manager',
      header: 'Portfolio Manager',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'email',
      header: 'Email',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'phone_number',
      header: 'Phone Number',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'location',
      header: 'Location',
      size: 150,
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row?.original.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.original.id)} className={styles.deleteRowIcon} />
        </div>
      ),
      size: 70,
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Portfolio Managers'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="portfolioManager"
              labelText={'Portfolio Manager'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.portfolioManager}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="email"
              labelText={'Email'}
              error={Boolean(formik.touched.email && formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email && formik.errors.email}
              placeholder="Enter Email Address"
              variant={'outlined'}
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="phone_number"
              labelText={'Phone Number'}
              placeholder="Enter phone number"
              error={Boolean(formik.touched.phone_number && formik.errors.phone_number)}
              helperText={formik.touched.phone_number && formik.errors.phone_number && formik.errors.phone_number}
              variant={'outlined'}
              value={formik.values.phone_number}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="location"
              labelText={'location'}
              placeholder="Enter Location"
              variant={'outlined'}
              value={formik.values.location}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={isUpdating || formik.isSubmitting || !formik.values.portfolioManager.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={isAdding || formik.isSubmitting || !formik.values.portfolioManager.trim()}
              >
                {'+ Add Portfolio Manager'}
              </Button>
            )}
          </div>
        </form>
        {isFetching ? (
          <Loader />
        ) : portfolioManagers?.length ? (
          <TanStackTable
            rows={sortData(portfolioManagers, 'portfolio_manager') as any}
            columns={columns}
            isOverflow={true}
          />
        ) : (
          <>No data</>
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddPortfolioManagers
function setSubmitting(arg0: boolean) {
  throw new Error('Function not implemented.')
}

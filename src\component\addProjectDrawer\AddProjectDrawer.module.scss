@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.container {
  width: 100%;
  height: 100%;

  @include respond-to('mobile') {
    max-width: 19.5rem;
    min-width: 19rem;
  }

  @include respond-to('tablet') {
    max-width: 37.5rem; // 600px
    min-width: 37rem;
    overflow: hidden;
  }

  @include respond-to('laptop') {
    max-width: 37.5rem; // 600px
    min-width: 37rem;
  }

  @include respond-to('desktop') {
    max-width: 85.625rem; // 1358px
    min-width: 64rem; // 1024px
  }

  @include respond-to('wideScreen') {
    max-width: 137.5rem; // 2200px
    min-width: 90rem; // 1440px
  }

  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.25rem;

    .headerTitle {
      font-family: Poppins, sans-serif;
      font-size: 1rem;
      font-weight: 600;
      line-height: 1.5rem;
      padding: 0.8125rem 1.25rem;

      @include respond-to('mobile') {
        font-size: 0.875rem;
        padding: 0.625rem 0;
      }

      @include respond-to('tablet') {
        font-size: 0.9375rem;
        padding: 0.75rem 0;
      }
      @include respond-to('laptop') {
        font-size: 0.9375rem;
        padding: 0.75rem 0;
      }
    }

    .actionButtons {
      display: flex;
      gap: 1.25rem;

      @include respond-to('mobile') {
        flex-direction: column;
      }

      .closeButton {
        padding: 0.5rem 0.625rem;
      }
    }
  }

  .content {
    margin: 1.25rem;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    height: calc(100% - 4rem);

    .addProjectButton {
      padding: 0.5rem 0.625rem;
      margin-left: 10px;
    }

    @include respond-to('mobile') {
      margin: 0.625rem;
      gap: 0.9375rem;
    }
  }

  .form {
    display: flex;
    gap: 1.25rem;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;

    @include respond-to('mobile') {
      gap: 0.9375rem;
      
      height: calc(100vh - 110px);
      margin-top: 10px;
    }
    @include respond-to('tablet') {
      margin-top: 0px;
      display: flex;
      gap: 1rem;
      flex-direction: column;
    }
  }
}

.textField {
  > div > div > input {
    min-width: 25rem; // 400px
    width: 100%;
    border: 1px solid $LIGHT;

    //USE_FOR: TextField Highlight things
    // &:focus {
    //   border-radius: 0.25rem;
    //   border: 1px solid $FOCUS_TEXTFIELD_BORDER;
    //   background: $FOCUS_TEXTFIELD_BG;
    // }

    @include respond-to('mobile') {
      min-width: 0rem;
    }
    @include respond-to('tablet') {
      min-width: 0rem;
    }
    @include respond-to('laptop') {
      min-width: 0rem;
    }
  }
}

.editActionButtons {
  display: flex;
  gap: 0.625rem; // 10px
}

.editContainer {
  display: flex;
  .editBox {
    display: flex;
    gap: 0.1875rem; // 3px
  }
}

//USE_FOR: ComboBox Highlight things
// .focusClass {
//   background: $FOCUS_TEXTFIELD_BG !important;
//   border-radius: 0.25rem; // 4px
//   border: 0.0625rem solid $FOCUS_TEXTFIELD_BORDER !important; // 1px
// }

.selectField {
  min-width: 25rem; // 400px
  width: 100%;

  @include respond-to('mobile') {
    min-width: 100%;
  }
}

.cursorPointer {
  cursor: pointer;
}

.actionButtons {
  display: flex;
  gap: 0.5rem; // 8px
}

.noteOfProject {
  font-size: 0.75rem; // 12px
  font-weight: bold;
  font-style: italic;
  padding-left: 10px;
}

.multiSelect {
  display: contents;
  > div > div > div {
    font-size: 0.875rem !important; // 14px
  }
}

.table {
  // overflow-y: auto;
  // overflow-x: auto;
  width: 83.125rem; // 1325px
  flex: 1;

  @include respond-to('mobile') {
    margin-top: 10px;
    width: 100%;
  }

  @include respond-to('tablet') {
    margin-top: 10px;
    margin-left: 0px;
    width: 100%;
  }

  @include respond-to('laptop') {
    margin-top: 10px;
    margin-left: 10px;
    width: 100%;
  }

  @include respond-to('desktop') {
    width: 100%;
  }

  @include respond-to('wideScreen') {
    width: 100%;
  }
}

.gridContainer {
  display: grid;

  @include respond-to('mobile') {
    grid-template-columns: 1fr;
    padding: 0.625rem;
    gap: 0.2rem;
  }

  @include respond-to('tablet') {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  @include respond-to('laptop') {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  @include respond-to('desktop') {
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 1.5rem;
  }

  @include respond-to('wideScreen') {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

.projectNameField {
  @include respond-to('mobile') {
    grid-column: unset;
  }

  @include respond-to('tablet') {
    grid-column: span 2;
  }

  @include respond-to('desktop') {
    grid-column: span 3;
  }
}

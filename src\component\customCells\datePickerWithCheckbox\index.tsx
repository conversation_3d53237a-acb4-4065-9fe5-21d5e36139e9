import React, { useState, ChangeEvent, useMemo } from 'react'
import { SwitchBaseProps } from '@mui/material/internal/SwitchBase'
import { format, parseISO } from 'date-fns'
import styles from './datePickerWithCheckbox.module.scss'
import Checkbox from '../../shared/checkbox'
import Loader from '../../shared/loader'
import CellDatePicker from '../../shared/tanStackTable/editableCell/CellDatepicker'
import ConfirmValueChangeModal from '../../shared/tanStackTable/editableCell/confirmValueChangeModal'
import { convertDDMMYYYYToLongDate } from '@/src/utils/dateUtils'
import { isLooseValueChanged } from '@/src/utils/stringUtils'

interface Props {
  val: string
  row: any
  column: any
  onCellUpdate: (cell: any, newValue: any, row: any) => any
  disabled: boolean
  onCheckBoxChange: SwitchBaseProps['onChange']
  checked?: boolean // <-- add this
}

const DatePickerWithCheckbox: React.FC<Props> = ({
  row,
  val,
  column,
  onCellUpdate,
  disabled = false,
  onCheckBoxChange,
  checked = false, // <-- add this
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [isLoader, setIsLoader] = useState(false)
  const [value, setValue] = useState<string | null>(val)
  // const [selectedDate, setSelectedDate] = useState<string | null>(null)

  const handleBlur = () => {
    setIsLoader(true)
    const newValue = value
    const cell = { rowId: row?.original?.id, columnId: column?.columnDef?.accessorKey }
    if (onCellUpdate) {
      const res = onCellUpdate(cell, newValue, row)
      if (res) {
        setIsLoader(false)
      }
    }
    setIsEditing(false)
  }

  const formattedDateValue = useMemo(() => {
    return column?.columnDef.editableType === 'date' && value
      ? convertDDMMYYYYToLongDate(value as string) || 'invalid date'
      : 'invalid date'
  }, [value])

  const resetToDefaultValue = () => {
    const activeCell = column?.columnDef?.accessorKey
    const defaultValue = row?.original?.[activeCell]?.toString() || ''
    setValue(defaultValue)
    setIsConfirmation(false)
    setIsEditing(false)
  }

  return (
    <div className={styles.wrapper}>
      {column.columnDef.isCustomCellEditable && (
        <div className={checked ? styles.checkboxCell : styles.highlightCheckBoxEditableCell}>
          <Checkbox onChange={onCheckBoxChange} checked={checked} disabled={isLoader} />
        </div>
      )}
      <div className={`${column.columnDef.isCustomCellEditable && !disabled && styles.highlightEditableCell}`}>
        {isLoader ? (
          <Loader smallLoader={true} width={'56px'} />
        ) : isEditing && column.columnDef.isCustomCellEditable && !disabled ? (
          <>
            <CellDatePicker
              placeholder="dd-MM-yyyy"
              className={styles.dataPickerInput}
              value={formattedDateValue}
              onChange={(val) => {
                if (val == 'Invalid Date' || val === null || !val) return setValue(null)
                const formattedDate = format(parseISO(val.toISOString()), 'dd-MM-yyyy')
                setValue(formattedDate)
              }}
              onAccept={(val) => {
                const activeCell = column?.columnDef?.accessorKey
                const defaultValue = row?.original?.[activeCell]?.toString() || ''
                const formattedDate =
                  val == 'Invalid Date' ? null : val ? format(parseISO(val.toISOString()), 'dd-MM-yyyy') : null

                if (column.columnDef.isSaveConfirmationRequired) {
                  const changed = isLooseValueChanged(formattedDate, defaultValue)
                  changed ? setIsConfirmation(true) : resetToDefaultValue()
                } else {
                  handleBlur()
                }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'transparent',
                  width: column?.getSize() - 70,
                  textAlign: column?.columnDef?.align || 'left',
                  flex: column?.columnDef.flex,
                  cursor: column?.columnDef.cursor,
                  borderTop: 0,
                  borderRight: 0,
                  borderLeft: 0,
                  borderRadius: 0,
                },
                '& .MuiOutlinedInput-input': {
                  padding: '6px 0px 6px 0px',
                  fontWeight: '400',
                  fontSize: '12px',
                  lineHeight: '18px',
                },
                '& .Mui-focused .MuiOutlinedInput-root': {
                  backgroundColor: '#f4f4fc',
                },
                '& .Mui-error': {
                  borderBottom: '1px solid #ffffff',
                },
              }}
            />
          </>
        ) : (
          <div className={styles.text} onClick={() => setIsEditing(true)}>
            {value ? value : '-'}
          </div>
        )}
        <ConfirmValueChangeModal
          open={column.columnDef.isSaveConfirmationRequired && isConfirmation}
          header={column.columnDef.header}
          value={value}
          onClose={resetToDefaultValue}
          handleConfirm={handleBlur}
        />
      </div>
    </div>
  )
}

export default DatePickerWithCheckbox

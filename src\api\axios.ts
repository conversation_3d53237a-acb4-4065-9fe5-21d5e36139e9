import axios, { AxiosError, AxiosInstance } from 'axios'
import axiosRetry, { exponentialDelay, isNetworkOrIdempotentRequestError } from 'axios-retry'

/**
 * Adds authorization headers and other request trace details.
 */
export const addTraceInterceptors = (api: AxiosInstance): void => {
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error: AxiosError) => {
      return Promise.reject(error)
    },
  )
}
/**
 * Determines if the error is due to too many requests.
 */
export const isTooManyRequestsError = (error: AxiosError): boolean => {
  return error.response?.status === 429 && error.code !== 'ECONNABORTED'
}

/**
 * Configures retry logic for the given Axios instance.
 */
export const addRetryLogic = (api: AxiosInstance): void => {
  axiosRetry(api, {
    retries: 5,
    retryDelay: exponentialDelay,
    retryCondition: (error: AxiosError) => isNetworkOrIdempotentRequestError(error) || isTooManyRequestsError(error),
  })
}

const externalApiInstance = axios.create()

/**
 * Axios instance with our gateway configuration
 */
const apiInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
})

addTraceInterceptors(apiInstance)

// PURPOSE: Automatically retry the API call if the previous attempt fails.
// addRetryLogic(apiInstance)
// addRetryLogic(externalApiInstance)

// #############
// ## EXPORTS ##
// #############

export const api = apiInstance

export const externalApi = externalApiInstance

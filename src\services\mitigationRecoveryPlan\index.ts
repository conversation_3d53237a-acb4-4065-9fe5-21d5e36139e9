import { IMitigationRecoveryPlanPayload, IUpdateMitigationRecoveryPlan } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_MITIGATION_RECOVERY_PLAN_URL = `/master-mitigation-recovery-plan`

// Get Master Mitigation Recovery Plan data
export const getMitigationRecoveryPlans = async () => {
  const response = await api.get(`${MASTER_MITIGATION_RECOVERY_PLAN_URL}`)
  return response?.data
}

export const deleteMitigationRecoveryPlan = async (data: number) => {
  const response = await api.delete(`${MASTER_MITIGATION_RECOVERY_PLAN_URL}/${data}`)
  return response?.data
}

// Create Master Mitigation Recovery Plan entry
export const createMitigationRecoveryPlan = async (data: IMitigationRecoveryPlanPayload) => {
  const response = await api.post(`${MASTER_MITIGATION_RECOVERY_PLAN_URL}`, data)
  return response
}

// Update Master Mitigation Recovery Plan entry
export const updateMitigationRecoveryPlan = async (data: IUpdateMitigationRecoveryPlan): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_MITIGATION_RECOVERY_PLAN_URL}/${data.id}`, { ...rest })
  return response
}

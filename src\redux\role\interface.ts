import { StatusEnum } from '../types'

export interface IRoleState {
  getRoleStatus: StatusEnum
  addRoleStatus: StatusEnum
  deleteRoleStatus: StatusEnum
  updateRoleStatus: StatusEnum
  viewPermissionsStatus: StatusEnum
  projectAndEntityStatus: StatusEnum
  projectAndEntities: []
  permissions: []
  roles: IRole[]
  role: IRole
}

export interface IRole {
  id?: any
  role_name?: string
  view_permissions?: string[]
  permission_type?: string
  owning_entities?: string[]
  projects?: string[]
}

export interface IGetMasterRatingResponse {
  data: IRole[]
  message: string
  success: true
}

import { useDispatch, useSelector } from 'react-redux'
import { deleteFile, fetchImage, updatePhase, updateOverallPhaseWithMultiPhase, uploadFile } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useFile = () => {
  const dispatch: AppDispatch = useDispatch()

  const UploadFileApi = (data: any) => dispatch(uploadFile(data))
  const deleteFileApi = (id: number) => dispatch(deleteFile(id))
  const updatePhaseApi = (data: any) => dispatch(updatePhase(data))
  const updateOverallPhaseWithMultiPhaseApi = (data: any) => dispatch(updateOverallPhaseWithMultiPhase(data))
  const getImageApi = (data: any) => dispatch(fetchImage(data))

  return {
    UploadFileApi,
    getImageApi,
    deleteFileApi,
    updatePhaseApi,
    updateOverallPhaseWithMultiPhaseApi,
  }
}

export default useFile

@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.noDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 200px;
  color: $NEUTRAL_400;
  font-size: 18px;
  font-weight: 500;
  background: $PULSE_LIGHT_GRAY;
  border-radius: 8px;
  padding: 20px;
  border: 1px dashed $PULSE_GRAY_8;
  box-shadow: 0 2px 5px $BOX_SHADOW;
  transition: all 0.3s ease-in-out;

  &:hover {
    background-color: $PULSE_GRAY;
  }
}

.noDataIcon {
  font-size: 50px !important;
  color: $GRAY_500;
  margin-bottom: 10px;
}

/* Responsive Design */
@include respond-to('tablet') {
  .noDataContainer {
    height: 180px;
    font-size: 16px;
  }

  .noDataIcon {
    font-size: 45px !important;
  }
}

@include respond-to('mobile') {
  .noDataContainer {
    height: 150px;
    font-size: 14px;
    padding: 15px;
  }

  .noDataIcon {
    font-size: 40px !important;
  }
}

@include respond-to('laptop') {
  .noDataContainer {
    height: 200px;
    font-size: 18px;
  }

  .noDataIcon {
    font-size: 50px !important;
  }
}

@include respond-to('desktop') {
  .noDataContainer {
    height: 220px;
    font-size: 20px;
  }

  .noDataIcon {
    font-size: 55px !important;
  }
}

@include respond-to('wideScreen') {
  .noDataContainer {
    height: 250px;
    font-size: 22px;
  }

  .noDataIcon {
    font-size: 60px !important;
  }
}

export interface IMitigationRecoveryPlanPayload {
  mitigation_recovery_plan: string
  mitigation_recovery_plan_code: string
}

export interface IUpdateMitigationRecoveryPlan extends IMitigationRecoveryPlanPayload {
  id: number
}

export interface IMitigationRecoveryPlan {
  id: number
  mitigation_recovery_plan: string
  mitigation_recovery_plan_code: string
}

export interface IGetMitigationRecoveryPlansRes {
  data: IMitigationRecoveryPlan[]
  message: string
  success: true
}

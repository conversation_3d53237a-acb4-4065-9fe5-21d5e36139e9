import React from 'react'
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined'
import RestartAltIcon from '@mui/icons-material/RestartAlt'
import styles from './FilterOption.module.scss'
import CustomTooltip from '../../shared/tooltip'
import useSearchDrawer from '@/src/redux/searchDrawer/useSearchDrawer'

const FilterOption = ({ handleFilterSelect }: any) => {
  const {
    searchQuery,
    categoryValue,
    entityValue,
    cpds,
    project_id,
    classificationValue,
    projectTypeValue,
    locationValue,
    portFolio,
    designManager,
    executive,
    deliveryDirector,
    control,
    procure,
    projectStatus,
    contractTypeValue,
    deviationStatusValue,
    startYear,
    labelValue,
    overall_forecasted_finish_date,
    userFilterFields,
    setStartYearApi,
    setProjectStatusApi,
    setSearchQueryApi,
    setCategoryValueApi,
    setEntityValueApi,
    setCpdsApi,
    setProjectIdApi,
    setClassificationValue<PERSON><PERSON>,
    setProjectTypeValue<PERSON><PERSON>,
    setLocationValue<PERSON><PERSON>,
    setPortFolioApi,
    setDesignManagerApi,
    setExecutiveApi,
    setDeliveryDirectorApi,
    setControlApi,
    setProcureApi,
    setDesignProjectOwnerApi,
    setContractTypeApi,
    setLabelValueApi,
    setDeviationStatusApi,
    setOverallForecastedFinishDateApi,
    setUserFilterFieldsApi,
    designProjectOwner,
    delivery_project_manager,
    setSvpApi,
  } = useSearchDrawer()

  const filterOptions = [
    {
      title: 'Category',
      field: 'entity_category',
      resetField: 'categoryValue',
      value: categoryValue,
      //  require: true
    },
    {
      title: 'Owning Entity',
      field: 'owning_entity',
      resetField: 'entityValue',
      value: entityValue,
      // require: true
    },
    {
      title: 'Contract type',
      field: 'contract_type',
      resetField: 'contractTypeValue',
      value: contractTypeValue,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Stage',
      field: 'project_status',
      resetField: 'projectStatus',
      value: projectStatus,
      // require: false,
    },
    {
      title: 'Project Classifications',
      field: 'project_classification',
      resetField: 'classificationValue',
      value: classificationValue,
      // require: true,
    },
    {
      title: 'Project Type',
      field: 'project_type',
      resetField: 'projectTypeValue',
      value: projectTypeValue,
      // require: true,
    },
    {
      title: 'Location',
      field: 'location',
      type: 'semiColon',
      resetField: 'locationValue',
      value: locationValue,
      // require: false,
    },
    {
      title: 'ERP Project ID',
      field: 'project_id',
      resetField: 'project_id',
      value: project_id,
      //  require: false
    },
    {
      title: 'CPDS Project ID',
      field: 'CPDS_project_id',
      resetField: 'cpds',
      value: cpds,
      //  require: false
    },
    {
      title: 'Project Deviation',
      field: 'deviation_status',
      resetField: 'deviationStatusValue',
      value: deviationStatusValue,
      // require: false,
    },
    {
      title: 'Start Year',
      field: 'decree_date',
      resetField: 'startYear',
      value: startYear,
      type: 'startYear',
      // require: false,
    },
    {
      title: 'Labels',
      field: 'label',
      resetField: 'labelValue',
      value: labelValue,
      type: 'boolean',
      // require: true,
    },
    {
      title: 'Portfolio Manager',
      field: 'portfolio_manager',
      resetField: 'portFolio',
      value: portFolio,
      // require: false,
    },
    {
      title: ' Design Executive Director',
      field: 'design_executive_director',
      resetField: 'designProjectOwner',
      value: designProjectOwner,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Design Manager',
      field: 'design_project_manager',
      resetField: 'designManager',
      value: designManager,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Procurement Manager',
      field: 'procurement_manager',
      resetField: 'procure',
      value: procure,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Controls Manager',
      field: 'controls_manager',
      resetField: 'control',
      value: control,
      //  require: false
    },
    {
      title: 'Delivery Executive Director',
      field: 'executive_director',
      resetField: 'executive',
      value: executive,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Delivery director',
      field: 'director',
      resetField: 'deliveryDirector',
      value: deliveryDirector,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Delivery PM',
      field: 'delivery_project_manager',
      resetField: 'delivery_project_manager',
      value: delivery_project_manager,
      type: 'semiColon',
      // require: false,
    },
    {
      title: 'Overall Forecast Finish',
      field: 'overall_forecasted_finish_date',
      resetField: 'overall_forecasted_finish_date',
      value: overall_forecasted_finish_date,
      type: 'date',
      // require: false,
    },
  ]

  const resetFilter = (filterField: string) => {
    const resetMap: Record<string, () => void> = {
      categoryValue: () => setCategoryValueApi([]),
      entityValue: () => setEntityValueApi([]),
      cpds: () => setCpdsApi([]),
      project_id: () => setProjectIdApi([]),
      classificationValue: () => setClassificationValueApi([]),
      projectTypeValue: () => setProjectTypeValueApi([]),
      designProjectOwner: () => setDesignProjectOwnerApi([]),
      svp: () => setSvpApi([]),
      locationValue: () => setLocationValueApi([]),
      portFolio: () => setPortFolioApi([]),
      designManager: () => setDesignManagerApi([]),
      executive: () => setExecutiveApi([]),
      deliveryDirector: () => setDeliveryDirectorApi([]),
      control: () => setControlApi([]),
      procure: () => setProcureApi([]),
      projectStatus: () => setProjectStatusApi([]),
      startYear: () => setStartYearApi([]),
      contractTypeValue: () => setContractTypeApi([]),
      labelValue: () => setLabelValueApi([]),
      deviationStatusValue: () => setDeviationStatusApi([]),
      overall_forecasted_finish_date: () => setOverallForecastedFinishDateApi([]),
    }

    const resetAction = resetMap[filterField]
    if (resetAction) {
      resetAction()
      const filterDefaultField = userFilterFields?.filter((res) => res !== filterField)
      setUserFilterFieldsApi([...filterDefaultField])
    } else {
      console.warn(`Unknown filter field: ${filterField}`)
    }
  }

  return (
    <div className={styles.options}>
      {/* {filterOptions.map(({ title, field, resetField, value, type, require }) => ( */}
      {filterOptions.map(({ title, field, resetField, value, type }) => (
        <div className={styles.option} key={field}>
          <div className={styles.title}>
            <span>{title}</span>
          </div>
          <div className={styles.actions}>
            <FilterAltOutlinedIcon
              className={styles.filterIcon}
              // onClick={(e: any) => handleFilterSelect(e, field, type, require)}
              onClick={(e: any) => handleFilterSelect(e, field, type)}
            />
            <CustomTooltip title={value?.length ? 'Reset filter' : 'No filter applied'}>
              <div
                className={`${styles.resetIconWrapper} ${value?.length ? styles.activeResetIcon : ''}`}
                onClick={() => value?.length && resetFilter(resetField)}
              >
                <RestartAltIcon className={styles.resetIcon} style={{ color: value?.length ? '#ea3323' : '#bebebe' }} />
              </div>
            </CustomTooltip>
          </div>
        </div>
      ))}
    </div>
  )
}

export default FilterOption

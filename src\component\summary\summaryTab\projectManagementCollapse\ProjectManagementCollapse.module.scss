@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.fields {
  display: flex;
  // gap: 50px;
  justify-content: space-between;
  padding: 20px 0px;
  margin: 0 12px;
  @include respond-to('mobile') {
    flex-direction: column;
    // gap: 5px;
    padding-bottom: 0;
  }
  @include respond-to('laptop') {
    flex-direction: row;
    // gap: 20px;
    padding-bottom: 0;
    border-bottom: 1px solid #8f8f8f;
  }
  @include respond-to('desktop') {
    // gap: 30px;
  }
  &:last-child {
    padding-bottom: 10px;
  }
  * {
    box-sizing: border-box;
  }
}

.leftContent {
  width: 50%;
  overflow: hidden;
  padding: 0 15px;
  @include respond-to('mobile') {
    width: 100%;
  }

  @include respond-to('laptop') {
    width: 50%;
  }
}

.leftContentBorder {
  > div {
    border-bottom: 1px solid #eaeaea;
    padding: 10px 0px;
  }
}

.rightContent {
  width: 50%;
  padding: 0 15px;
  @include respond-to('mobile') {
    width: 100%;
  }

  @include respond-to('laptop') {
    width: 50%;
  }
}

.groupCheckbox {
  // min-width: 300px !important;
  padding-right: 10px;
  > div > div > div > span {
    border-bottom: none !important;
  }
  @include respond-to('mobile') {
    border-bottom: 1px solid $LIGHT_200 !important;
    display: flex;
    align-items: center;
    width: 100%;
  }
  @include respond-to('tablet') {
    display: unset;
    border-bottom: none !important;
    width: unset;
  }
  @include respond-to('laptop') {
    display: flex;
    border-bottom: 1px solid $LIGHT_200 !important;
    padding-bottom: 5px;
    width: 99%;
  }
  @include respond-to('desktop') {
    display: unset;
    border-bottom: none !important;
    width: unset;
  }
}

.labelStyle {
  min-width: 50px;

  @include respond-to('mobile') {
    min-width: 100px !important;
    max-width: 120px !important;
    white-space: unset !important;
  }

  @include respond-to('tablet') {
    min-width: 130px !important;
    max-width: 140px !important;
    white-space: unset !important;
  }

  @include respond-to('laptop') {
    min-width: 140px !important;
    max-width: 150px !important;
    white-space: unset !important;
  }
}

.dotStyle {
  min-height: 23px;
  padding: 9px 0 6px 5px;
  border-bottom: 1px solid $LIGHT_200;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  padding-right: 4px;
}

.valueStyle {
  padding: 9px 0 6px 8px;
  white-space: nowrap;
  width: 100%;
  border-bottom: 1px solid $LIGHT_200;
}

.mainContainer {
  width: 100%;
}

.twoElementContainer {
  // display: grid;
  // grid-template-columns: 1fr 1fr;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  // gap: 16px;
  border-bottom: 1px solid $LIGHT_200;


  > div {
    min-width: 315px;
    flex-grow: 1;
    width: 50%;
  }

  @include respond-to('mobile') {
    // grid-template-columns: 1fr;
    :first-child {
      border-bottom: 1px solid $LIGHT_200;
    }
  }
  @include respond-to('laptop') {
    // grid-template-columns: 1fr;
  }
  @include respond-to('desktop') {
    // grid-template-columns: 1fr 1fr;
    :first-child {
      border-bottom: none;
    }
  }
  > div > div > p {
    height: auto;
    max-height: 50px;
    overflow: scroll;
    text-wrap: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 4px;
    }
  }
  // NOTE : below is for show text with ellipsis when overflow
  // > div {
  //   display: grid;
  //   grid-template-columns: auto 1fr; /* Label and value */
  //   gap: 4px;
  //   overflow: hidden;
  //   min-width: 0;
  // }

  // > div > div {
  //   overflow: hidden;
  //   text-overflow: ellipsis;
  //   white-space: nowrap;
  //   min-width: 0;
  // }
}

.editTwoElementContainer {
  // display: grid;
  // grid-template-columns: 1fr 1fr;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  // gap: 16px;

  > div {
    min-width: 315px;
    flex-grow: 1;
    width: 50%;
  }

  @include respond-to('mobile') {
    // grid-template-columns: 1fr;
    > div {
      border-bottom: 1px solid #eaeaea;
      padding-top: 5px;
    }
  }
  @include respond-to('laptop') {
    // grid-template-columns: 1fr;
  }
  @include respond-to('desktop') {
    // grid-template-columns: 1fr 1fr;
    padding-top: 2px !important;
    > div {
      border-bottom: none !important;
    }
  }
}

.singleElementContainer {
  display: grid;
  grid-template-columns: 1fr;
  overflow: hidden;
  > div > div > p {
    height: auto;
    max-height: 50px;
    overflow: scroll;
    text-wrap: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 4px;
    }
  }
}

.budgetUplift {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid $LIGHT_200;
  border-top: none;
  padding: 4px 0 8px 4px;
  @include respond-to('mobile') {
    flex-direction: column;
  }
  @include respond-to('tablet') {
    flex-direction: row;
  }
  @include respond-to('laptop') {
    flex-direction: column;
  }
  @include respond-to('desktop') {
    flex-direction: row;
  }
}

.borderBottomNone {
  border-bottom: none !important;
}

.budgetValue {
  min-height: 22px;
  padding: 0px 0 8px 8px;
  min-width: 100px !important;
  border-bottom: 1px solid $LIGHT_200;
}

.container {
  padding: 15px;
  > div > div > p {
    height: auto;
    max-height: 50px;
    overflow: scroll;
    text-wrap: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 4px;
    }
  }
}

.richTextEditor {
  padding-top: 8px;
  height: calc(100%) !important;
  // height: 142px !important;
  // @include respond-to('mobile') {
  //   height: 85px !important;
  //   > div > div {
  //     min-height: 85px !important;
  //   }
  // }
  // @include respond-to('laptop') {
  //   height: 376px !important;
  //   > div > div {
  //     min-height: 376px !important;
  //   }
  // }
  // @include respond-to('desktop') {
  //   height: 147px !important;
  //   > div > div {
  //     min-height: 147px !important;
  //   }
  // }
}

.editRichTextEditor {
  // height: 264px !important;
  // padding: 4px;
  // padding-bottom: 0;
  // @include respond-to('mobile') {
  //   height: 100px !important;
  //   > div > div {
  //     min-height: 100px !important;
  //   }
  // }
  // @include respond-to('laptop') {
  //   height: 505px !important;
  //   > div > div {
  //     min-height: 505px !important;
  //   }
  // }
  // @include respond-to('desktop') {
  //   height: 285px !important;
  //   > div > div {
  //     min-height: 285px !important;
  //   }
  // }
}

.reasonCategoryContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
  border-bottom: 1px solid $LIGHT_200;
  > div > div > div > div {
    height: 100%;
  }
  @include respond-to('mobile') {
    grid-template-columns: 1fr;
    gap: 10px;
    :first-child {
      border-bottom: 1px solid $LIGHT_200;
    }
  }

  @include respond-to('laptop') {
    grid-template-columns: 1fr 1fr;
    gap: 0px;
    :first-child {
      border-bottom: none;
    }
    > :nth-child(1) {
      margin-right: 12px;
    }
    > :nth-child(2) {
      margin-left: 10px;
    }
  }
  > div > div > p {
    height: auto;
    max-height: 50px;
    overflow: scroll;
    text-wrap: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 4px;
    }
  }
}

.editReasonCategoryContainer {
  padding-bottom: 10px;
  @include respond-to('mobile') {
    :first-child {
      border-bottom: none;
    }
  }
}

.borderBottom {
  @include respond-to('mobile') {
    border-bottom: 1px solid #eaeaea !important;
  }
  @include respond-to('laptop') {
    border-bottom: none !important;
  }
}

.multiselect {
  > div > div {
    > div {
      display: flex !important;
      word-wrap: break-word;
    }
  }
}

.numberField {
  > div > div {
    max-height: unset !important;
  }
  input {
    height: 33px !important;
  }
}

.containerBorderBottom {
  border-bottom: 1px solid #eaeaea !important;
}

.rich_root {
  height: calc(100%) !important;
  min-height: 85px;

  @include respond-to('laptop') {
    :global(.tiptap.ProseMirror) {
      max-height: unset !important;
    }
  }
}

.rich_root_project_key {
  height: calc(100% - 194px) !important;
  min-height: 85px;

  @include respond-to('laptop') {
    // max-height: 942px;
    :global(.tiptap.ProseMirror) {
      max-height: unset !important;
    }
  }

  // @include respond-to('desktop') {
  //   max-height: 462px;
  // }

  // @include respond-to('wideScreen') {
  //   max-height: 423px;
  // }
}

.edit_rich_root {
  height: 100% !important;
  min-height: 85px;

  @include respond-to('laptop') {
    :global(.tiptap.ProseMirror) {
      max-height: unset !important;
    }
  }
}

.edit_rich_root_project_key {
  height: calc(100% - 315px) !important;
  min-height: 85px;

  @include respond-to('laptop') {
    // max-height: 1140px;
    :global(.tiptap.ProseMirror) {
      max-height: unset !important;
    }
  }

  // @include respond-to('desktop') {
  //   max-height: 610px;
  // }
}

.lowerRightContent {
  :global(#third-section > div) {
    margin-bottom: 10px;
  }
}

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './MitigationRecoveryPlan.module.scss'
import { DrawerStates } from '../../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateMitigationRecoveryPlan,
  useDeleteMitigationRecoveryPlan,
  useGetMitigationRecoveryPlan,
  useUpdateMitigationRecoveryPlan,
} from '@/src/hooks/useMitigationRecoveryPlan'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const MitigationRecoveryPlan: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({
  drawerStates,
  onClose,
}) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { mitigationRecoveryPlan } = drawerStates
  //REACT-QUERY
  const { mitigationRecoveryPlans } = useGetMitigationRecoveryPlan(mitigationRecoveryPlan)
  // MUTATIONS
  const { mutate: addMitigationRecoveryPlan } = useCreateMitigationRecoveryPlan()
  const { mutate: deleteMitigationRecoveryPlan } = useDeleteMitigationRecoveryPlan()
  const { mutate: updateMitigationRecoveryPlan } = useUpdateMitigationRecoveryPlan()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: {
      mitigationRecoveryPlan: '',
      mitigationRecoveryPlanCode: '',
    },
    onSubmit: async (values) => {
      const payload = {
        mitigation_recovery_plan: values.mitigationRecoveryPlan,
        mitigation_recovery_plan_code: values.mitigationRecoveryPlanCode,
      }
      if (editIndex !== null) {
        updateMitigationRecoveryPlan(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMitigationRecoveryPlan(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteMitigationRecoveryPlan(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPlan = mitigationRecoveryPlans.find((plan) => plan.id === id)
    formik.setValues({
      mitigationRecoveryPlan: editPlan?.mitigation_recovery_plan || '',
      mitigationRecoveryPlanCode: editPlan?.mitigation_recovery_plan_code || '',
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    { accessorKey: 'mitigation_recovery_plan_code', header: 'Code', flex: 1 },
    { accessorKey: 'mitigation_recovery_plan', header: 'Mitigation Recovery Plan', flex: 1 },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.row?.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text="Add Mitigation Recovery Plan"
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div className={styles.formGroup}>
            <TextInputField
              className={styles.field}
              name="mitigationRecoveryPlan"
              labelText="Mitigation Recovery Plan"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.mitigationRecoveryPlan}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.field}
              name="mitigationRecoveryPlanCode"
              labelText="Mitigation Recovery Plan Code"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.mitigationRecoveryPlanCode}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <Button
            className={styles.submitButton}
            type="submit"
            disabled={
              (!formik.values.mitigationRecoveryPlan && !formik.values.mitigationRecoveryPlanCode) ||
              formik.isSubmitting
            }
          >
            {editIndex !== null ? '+ Update Mitigation Recovery Plan' : '+ Add Mitigation Recovery Plan'}
          </Button>
        </form>

        {mitigationRecoveryPlans?.length > 0 && (
          <TanStackTable
            rows={sortData(mitigationRecoveryPlans, 'mitigation_recovery_plan_code') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default MitigationRecoveryPlan

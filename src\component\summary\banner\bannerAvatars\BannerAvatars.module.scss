@import '/styles/color.scss';

.isAccordion {
  display: none;
  transition: all 0.7s ease;
}

.detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.avatarDetailContainer {
  display: flex;
  overflow-y: auto;
  // flex-wrap: wrap;
  gap: 0.625rem; // 10px → 0.625rem

  &::-webkit-scrollbar {
    width: 0; // Hide scrollbar
  }

  &::-webkit-scrollbar-track {
    border-radius: 0.3125rem; // 5px → 0.3125rem
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 0.3125rem; // 5px → 0.3125rem
  }
}

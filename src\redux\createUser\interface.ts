import { StatusEnum } from '../types'

export interface ICreateUserState {
  getUserStatus: StatusEnum
  addUserStatus: StatusEnum
  deleteUserStatus: StatusEnum
  updateUserStatus: StatusEnum
  users: IUser[]
  user: IUser
}
export interface IUser {
  id?: string
  email?: string
  user_type?: string
  organization?: string
  name?: string
  role?: string
}

export interface IGetMasterRatingResponse {
  data: IUser[]
  message: string
  success: true
}

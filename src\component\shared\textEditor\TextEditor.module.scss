@import '/styles/color.scss';

.ql-container.ql-snow {
  border: none !important;
}

.container {
  .label {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: $GRAY_500;
    gap: 2px;
    > div {
      display: flex;
    }
  }
  &.vertical textarea {
    resize: none;
  }
  .textEditor {
    font-family: Poppins;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    line-height: 16px;
    font-weight: 400;
    background-color: #fafafa;
    color: #444444;
    padding: 11px 12px 54px 12px;
    min-height: 71px;
    width: -webkit-fill-available;
    &::placeholder {
      color: $GRAY_500;
      opacity: 50%;
    }
    &:focus {
      border-radius: 4px;
    }
    &:hover {
      border-radius: 4px;
    }
    &:focus-visible {
      outline: none;
    }
    &.errorField {
      border: 1px solid $ERROR;
      border-radius: 4px;
    }
  }
  .text {
    font-size: 12px;
    line-height: 16px;
    height: 16px;
    color: $GRAY_500;

    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    &.error {
      color: $ERROR;
    }
  }
}

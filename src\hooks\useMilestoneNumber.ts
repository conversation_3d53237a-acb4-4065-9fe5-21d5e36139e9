import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMilestoneNumber,
  deleteMilestoneNumber,
  getMilestoneNumber,
  updateMilestoneNumber,
} from '../services/milestoneNumber'
import { IGetMilestoneNumberResponse, IUpdateMilestoneNumber } from '../services/milestoneNumber/interface'

export const QUERY_MILESTONE_NUMBER = 'milestone_number'

/**
 * Hook to fetch Milestone Number
 * @param enabled - Enables or disables the query
 */
export const useGetMilestoneNumber = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMilestoneNumberResponse>({
    queryKey: [QUERY_MILESTONE_NUMBER],
    queryFn: getMilestoneNumber,
    enabled,
  })

  return {
    milestoneNumbers: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Milestone Number
 */
export const useCreateMilestoneNumber = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMilestoneNumber,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MILESTONE_NUMBER] })
    },
  })
}

/**
 * Hook to delete a Milestone Number
 */
export const useDeleteMilestoneNumber = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMilestoneNumber,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MILESTONE_NUMBER] })
    },
  })
}

/**
 * Hook to update a Milestone Number
 */
export const useUpdateMilestoneNumber = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMilestoneNumber,
    onMutate: async (updatedData: IUpdateMilestoneNumber) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_MILESTONE_NUMBER] })
      const previousData = queryClient.getQueryData<IGetMilestoneNumberResponse>([QUERY_MILESTONE_NUMBER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMilestoneNumberResponse>([QUERY_MILESTONE_NUMBER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, milestone_number: updatedData.milestone_number } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_MILESTONE_NUMBER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_MILESTONE_NUMBER] })
    },
  })
}

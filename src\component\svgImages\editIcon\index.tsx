import React, { ReactElement, forwardRef } from 'react'

interface EditIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: string
  width?: string
  height?: string
}

const EditIcon: React.FC<EditIconProps> = forwardRef<SVGSVGElement, EditIconProps>((props, ref): ReactElement => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.width || '16'}
      height={props.height || '16'}
      viewBox="0 0 16 16"
      fill="none"
      ref={ref}
      {...props}
    >
      <g clipPath="">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.9624 0.833985L9.00065 0.833985C9.27679 0.833985 9.50065 1.05784 9.50065 1.33398C9.50065 1.61013 9.27679 1.83398 9.00065 1.83398H8.00065C6.41517 1.83398 5.27635 1.83505 4.4097 1.95156C3.55743 2.06615 3.04362 2.28408 2.66385 2.66385C2.28408 3.04362 2.06615 3.55743 1.95156 4.4097C1.83505 5.27635 1.83398 6.41517 1.83398 8.00065C1.83398 9.58613 1.83505 10.7249 1.95156 11.5916C2.06615 12.4439 2.28408 12.9577 2.66385 13.3375C3.04362 13.7172 3.55743 13.9352 4.4097 14.0497C5.27635 14.1663 6.41517 14.1673 8.00065 14.1673C9.58613 14.1673 10.7249 14.1663 11.5916 14.0497C12.4439 13.9352 12.9577 13.7172 13.3375 13.3375C13.7172 12.9577 13.9352 12.4439 14.0497 11.5916C14.1663 10.7249 14.1673 9.58613 14.1673 8.00065V7.00065C14.1673 6.72451 14.3912 6.50065 14.6673 6.50065C14.9435 6.50065 15.1673 6.72451 15.1673 7.00065V8.0389C15.1673 9.57783 15.1673 10.7839 15.0408 11.7248C14.9113 12.688 14.6411 13.448 14.0446 14.0446C13.448 14.6411 12.688 14.9113 11.7248 15.0408C10.7839 15.1673 9.57783 15.1673 8.0389 15.1673H7.9624C6.42347 15.1673 5.21745 15.1673 4.27645 15.0408C3.3133 14.9113 2.55328 14.6411 1.95674 14.0446C1.3602 13.448 1.08998 12.688 0.960482 11.7248C0.833969 10.7839 0.833976 9.57783 0.833985 8.0389V7.9624C0.833976 6.42347 0.833969 5.21745 0.960482 4.27645C1.08998 3.3133 1.3602 2.55328 1.95674 1.95674C2.55328 1.3602 3.3133 1.08998 4.27645 0.960482C5.21745 0.833969 6.42347 0.833976 7.9624 0.833985ZM11.181 1.51793C12.0929 0.606004 13.5715 0.606004 14.4834 1.51793C15.3953 2.42985 15.3953 3.90837 14.4834 4.8203L10.0513 9.25237C9.80379 9.49992 9.64874 9.65499 9.47571 9.78995C9.27191 9.94891 9.0514 10.0852 8.81808 10.1964C8.61999 10.2908 8.41195 10.3601 8.07984 10.4708L6.14349 11.1162C5.78599 11.2354 5.39185 11.1424 5.12539 10.8759C4.85893 10.6094 4.76589 10.2153 4.88505 9.85781L5.5305 7.92147C5.64118 7.58935 5.71051 7.38131 5.80492 7.18322C5.91611 6.9499 6.05239 6.72939 6.21135 6.5256C6.34631 6.35256 6.50139 6.19751 6.74895 5.94998L11.181 1.51793ZM13.7763 2.22503C13.2549 1.70363 12.4095 1.70363 11.8881 2.22503L11.637 2.47611C11.6522 2.54002 11.6733 2.61616 11.7028 2.70109C11.7983 2.97647 11.9791 3.33913 12.3207 3.68065C12.6622 4.02217 13.0248 4.20296 13.3002 4.2985C13.3851 4.32796 13.4613 4.34914 13.5252 4.36427L13.7763 4.11319C14.2977 3.59179 14.2977 2.74643 13.7763 2.22503ZM12.7374 5.15204C12.3934 5.00411 11.9928 4.76696 11.6136 4.38775C11.2343 4.00855 10.9972 3.60787 10.8493 3.26389L7.47899 6.63416C7.20131 6.91183 7.09241 7.02195 6.99986 7.14062C6.88557 7.28714 6.78759 7.44569 6.70764 7.61344C6.6429 7.74929 6.5931 7.89594 6.46892 8.26848L6.18099 9.13227L6.86903 9.82031L7.73282 9.53238C8.10536 9.4082 8.25201 9.35841 8.38786 9.29366C8.55561 9.21372 8.71416 9.11573 8.86069 9.00144C8.97935 8.90889 9.08947 8.79999 9.36715 8.52231L12.7374 5.15204Z"
          fill={props.fill || '#000'}
        />
      </g>
      <defs>
        <clipPath id="clip0_1291_88662">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
})
EditIcon.displayName = 'EditIcon'

export default EditIcon

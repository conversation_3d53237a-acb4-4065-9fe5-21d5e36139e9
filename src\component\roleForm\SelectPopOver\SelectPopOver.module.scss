.model {
  z-index: 1500 !important;
  backdrop-filter: blur(8px);
  border-radius: 12px !important;
  position: relative;
}

.closeIcon {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background-color: white;
  padding: 30px 20px 20px 20px;
  border-bottom: 1px solid #ccc;
  z-index: 100;
}

.confirmContent {
  position: relative;
  padding-top: 16px;
  height: 100%;
  max-height: 640px;
}

.title {
  font-size: 16px;
  color: #444444;
  font-weight: 700;
}

.submitIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  // bottom: -10px;
  background-color: white;
  border-top: 1px solid #ccc;
  padding: 20px 20px 20px 20px;
  z-index: 100;
}
.icon {
  cursor: pointer;
}

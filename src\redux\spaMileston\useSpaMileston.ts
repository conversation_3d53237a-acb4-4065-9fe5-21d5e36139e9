import { useDispatch, useSelector } from 'react-redux'
import { getSpa, addSpa, updateSpa, updatePDFSpa, deleteSpa, spaSort } from '.'
import { ISpa } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useSpa: any = () => {
  const dispatch: AppDispatch = useDispatch()

  const getSpaStatus = useSelector((state: RootState) => state.spa.getSpaStatus)
  const spas = useSelector((state: RootState) => state.spa.spas)

  const getSpaApi = (data: { period: string; project_name?: string }) => dispatch(getSpa(data))
  const addSpaApi = (data: ISpa) => dispatch(addSpa(data))
  const updateSpaApi = (data: { id: number; data: ISpa }) => dispatch(updateSpa(data))
  const updatePDFSpaApi = (data: { id: number; data: FormData }) => dispatch(updatePDFSpa(data))
  const sortSpaApi = (data: any) => dispatch(spaSort(data))
  const deleteSpaApi = (data: number) => dispatch(deleteSpa(data))

  return {
    getSpaStatus,
    spas,
    getSpaApi,
    addSpaApi,
    updateSpaApi,
    updatePDFSpaApi,
    sortSpaApi,
    deleteSpaApi,
  }
}
export default useSpa

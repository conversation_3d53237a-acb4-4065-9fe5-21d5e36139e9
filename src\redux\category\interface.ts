import { StatusEnum } from '../types'

export interface ICategoryState {
  getMasterCategoryStatus: StatusEnum
  addCategoryStatus: StatusEnum
  renameCategoryStatus: StatusEnum
  deleteCategoryStatus: StatusEnum
  categories: ICategory[]
  category: ICategory
  localCategory: any[]
}
export interface ICategory {
  id: number
  category: string //replace with the actual field name with backend
}

export interface IGetMasterCategoryResponse {
  data: ICategory[]
  message: string
  success: true
}

export interface IAddCategoryPayload {
  project_name: string
  period: string
  master_project_phase_category_id: number | null
}

export interface IRenameCategoryPayload {
  project_name: string
  period: string
  old_master_project_phase_category_id: number
  master_project_phase_category_id: number | null
}

export interface IDeleteCategoryPayload {
  master_project_phase_category_id: number
}

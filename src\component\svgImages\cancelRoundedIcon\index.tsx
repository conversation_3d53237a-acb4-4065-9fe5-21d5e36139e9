import React, { ReactElement, forwardRef } from 'react'

interface CancelRoundedIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: string
  color?: string
}

const CancelRoundedIcon: React.FC<CancelRoundedIconProps> = forwardRef<SVGSVGElement, CancelRoundedIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
        ref={ref}
      >
        <g clipPath="url(#clip0_1968_12417)">
          <path
            d="M6.68752 5.98042C6.49226 5.78516 6.17567 5.78516 5.98041 5.98042C5.78515 6.17568 5.78515 6.49227 5.98041 6.68753L7.29353 8.00065L5.98042 9.31375C5.78516 9.50902 5.78516 9.8256 5.98042 10.0209C6.17569 10.2161 6.49227 10.2161 6.68753 10.0209L8.00064 8.70776L9.31373 10.0208C9.50899 10.2161 9.82557 10.2161 10.0208 10.0208C10.2161 9.82559 10.2161 9.509 10.0208 9.31374L8.70774 8.00065L10.0209 6.68754C10.2161 6.49228 10.2161 6.1757 10.0209 5.98043C9.82559 5.78517 9.50901 5.78517 9.31374 5.98043L8.00064 7.29354L6.68752 5.98042Z"
            fill={props.color || '#444444'}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.00065 0.833984C4.04261 0.833984 0.833984 4.04261 0.833984 8.00065C0.833984 11.9587 4.04261 15.1673 8.00065 15.1673C11.9587 15.1673 15.1673 11.9587 15.1673 8.00065C15.1673 4.04261 11.9587 0.833984 8.00065 0.833984ZM1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065Z"
            fill={props.color || '#444444'}
          />
        </g>
        <defs>
          <clipPath id="clip0_1968_12417">
            <rect width="16" height="16" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  },
)
CancelRoundedIcon.displayName = 'CancelRoundedIcon'

export default CancelRoundedIcon

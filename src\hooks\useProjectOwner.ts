import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createProjectOwner,
  deleteProjectOwner,
  getProjectOwners,
  updateProjectOwner,
} from '../services/projectOwnerCard'
import { IGetProjectOwnerResponse, IUpdateProjectOwner } from '../services/projectOwnerCard/interface'

export const QUERY_PROJECT_OWNER = 'project-owner'

/**
 * Hook to fetch Project owner
 * @param enabled - Enables or disables the query
 */
export const useGetProjectOwners = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetProjectOwnerResponse>({
    queryKey: [QUERY_PROJECT_OWNER],
    queryFn: getProjectOwners,
    enabled,
  })

  return {
    projectOwners: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project owner
 */
export const useCreateProjectOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createProjectOwner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_OWNER] })
    },
  })
}

/**
 * Hook to delete a Project owner
 */
export const useDeleteProjectOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteProjectOwner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_OWNER] })
    },
  })
}

/**
 * Hook to update a Project owner
 */
export const useUpdateProjectOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateProjectOwner,
    onMutate: async (updatedData: IUpdateProjectOwner) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_OWNER] })
      const previousData = queryClient.getQueryData<IGetProjectOwnerResponse>([QUERY_PROJECT_OWNER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetProjectOwnerResponse>([QUERY_PROJECT_OWNER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? { ...manager, design_executive_director: updatedData.design_executive_director }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_OWNER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_OWNER] })
    },
  })
}

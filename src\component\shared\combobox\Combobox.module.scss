@import '/styles/color.scss';

.root {
  width: 100%;
}

.label {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  // margin-bottom: 4px;
  color: $GRAY_500;
  .required {
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: $GRAY_500;
    margin-left: 2px;
  }
}

.container {
  .control {
    cursor: pointer !important;
    background: #fafafa;
    border: none;
    box-shadow: none;
    .valueContainer {
      padding: 10px 0px 10px 12px;
    }
    &:hover {
      border: none;
    }
    &.focusBorder {
      // border: 1px solid #dddddd !important;
      border-radius: 0px;
    }
    &.errorBorder {
      border: 1px solid $ERROR !important;
      border-radius: 4px;
    }
    &.disabled {
      border: none;
    }
  }
  .placeholder {
    line-height: 20px;
    font-size: 14px;
    color: $GRAY_500;
    opacity: 50%;
    font-weight: 400;
    margin: 0;
  }
  .singleValue {
    line-height: 16px;
    font-size: 14px;
    color: $DARK !important;
    font-weight: 400;
    margin: 0;
  }
  .inputField {
    line-height: 16px;
    font-size: 14px;
    color: black;
    font-weight: 400;
    margin: 0;
    // padding: 10px 0px 10px 12px;
  }

  .indicatorsContainer {
    .indicatorSeparator {
      background-color: transparent;
    }
    > div {
      padding: 7px 7px 7px 6px;
      .searchIcon {
        height: 18px;
        width: 18px;
        fill: $GRAY_500;
      }
      .arrowIcon {
        height: 18px;
        width: 18px;
        fill: $GRAY_500;
      }
    }
  }
}

.clearIcon {
  width: 6px;
  margin-right: -12px;
  color: #565656;
  height: 15px !important;
  width: 15px !important;
}

.text {
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: $GRAY_500;
  &.error {
    color: $ERROR;
  }
}

.labelContainer {
  display: flex;
  align-items: center;
  svg {
    height: 16px;
    width: 16px;
    fill: $GRAY_500;
    margin-right: 8px;
  }
}

.menuPortal {
  .menu {
    margin: 0;
    border-radius: 0px;
    background: $WHITE;
    box-shadow: 0px 4px 30px 0px #0000000d;
    max-width: 304px;
    width: min-content;
    min-width: -webkit-fill-available;
    .menuList {
      padding: 0;
      .option {
        cursor: pointer !important;
        padding: 11px 0px 11px 16px;
        border-bottom: 1px solid $LIGHT;
        font-family: Poppins;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0em;
        text-align: left;
        color: $BLACK;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: transparent;
        // &:hover {
        //   // background-color: #dddddd;
        // }
        &.selectedOption {
          background-color: #dddddd;
        }
        // &.focusedOption {
        //   // background-color: $WHITE;
        // }
      }
    }
    .noOptionsMessage {
      font-size: 14px;
      line-height: 16px;
      font-style: italic;
      color: $GRAY_500;
    }
  }
}

.error {
  border: 1px px solid #ea3323;
}

.react_select__menu-list {
  z-index: 9998 !important;
}

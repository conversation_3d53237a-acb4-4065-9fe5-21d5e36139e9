import React, { useEffect, useState, useMemo } from 'react'
import { Modal, Box, Typography } from '@mui/material'
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers'
import { isSameDay } from 'date-fns'
import styles from './FinalFreezePeriodModel.module.scss'
import FreezePicker from '../perminateFreezePicker'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import TypographyField from '@/src/component/shared/typography'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { payloadDateFormate } from '@/src/utils/dateUtils'
import { errorToast } from '@/src/utils/toastUtils'

interface ModalProps {
  open: boolean
  onClose: () => void
  onClickFreezePeriod: () => void
  selectedAction: string
}

const FinalFreezePeriodModel: React.FC<ModalProps> = ({ open, onClose, selectedAction, onClickFreezePeriod }) => {
  const [loader, setLoader] = useState(false)
  const [date, setDate] = useState<string | null>(null)
  const {
    addMasterPeriodsApi,
    finalFreezePeriodApi,
    getMasterPeriodsApi,
    setPeriodApi,
    setIsPeriodChangeFromProjectListApi,
  } = useMasterPeriod()

  const [isAdd, setIsAdd] = useState(false)
  const style = useMemo(
    () => ({
      position: 'absolute' as 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '594px',
      bgcolor: 'background.paper',
      borderRadius: '12px',
      pt: '20px',
      px: '20px',
      pb: '20px',
      zIndex: 1,
    }),
    [],
  )

  useEffect(() => {
    if (open) setLoader(false)
  }, [open])

  const highlightedDays: Array<Date> = useMemo(() => [new Date('2024-04-10')], [])

  const CustomDay = (props: PickersDayProps<Date>) => {
    const matchedStyles = highlightedDays.reduce((a, v) => {
      return isSameDay(new Date(props.day), v) ? { backgroundColor: '#999999' } : a
    }, {})

    return <PickersDay {...props} sx={{ ...matchedStyles }} />
  }

  const handleAdd = async () => {
    if (!date) return
    setLoader(true)
    try {
      onClickFreezePeriod()
      const res: any = await finalFreezePeriodApi()
      if (!res.payload?.success) errorToast(res.payload?.response?.data?.message || 'Something went wrong')

      if (res.payload.success) {
        setIsPeriodChangeFromProjectListApi(false)
        const response: any = await addMasterPeriodsApi({
          period: payloadDateFormate(date) as any,
        })
        if (!response.payload?.success) errorToast(response.payload?.response?.data?.message || 'Something went wrong')

        if (response.payload.success) {
          const res: any = await getMasterPeriodsApi()
          if (!res.payload?.success) errorToast(res.payload?.response?.data?.message || 'Something went wrong')
          if (res.payload?.success) {
            setPeriodApi(res?.payload?.data?.period)
            setLoader(false)
            onClose()
          }
        }
      }
    } catch (error: any) {
      console.log('error: ', error)
    } finally {
      setLoader(false)
      onClose()
    }
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        {loader ? (
          <Loader />
        ) : (
          <>
            <div className={styles.confirmButtonContent}>
              <div className={styles.header}>
                <div>Add New Period & Permanent Freeze </div>
              </div>
              <div className={styles.confirmContent}>
                Are you sure you want to add a new period? Please note the action is irreversible.
                <div>
                  You are about to freeze the period for all users. After this step, no one will be able to enter data
                  for the current period. Do you want to proceed?
                </div>
              </div>
            </div>
            <Typography sx={{ mt: 2 }}>Select the new reporting period </Typography>
            <div className={styles.confirmButtonContent}>
              <div className={styles.freezeTime}>
                <FreezePicker
                  isAdd={isAdd}
                  setIsAdd={setIsAdd}
                  setDate={setDate}
                  date={date}
                  actionButtons={true}
                  CustomDay={CustomDay}
                  sx={{
                    display: 'content',
                    zIndex: '999999',
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'transparent',
                      borderTop: 0,
                      borderRight: 0,
                      borderLeft: 0,
                      borderRadius: 0,
                    },
                    '& .MuiOutlinedInput-input': {
                      width: '120px',
                      padding: '6px 0px 6px 8px',
                      fontWeight: '400',
                      fontSize: '12px',
                      lineHeight: '18px',
                      color: 'black',
                      display: 'none',
                    },
                    '& .MuiButtonBase-root': {
                      top: '14px',
                      left: '-17px',
                      width: 'unset',
                    },
                    '& .Mui-disabled': {
                      fontWeight: '400',
                      fontSize: '12px',
                      lineHeight: '18px',
                      color: 'black !important',
                      '& .MuiInputBase-input': {
                        '-webkit-text-fill-color': 'black',
                      },
                    },
                    '& .Mui-focused': {
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#f4f4fc',
                      },
                    },
                  }}
                />
                <TypographyField
                  variant="bodyBold"
                  style={{ marginLeft: '-16px', marginTop: '4px' }}
                  text={date ? date.toString() : ''}
                />
              </div>
              <div className={styles.reassignButtons}>
                <Button disabled={!isAdd} onClick={handleAdd}>
                  Proceed
                </Button>
                <Button color="secondary" onClick={onClose}>
                  Cancel
                </Button>
              </div>
            </div>
          </>
        )}
      </Box>
    </Modal>
  )
}

export default FinalFreezePeriodModel

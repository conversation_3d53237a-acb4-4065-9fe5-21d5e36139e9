import { useQuery } from '@tanstack/react-query'
import { getProjectToProjectPhaseCategories } from '../services/masterProjectToProjectPhaseCategories'
import { IGetProjectPhaseCategoryResponse } from '../services/masterProjectToProjectPhaseCategories/interface'

export const QUERY_PROJECT_TO_PROJECT_PHASE_CATEGORY = 'project-to-project-phase-categories'

/**
 * Hook to fetch Project Phase Categories
 * @param enabled - Enables or disables the query
 */
export const useGetProjectToProjectPhaseCategories = (
  period: string,
  project_name: string,
  enabled: boolean = true,
) => {
  const { data, isError, ...rest } = useQuery<IGetProjectPhaseCategoryResponse>({
    queryKey: [QUERY_PROJECT_TO_PROJECT_PHASE_CATEGORY],
    queryFn: () => getProjectToProjectPhaseCategories(period, project_name),
    enabled,
  })

  return {
    projectToProjectPhaseCategories: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

// CommonDataGrid.tsx
import * as React from 'react'
import Box from '@mui/material/Box'
import {
  DataGrid,
  GridColDef,
  GridRowModesModel,
  GridRowId,
  GridEventListener,
  GridRowModel,
  GridRowModes,
  DataGridProps,
} from '@mui/x-data-grid'

interface CommonDataGridProps extends DataGridProps {
  className?: string
}

const CommonDataGrid: React.FC<CommonDataGridProps> = ({
  rows,
  columns,
  className,
  ...props
  // classes,
}) => {
  return (
    <Box className={`${className}`} sx={{ height: 'calc(100vh - 256px)' }}>
      <DataGrid
        {...props}
        rows={rows}
        columns={columns}
        editMode="row"
        rowHeight={44}
        columnHeaderHeight={40}
        hideFooter
        hideFooterPagination
        hideFooterSelectedRowCount
      />
    </Box>
  )
}

export default CommonDataGrid

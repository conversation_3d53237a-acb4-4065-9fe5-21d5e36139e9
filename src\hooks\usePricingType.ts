import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createPricingType, deletePricingType, getPricingType, updatePricingType } from '../services/pricingType'
import { IGetMasterPricingTypeResponse, IUpdatePricingType } from '../services/pricingType/interface'

export const QUERY_PRICING_TYPE = 'pricing-type'

/**
 * Hook to fetch Pricing Types
 * @param enabled - Enables or disables the query
 */
export const useGetPricingType = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterPricingTypeResponse>({
    queryKey: [QUERY_PRICING_TYPE],
    queryFn: getPricingType,
    enabled,
  })

  return {
    pricingTypes: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Pricing Type
 */
export const useCreatePricingType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createPricingType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PRICING_TYPE] })
    },
  })
}

/**
 * Hook to delete a Pricing Type
 */
export const useDeletePricingType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deletePricingType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PRICING_TYPE] })
    },
  })
}

/**
 * Hook to update a Pricing Type
 */
export const useUpdatePricingType = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updatePricingType,
    onMutate: async (updatedData: IUpdatePricingType) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PRICING_TYPE] })
      const previousData = queryClient.getQueryData<IGetMasterPricingTypeResponse>([QUERY_PRICING_TYPE])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterPricingTypeResponse>([QUERY_PRICING_TYPE], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((price) =>
            price.id === updatedData.id ? { ...price, pricing_type: updatedData.pricing_type } : price,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PRICING_TYPE], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PRICING_TYPE] })
    },
  })
}

export function formatNumberWithCommas(number: number) {
  if (number == 0) return '0'
  if (!number || number?.toString() === '0.00%') {
    return ''
  }
  /*
    Convert a number to a string with commas for thousands separators.
    */
  return number?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}
//
// `convertToPercentage` converts a numeric value to a percentage representation
// (multiplies by 100 and formats to two decimal places) for displaying in the update
// progress form and status table. Returns an empty string if the input is null or undefined.
//
// @param value - The numeric value to convert to a percentage.
// @returns A string representing the percentage value or an empty string if input is null or undefined.
//
export const convertToPercentage = (value: string | number | undefined | null) => {
  if (value === null || !value) return 0

  const percentage = Number(value) * 100
  return percentage === 100 ? 100 : parseFloat(percentage.toFixed(2))
}
//
// `toString` converts a value to its string representation. It is used for displaying values
// in the update progress form and status table. Returns an empty string if the input is null or undefined.
//
// @param value - The value to convert to a string.
// @returns The string representation of the value or an empty string if input is null or undefined.
//
export const toString = (value: string | number | undefined | null) => (value != null ? value.toString() : '')

export const removeCommasAndConvert = (str: any) => {
  // Ensure the input is a string, then remove commas and convert to a number
  if (typeof str === 'string') {
    const cleanedStr: any = str.replace(/,/g, '') // Remove commas
    return isNaN(cleanedStr) ? str : parseFloat(cleanedStr) // Return number if valid, otherwise return original string
  }
  return str // If input is already a number, return it as is
}

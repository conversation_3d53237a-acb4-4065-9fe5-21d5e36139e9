import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterSubLocations,
  deleteMasterSubLocations,
  getMasterSubLocations,
  updateMasterSubLocations,
} from '../services/subLocation'
import { IGetMasterSubLocationsResponse, IUpdateSubLocation } from '../services/subLocation/interface'

export const QUERY_SUB_LOCATION = 'sub-locations'

/**
 * Hook to fetch Sub Locations
 * @param enabled - Enables or disables the query
 */
export const useGetSubLocations = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterSubLocationsResponse>({
    queryKey: [QUERY_SUB_LOCATION],
    queryFn: getMasterSubLocations,
    enabled,
  })

  return {
    subLocations: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Sub Locations
 */
export const useCreateSubLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterSubLocations,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SUB_LOCATION] })
    },
  })
}

/**
 * Hook to delete a Sub Locations
 */
export const useDeleteSubLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterSubLocations,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SUB_LOCATION] })
    },
  })
}

/**
 * Hook to update a Sub Locations
 */
export const useUpdateSubLocations = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterSubLocations,
    onMutate: async (updatedData: IUpdateSubLocation) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_SUB_LOCATION] })
      const previousData = queryClient.getQueryData<IGetMasterSubLocationsResponse>([QUERY_SUB_LOCATION])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterSubLocationsResponse>([QUERY_SUB_LOCATION], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, sub_location: updatedData.sub_location } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_SUB_LOCATION], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SUB_LOCATION] })
    },
  })
}

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import { IAddProjectClassificationProps } from './interface'
import styles from './ProjectClassification.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateProjectClassifications,
  useDeleteProjectClassifications,
  useGetProjectClassifications,
  useUpdateProjectClassifications,
} from '@/src/hooks/useProjectClassification'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddProjectClassification: React.FC<IAddProjectClassificationProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addProjectClassification } = drawerStates
  //REACT-QUERY
  const { projectClassifications } = useGetProjectClassifications(addProjectClassification)
  // MUTATIONS
  const { mutate: addProjectClassifications } = useCreateProjectClassifications()
  const { mutate: deleteProjectClassifications } = useDeleteProjectClassifications()
  const { mutate: updateProjectClassifications } = useUpdateProjectClassifications()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { projectClassification: '' },
    onSubmit: async (values) => {
      const payload = { project_classification: values.projectClassification }
      if (editIndex !== null) {
        updateProjectClassifications(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addProjectClassifications(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteProjectClassifications(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = projectClassifications.find((projectClassification) => {
      //TODO
      return projectClassification.id === id
    })
    formik.setValues({ projectClassification: editControlManagers?.project_classification })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_classification',
      header: 'Project Classifications',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text={'Add Project Classifications'}
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="projectClassification"
              labelText={'Project Classifications'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.projectClassification}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectClassification?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectClassification?.trim()}
              >
                {'+ Add Project Classifications'}
              </Button>
            )}
          </div>
        </form>
        {projectClassifications?.length >= 1 && (
          // <Table data={projectClassifications} columns={columns} />
          <TanStackTable
            rows={sortData(projectClassifications, 'project_classification') as any}
            columns={columns}
            isOverflow={true}
          />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddProjectClassification

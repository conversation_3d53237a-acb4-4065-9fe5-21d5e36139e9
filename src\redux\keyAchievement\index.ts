import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetKeyAchievementResponse, IKeyAchievement, IKeyAchievementsStatus } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IKeyAchievementsStatus = {
  getKeyAchievementStatus: StatusEnum.Idle,
  addKeyAchievementStatus: StatusEnum.Idle,
  deleteKeyAchievementStatus: StatusEnum.Idle,
  updateKeyAchievementStatus: StatusEnum.Idle,
  keyAchievementSortStatus: StatusEnum.Idle,
  keyAchievements: [],
  keyAchievement: {},
  formData: {
    open: false,
    openPlan: false,
    newHighlight: '',
    startDate: '',
    endDate: '',
    plan: '',
    highlights: [],
    plans: [],
  },
}

export const getKeyAchievements = createAsyncThunk(
  '/get-key-achievements-plan',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(
        `/key-achievements?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addKeyAchievements = createAsyncThunk(
  '/add-key-achievements-plan',
  async (data: IKeyAchievement, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/key-achievements`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)
export const keyAchievementSort = createAsyncThunk('/key-achievements/bulk-sort', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/key-achievements/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateKeyAchievements = createAsyncThunk(
  '/update-key-achievements-plan',
  async (data: { id?: number; data: IKeyAchievement }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/key-achievements/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteKeyAchievements = createAsyncThunk(
  '/delete-key-achievements-plan',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/key-achievements/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const keyAchievementSlice = createSlice({
  name: 'keyAchievement',
  initialState,
  reducers: {
    //set selected rows
    updateFormData(state, action) {
      state.formData = action.payload
    },
  },
  extraReducers: (builder) => {
    //getKeyAchievements
    builder.addCase(getKeyAchievements.pending, (state) => {
      state.getKeyAchievementStatus = StatusEnum.Pending
    })
    builder.addCase(getKeyAchievements.fulfilled, (state, action) => {
      state.getKeyAchievementStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetKeyAchievementResponse
      state.keyAchievements = actionPayload.data
    })
    builder.addCase(getKeyAchievements.rejected, (state) => {
      state.getKeyAchievementStatus = StatusEnum.Failed
    })
    //keyAchievementSort
    builder.addCase(keyAchievementSort.pending, (state) => {
      state.keyAchievementSortStatus = StatusEnum.Pending
    })
    builder.addCase(keyAchievementSort.fulfilled, (state, action) => {
      state.keyAchievementSortStatus = StatusEnum.Success
    })
    builder.addCase(keyAchievementSort.rejected, (state) => {
      state.keyAchievementSortStatus = StatusEnum.Failed
    })
    //addKeyAchievements
    builder.addCase(addKeyAchievements.pending, (state) => {
      state.addKeyAchievementStatus = StatusEnum.Pending
    })
    builder.addCase(addKeyAchievements.fulfilled, (state, action) => {
      state.addKeyAchievementStatus = StatusEnum.Success
    })
    builder.addCase(addKeyAchievements.rejected, (state) => {
      state.addKeyAchievementStatus = StatusEnum.Failed
    })
    //updateKeyAchievements
    builder.addCase(updateKeyAchievements.pending, (state) => {
      state.updateKeyAchievementStatus = StatusEnum.Pending
    })
    builder.addCase(updateKeyAchievements.fulfilled, (state, action) => {
      state.updateKeyAchievementStatus = StatusEnum.Success
    })
    builder.addCase(updateKeyAchievements.rejected, (state) => {
      state.updateKeyAchievementStatus = StatusEnum.Failed
    })
    //deleteKeyAchievements
    builder.addCase(deleteKeyAchievements.pending, (state) => {
      state.deleteKeyAchievementStatus = StatusEnum.Pending
    })
    builder.addCase(deleteKeyAchievements.fulfilled, (state, action) => {
      state.deleteKeyAchievementStatus = StatusEnum.Success
    })
    builder.addCase(deleteKeyAchievements.rejected, (state) => {
      state.deleteKeyAchievementStatus = StatusEnum.Failed
    })
  },
})

export const { updateFormData } = keyAchievementSlice.actions

// Export the reducer
export default keyAchievementSlice.reducer

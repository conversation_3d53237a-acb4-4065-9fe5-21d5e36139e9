@import '/styles/color.scss';

.accordion {
  position: relative;
  box-shadow:
    0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14),
    0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  border-radius: 8px !important;
  margin-bottom: 16px;
  &::before {
    content: none;
  }
  .summary {
    // padding: 0 20px;
    padding-left: 0 !important;
    min-height: 56px;
    align-items: baseline;
    max-height: 72px;
    .expandIcon {
      width: 16px;
      height: 16px;
    }
    .content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 18px 8px 40px 0px;
      position: relative;
      // &::after {
      //   content: "";
      //   position: absolute;
      //   left: 0;
      //   right: 16px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   border: 1px solid $LIGHT_200;
      // }
      .header {
        width: fit-content;
        display: flex;
        gap: 10px;
        align-items: center;
        padding: 8px 20px;
        background: #eeeeec;
        border-radius: 0 8px 8px 0;
        min-width: 178px;
        font-size: 12px;
        font-weight: 700;
        line-height: 18px;
        color: $DARK;
        white-space: nowrap;
        position: relative;
        z-index: 1;
        padding-right: 8px;
      }
    }
  }
  .details {
    padding: 0;
    margin: 0 16px 16px;
  }
}
.timerIcon {
  display: flex;
  align-items: center;
  gap: 4px;
}

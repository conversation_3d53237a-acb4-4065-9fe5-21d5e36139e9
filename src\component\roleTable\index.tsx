import React, { useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import styles from './RoleTable.module.scss'
import ConfirmDeleteModal from '../confirmDeleteModal'
import PopOver from '../popover'
import Button from '../shared/button'
import Loader from '../shared/loader'
import TanStackTable from '../shared/tanStackTable'
import { CustomColumnDef } from '../shared/tanStackTable/interface'
import DeleteIcon from '../svgImages/deleteIcon'
import EditIcon from '../svgImages/editIcon'
import { Routes } from '@/src/constant/enum'
import useRole from '@/src/redux/role/useRole'
import {
  convertAndSortData,
  convertMultiSelectOption,
  getUniqueValuesFromArray,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'

const RoleTable = () => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const { getRoleApi, roles, deleteRoleApi } = useRole()
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [modalOpen, setModalOpen] = useState(false)
  const [modalContent, setModalContent] = useState<any>(null)
  useEffect(() => {
    setLoading(true)
    handleData()
  }, [])

  const handleData = async () => {
    const res: any = await getRoleApi()
    if (res.payload.success) setLoading(false)
  }

  const handleDelete = async (id: number) => {
    const res: Record<string, any> = await deleteRoleApi(id)
    if (!res.payload.success) return
    getRoleApi()
    setDeleteModel(null)
  }

  const openModal = (row: any) => {
    setModalContent(row)
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
  }

  const findUniqueElements = (arr: string[]) => {
    return [...getUniqueValuesFromArray(arr)]
  }

  const optionOfRoleName = useMemo(
    () =>
      convertAndSortData(
        convertMultiSelectOption(findUniqueElements(roles.map((item: any) => item.role_name))).filter(
          (item) => item.id !== null && item.name !== null,
        ),
      ),
    [roles],
  )

  const optionOfPermissionType = useMemo(
    () =>
      convertAndSortData(
        convertMultiSelectOption(findUniqueElements(roles.map((item: any) => item.permission_type))).filter(
          (item) => item.id !== null && item.name !== null,
        ),
      ),
    [roles],
  )

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'actionCol',
      header: '',
      cell: ({ row }) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon
              className={styles.button}
              onClick={() => {
                router.push({
                  pathname: `${Routes.USER_MANAGEMENT_ROLE}`,
                  query: { id: row.id },
                })
              }}
            />
            <DeleteIcon className={styles.button} onClick={() => setDeleteModel(Number(row.id))} />
          </div>
        )
      },
      size: 80,
      align: 'left',
    },

    {
      accessorKey: 'role_name',
      header: 'Role Name',
      size: 140,
      filterType: 'list',
      listOption: optionOfRoleName,
    },
    {
      accessorKey: 'view_permissions',
      header: 'View Permission',
      size: 1000,
    },
    {
      accessorKey: 'permission_type',
      header: 'Permission Type',
      size: 140,
      // flex: 1,
      filterType: 'list',
      listOption: optionOfPermissionType,
    },
    {
      accessorKey: 'projects',
      header: 'Projects / Owning Entity',
      flex: 1,
      cell: ({ row }) => {
        return (
          <div>
            <Button className={styles.projectList} onClick={() => openModal(row.original)}>
              {row.original.permission_type === 'entity' ? 'View Entity' : 'View Project'}
            </Button>
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      {loading ? (
        <Loader />
      ) : (
        <TanStackTable rows={(sortArrayByKeyWithTypeConversion(roles, 'role_name') as any) || []} columns={columns} />
      )}
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete(deleteModel as number)}
      />
      {modalContent && (
        <PopOver
          open={modalOpen}
          onClose={closeModal}
          title={modalContent.permission_type === 'entity' ? 'Entity' : 'Project List'}
        >
          <div className={styles.contentWrapper}>
            {modalContent.permission_type === 'entity' && (
              <div>
                {modalContent?.owning_entities?.length > 0 ? (
                  modalContent?.owning_entities?.map((entity: string, index: number) => (
                    <p key={index}>
                      <span className={styles.indexList}>{index + 1}. </span>
                      {entity}
                    </p>
                  ))
                ) : (
                  <p>No entity assign</p>
                )}
              </div>
            )}
            {modalContent.permission_type === 'projects' && (
              <div>
                {modalContent?.projects?.length > 0 ? (
                  modalContent?.projects?.map((project: string, index: number) => (
                    <p key={index}>
                      <span className={styles.indexList}>{index + 1}. </span>
                      {project}
                    </p>
                  ))
                ) : (
                  <p>No Project Assign</p>
                )}
              </div>
            )}
          </div>
        </PopOver>
      )}
    </div>
  )
}

export default RoleTable

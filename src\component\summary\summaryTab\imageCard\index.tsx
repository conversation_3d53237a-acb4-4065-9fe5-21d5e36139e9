import React from 'react'
import Image from 'next/image'
import styles from './ImageCard.module.scss'
import { ImageCardProps } from './interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'

const ImageCard: React.FC<ImageCardProps> = ({
  label,
  value,
  imageSrc,
  formik,
  summaryEdit,
  fieldName,
  imageHeight,
  imageWidth,
  isEditable,
  onChange,
  isFormattedNumber,
  isPercentage,
  className,
}) => {
  return (
    <div className={`${styles.card} ${className}`}>
      <div className={styles.content}>
        <TypographyField className={styles.label} variant="body1" text={label} />
        <div className={styles.innerContent}>
          {summaryEdit && isEditable ? (
            <TextInputField
              name={fieldName}
              variant="outlined"
              className={styles.textField}
              onChange={(e) => onChange(e.target.value)}
              value={value} // Ensure value is never undefined
              onBlur={formik?.handleBlur}
            />
          ) : (
            <TypographyField
              className={value === '-' ? styles.emptyValue : styles.value}
              variant="h1"
              text={`${value}`}
            />
          )}
          {imageSrc && (
            <Image src={imageSrc} alt={label} height={imageHeight} width={imageWidth} className={styles.cardImage} />
          )}
        </div>
      </div>
    </div>
  )
}

export default ImageCard

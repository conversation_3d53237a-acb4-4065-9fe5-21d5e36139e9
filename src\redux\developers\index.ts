import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterDeveloperResponse, IDeveloperStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IDeveloperStates = {
  getMasterDevelopersStatus: StatusEnum.Idle,
  addMasterDeveloperStatus: StatusEnum.Idle,
  deleteMasterDeveloperStatus: StatusEnum.Idle,
  updateMasterDeveloperStatus: StatusEnum.Idle,
  developers: [],
  developer: {
    id: 0,
    developers: '',
  },
}

export const getMasterDevelopers = createAsyncThunk('/get-master-developer', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-developer`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterDeveloper = createAsyncThunk(
  '/add-master-developer',
  async (data: { developers: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-developer`, {
        developers: data.developers,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateMasterDeveloper = createAsyncThunk(
  '/update-master-developer',
  async (data: { id: number; developers: string }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/master-developer/${data.id}`, {
        developers: data.developers,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteMasterDeveloper = createAsyncThunk('/delete-master-developer', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/master-developer/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const contractorsSlice = createSlice({
  name: 'developer',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterDevelopers
    builder.addCase(getMasterDevelopers.pending, (state) => {
      state.getMasterDevelopersStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterDevelopers.fulfilled, (state, action) => {
      state.getMasterDevelopersStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterDeveloperResponse
      state.developers = actionPayload.data
    })
    builder.addCase(getMasterDevelopers.rejected, (state) => {
      state.getMasterDevelopersStatus = StatusEnum.Failed
    })
    //addMasterDeveloper
    builder.addCase(addMasterDeveloper.pending, (state) => {
      state.addMasterDeveloperStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterDeveloper.fulfilled, (state, action) => {
      state.addMasterDeveloperStatus = StatusEnum.Success
    })
    builder.addCase(addMasterDeveloper.rejected, (state) => {
      state.addMasterDeveloperStatus = StatusEnum.Failed
    })
    //updateMasterDeveloper
    builder.addCase(updateMasterDeveloper.pending, (state) => {
      state.updateMasterDeveloperStatus = StatusEnum.Pending
    })
    builder.addCase(updateMasterDeveloper.fulfilled, (state, action) => {
      state.updateMasterDeveloperStatus = StatusEnum.Success
    })
    builder.addCase(updateMasterDeveloper.rejected, (state) => {
      state.updateMasterDeveloperStatus = StatusEnum.Failed
    })
    //deleteMasterDeveloper
    builder.addCase(deleteMasterDeveloper.pending, (state) => {
      state.deleteMasterDeveloperStatus = StatusEnum.Pending
    })
    builder.addCase(deleteMasterDeveloper.fulfilled, (state, action) => {
      state.deleteMasterDeveloperStatus = StatusEnum.Success
    })
    builder.addCase(deleteMasterDeveloper.rejected, (state) => {
      state.deleteMasterDeveloperStatus = StatusEnum.Failed
    })
  },
})

export const {} = contractorsSlice.actions

// Export the reducer
export default contractorsSlice.reducer

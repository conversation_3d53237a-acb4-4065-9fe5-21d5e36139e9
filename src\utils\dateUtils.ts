import { differenceInDays, format, isAfter, isBefore, isEqual, isValid, parse, parseISO } from 'date-fns'

export const parseDateFromString = (date: string) => {
  if (/^\d+$/.test(date)) {
    const dateTimeStamp = Number(date)
    return new Date(dateTimeStamp)
  }
  return new Date(date)
}

export const formatDMYDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = String(date.getFullYear())
  return `${day}-${month}-${year}`
}

export function formateParamsPeriod(date: any) {
  const inputDate = new Date(date)

  // Check if inputDate is a valid date
  if (isNaN(inputDate.getTime())) {
    return '' // or handle the error in a suitable way
  }

  // Get year, month, and day from the date object
  const yyyy = inputDate.getFullYear()
  const mm = String(inputDate.getMonth() + 1).padStart(2, '0')
  const dd = String(inputDate.getDate()).padStart(2, '0')

  // Form the yyyy-mm-dd format
  const formattedDate = dd + '-' + mm + '-' + yyyy

  return formattedDate
}

export function toISOFormatDate(dateString: string) {
  //convert YYYY-MM-DD  or DD-MM-YYYY into ==> YYYY-MM-DD
  if (dateString) {
    const trimmedDateString = dateString.toString().trim()
    let day, month, year

    // Check if the string contains "-"
    if (trimmedDateString.includes('-')) {
      const parts = trimmedDateString.split('-')

      if (parts.length === 3) {
        if (parts[0].length === 4) {
          // Format is "YYYY-MM-DD"
          ;[year, month, day] = parts
        } else {
          // Format is "DD-MM-YYYY"
          ;[day, month, year] = parts
        }

        // Validate the date parts
        if (
          year.length === 4 &&
          month.length === 2 &&
          day.length === 2 &&
          !isNaN(Date.parse(`${year}-${month}-${day}`))
        ) {
          const dateObject = new Date(`${year}-${month}-${day}`) // this is iso formate
          // Ensure the constructed date is valid
          if (
            dateObject.getFullYear() === parseInt(year) &&
            dateObject.getMonth() + 1 === parseInt(month) &&
            dateObject.getDate() === parseInt(day)
          ) {
            const isoDateString = dateObject.toISOString().split('T')[0] // this is yyyy-mm-dd formate
            return isoDateString
          }
        }
      }
    }
  }
  return ''
}

export function DDMMYYYYformate(date: string) {
  if (!date) return null
  //it is work for  yyyy-mm-dd to dd-mm-yyyy
  // Parse the date from 'yyyy-MM-dd' format
  const parsedDate = date && parse(date, 'yyyy-MM-dd', new Date())
  // Check if the parsed date is valid
  if (!isValid(parsedDate)) {
    return null
  }
  // Format the parsed date to 'dd-MM-yyyy'
  return format(parsedDate, 'dd-MM-yyyy')
}

export const isoToYYYYMMDD = (value: any) => {
  if (!value) return
  // Convert the value to ISO string if it exists, else assign an empty string
  const val = value ? value.toISOString() : null
  // Format the date if the ISO string is valid, else return an empty string
  const date = val ? format(parseISO(val), 'yyyy-MM-dd') : null
  // const date = val ? val : null
  return date
}

export function payloadDateFormate(isoDate: string) {
  //it is work for dd-mm-yyyy to yyyy-mm-dd
  if (toISOFormatDate(isoDate)) {
    const date = new Date(toISOFormatDate(isoDate))

    // Extracting year, month, and day
    const year = date.getFullYear().toString() // Get last two digits of the year
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding leading zero if necessary
    const day = date.getDate().toString().padStart(2, '0') // Adding leading zero if necessary

    // Concatenating in YY-DD-MM format
    const YYYYMMDDFormat = `${year}-${month}-${day}`
    return YYYYMMDDFormat
  } else {
    return null
  }
}

export function isoToPayload(isoDate: string | Date): string | null {
  // Check if isoDate is a valid date
  const date = new Date(isoDate)
  if (isNaN(date.getTime())) return null

  // Extracting year, month, and day
  const year = date.getFullYear().toString()
  const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding leading zero if necessary
  const day = date.getDate().toString().padStart(2, '0') // Adding leading zero if necessary

  // Concatenating in YYYY-MM-DD format
  const YYYYMMDDFormat = `${year}-${month}-${day}`
  return YYYYMMDDFormat
}

export function convertToISO(dateString: string) {
  // Ensure the input format is correct (dd-mm-yyyy)
  if (!/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
    return ''
  }

  let parts = dateString.split('-')

  // Extract day, month, and year from the parts
  let day = parseInt(parts[0], 10)
  let month = parseInt(parts[1], 10) - 1 // JavaScript months are 0-based
  let year = parseInt(parts[2], 10)

  // Create a new Date object using the extracted parts
  let dateObject = new Date(Date.UTC(year, month, day))

  // Convert the Date object to an ISO 8601 string
  let isoString = dateObject.toISOString()

  return isoString
}

export function formatDateString(dateString: any) {
  const currentFormat = 'yyyy-MM-dd'
  const outputFormat = 'MMM dd, yyyy'

  // Parse the date string into a Date object
  const parsedDate: any = parse(dateString, currentFormat, new Date())
  // Format the parsed date into the desired output format
  return format(parsedDate, outputFormat)
}

export function convertDDMMYYYYToLongDate(dateString: string) {
  if (dateString) {
    const [day, month, year] = dateString && dateString.toString()?.split('-')
    const date = new Date(`${month}-${day}-${year}`)
    const formattedDate = date.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Asia/Kolkata',
    })
    if (formattedDate === 'Invalid Date') {
      const [year, month, day] = dateString.toString()?.split('-')
      const date = new Date(`${month}-${day}-${year}`)
      const formattedDate = date.toLocaleString('en-US', {
        weekday: 'short',
        month: 'short',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Kolkata',
      })
      return formattedDate
    }
    return formattedDate
  } else {
    return null
  }
}

/**
 * Finds the latest date from an array of dates.
 *
 * This function performs the following steps:
 * 1. Filters out null and undefined values from the array of dates.
 * 2. Converts the remaining date values into Date objects after normalizing them.
 * 3. Finds the maximum date from the array of Date objects.
 * 4. Returns the latest date if valid, or an empty string if no valid dates are found.
 *
 * @param dates - An array of dates (can include null or undefined values).
 * @returns The latest date as a Date object, or an empty string if no valid dates are present.
 */
export const findLatestDate = (dates: any) => {
  // Filter out null and undefined dates
  const validDates = dates.filter((date: any) => date !== null && date !== undefined)

  // Convert valid dates to Date objects
  const dateObjects = validDates.map((date: any) => new Date(normalizeDate(date)))

  // Find the maximum date from the Date objects
  const latestDate: any = new Date(Math.max(...dateObjects))

  // Check if the latest date is valid
  if (isNaN(latestDate.getTime())) {
    return ''
  }

  return latestDate
}

const normalizeDate = (dateStr: any) => {
  if (!dateStr) return
  // If the date format is DD-MM-YYYY, convert it to YYYY-MM-DD
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-')
    if (parts[0].length === 2 && parts[2].length === 4) {
      return `${parts[2]}-${parts[1]}-${parts[0]}`
    }
  }
  return dateStr
}

export function getLastUpdate(dateTimeString: string): string {
  if (!dateTimeString) return ''

  const date = new Date(dateTimeString)
  if (isNaN(date.getTime())) return '' // Handle invalid dates

  // Return the formatted date and time using date-fns as a string
  return format(date, "dd-MM-yyyy'T'HH:mm")
}

export function getLastUpdatedTime(dateTimeString: string): string {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)

  // Format date
  const formattedDate: any = `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}-${date.getFullYear()}`

  // Format time in 24-hour format
  const formattedTime = `${date.getHours().toString().padStart(2, '0')}:${date
    .getMinutes()
    .toString()
    .padStart(2, '0')}`
  return `${formattedDate}    ${formattedTime}`
}

export const getDateDifference = (date1: string, date2: string): number => {
  const format = 'yyyy-MM-dd' // Define the date format

  const d1 = parse(date1, format, new Date())
  const d2 = parse(date2, format, new Date())

  // return Math.abs(differenceInDays(d1, d2))
  return differenceInDays(d1, d2)
}

/**
 * Checks if the first date is before the second date.
 * Accepts both 'yyyy-MM-dd' and 'dd-MM-yyyy' formats.
 */
export const isDateBefore = (date1: string, date2: string) => {
  try {
    const d1 = parseDate(date1)
    const d2 = parseDate(date2)
    return isBefore(d1, d2)
  } catch (error) {
    console.error('Invalid date format:', error)
    return false
  }
}

/**
 * Checks if the first date is before the second date.
 * Accepts both 'yyyy-MM-dd' and 'dd-MM-yyyy' formats.
 */
export const isDateAfter = (date1: string, date2: string) => {
  try {
    const d1 = parseDate(date1)
    const d2 = parseDate(date2)
    return isAfter(d1, d2)
  } catch (error) {
    console.error('Invalid date format:', error)
    return false
  }
}

/**
 * Converts a JavaScript Date object to the DD-MM-YYYY format.
 *
 * This function performs the following steps:
 * 1. Creates a new Date object from the input date string.
 * 2. Extracts the day, month, and year from the Date object.
 * 3. Returns the date in the DD-MM-YYYY format.
 *
 * @param dateStr - The date string to convert.
 * @returns The date in the DD-MM-YYYY format.
 */
export const jsDateToDDMMYYYY = (dateStr: string) => {
  const date = new Date(dateStr)

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0') // Months are zero-based
  const year = date.getFullYear()

  return `${day}-${month}-${year}`
}

/**
 * Validates if a date string is in YYYY-MM-DD format and is a valid date.
 *
 * This function performs the following steps:
 * 1. Checks if the date string is null or undefined.
 * 2. Parses the date string into a Date object using the 'yyyy-MM-dd' format.
 * 3. Returns true if the parsed date is valid, otherwise returns false.
 *
 * @param dateStr - The date string to validate.
 * @returns true if the date string is in YYYY-MM-DD format and is a valid date, otherwise false.
 */
export const isValidYMDDate = (dateStr: string | null): boolean => {
  if (!dateStr) return false
  const parsed = parse(dateStr, 'yyyy-MM-dd', new Date())
  return isValid(parsed)
}

/**
 *
 * @param dateStr check is passed date is valid DD-MM-YYYY formate
 * @returns true if date string is valid DD-MM-YYYY
 */
export const isValidDDMMYYYYDate = (dateStr: string) => {
  if (dateStr === '-' || !dateStr) return false
  try {
    const parsedDate = parse(dateStr, 'dd-MM-yyyy', new Date())
    return parsedDate instanceof Date && !isNaN(parsedDate.getTime())
  } catch {
    return false
  }
}

/**
 * Parses a date string into a Date object.
 * Supports 'yyyy-MM-dd' and 'dd-MM-yyyy' formats.
 */
export const parseDate = (dateStr: string): Date => {
  const yyyyMMddRegex = /^\d{4}-\d{2}-\d{2}$/
  return yyyyMMddRegex.test(dateStr)
    ? parse(dateStr, 'yyyy-MM-dd', new Date())
    : parse(dateStr, 'dd-MM-yyyy', new Date())
}

/**
 * Function to return the maximum date and its index between two dates.
 * @param date1 - First date in "DD-MM-YYYY" or "YYYY-MM-DD" format.
 * @param date2 - Second date in "DD-MM-YYYY" or "YYYY-MM-DD" format.
 * @returns An object containing the maximum date in "DD-MM-YYYY" format and its index (0 for date1, 1 for date2), or null if both are unavailable.
 */
export const getMaxDateWithIndex = (
  date1: string | null,
  date2: string | null,
): { maxDate: string; index: number } | null => {
  if (!date1 && !date2) return null // Return null if both dates are unavailable
  const parsedDate1 = date1 ? parseDate(date1) : null
  const parsedDate2 = date2 ? parseDate(date2) : null

  if (!parsedDate1 && !parsedDate2) return null // Return null if no valid date is found

  let maxDate: Date | null = null
  let index: number = -1

  if (parsedDate1 && parsedDate2) {
    maxDate = parsedDate1 > parsedDate2 ? parsedDate1 : parsedDate2
    index = parsedDate1 > parsedDate2 ? 0 : 1
  } else if (parsedDate1) {
    maxDate = parsedDate1
    index = 0
  } else if (parsedDate2) {
    maxDate = parsedDate2
    index = 1
  }

  return {
    maxDate: maxDate ? format(maxDate, 'dd-MM-yyyy') : '',
    index,
  }
}

/**
 * Compares two dates to check if the last date is greater than or equal to the first date.
 * Only the date part is considered, ignoring the time.
 *
 * @param firstDate - The first date in "DD-MM-YYYY" or "YYYY-MM-DD" format.
 * @param lastDate - The last date in "DD-MM-YYYY" or "YYYY-MM-DD" format.
 * @returns true if the last date is greater than or equal to the first date, false otherwise.
 */
export const checkLastDateGreaterOrEqual = (firstDate: string, lastDate: string): boolean => {
  if (!firstDate || !lastDate) return true

  const formattedFirstDate = parseDate(firstDate)
  const formattedLastDate = parseDate(lastDate)

  if (!formattedFirstDate || !formattedLastDate) return false

  //* Compare only the date part
  return isEqual(formattedLastDate, formattedFirstDate) || isAfter(formattedLastDate, formattedFirstDate)
}

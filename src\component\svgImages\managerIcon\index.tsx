import React, { ReactElement, forwardRef } from 'react'

interface ManagerIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: string
}

const ManagerIcon: React.FC<ManagerIconProps> = forwardRef<SVGSVGElement, ManagerIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="35px" height="35px" viewBox="0 0 40 40" version="1.1">
        <g id="surface1">
          <path
            style={{
              fillRule: 'evenodd',
              fill: 'rgb(93.72549%,95.686275%,98.039216%)',
              fillOpacity: 1,
              strokeWidth: 1.14583,
              strokeLinecap: 'butt',
              strokeLinejoin: 'miter',
              stroke: 'rgb(100%,100%,100%)',
              strokeOpacity: 1,
              strokeMiterlimit: 8,
            }}
            d="M 3895.500401 194.49627 C 3895.500401 162.467008 3921.462873 136.504537 3953.492135 136.504537 C 3985.521396 136.504537 4011.495977 162.467008 4011.495977 194.49627 C 4011.495977 226.537641 3985.521396 252.500112 3953.492135 252.500112 C 3921.462873 252.500112 3895.500401 226.537641 3895.500401 194.49627 Z M 3895.500401 194.49627"
            transform="matrix(0.322581,0,0,0.322581,-1255.16129,-42.580645)"
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(25.098039%,25.098039%,25.098039%)',
              fillOpacity: 1,
            }}
            d="M 19.90625 32.574219 C 19.070312 32.554688 18.234375 32.542969 17.402344 32.511719 C 17.035156 32.496094 16.667969 32.4375 16.304688 32.398438 C 15.761719 32.339844 15.222656 32.289062 14.683594 32.222656 C 14.394531 32.191406 14.105469 32.152344 13.820312 32.097656 C 13.476562 32.03125 13.132812 31.953125 12.792969 31.878906 C 12.78125 31.875 12.769531 31.871094 12.757812 31.871094 C 12.457031 31.808594 12.152344 31.757812 11.855469 31.6875 C 11.609375 31.628906 11.367188 31.546875 11.125 31.476562 C 10.90625 31.414062 10.6875 31.355469 10.46875 31.289062 C 10.269531 31.226562 10.074219 31.152344 9.875 31.078125 C 9.707031 31.019531 9.539062 30.957031 9.375 30.890625 C 9.195312 30.824219 9.019531 30.75 8.847656 30.675781 C 8.707031 30.613281 8.570312 30.550781 8.4375 30.480469 C 8.222656 30.371094 8.007812 30.261719 7.796875 30.140625 C 7.605469 30.03125 7.417969 29.921875 7.242188 29.789062 C 7.039062 29.636719 6.835938 29.476562 6.660156 29.296875 C 6.46875 29.105469 6.296875 28.890625 6.136719 28.671875 C 6.050781 28.554688 6.007812 28.402344 5.945312 28.265625 C 5.933594 28.242188 5.933594 28.210938 5.921875 28.1875 C 5.746094 27.824219 5.796875 27.453125 5.894531 27.082031 C 5.917969 26.996094 5.929688 26.902344 5.949219 26.8125 C 5.972656 26.710938 6 26.613281 6.027344 26.511719 C 6.035156 26.484375 6.050781 26.460938 6.058594 26.433594 C 6.117188 26.199219 6.167969 25.964844 6.234375 25.738281 C 6.296875 25.519531 6.378906 25.304688 6.449219 25.089844 C 6.460938 25.054688 6.472656 25.023438 6.484375 24.988281 C 6.5625 24.675781 6.695312 24.386719 6.859375 24.113281 C 7.078125 23.75 7.394531 23.488281 7.730469 23.246094 C 7.957031 23.082031 8.175781 22.910156 8.40625 22.757812 C 8.566406 22.648438 8.738281 22.5625 8.902344 22.46875 C 9.046875 22.382812 9.191406 22.292969 9.34375 22.210938 C 9.535156 22.105469 9.730469 22.007812 9.925781 21.910156 C 10.070312 21.832031 10.214844 21.757812 10.359375 21.683594 C 10.46875 21.628906 10.578125 21.574219 10.691406 21.523438 C 10.832031 21.457031 10.976562 21.402344 11.117188 21.335938 C 11.25 21.273438 11.382812 21.199219 11.515625 21.136719 C 11.625 21.085938 11.738281 21.042969 11.847656 21 C 12.015625 20.929688 12.1875 20.867188 12.355469 20.796875 C 12.523438 20.726562 12.6875 20.648438 12.855469 20.578125 C 12.964844 20.535156 13.078125 20.5 13.191406 20.460938 C 13.363281 20.394531 13.539062 20.328125 13.714844 20.265625 C 13.875 20.203125 14.039062 20.160156 14.191406 20.085938 C 14.5625 19.902344 14.550781 20.003906 14.777344 20.203125 C 15.015625 20.414062 15.257812 20.621094 15.503906 20.828125 C 15.640625 20.941406 15.78125 21.054688 15.929688 21.15625 C 16.109375 21.277344 16.296875 21.390625 16.480469 21.507812 C 16.597656 21.578125 16.710938 21.660156 16.835938 21.722656 C 16.964844 21.792969 17.097656 21.847656 17.230469 21.90625 C 17.34375 21.957031 17.457031 22.007812 17.570312 22.046875 C 17.765625 22.121094 17.96875 22.1875 18.167969 22.253906 C 18.199219 22.265625 18.234375 22.277344 18.269531 22.285156 C 19.109375 22.441406 19.953125 22.492188 20.800781 22.40625 C 21.070312 22.375 21.335938 22.28125 21.597656 22.207031 C 21.789062 22.15625 21.980469 22.097656 22.167969 22.027344 C 22.316406 21.972656 22.460938 21.890625 22.605469 21.820312 C 22.71875 21.765625 22.832031 21.710938 22.941406 21.644531 C 23.160156 21.519531 23.382812 21.402344 23.589844 21.257812 C 23.910156 21.027344 24.222656 20.789062 24.53125 20.539062 C 24.699219 20.402344 24.855469 20.246094 25.011719 20.09375 C 25.136719 19.980469 25.253906 19.96875 25.421875 20.039062 C 25.628906 20.128906 25.84375 20.195312 26.054688 20.273438 C 26.207031 20.328125 26.359375 20.390625 26.507812 20.453125 C 26.695312 20.527344 26.882812 20.605469 27.070312 20.683594 C 27.234375 20.753906 27.402344 20.832031 27.566406 20.90625 C 27.6875 20.960938 27.804688 21.015625 27.925781 21.066406 C 28.070312 21.125 28.214844 21.179688 28.355469 21.242188 C 28.496094 21.308594 28.628906 21.382812 28.765625 21.453125 C 28.855469 21.5 28.949219 21.542969 29.042969 21.585938 C 29.171875 21.644531 29.300781 21.703125 29.429688 21.765625 C 29.5625 21.832031 29.691406 21.90625 29.824219 21.976562 C 29.925781 22.03125 30.023438 22.089844 30.125 22.140625 C 30.335938 22.25 30.550781 22.351562 30.757812 22.46875 C 30.953125 22.574219 31.136719 22.695312 31.328125 22.8125 C 31.480469 22.910156 31.628906 23.019531 31.785156 23.109375 C 32.0625 23.265625 32.273438 23.496094 32.496094 23.707031 C 32.726562 23.925781 32.871094 24.207031 33.011719 24.480469 C 33.089844 24.636719 33.128906 24.808594 33.191406 24.972656 C 33.257812 25.15625 33.332031 25.34375 33.402344 25.527344 C 33.40625 25.542969 33.417969 25.558594 33.421875 25.574219 C 33.476562 25.78125 33.53125 25.988281 33.585938 26.195312 C 33.609375 26.277344 33.632812 26.355469 33.660156 26.4375 C 33.667969 26.460938 33.683594 26.480469 33.6875 26.503906 C 33.785156 26.988281 33.9375 27.472656 33.839844 27.976562 C 33.804688 28.160156 33.695312 28.332031 33.621094 28.511719 C 33.519531 28.757812 33.34375 28.957031 33.15625 29.132812 C 32.898438 29.375 32.625 29.605469 32.34375 29.816406 C 32.097656 29.996094 31.832031 30.152344 31.566406 30.300781 C 31.34375 30.425781 31.109375 30.519531 30.878906 30.628906 C 30.734375 30.699219 30.59375 30.769531 30.449219 30.835938 C 30.332031 30.886719 30.210938 30.941406 30.085938 30.984375 C 29.90625 31.050781 29.726562 31.105469 29.546875 31.167969 C 29.335938 31.238281 29.125 31.3125 28.914062 31.382812 C 28.851562 31.40625 28.78125 31.414062 28.71875 31.433594 C 28.632812 31.460938 28.550781 31.488281 28.46875 31.515625 C 28.449219 31.523438 28.433594 31.539062 28.414062 31.542969 C 28.183594 31.597656 27.949219 31.652344 27.71875 31.707031 C 27.621094 31.730469 27.527344 31.761719 27.429688 31.785156 C 27.359375 31.804688 27.285156 31.824219 27.210938 31.839844 C 27.066406 31.871094 26.921875 31.898438 26.777344 31.925781 C 26.695312 31.945312 26.613281 31.960938 26.53125 31.976562 C 26.378906 32.003906 26.226562 32.035156 26.074219 32.066406 C 25.996094 32.082031 25.917969 32.097656 25.839844 32.109375 C 25.660156 32.140625 25.476562 32.167969 25.296875 32.195312 C 25.246094 32.207031 25.195312 32.222656 25.144531 32.230469 C 24.578125 32.285156 24.007812 32.339844 23.441406 32.398438 C 23.234375 32.421875 23.027344 32.457031 22.820312 32.46875 C 22.292969 32.496094 21.765625 32.523438 21.238281 32.539062 C 20.796875 32.546875 20.351562 32.539062 19.90625 32.539062 C 19.90625 32.550781 19.90625 32.5625 19.90625 32.574219 Z M 19.90625 32.574219 "
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'nonzero',
              fill: 'rgb(25.098039%,25.098039%,25.098039%)',
              fillOpacity: 1,
            }}
            d="M 12.976562 11.980469 C 13.011719 11.683594 13.050781 11.363281 13.085938 11.046875 C 13.09375 10.992188 13.109375 10.933594 13.109375 10.878906 C 13.09375 10.5625 13.230469 10.277344 13.285156 9.972656 C 13.316406 9.78125 13.417969 9.601562 13.492188 9.414062 C 13.554688 9.253906 13.609375 9.085938 13.679688 8.925781 C 13.738281 8.792969 13.8125 8.667969 13.886719 8.539062 C 13.941406 8.4375 14.007812 8.335938 14.066406 8.230469 C 14.273438 7.84375 14.53125 7.488281 14.835938 7.171875 C 15.125 6.875 15.417969 6.578125 15.722656 6.296875 C 15.855469 6.175781 16.019531 6.085938 16.171875 5.984375 C 16.289062 5.902344 16.40625 5.824219 16.527344 5.75 C 16.609375 5.699219 16.695312 5.65625 16.777344 5.613281 C 16.910156 5.550781 17.042969 5.488281 17.175781 5.429688 C 17.347656 5.355469 17.519531 5.285156 17.691406 5.214844 C 17.707031 5.207031 17.71875 5.195312 17.734375 5.191406 C 17.941406 5.136719 18.148438 5.078125 18.355469 5.023438 C 18.445312 5 18.53125 4.980469 18.621094 4.960938 C 18.707031 4.9375 18.792969 4.910156 18.878906 4.898438 C 19.515625 4.824219 20.15625 4.824219 20.792969 4.90625 C 21.082031 4.941406 21.363281 5.046875 21.648438 5.125 C 21.835938 5.179688 22.027344 5.242188 22.210938 5.3125 C 22.367188 5.375 22.519531 5.453125 22.671875 5.527344 C 22.769531 5.574219 22.871094 5.621094 22.964844 5.675781 C 23.136719 5.773438 23.304688 5.867188 23.46875 5.96875 C 23.894531 6.226562 24.269531 6.546875 24.617188 6.902344 C 24.890625 7.179688 25.144531 7.480469 25.34375 7.820312 C 25.476562 8.046875 25.621094 8.265625 25.75 8.492188 C 25.875 8.710938 25.996094 8.933594 26.097656 9.164062 C 26.179688 9.339844 26.230469 9.53125 26.285156 9.71875 C 26.363281 9.972656 26.445312 10.230469 26.5 10.488281 C 26.542969 10.6875 26.554688 10.894531 26.570312 11.097656 C 26.597656 11.480469 26.644531 11.859375 26.632812 12.242188 C 26.625 12.667969 26.582031 13.101562 26.523438 13.523438 C 26.480469 13.824219 26.382812 14.113281 26.308594 14.40625 C 26.25 14.625 26.199219 14.84375 26.132812 15.054688 C 26.070312 15.25 25.988281 15.441406 25.914062 15.636719 C 25.902344 15.660156 25.890625 15.6875 25.878906 15.714844 C 25.796875 15.902344 25.71875 16.089844 25.636719 16.277344 C 25.578125 16.40625 25.519531 16.539062 25.453125 16.667969 C 25.386719 16.800781 25.304688 16.929688 25.226562 17.058594 C 25.132812 17.21875 25.035156 17.378906 24.933594 17.535156 C 24.820312 17.710938 24.710938 17.894531 24.582031 18.058594 C 24.335938 18.359375 24.085938 18.65625 23.820312 18.9375 C 23.648438 19.125 23.449219 19.289062 23.253906 19.457031 C 23.046875 19.628906 22.835938 19.800781 22.617188 19.953125 C 22.4375 20.078125 22.242188 20.175781 22.054688 20.285156 C 21.960938 20.335938 21.867188 20.386719 21.769531 20.433594 C 21.605469 20.507812 21.441406 20.585938 21.269531 20.640625 C 21.082031 20.699219 20.886719 20.742188 20.695312 20.765625 C 20.058594 20.84375 19.421875 20.871094 18.789062 20.722656 C 18.542969 20.667969 18.300781 20.605469 18.0625 20.53125 C 17.945312 20.496094 17.832031 20.429688 17.722656 20.367188 C 17.527344 20.253906 17.332031 20.136719 17.148438 20.007812 C 16.929688 19.863281 16.714844 19.710938 16.511719 19.542969 C 16.3125 19.382812 16.117188 19.207031 15.933594 19.023438 C 15.761719 18.855469 15.605469 18.675781 15.445312 18.496094 C 15.324219 18.359375 15.207031 18.21875 15.097656 18.070312 C 14.976562 17.910156 14.867188 17.746094 14.757812 17.578125 C 14.632812 17.394531 14.515625 17.207031 14.398438 17.015625 C 14.34375 16.925781 14.289062 16.832031 14.242188 16.734375 C 14.132812 16.503906 14.027344 16.269531 13.921875 16.039062 C 13.855469 15.894531 13.785156 15.746094 13.71875 15.597656 C 13.710938 15.585938 13.703125 15.570312 13.699219 15.554688 C 13.589844 15.199219 13.480469 14.847656 13.378906 14.492188 C 13.300781 14.222656 13.234375 13.949219 13.164062 13.679688 C 13.15625 13.648438 13.148438 13.617188 13.144531 13.585938 C 13.089844 13.058594 13.035156 12.53125 12.976562 11.980469 Z M 12.976562 11.980469 "
          />
        </g>
      </svg>
    )
  },
)
ManagerIcon.displayName = 'ManagerIcon'

export default ManagerIcon

import React, { ReactElement, forwardRef } from 'react'

interface loginBgProps {
  className?: string
  onClick?: (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => void
}

const KeyBoardArrowUp: React.FC<loginBgProps> = forwardRef<SVGSVGElement, loginBgProps>((props, ref): ReactElement => {
  const { className, onClick } = props

  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      ref={ref}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.67526 5.62037C7.86251 5.45988 8.13881 5.45988 8.32606 5.62037L12.9927 9.62037C13.2024 9.80008 13.2267 10.1157 13.047 10.3254C12.8672 10.5351 12.5516 10.5593 12.3419 10.3796L8.00066 6.65854L3.65939 10.3796C3.44973 10.5593 3.13408 10.5351 2.95437 10.3254C2.77465 10.1157 2.79893 9.80008 3.0086 9.62037L7.67526 5.62037Z"
        fill="#404040"
      />
    </svg>
  )
})
KeyBoardArrowUp.displayName = 'KeyBoardArrowUp'

export default KeyBoardArrowUp

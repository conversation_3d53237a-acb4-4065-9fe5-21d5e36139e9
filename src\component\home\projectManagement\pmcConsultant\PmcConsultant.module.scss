@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.container {
  width: 100%;
  @include respond-to('mobile') {
    max-width: 19.5rem;
    min-width: 19rem;
  }

  @include respond-to('tablet') {
    max-width: 32rem;
    min-width: 30rem;
  }

  @include respond-to('laptop') {
    max-width: 34rem;
    min-width: 32rem;
  }

  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;

    .headerTitle {
      text-align: left;
      padding: 13px 0 13px 20px;
      color: $BLACK;
      @include respond-to('mobile') {
        font-size: 0.875rem;
      }

      @include respond-to('tablet') {
        font-size: 0.9375rem;
      }
    }

    .actionButtons {
      display: flex;
      gap: 20px;

      .closeButton {
        padding: 8px 10px;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 20px 10px 0px 20px;
    height: calc(100vh - 101px);
    > div {
      padding-inline-end: 10px;
      padding-bottom: 10px;
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }
      &::-webkit-scrollbar-track {
        border-radius: 5px;
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: #dddddd;
        border-radius: 5px;
        width: 4px;
      }
    }

    .addProjectButton {
      padding: 8px 10px;
    }
  }

  .entityField {
    > div > div > input {
      color: $BLACK;
      border-radius: 4px;
      min-width: 400px;
      @include respond-to('mobile') {
        min-width: 250px;
      }

      @include respond-to('tablet') {
        min-width: 300px;
      }

      &:focus {
        border-radius: 4px;
        background: $FOCUS_TEXTFIELD_BG;
        border: 1px solid $FOCUS_TEXTFIELD_BORDER;
      }
    }
  }
}
.form {
  display: flex;
  gap: 20px;
  flex-direction: column;
}
.actionButtons {
  display: flex;
  gap: 5px;
  .editRowIcon,
  .deleteRowIcon {
    cursor: pointer;
  }
}

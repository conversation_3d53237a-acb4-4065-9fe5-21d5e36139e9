// Array defining the sequence of project stage statuses used to determine the correct order during the automation process.
// This order is crucial for correctly identifying and updating the project's current status.
export const statusOrder = [
  'Initiation',
  'LDC Procurement',
  'Design',
  'Contractor Procurement',
  'Construction',
  'DLP and Project Closeout',
]

export const healthAndSafetyFields: string[] = [
  'incidents',
  'man_hours',
  'planned_manpower',
  'actual_manpower',
  'lti',
  'is_external_lookup_for_health_and_safety',
]

export const unnecessaryOfSummaryFormField = [
  'id',
  'waste_recycled_percentage',
  'reinvented_economy_percentage',
  'reinvented_economy_value',
  'pearl_rating_percentage',
  'recycled_material_percentage',
  'workers_welfare_compliance_percentage',
  'initiation',
  'ldc_procurement',
  'design',
  'contractor_procurement',
  'construction',
  'handover',
  'overall',
  'governanceId',
  'sustainabilityId',
  'updated_by',
  'project_management_updated_by',
  'health_safety_updated_by',
  'Overall_Forecasted_Finish_',
  'controls_manager_avatar',
  'design_project_manager_avatar',
  'design_project_owner_avatar',
  'director_avatar',
  'executive_director_avatar',
  'portfolio_manager_avatar',
  'procurement_manager_avatar',
  'delivery_project_manager_avatar',
  'MasterProjectClassification',
  'MasterPortfolioManager',
  'MasterControlManager',
  'MasterEntityCategory',
  'MasterPricingType',
  'MasterDirector',
  'DeliveryProjectManagers',
  'DesignProjectManagers',
  'Locations',
  'SubLocations',
  'ExecutiveDirectors',
  'DesignExecutiveDirectors',
  'NextStepsToAdvanceProgress',
  'NonRecoverableDelayJustifications',
  'MitigationRecoveryPlans',
  'ReasonsForDelay',
  'PrimaryReasonsForDelay',
]

export const tabPermissions: any = {
  Progress: 'Status',
  Summary: 'Summary',
  Commercials: 'Commercials',
  'Project Overview': 'Project Overview',
  'SPA & Milestones': 'SPA & Milestones',
  'Authority Approvals Tracking': 'Authority Approvals Tracking',
  Handover: 'Handover',
  Media: 'Media',
}

export const governanceField = [
  'initiation',
  'ldc_procurement',
  'design',
  'contractor_procurement',
  'construction',
  'handover',
]

export const sustainabilityField = [
  'waste_recycled_percentage',
  'reinvented_economy_percentage',
  'reinvented_economy_value',
  'pearl_rating_percentage',
  'recycled_material_percentage',
  'workers_welfare_compliance_percentage',
]

export const projectDetailsField = [
  'project_name_ar',
  'project_id',
  'erp_project_name',
  'cpds_project_id',
  'dof_number',
  'owning_entity',
  'entity_category',
  'location',
  'project_brief',
  'project_type',
  'project_status',
  'sub_location',
  'latitude',
  'longitude',
  'project_pricing_type',
  'project_classification',
  'isExecutiveProject',
  'executive_sorting_order',
  'sub_location_ids',
]

export const projectManagementField = [
  'decree_date',
  'commencement_date',
  'decree_end_date',
  'overall_planned_finish_date',
  'overall_forecasted_finish_date',
  'delay_reason',
  'reason_for_delay',
  'other_reason_for_delay',
  'is_subject_to_rebaseline',
  'budget_uplift_submitted',
  'reason_rebaseline',
  'notes',
  'budget_uplift_submission_date',
  'budget_uplift_last_submission_date',
  'budget_uplift_submission_notes',
  'budget_uplift_status',
  'uplifted_budget',
  `tip_submitted`,
  `tip_submission_date`,
  `submitted_eot_date`,
  `tip_approval_date`,
  'tip_last_submission_date',
  'tip_notes',
  `eot_submitted`,
  `eot_submission_date`,
  `eot_approval_date`,
  `eot_last_submission_date`,
  'eot_notes',
  'budget_uplift_approval_date',
  'delay_code',
  'non_recoverable_delay_justification',
  'next_steps_to_advance_progress',
  'mitigation_recovery_plan',
  'scope_change',
  'scope_change_approval_date',
  'scope_change_last_submission_date',
  'scope_change_submission_date',
  'scope_change_notes',
  'potential_budget_uplift_submitted',
  'potential_budget_uplift_value',
  'potential_budget_uplift_reason',
]

export const excludeProjectStatusFields = ['ProcurementManagers']

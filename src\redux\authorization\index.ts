import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IAuthorizationStates } from './interface'
import { StatusEnum } from '../types'
import { ApiGetNoAuth, ApiPostNoAuth } from '@/src/api'
import { permissionList, whiteListedUserList } from '@/src/constant/enum'

const initialState: IAuthorizationStates = {
  authUrlStatus: StatusEnum.Idle,
  getAccessTokenStatus: StatusEnum.Idle,
  logInUserStatus: StatusEnum.Idle,
  logOutUserStatus: StatusEnum.Idle,
  currentLogInUserStatus: StatusEnum.Idle,
  currentUser: {
    email: '',
    id: 0,
    name: '',
    organization: '',
    role: {
      id: 0,
      role_name: '',
      permission_type: '',
      owning_entities: [],
      projects: [],
      view_permissions: [],
    },
    user_type: 'user',
  },
}

export const authUrl = createAsyncThunk('/auth/get-authorization-url', async (data: string, thunkAPI) => {
  try {
    const response = await ApiGetNo<PERSON>uth(`/auth/get-authorization-url?redirect_uri=${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const getAccessToken = createAsyncThunk('/auth/obtain-access-token', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/auth/obtain-access-token?redirect_uri=${data.redirectUrl}`, {
      code: data.code,
    })
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const logInUser = createAsyncThunk('/auth/user', async (data: { period: string }, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/auth/user?period=${data.period}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const logOutUser = createAsyncThunk('/auth/logout', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/auth/logout`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const currentLogInUser = createAsyncThunk('/auth/currentUser', async (data: string, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/auth/user?period=${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const authorizationSlice = createSlice({
  name: 'authorization',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    builder.addCase(authUrl.pending, (state) => {
      state.authUrlStatus = StatusEnum.Pending
    })
    builder.addCase(authUrl.fulfilled, (state, action) => {
      state.authUrlStatus = StatusEnum.Success
    })
    builder.addCase(authUrl.rejected, (state) => {
      state.authUrlStatus = StatusEnum.Failed
    })

    builder.addCase(getAccessToken.pending, (state) => {
      state.getAccessTokenStatus = StatusEnum.Pending
    })
    builder.addCase(getAccessToken.fulfilled, (state, action) => {
      state.getAccessTokenStatus = StatusEnum.Success
    })
    builder.addCase(getAccessToken.rejected, (state) => {
      state.getAccessTokenStatus = StatusEnum.Failed
    })

    builder.addCase(logInUser.pending, (state) => {
      state.logInUserStatus = StatusEnum.Pending
    })
    builder.addCase(logInUser.fulfilled, (state, action) => {
      state.logInUserStatus = StatusEnum.Success
    })
    builder.addCase(logInUser.rejected, (state) => {
      state.logInUserStatus = StatusEnum.Failed
    })

    builder.addCase(logOutUser.pending, (state) => {
      state.logOutUserStatus = StatusEnum.Pending
    })
    builder.addCase(logOutUser.fulfilled, (state, action) => {
      state.logOutUserStatus = StatusEnum.Success
    })
    builder.addCase(logOutUser.rejected, (state) => {
      state.logOutUserStatus = StatusEnum.Failed
    })

    builder.addCase(currentLogInUser.pending, (state) => {
      state.currentLogInUserStatus = StatusEnum.Pending
    })
    builder.addCase(currentLogInUser.fulfilled, (state, action) => {
      state.currentLogInUserStatus = StatusEnum.Success
      let actionPayload = action.payload as any

      if (whiteListedUserList.includes(actionPayload.data.email)) {
        actionPayload.data.role.view_permissions = permissionList
      }

      // actionPayload.data.user_type = 'super_admin'
      state.currentUser = actionPayload.data
    })
    builder.addCase(currentLogInUser.rejected, (state) => {
      state.currentLogInUserStatus = StatusEnum.Failed
    })
  },
})

export const {} = authorizationSlice.actions

// Export the reducer
export default authorizationSlice.reducer

import React, { useMemo } from 'react'
import { InputAdornment, Tooltip } from '@mui/material'
import { FormikValues } from 'formik'
import styles from './ProjectManagement.module.scss'
import NumberInputField from '@/src/component/shared/numberInputField'
import TextInputField from '@/src/component/shared/textInputField'
import PercentageIcon from '@/src/component/svgImages/percentageIcon'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'

interface IProjectManagementProps {
  formik: FormikValues
  edit: any
}

const ProjectManagement: React.FC<IProjectManagementProps> = ({ formik, edit }) => {
  const isInitiation = formik.values.stage_status === INITIATION
  const isDesign = formik.values.stage_status === DESIGN
  const isContractorProcurement = formik.values.stage_status === CONTRACTOR_PROCUREMENT
  const isDlp = formik.values.stage_status === DLP_PROJECT_CLOSEOUT
  const isLdcProcurement = formik.values.stage_status === LDC_PROCUREMENT
  const isConstruction = formik.values.stage_status === CONSTRUCTION

  const REGEX = useMemo(() => /^[0-9]*(\.[0-9]*)?$/, [])

  return (
    <div>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: isConstruction ? '1fr 1fr 1fr 1fr 1fr' : '1fr',
          gap: '24px',
          marginTop: '16px',
        }}
      >
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement && !isConstruction ? (
          <div>
            <NumberInputField
              name="cost_per_sqm"
              labelText={'Cost per sqm'}
              disabled={!edit}
              placeholder="1,2,3..."
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              value={Number(formik?.values?.cost_per_sqm)}
              onChange={(value) => formik.setFieldValue('cost_per_sqm', Boolean(value))}
            />
          </div>
        ) : null}

        {/* BACKEND:AUTO_CALCULATED */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Today - Contract Start Date/Contract End Date - Contract Start Date * 100'} arrow>
              <div>
                <TextInputField
                  name="time_elapsed_perc"
                  disabled={true}
                  placeholder="1,2,3,..."
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'Time Elapsed %'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('time_elapsed_percentage', inputValue)
                }
              }}
              value={formik.values?.time_elapsed_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        ) : null} */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Today - Contract End Date/Contract Start Date - Contract Start Date * 100'} arrow>
              <div>
                <TextInputField
                  name="SPI"
                  disabled={true}
                  placeholder="Type something..."
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'SPI'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('spi', inputValue)
                }
              }}
              value={formik.values?.spi}
            />
          </div>
        ) : null} */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Actual Progress % - Contractor Plan Progress %'} arrow>
              <div>
                <TextInputField
                  name="VAR"
                  placeholder="Type something..."
                  disabled={true}
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'VAR'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('project_management_var', inputValue)
                }
              }}
              value={formik.values?.project_management_var}
            />
          </div>
        ) : null} */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Previous period Forecast/Actual Finish Date'} arrow>
              <div>
                <TextInputField
                  name="forecast_completion_last_week"
                  placeholder="Type something..."
                  className={`${styles.inputFields}`}
                  disabled={true}
                  variant={'outlined'}
                  labelText={'Forecast Completion Last Period'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('forecast_completion_last_period',  parseFloat(String(value)))
                }
              }}
              value={formik.values?.forecast_completion_last_period}
            />
          </div>
        ) : null} */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Forecast/Actual Finish Date - last period Forecast/Actual Finish Date'} arrow>
              <div>
                <TextInputField
                  name="delay_this_week"
                  placeholder="Type something..."
                  className={`${styles.inputFields}`}
                  disabled={true}
                  variant={'outlined'}
                  labelText={'Delay This Period'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('delay_this_period', inputValue)
                }
              }}
              value={formik.values?.delay_this_period}
            />
          </div>
        ) : null} */}
        {/* {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <Tooltip title={'Forecast/Actual Finish Date - Contract End Date'} arrow>
              <div>
                <TextInputField
                  name="cumulative_delay"
                  placeholder="Type something..."
                  disabled={true}
                  variant={'outlined'}
                  className={`${styles.inputFields}`}
                  labelText={'Cumulative Delay'}
                  onChange={(event) => {
                    const inputValue = event.target.value
                    const isValidInput = REGEX.test(inputValue)

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('cumulative_delay', inputValue)
                }
              }}
              value={formik.values?.cumulative_delay}
            />
          </div>
        ) : null} */}
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <NumberInputField
              name="manpower_planned"
              disabled={!edit}
              labelText={'Manpower Planned'}
              placeholder="1,2,3..."
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              value={formik?.values?.manpower_planned}
              onChange={(value) => formik.setFieldValue('manpower_planned', value)}
            />
          </div>
        ) : null}
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <NumberInputField
              name="manpower_actual"
              labelText={'Manpower Actual'}
              disabled={!edit}
              placeholder="1,2,3..."
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              value={formik?.values?.manpower_actual}
              onChange={(value) => formik.setFieldValue('manpower_actual', value)}
            />
          </div>
        ) : null}

        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <NumberInputField
              name="man_hours"
              disabled={!edit}
              placeholder="1,2,3..."
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              // variant={'outlined'}
              labelText={'Manhours'}
              value={formik?.values?.man_hours}
              onChange={(value) => formik.setFieldValue('man_hours', value)}
            />
          </div>
        ) : null}
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <NumberInputField
              name="incidents"
              labelText={'No of LTI’s'}
              disabled={!edit}
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              placeholder="1,2,3..."
              value={formik?.values?.incidents}
              onChange={(value) => formik.setFieldValue('incidents', value ? parseInt(String(value)) : 0)}
            />
          </div>
        ) : null}
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            <NumberInputField
              name="fatalities"
              labelText={'Fatalities'}
              disabled={!edit}
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              placeholder="1,2,3..."
              value={formik?.values?.fatalities}
              onChange={(value) => formik.setFieldValue('fatalities', parseInt(String(value)))}
            />
          </div>
        ) : null}
        {!isInitiation && !isDesign && !isContractorProcurement && !isDlp && !isLdcProcurement ? (
          <div>
            {/* BACKEND:AUTO_CALCULATED */}
            <Tooltip title={'(Incidents Number / Manhours Number) * 1000000'} arrow>
              <div>
                <TextInputField
                  name="LTI"
                  disabled={true}
                  className={`${styles.inputFields}`}
                  variant={'outlined'}
                  labelText={'LTI Ratio (Per 1M Mnhrs)'}
                  value={formik?.values?.LTI}
                  onChange={formik.handleChange}
                  placeholder="Type something..."
                />
              </div>
            </Tooltip>
          </div>
        ) : null}
        {!isInitiation && !isDesign && !isConstruction && !isDlp ? (
          <div>
            <TextInputField
              name="procurement_package"
              placeholder="Type something..."
              className={`${styles.inputFields} ${!edit ? '' : styles.highlightField}`}
              disabled={!edit}
              variant={'outlined'}
              labelText={'Procurement Package'}
              value={formik?.values?.procurement_package}
              onChange={formik.handleChange}
            />
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default ProjectManagement

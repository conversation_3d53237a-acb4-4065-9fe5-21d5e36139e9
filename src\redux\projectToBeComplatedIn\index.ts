import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetGovernanceResponse, IProjectToBeCompletedInStatus, IProjectToBeCompletedIn } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IProjectToBeCompletedInStatus = {
  getProjectToBeCompletedInStatus: StatusEnum.Idle,
  addProjectToBeCompletedInStatus: StatusEnum.Idle,
  deleteProjectToBeCompletedInStatus: StatusEnum.Idle,
  updateProjectToBeCompletedInStatus: StatusEnum.Idle,
  projectToBeCompletedIns: [],
  projectToBeCompletedIn: {},
}

export const getProjectToBeCompletedIN = createAsyncThunk(
  '/get-project-to-be-completed-in',
  async (data: { period: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiG<PERSON>NoA<PERSON>(`/project-to-be-completed-in?period=${period}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addProjectToBeCompletedIN = createAsyncThunk(
  '/add-project-to-be-completed-in',
  async (data: IProjectToBeCompletedIn, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/project-to-be-completed-in`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateProjectToBeCompletedIN = createAsyncThunk(
  '/update-project-to-be-completed-in',
  async (data: { id: any; data: IProjectToBeCompletedIn }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/project-to-be-completed-in/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteProjectToBeCompletedIN = createAsyncThunk(
  '/delete-project-to-be-completed-in',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/project-to-be-completed-in/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const entityKeyAchievementSlice = createSlice({
  name: 'entityKeyAchievement',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getProjectToBeCompletedIN
    builder.addCase(getProjectToBeCompletedIN.pending, (state) => {
      state.getProjectToBeCompletedInStatus = StatusEnum.Pending
    })
    builder.addCase(getProjectToBeCompletedIN.fulfilled, (state, action) => {
      state.getProjectToBeCompletedInStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetGovernanceResponse
      state.projectToBeCompletedIns = actionPayload.data
    })
    builder.addCase(getProjectToBeCompletedIN.rejected, (state) => {
      state.getProjectToBeCompletedInStatus = StatusEnum.Failed
    })
    //addProjectToBeCompletedIN
    builder.addCase(addProjectToBeCompletedIN.pending, (state) => {
      state.addProjectToBeCompletedInStatus = StatusEnum.Pending
    })
    builder.addCase(addProjectToBeCompletedIN.fulfilled, (state, action) => {
      state.addProjectToBeCompletedInStatus = StatusEnum.Success
    })
    builder.addCase(addProjectToBeCompletedIN.rejected, (state) => {
      state.addProjectToBeCompletedInStatus = StatusEnum.Failed
    })
    //updateEntityKeyAchievement
    builder.addCase(updateProjectToBeCompletedIN.pending, (state) => {
      state.updateProjectToBeCompletedInStatus = StatusEnum.Pending
    })
    builder.addCase(updateProjectToBeCompletedIN.fulfilled, (state, action) => {
      state.updateProjectToBeCompletedInStatus = StatusEnum.Success
    })
    builder.addCase(updateProjectToBeCompletedIN.rejected, (state) => {
      state.updateProjectToBeCompletedInStatus = StatusEnum.Failed
    })
    //deleteEntityKeyAchievement
    builder.addCase(deleteProjectToBeCompletedIN.pending, (state) => {
      state.deleteProjectToBeCompletedInStatus = StatusEnum.Pending
    })
    builder.addCase(deleteProjectToBeCompletedIN.fulfilled, (state, action) => {
      state.deleteProjectToBeCompletedInStatus = StatusEnum.Success
    })
    builder.addCase(deleteProjectToBeCompletedIN.rejected, (state) => {
      state.deleteProjectToBeCompletedInStatus = StatusEnum.Failed
    })
  },
})

export const {} = entityKeyAchievementSlice.actions

// Export the reducer
export default entityKeyAchievementSlice.reducer

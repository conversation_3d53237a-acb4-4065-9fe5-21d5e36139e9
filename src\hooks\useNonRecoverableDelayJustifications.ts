import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createNonRecoverableDelayJustification,
  deleteNonRecoverableDelayJustification,
  getNonRecoverableDelayJustifications,
  updateNonRecoverableDelayJustification,
} from '../services/nonRecoverableDelayJustification'
import {
  IGetNonRecoverableDelayJustificationRes,
  IUpdateNonRecoverableDelayJustification,
} from '../services/nonRecoverableDelayJustification/interface'

export const QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION = 'non-recoverable-delay-justification'

/**
 * Hook to fetch Non Recoverable Delay Justifications
 * @param enabled - Enables or disables the query
 */
export const useGetNonRecoverableDelayJustifications = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetNonRecoverableDelayJustificationRes>({
    queryKey: [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION],
    queryFn: getNonRecoverableDelayJustifications,
    enabled,
  })

  return {
    nonRecoverableDelayJustifications: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Non Recoverable Delay Justifications
 */
export const useCreateNonRecoverableDelayJustifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createNonRecoverableDelayJustification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION] })
    },
  })
}

/**
 * Hook to delete a Non Recoverable Delay Justifications
 */
export const useDeleteNonRecoverableDelayJustifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteNonRecoverableDelayJustification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION] })
    },
  })
}

/**
 * Hook to update a Non Recoverable Delay Justifications
 */
export const useUpdateNonRecoverableDelayJustifications = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateNonRecoverableDelayJustification,
    onMutate: async (updatedData: IUpdateNonRecoverableDelayJustification) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION] })
      const previousData = queryClient.getQueryData<IGetNonRecoverableDelayJustificationRes>([
        QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION,
      ])
      // Optimistically update the cache
      queryClient.setQueryData<IGetNonRecoverableDelayJustificationRes>(
        [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION],
        (old) => {
          if (!old) return old
          return {
            ...old,
            data: old.data?.map((entity) =>
              entity.id === updatedData.id
                ? {
                    ...entity,
                    non_recoverable_delay_justification: updatedData.non_recoverable_delay_justification,
                    non_recoverable_delay_justification_code: updatedData.non_recoverable_delay_justification_code,
                  }
                : entity,
            ),
          }
        },
      )

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_NON_RECOVERABLE_DELAY_JUSTIFICATION] })
    },
  })
}

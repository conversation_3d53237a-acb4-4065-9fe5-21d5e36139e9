import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterConsultant,
  deleteMasterConsultant,
  getMasterConsultants,
  updateMasterConsultant,
} from '../services/consultant'
import { IGetMasterConsultantsResponse, IUpdateConsultant } from '../services/consultant/interface'

export const QUERY_CONSULTANT = 'consultant'

/**
 * Hook to fetch consultant
 * @param enabled - Enables or disables the query
 */
export const useGetConsultant = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterConsultantsResponse>({
    queryKey: [QUERY_CONSULTANT],
    queryFn: getMasterConsultants,
    enabled,
  })

  return {
    consultants: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a consultant
 */
export const useCreateConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterConsultant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONSULTANT] })
    },
  })
}

/**
 * Hook to delete a consultant
 */
export const useDeleteConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterConsultant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONSULTANT] })
    },
  })
}

/**
 * Hook to update a consultant
 */
export const useUpdateConsultant = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterConsultant,
    onMutate: async (updatedData: IUpdateConsultant) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_CONSULTANT] })
      const previousData = queryClient.getQueryData<IGetMasterConsultantsResponse>([QUERY_CONSULTANT])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterConsultantsResponse>([QUERY_CONSULTANT], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, consultant: updatedData.consultant } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_CONSULTANT], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_CONSULTANT] })
    },
  })
}

import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IDirectorPayload {
  director: string
  avatar?: string
  email: string
  phone_number: string
  location: string
}

export interface IUpdateDirector extends IDirectorPayload {
  id: number
}

export interface IDirector {
  id: number
  director: string
  avatar: AvatarItem
  email: string
  phone_number: string
  location: string
}

export interface IGetDirectorResponse {
  data: IDirector[]
  message: string
  success: true
}

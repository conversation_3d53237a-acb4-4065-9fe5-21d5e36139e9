import { useMemo } from 'react'
import useAuthorization from '../redux/authorization/useAuthorization'
import { ILookupProjectToPhase, IStatus } from '../redux/status/interface'
import {
  getUniqueValuesById,
  getUniqueValues,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareDropdownOptionsFromObject,
  sortArrayByKeyWithTypeConversion,
} from '../utils/arrayUtils'
import { getStageStatusByPermission } from '../utils/statusTab/stageStatusByPermission'

export const useOptions = (
  statuses: IStatus[],
  phaseId: number[],
  currentUser: any,
  handleStatusUpdate: any,
  isStatusChanged?: boolean,
) => {
  return useMemo(() => {
    const statusesByPermission = statuses.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )
    const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
      statusesByPermission.map((status) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const phases = populateDropdownOptions(sortedStatusesByPermission, 'LookupProjectToPhase')

    // const phaseOptions = getUniqueValuesById(phases.flat())

    const statusesByPhase = statuses.filter(
      (item: any) =>
        !!item?.LookupProjectToPhase?.find((fi: ILookupProjectToPhase) => phaseId?.find((id) => id === fi.id)),
    )

    const sortedStatusesByPhase = sortArrayByKeyWithTypeConversion(
      statusesByPhase.map((status: any) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const stage_status_option = getUniqueValues(
      prepareDropdownOptionsFromObject(sortedStatusesByPhase, 'MasterProjectStageStatus', 'project_stage_status'),
    )

    if (isStatusChanged) {
      handleStatusUpdate(statusesByPhase)
    }
    const sub_stage = getUniqueValues([
      ...prepareDropdownOptionsFromObject(sortedStatusesByPhase, 'MasterProjectSubStage', 'project_sub_stage'),
    ])

    return {
      phases,
      stage_status: stage_status_option,
      sub_stage,
    }
  }, [statuses, phaseId, currentUser?.role, isStatusChanged])
}

export const getOptionsForRow = (rowData: any, statuses: IStatus[], currentUser: any) => {
  const { phase: selectedPhase, stage_status: selectedStageStatus } = rowData

  const statusesByPermission = statuses.filter((item: IStatus) =>
    getStageStatusByPermission(currentUser.role).includes(item.stage_status),
  )

  const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
    statusesByPermission.map((status) => ({
      ...status,
      project_status_sorting_order: Number(status.project_status_sorting_order),
    })),
    'project_status_sorting_order',
    true,
  )

  const phaseOptions = getUniqueValues(populateDropdownOptions(sortedStatusesByPermission, 'phase'))

  const statusesByPhase = statusesByPermission.filter((status) => status.phase === selectedPhase)

  const sortedStatusesByPhase = sortArrayByKeyWithTypeConversion(
    statusesByPhase.map((status: any) => ({
      ...status,
      project_status_sorting_order: Number(status.project_status_sorting_order),
    })),
    'project_status_sorting_order',
    true,
  )

  const stage_status = getUniqueValues(populateDropdownOptions(sortedStatusesByPhase, 'stage_status'))

  const sub_stage = getUniqueValues(populateDropdownOptions(sortedStatusesByPhase, 'sub_stage'))

  return {
    phaseOptions,
    stage_status,
    sub_stage,
  }
}

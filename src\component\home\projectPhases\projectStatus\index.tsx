import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import { IAddProjectStatusProps } from './interface'
import styles from './ProjectStatus.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateProjectStatuses,
  useDeleteProjectStatuses,
  useGetProjectStatuses,
  useUpdateProjectStatuses,
} from '@/src/hooks/useProjectStatus'
import { IProjectStatus } from '@/src/services/projectStatus/interface'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddProjectStatus: React.FC<IAddProjectStatusProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addProjectStatus } = drawerStates
  //REACT-QUERY
  const { projectStatuses } = useGetProjectStatuses(addProjectStatus)
  // MUTATIONS
  const { mutate: addMasterProjectStatuses } = useCreateProjectStatuses()
  const { mutate: deleteProjectStatuses } = useDeleteProjectStatuses()
  const { mutate: updateProjectStatuses } = useUpdateProjectStatuses()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { projectStatus: '' },
    onSubmit: async (values) => {
      const payload = { project_status: values.projectStatus }
      if (editIndex !== null) {
        updateProjectStatuses(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterProjectStatuses(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteStatus = async (id: number) => {
    deleteProjectStatuses(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const selectedProjectStatus: IProjectStatus | null =
      projectStatuses.find((projectStatus) => projectStatus.id === id) || null
    if (selectedProjectStatus) {
      formik.setValues({
        projectStatus: selectedProjectStatus?.project_status,
      })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_status',
      header: 'Project Statuses',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      align: 'center',
      cell: (row: Record<string, any>) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Project Statuses'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.statusField}
              name="projectStatus"
              labelText={'Project Statuses'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.projectStatus}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectStatus?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.projectStatus?.trim()}
              >
                {'+ Add Project Statuses'}
              </Button>
            )}
          </div>
        </form>
        {projectStatuses?.length >= 1 && (
          // <Table data={projectStatuses} columns={columns} />
          <TanStackTable
            rows={sortData(projectStatuses, 'project_status') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteStatus(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddProjectStatus

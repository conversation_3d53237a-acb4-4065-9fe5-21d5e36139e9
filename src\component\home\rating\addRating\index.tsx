import React, { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import styles from './AddRating.module.scss'
import { IAddRatingProps } from '../interface'
import Button from '@/src/component/shared/button'
import Table from '@/src/component/shared/table'
import { ITableColumn } from '@/src/component/shared/table/interface'
import TextInput<PERSON>ield from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON><PERSON> from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import useRating from '@/src/redux/rating/useRating'

const AddRating: React.FC<IAddRatingProps> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { ratings, getMasterRatingsApi, addMasterRatingsApi, updateMasterRatingsApi, deleteMasterRatingsApi } =
    useRating()
  const { addRating } = drawerStates
  useEffect(() => {
    addRating && getMasterRatingsApi()
  }, [addRating])

  const formik = useFormik({
    initialValues: {
      rating: '',
    },
    onSubmit: async (values) => {
      if (editIndex !== null) {
        const response: Record<string, any> = await updateMasterRatingsApi({
          id: editIndex,
          rating: values.rating,
        })
        if (response.payload.success === true) getMasterRatingsApi()
        setEditIndex(null)
      } else {
        const response: Record<string, any> = await addMasterRatingsApi({
          rating: values.rating,
        })
        if (response.payload.success === true) getMasterRatingsApi()
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    const response: Record<string, any> = await deleteMasterRatingsApi(id)
    if (response.payload.success === true) {
      getMasterRatingsApi()
    }
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = ratings.find((rating) => {
      //TODO
      return rating.id === id
    })

    formik.setValues({ rating: editControlManagers?.rating })
    setEditIndex(id)
  }

  const columns: ITableColumn[] = [
    {
      key: 'rating',
      label: 'Rating',
    },
    {
      key: 'action',
      label: 'Action',
      cellStyle: {
        width: '40px',
      },
      renderCell: (value: unknown, row: Record<string, any>, rowIndex: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
            <DeleteIcon onClick={() => handleDeleteEntity(row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Rating'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} color="secondary" onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {ratings?.length >= 1 && <Table data={ratings} columns={columns} />}
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="rating"
              labelText={'Rating'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.rating}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.rating?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.rating?.trim()}
              >
                {'+ Add Rating'}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddRating

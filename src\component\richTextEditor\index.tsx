import React, { useEffect } from 'react'
// import Blockquote from '@tiptap/extension-blockquote'
// import Bold from '@tiptap/extension-bold'
import { InfoOutlined } from '@mui/icons-material'
import { Box, Tooltip } from '@mui/material'
import { CodeBlock } from '@tiptap/extension-code-block'
import { Color } from '@tiptap/extension-color'
// import Document from '@tiptap/extension-document'
import Link from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
// import Paragraph from '@tiptap/extension-paragraph'
// import Text from '@tiptap/extension-text'
import TextStyle from '@tiptap/extension-text-style'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import styles from './RichTextEditor.module.scss'

interface IRichTextEditorProps {
  labelText?: string
  value: string
  handleChange?: (args: string | null) => void
  isEdit: boolean
  onBlur?: any
  isGrid?: boolean
  className?: any
  isShowDisableBorder?: boolean
}

const RichTextEditor: React.FC<IRichTextEditorProps> = ({
  labelText,
  value,
  handleChange,
  isEdit,
  onBlur,
  isGrid = false,
  className,
  isShowDisableBorder = true,
}) => {
  const CustomTextStyle = TextStyle.extend({
    addAttributes() {
      return {
        style: {
          default: null,
          parseHTML: (element) => element.getAttribute('style'),
          renderHTML: (attributes) => (attributes.style ? { style: attributes.style } : {}),
        },
      }
    },
  })

  const CustomColorExtension = Color.extend({
    addAttributes() {
      return {
        style: {
          default: null, // Allows inline styles for color
          parseHTML: (element: any) => element.getAttribute('style'),
          renderHTML: (attributes: any) => {
            return attributes.style ? { style: attributes.style } : {}
          },
        },
        class: {
          default: null,
          parseHTML: (element: any) => element.getAttribute('class'),
          renderHTML: (attributes: any) => (attributes.class ? { class: attributes.class } : {}),
        },
      }
    },
  })

  const editor = useEditor({
    extensions: [
      // Document,
      // Paragraph,
      // Text,
      // Blockquote,
      StarterKit,
      CustomTextStyle,
      CodeBlock,
      ListItem,
      // Bold,
      Link.configure({
        openOnClick: false, // Prevents opening links when clicking in the editor
      }),
      CustomColorExtension.configure({ types: ['textStyle', 'code'] }),
    ],
    content: value && value,
    editable: isEdit,
    onBlur: onBlur,
    onUpdate: ({ editor }) => {
      const htmlContent = editor.getHTML().trim()
      // Check if content is empty
      const isEmpty = htmlContent === '<p></p>' || htmlContent === '<p><br></p>'
      handleChange && handleChange(isEmpty ? null : htmlContent)
    },
  })

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '')
    }
  }, [value, editor])

  useEffect(() => {
    if (editor) {
      editor.setEditable(isEdit)
    }
  }, [isEdit, editor])

  return (
    <div style={{ height: isGrid ? 'auto' : '85px' }} className={`${isGrid ? styles.gridContainer : ''} ${className}`}>
      {labelText && (
        <div className={styles.labelContainer}>
          {/* <label className={styles.label}>{labelText}</label> */}
          <div>{labelText}</div>
          <div>
            <Tooltip
              title={
                <Box>
                  <span className={styles.tooltipText}>Ctrl + B to make text bold.</span>
                  <br />
                  <span className={styles.tooltipText}>Ctrl + I to italicize text.</span>
                  <br />
                  <span className={styles.tooltipText}>Ctrl + Z to undo </span>
                  <br />
                  <span className={styles.tooltipText}>Ctrl + Y to redo.</span>
                  <br />
                  <span className={styles.tooltipText}>Ctrl + Shift + 8 for a bullet list.</span>
                  <br />
                  <span className={styles.tooltipText}>Ctrl + Shift + 7 for a numbered list.</span>
                </Box>
              }
              arrow
            >
              <InfoOutlined />
            </Tooltip>
          </div>
        </div>
      )}
      <EditorContent
        editor={editor}
        className={`${styles.editorContent} ${isEdit ? styles.active : isShowDisableBorder && styles.disable} ${isGrid ? styles.gridCell : ''}`}
      />
    </div>
  )
}

export default RichTextEditor

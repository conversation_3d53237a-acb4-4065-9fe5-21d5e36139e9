import React, { useMemo } from 'react'
import ClearIcon from '@mui/icons-material/Clear'
import { Avatar, Checkbox } from '@mui/material'
import Autocomplete from '@mui/material/Autocomplete'
import styles from './MultiAutoSelect.module.scss'
import DropDownIcon from '../../svgImages/dropDownIcon'
import TextInputField from '../textInputField'
import { naturalSort } from '@/src/utils/sortingUtils'

// Interface for the options in the autocomplete
export interface IAutoCompleteOption {
  id: string
  name: string
  avatar?: string
  subOptions?: IAutoCompleteOption[]
}

// Props interface for the MultiAutoSelect component
export interface IAutocompleteSwitch {
  isSubOption?: boolean
  options: IAutoCompleteOption[]
  handleSelectedOption: (option: string[]) => void
  value: string[] | number[]
  labelText?: string
  placeholder?: string
  className?: string
  isSx?: boolean
  disabled?: boolean
  clearIcon?: boolean
  isAvatarOption?: boolean
  isMultiLine?: boolean
  customRenderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => React.ReactNode
  onBlur?: () => void
  inputValue?: string
  setInputValue?: (val: string) => void
}

// MultiAutoSelect component
const MultiAutoSelect: React.FC<IAutocompleteSwitch> = ({
  isSubOption = true,
  value: valueParent = [],
  isSx = true,
  labelText,
  placeholder,
  options,
  className,
  disabled,
  handleSelectedOption,
  customRenderOption,
  clearIcon,
  isAvatarOption = false,
  isMultiLine,
  onBlur,
  inputValue,
  setInputValue,
}) => {
  const val = (valueParent || [])?.map((item: any) => item) as string[]
  const handleChange = (id: string) => {
    const isSelected = val?.includes(id)
    const selectAllId = 'Select All'

    // Recursive function to get all sub-option IDs
    const getSubOptionIds = (optionId: string, options: IAutoCompleteOption[]): string[] => {
      const option = options?.find((option) => option.id === optionId)
      if (!option || !option.subOptions) return []
      return option?.subOptions?.flatMap((sub) => [sub?.id, ...getSubOptionIds(sub?.id, options)])
    }

    // Calculate all option IDs, including sub-options
    const allOptionIds = options.flatMap((option) => [option?.id, ...getSubOptionIds(option?.id, options)])

    let updatedValues: string[] = []

    if (id === selectAllId) {
      // Handle 'Select All' selection
      updatedValues =
        val?.length === allOptionIds.length
          ? [] // Deselect all
          : allOptionIds // Select all
    } else {
      // Handle individual selection
      updatedValues = isSelected
        ? val?.filter((v) => v !== id) // Deselect
        : [
            ...val,
            id,
            ...getSubOptionIds(id, options), // Select the id and its sub-options
          ]

      // Check if 'Select All' needs to be deselected
      if (val?.includes(selectAllId)) {
        if (updatedValues.length !== allOptionIds.length) {
          updatedValues = updatedValues.filter((v) => v !== selectAllId)
        }
      }
    }

    handleSelectedOption(updatedValues)
  }

  const handleClear = () => {
    handleSelectedOption([])
  }

  // Helper function to get option name by ID
  const getOptionNameById = (id: string): string => {
    // Search through options and their suboptions to find the matching ID
    for (const option of options) {
      if (option.id === id) return option.name

      if (option.subOptions) {
        for (const subOption of option.subOptions) {
          if (subOption.id === id) return subOption.name
        }
      }
    }
    return id // Return the ID if no matching option is found
  }

  const getSelectedOptions = () => {
    return (
      options?.filter(
        (option) =>
          val?.includes(option?.id) ||
          (option.subOptions && option?.subOptions?.some((sub) => val?.includes(sub?.id?.trim()))),
      ) || []
    )
  }

  const flattenOptions = (options: IAutoCompleteOption[]): IAutoCompleteOption[] => {
    return options?.flatMap((option) => {
      return [
        { id: option?.id, name: option?.name?.trim(), avatar: option?.avatar },
        ...(option.subOptions || []).map((sub) => ({ id: sub?.id?.trim(), name: sub?.name?.trim() })),
      ]
    })
  }

  const renderSelectedValues = () => {
    // Get option names from IDs
    const selectedNames = val?.map((id) => getOptionNameById(id))
    return selectedNames.join(', ')
  }

  const renderSubOptions = (subOptions: IAutoCompleteOption[]) => {
    return (
      <div className={styles.subOptions}>
        {subOptions?.map((subOption) => {
          return (
            subOption?.id && (
              <li
                key={subOption?.id}
                onClick={(e) => {
                  e.stopPropagation() // Prevent click event from bubbling up to the parent
                  handleChange(subOption?.id)
                }}
              >
                <Checkbox checked={val?.includes(subOption?.id)} classes={{ root: styles.checkBox }} />
                {subOption?.name}
              </li>
            )
          )
        })}
      </div>
    )
  }

  const defaultRenderOption = (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => (
    <li {...props} onClick={() => handleChange(option.id)} className={styles.optionLi}>
      <div className={styles.options}>
        <Checkbox checked={val?.includes(option.id)} classes={{ root: styles.checkBox }} />
        {isAvatarOption && (
          <Avatar src={option?.avatar} sx={{ width: 24, height: 24, marginRight: 2, marginLeft: 2 }} />
        )}
        <div className={styles.mainOptions}>
          <div className={styles.optionName}>{option.name}</div>
        </div>
      </div>
      {option?.subOptions && renderSubOptions(option.subOptions)}
    </li>
  )

  const selectedOptions = getSelectedOptions()
  const value = flattenOptions(selectedOptions)

  const sortedOptions = useMemo(() => {
    return options?.sort((a, b) => {
      return naturalSort(a?.name, b?.name)
    })
  }, [options])

  return (
    <div className={styles.root}>
      {labelText && <div className={styles.labelText}>{labelText}</div>}
      <Autocomplete
        multiple
        disabled={disabled}
        size="small"
        id="checkboxes-tags-demo"
        options={sortedOptions}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue && setInputValue(newInputValue)
        }}
        value={isSubOption ? value : (val as unknown as IAutoCompleteOption[])}
        disableCloseOnSelect
        clearIcon={
          clearIcon && val.length > 0 ? <ClearIcon onClick={handleClear} className={styles.clearIcon} /> : null
        }
        getOptionLabel={(option) => option.name}
        renderOption={customRenderOption ?? defaultRenderOption}
        popupIcon={<DropDownIcon className={styles.downArrow} />}
        renderTags={() => (
          <div className={`${isMultiLine ? styles.isMultiLine : ''} ${styles.inputText}`}>{renderSelectedValues()}</div>
        )}
        onBlur={onBlur}
        isOptionEqualToValue={(option, valueParent) => option.id === valueParent?.id}
        classes={{
          input: styles.tagInput,
          popper: styles.popper,
          paper: styles.paper,
          listbox: styles.listBox,
          popupIndicator: styles.popupIndicator,
        }}
        renderInput={(params) => (
          <TextInputField
            variant="outlined"
            {...params}
            className={className}
            placeholder={placeholder}
            sx={
              isSx
                ? {
                    '& .Mui-focused': {
                      backgroundColor: '#f0f8ff !important',
                      borderBottom: '1px solid #2333C2BF',
                      '& .MuiOutlinedInput-input': {
                        backgroundColor: '#f0f8ff',
                        color: 'black !important',
                      },
                    },
                  }
                : undefined
            }
          />
        )}
      />
    </div>
  )
}

export default MultiAutoSelect

import { removeCommasAndConvert } from '@/src/utils/numberUtils'

export const getValue = (
  formik: any,
  summaryEdit: boolean,
  sustainability: any,
  field: string,
  formatted: boolean = false,
  isPercentage: boolean = false,
  withOutCommas: boolean = false,
) => {
  const value = formik?.values[field]
  const sustainabilityValue = sustainability ? sustainability[field] : ''

  if (summaryEdit && withOutCommas) {
    const convertValue = value ? removeCommasAndConvert(value) : ''
    return convertValue
  }

  if (!summaryEdit && !sustainabilityValue) {
    return ''
  }

  if (formatted && value) {
    return (value ? `${value}${isPercentage ? '%' : ''}` : '') || ''
  }

  return value?.toString() ? value : ''
}

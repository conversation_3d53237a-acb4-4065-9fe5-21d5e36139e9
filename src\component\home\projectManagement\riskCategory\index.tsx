import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './RiskCategory.module.scss'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import { showCustomToast } from '../../../toast/ToastManager'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateRiskCategory,
  useDeleteRiskCategory,
  useGetRiskCategory,
  useUpdateRiskCategory,
} from '@/src/hooks/useRiskCategory'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddRiskCategory: React.FC<any> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { riskCategory } = drawerStates
  //REACT-QUERY
  const { riskCategories, isLoading: isFetching } = useGetRiskCategory(riskCategory)
  // MUTATIONS
  const { mutate: addMasterRiskCategory, isPending: isAdding } = useCreateRiskCategory()
  const { mutate: deleteRiskCategory } = useDeleteRiskCategory()
  const { mutate: updateRiskCategory, isPending: isUpdating } = useUpdateRiskCategory()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { riskCategory: '' },
    onSubmit: async (values) => {
      const payload = { risk_category: values.riskCategory }
      if (editIndex !== null) {
        updateRiskCategory(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterRiskCategory(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteRiskCategory(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editRiskCategory: any = riskCategories.find((riskCategory) => riskCategory.id === id)

    formik.setValues({
      riskCategory: editRiskCategory?.risk_category,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'risk_category',
      header: 'Risk Category',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Risk Category'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="riskCategory"
              labelText={'Risk Category'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.riskCategory}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskCategory?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.riskCategory?.trim()}
              >
                {'+ Add Risk Category'}
              </Button>
            )}
          </div>
        </form>
        {riskCategories?.length >= 1 && (
          // <Table data={riskCategories} columns={columns} />
          <TanStackTable rows={sortData(riskCategories, 'risk_category') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddRiskCategory

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createReasonForDelay,
  deleteReasonForDelay,
  getReasonForDelays,
  updateReasonForDelay,
} from '../services/reasonForDelay'
import { IGetReasonForDelayResponse, IUpdateReasonForDelay } from '../services/reasonForDelay/interface'

export const QUERY_REASON_FOR_DELAY = 'reason-for-delay'

/**
 * Hook to fetch ReasonForDelay
 * @param enabled - Enables or disables the query
 */
export const useGetReasonForDelay = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetReasonForDelayResponse>({
    queryKey: [QUERY_REASON_FOR_DELAY],
    queryFn: getReasonForDelays,
    enabled,
  })

  return {
    reasonForDelays: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a ReasonForDelay
 */
export const useCreateReasonForDelay = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createReasonForDelay,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_REASON_FOR_DELAY] })
    },
  })
}

/**
 * Hook to delete a ReasonForDelay
 */
export const useDeleteReasonForDelay = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteReasonForDelay,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_REASON_FOR_DELAY] })
    },
  })
}

/**
 * Hook to update a ReasonForDelay
 */
export const useUpdateReasonForDelay = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateReasonForDelay,
    onMutate: async (updatedData: IUpdateReasonForDelay) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_REASON_FOR_DELAY] })
      const previousData = queryClient.getQueryData<IGetReasonForDelayResponse>([QUERY_REASON_FOR_DELAY])
      // Optimistically update the cache
      queryClient.setQueryData<IGetReasonForDelayResponse>([QUERY_REASON_FOR_DELAY], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id
              ? {
                  ...manager,
                  reason_for_delay: updatedData.reason_for_delay,
                  reason_for_delay_code: updatedData.reason_for_delay_code,
                }
              : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_REASON_FOR_DELAY], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_REASON_FOR_DELAY] })
    },
  })
}

import React, { useMemo, useRef, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { Box } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './SpaTable.module.scss'
import { ISpaTableProps } from '../interface'
import LetterReferenceCell from '../letterRefCell'
import { preparePayload, filterByProjectName, getSpaData } from '../utils'
import DragCell from '@/src/component/customCells/dragCell'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { dateSortingFn } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILetterRefAttach } from '@/src/redux/spaMileston/interface'
import useSpa from '@/src/redux/spaMileston/useSpaMileston'
import { getMasterOneProject } from '@/src/services/projects'
import { dateNonRequiredFields, sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'
import { DDMMYYYYformate } from '@/src/utils/dateUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const SpaTable: React.FC<ISpaTableProps> = ({
  isEditTable,
  setIsOpenDrawer,
  setEditSpa,
  setDeleteModel,
  setSpaData,
  spaData,
  group,
  expanded,
  gridFilters,
  setGridFilters,
}) => {
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { getSpaApi, updateSpaApi, updatePDFSpaApi, sortSpaApi } = useSpa()
  const router = useRouter()

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const onCellUpdate = async (cell: any, newValue: any, row: any): Promise<boolean> => {
    if (!row) return false
    try {
      const createFormData = (payload: Record<string, any>, cell: any) => {
        const formData = new FormData()
        Object.entries(payload).forEach(([key, value]) => {
          if (value != null || dateNonRequiredFields(key, cell?.columnId?.toString())) formData.append(key, value)
        })
        return formData
      }

      const payload = preparePayload(row, cell, newValue)
      const { period, phase, project_name, id } = row || {}
      const updatedPayload = {
        ...payload,
        period: currentPeriod,
        phase: phase,
        project_name: router.query.slug?.toString() as string,
      }
      const response: Record<string, any> = await updateSpaApi({
        id: id,
        data: createFormData(updatedPayload, cell),
      })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || 'Failed to update cell')
        return false
      }
      const res: Record<string, any> = await getSpaApi({
        period: currentPeriod,
        project_name: router.query.slug as string,
      })

      if (!res?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || `Failed to fetch updated data`)
        return false
      }
      const formateData = getSpaData(res.payload.data)
      setSpaData(filterByProjectName(formateData, router.query.slug as string) as any)
      return true
    } catch (error) {
      errorToast('Failed...')
      return false
    }
  }

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    row: any,
    onStart?: () => void,
    onFinish?: () => void,
  ) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }
    const file = event.target.files?.[0]
    if (!file) return

    try {
      onStart?.() // Start loader

      const formData = new FormData()
      const { LookupProjectToPhase, id } = row?.original || {}

      formData.append('period', currentPeriod)
      formData.append('lookup_project_to_phase_id', LookupProjectToPhase?.id || null)
      formData.append('project_name', router.query.slug as string)
      formData.append('letter_reference_attachment', file)

      const response: Record<string, any> = await updatePDFSpaApi({ id, data: formData })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || 'Upload failed')
        return true
      }

      const res: Record<string, any> = await getSpaApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })

      if (!res?.payload?.success) {
        errorToast(res?.payload?.response?.data?.message || 'Failed to fetch data')
        return true
      }

      successToast('Letter Reference Attachment Successfully Uploaded')
    } catch (error) {
      console.error('Error uploading file:', error)
      errorToast('An unexpected error occurred')
      return true
    } finally {
      onFinish?.() // Stop loader
    }

    return true
  }

  const handleDownload = (letter_reference_attachment: ILetterRefAttach[]) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }
    if (!letter_reference_attachment) {
      toast('No attachment found')
      return
    }
    const a = document.createElement('a')
    a.href = letter_reference_attachment[0]?.url
    a.download = letter_reference_attachment[0]?.name // This may not work for all URLs (e.g., if the server doesn't allow it)
    a.click()
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'center',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => (
        <>
          {isEditForUser ? (
            <div className={styles.actions}>
              <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
              <EditIcon
                className={styles.deleteRowIcon}
                onClick={() => {
                  setIsOpenDrawer(true)
                  setEditSpa(row.original.id)
                }}
              />
            </div>
          ) : (
            <></>
          )}
        </>
      ),
      size: 70,
    },
    { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
    {
      accessorKey: 'phase',
      header: 'Phase',
      size: 150,
      cell: ({ row }) => {
        const val = row.original?.LookupProjectToPhase?.phase || '_'
        return <span>{val || ' '}</span>
      },
    },
    {
      accessorKey: 'milestone_number',
      header: 'Milestone Number',
      size: 170,
      cell: ({ row }) => {
        const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
        return <span>{val}</span>
      },
    },
    {
      accessorKey: 'milestone_description',
      header: 'Milestone Description',
      size: 450,
      flex: 1,
      cell: ({ row }) => {
        const val = row.original.milestone_description
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
    },
  ]

  const governmentColumns: CustomColumnDef<any>[] = [
    ...columns,
    {
      accessorKey: 'duration_from_start',
      header: 'Duration from Start (Days)',
      size: 220,
    },
    {
      accessorKey: 'planned_completion_date',
      header: 'Planned Completion Date',
      size: 210,
      filterType: 'date',
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      tableId: 'SPAGovernmentTable',
    },
    {
      accessorKey: 'remaining_duration',
      header: 'Remaining in Days',
      size: 170,
    },
    {
      accessorKey: 'actual_forecast_milestone_completion',
      header: 'Forecasted / Actual Completion Date',
      size: 285,
      isSaveConfirmationRequired: true,
      isEditableCell: isEditTable,
      editableType: 'date',
      filterType: 'date',
      onEditCell: (cell, newValue, row) => {
        onCellUpdate(cell, newValue, row)
      },
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
    },
    {
      accessorKey: 'variance_in_days',
      header: 'Variance (In Days)',
      size: 170,
      tableId: 'SPAGovernmentTable',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      size: 100,
    },
    {
      accessorKey: 'delay_damage_per_diem',
      header: 'Delay Damage per diem (AED)',
      size: 240,
    },
    {
      accessorKey: 'conditions_precedence',
      header: 'Conditions Precedence',
      size: 350,
      cell: ({ row }) => {
        const val = row.original.conditions_precedence
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
    },
    {
      accessorKey: 'remarks',
      header: 'Remarks',
      size: 300,
      cell: ({ row }) => {
        const val = row.original.remarks
        return (
          <TypographyField
            style={{ fontSize: '12px', color: 'black', marginLeft: '5px' }}
            variant={'body1'}
            text={val || ' '}
          />
        )
      },
    },
  ]

  const nonGovernmentColumnsA: CustomColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'dragCol',
        header: '',
        cell: ({ row }) => <DragCell rowId={row?.id} />,
        size: 40,
        align: 'center',
      },
      {
        accessorKey: 'actionCol',
        header: 'Action',
        cell: ({ row }) => (
          <>
            {isEditForUser ? (
              <div className={styles.actions}>
                <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
                <EditIcon
                  className={styles.deleteRowIcon}
                  onClick={() => {
                    setIsOpenDrawer(true)
                    setEditSpa(row.original.id)
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </>
        ),
        size: 70,
      },
      { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
      {
        accessorKey: 'phase',
        header: 'Phase',
        size: 150,
        cell: ({ row }) => {
          const val = row.original?.LookupProjectToPhase?.phase || '-'
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_number',
        header: 'Milestone Number',
        size: 170,
        cell: ({ row }) => {
          const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_description',
        header: 'Milestone Description',
        size: 450,
        cell: ({ row }) => {
          const val = row.original.milestone_description
          return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
        },
      },
      {
        accessorKey: 'milestone_type',
        header: 'Milestone Type',
        size: 200,
      },
      {
        accessorKey: 'milestone_date',
        header: 'SPA Date',
        size: 120,
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => {
          onCellUpdate(cell, newValue, row)
        },
        //TODO: Fix date format issue
        // cell: ({ row }) => {
        //   const val = DDMMYYYYformate(row.original.milestone_date)
        //   return <TypographyField style={{ fontSize: '12px', color: 'black' }} variant={'body1'} text={val || ' '} />
        // },
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'revised_spa_communication',
        header: 'Revised SPA Communication',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 230,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'actual_forecast_milestone_collection',
        header: 'Actual / Forecast Milestone Collection',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 280,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'latest_communication_date',
        header: 'Latest Communication Date',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'communication_variance',
        header: 'Comm Var',
        tableId: 'SPAGroupATable',
      },
      {
        accessorKey: 'spa_variance',
        header: 'SPA Variance',
        size: 150,
        tableId: 'SPAGroupATable',
      },
      {
        accessorKey: 'milestone_status',
        header: 'Milestone Status',
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4
        //   return (
        //     <TypographyField
        //       style={{ fontSize: '12px', color: 'black', marginLeft: '3px' }}
        //       variant={'body1'}
        //       text={val || ' '}
        //     />
        //   )
        // },
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4?.toLocaleString('en-IN')
        //   return (
        //     <TypographyField
        //       sx={'body1'}
        //       style={{ fontSize: '12px', color: 'black' }}
        //       text={val ? val.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ' '}
        //     />
        //   )
        // },
        flex: 1,
      },
      {
        accessorKey: 'letter_reference',
        header: 'Letter Reference',
        size: 300,
        cell: ({ row }) => (
          <LetterReferenceCell
            value={row.original.letter_reference}
            attachments={row.original.letter_reference_attachment}
            onUpload={(event: any, onStart: any, onFinish: any) => handleFileUpload(event, row, onStart, onFinish)}
            onDownload={() => handleDownload(row.original.letter_reference_attachment)}
            isEditForUser={isEditForUser}
          />
        ),
      },
    ],
    [isEditTable],
  ) // Dependencies to re-compute when changed

  const nonGovernmentColumnsB: CustomColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'dragCol',
        header: '',
        cell: ({ row }) => <DragCell rowId={row?.id} />,
        size: 40,
        align: 'center',
      },
      {
        accessorKey: 'actionCol',
        header: 'Action',
        cell: ({ row }) => (
          <>
            {isEditForUser ? (
              <div className={styles.actions}>
                <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
                <EditIcon
                  className={styles.deleteRowIcon}
                  onClick={() => {
                    setIsOpenDrawer(true)
                    setEditSpa(row.original.id)
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </>
        ),
        size: 70,
      },
      { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
      {
        accessorKey: 'phase',
        header: 'Phase',
        size: 150,
        cell: ({ row }) => {
          const val = row.original?.LookupProjectToPhase?.phase || '-'
          return <span>{val || ' '}</span>
        },
      },
      {
        accessorKey: 'milestone_number',
        header: 'Milestone Number',
        size: 170,
        cell: ({ row }) => {
          const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_description',
        header: 'Milestone Description',
        size: 450,
        cell: ({ row }) => {
          const val = row.original.milestone_description
          return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
        },
      },
      {
        accessorKey: 'baseline_end_date',
        header: 'Contract Date',
        flex: 1,
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        cell: ({ row }) => {
          const val = DDMMYYYYformate(row.original.baseline_end_date)
          return <TypographyField style={{ fontSize: '12px', color: 'black' }} variant={'body1'} text={val || ' '} />
        },
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'eot',
        header: 'EOT',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
        size: 120,
      },
      {
        accessorKey: 'forecasted_end_date',
        header: 'Actual / Forecast Milestone Completion',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'latest_communication_date',
        header: 'Latest Communication Date',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'communication_variance',
        header: 'Comm Var',
        tableId: 'SPAGroupBTable',
      },
      {
        accessorKey: 'contract_variance',
        header: 'SPA Variance',
        size: 150,
        tableId: 'SPAGroupBTable',
      },
      {
        accessorKey: 'milestone_status',
        header: 'Milestone Status',
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4?.toLocaleString('en-IN')
        //   return (
        //     <TypographyField
        //       sx={'body1'}
        //       style={{ fontSize: '12px' }}
        //       text={val ? val.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ' '}
        //     />
        //   )
        // },
        flex: 1,
      },
      {
        accessorKey: 'letter_reference',
        header: 'Letter Reference',
        size: 300,
        cell: ({ row }) => (
          <LetterReferenceCell
            value={row.original.letter_reference}
            attachments={row.original.letter_reference_attachment}
            onUpload={(event: any, onStart: any, onFinish: any) => handleFileUpload(event, row, onStart, onFinish)}
            onDownload={() => handleDownload(row.original.letter_reference_attachment)}
            isEditForUser={isEditForUser}
          />
        ),
      },
    ],
    [isEditTable],
  ) // Dependencies to re-compute when changed

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    // const dragItem = spas.find((item: any) => item.id == dragId)
    // const dropItem = spas.find((item: any) => item.id == dropId)

    const newPayload = data?.map((item: any, index: number) => ({
      id: item?.id,
      milestone_sorting_order: 1 + index,
    }))

    const res: any = await sortSpaApi({ period: currentPeriod, SPARecords: newPayload })

    if (res?.payload.success) {
      await getSpaApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
    }
  }
  return (
    <>
      <div className={`${styles.spaTableContainer} ${!expanded && styles.expandTable}`}>
        {project?.MasterEntityCategory?.entity_category === 'Government' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={governmentColumns}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
          />
        )}
        {project?.MasterEntityCategory?.entity_category !== 'Government' && group === 'A' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={nonGovernmentColumnsA}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
          />
        )}
        {project?.MasterEntityCategory?.entity_category !== 'Government' && group === 'B' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={nonGovernmentColumnsB}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
          />
        )}
      </div>
    </>
  )
}

export default SpaTable

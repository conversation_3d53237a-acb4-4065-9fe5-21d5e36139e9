import { useDispatch, useSelector } from 'react-redux'
import { addLastUpdateOfProgress } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useLastUpdated = () => {
  const dispatch: AppDispatch = useDispatch()

  const lastUpdateOfProgress = useSelector((state: RootState) => state.lastUpdated.lastUpdateOfProgress)

  const addLastUpdateOfProgressReducer = (data: any) => dispatch(addLastUpdateOfProgress(data))

  return { lastUpdateOfProgress, addLastUpdateOfProgressReducer }
}
export default useLastUpdated

import React, { ReactElement, useState } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { TextFieldProps } from '@mui/material'
import { LocalizationProvider, PickersActionBarProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
import ConfirmModel from './confirmModel'
import styles from './PeriodDatepicker.module.scss'
import Button from '@/src/component/shared/button'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import { DARK_200 } from '@/src/constant/color'
import { formateParamsPeriod } from '@/src/utils/dateUtils'

interface MuiDatePickerProps {
  labelText?: string
  value?: string | number | Date | null
  onChange?: (date: any) => void
  className?: any
  minDate?: string | number | Date | null
  maxDate?: string | number | Date
  error?: boolean
  helperText?: string
  disableEditable?: boolean
  onBlur?: any
  placeholder?: string
  onError?: (reason?: string | null) => void
  shouldDisableDate?: any
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: any
  actionButtons?: any
  CustomDay?: any
  shouldCloseOnSelect?: boolean
}

const PeriodDatePicker: React.FC<MuiDatePickerProps> = ({
  labelText,
  value,
  onChange = () => null,
  className,
  minDate,
  maxDate,
  error = false,
  helperText,
  disableEditable = false,
  onBlur,
  placeholder = '',
  onError = () => null,
  shouldDisableDate,
  actionButtons = false,
  disabled = false,
  required = false,
  name = '',
  sx,
  CustomDay,
  ...props
}): ReactElement => {
  const [isOpen, setIsOpen] = useState(false)
  const [date, setDate] = useState()

  const CustomTextInput = (params: TextFieldProps) => {
    return (
      <TextInputField
        {...params}
        id="textfield"
        name={name}
        variant="outlined"
        autoComplete="off"
        labelText={labelText}
        className={className}
        error={error}
        helperText={helperText}
        onBlur={onBlur}
        required={required}
        inputProps={{ ...params.inputProps, placeholder: placeholder }}
        sx={sx}
      />
    )
  }

  const CustomActionBar = (props: PickersActionBarProps) => {
    const { onAccept, onClear, onCancel, onSetToday, actions, className } = props
    const [idModel, setIsModel] = useState(false)
    return (
      <div className={styles.buttonActions} style={{ display: 'flex', gap: '10px' }}>
        <Button color="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={() => setIsModel(true)} disabled={date ? false : true}>
          Add
        </Button>
        <ConfirmModel
          open={idModel}
          onClose={() => setIsModel(false)}
          date={date ? formateParamsPeriod(date) : ''}
          onConfirmClose={() => {
            setIsModel(false)
            onClear()
          }}
          onConfirmOpen={() => onAccept()}
        />
      </div>
    )
  }

  return (
    <div style={{ width: '100%' }}>
      {labelText && (
        <div>
          <TypographyField style={{ color: DARK_200 }} variant={`caption`} text={`${labelText}`} />
        </div>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          {...props}
          open={isOpen}
          onClose={() => setIsOpen(false)}
          format="dd/MM/yyyy"
          closeOnSelect={false}
          // value={dateValue ? dateValue : null}
          disabled={disabled}
          onChange={(val) => {
            setDate(val as any)
            onChange(val)
          }}
          slots={{
            actionBar: CustomActionBar,
            openPickerIcon: () => <CalendarIcon />,
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay && CustomDay,
          }}
          slotProps={{
            popper: {
              sx: { zIndex: '99999999' },
            },
            layout: {
              sx: { display: 'block', textAlign: 'end' },
            },
            textField: {
              sx: sx,
              disabled: true,
              onClick: () => setIsOpen(true),
            },
          }}
          views={['month', 'year', 'day']}
          minDate={minDate}
          maxDate={maxDate}
          shouldDisableDate={shouldDisableDate ? shouldDisableDate : false}
          onError={(reason) => {
            if (reason) {
              onError('InValid Value')
            } else {
              onError(null)
            }
          }}
        />
      </LocalizationProvider>
    </div>
  )
}

export default PeriodDatePicker

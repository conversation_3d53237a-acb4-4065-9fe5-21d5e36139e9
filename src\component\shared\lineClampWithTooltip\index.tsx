import React from 'react'
import { Typography } from '@mui/material'
import { styled } from '@mui/material/styles'
import CustomTooltip from '../tooltip'

type LineClampWithTooltipProps = {
  lineNumber?: number
  children: React.ReactNode
}

const EllipsisTypography = styled(Typography)<{ maxLines: number }>(({ maxLines }) => ({
  display: '-webkit-box',
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  WebkitLineClamp: maxLines,
  whiteSpace: maxLines === 1 ? 'nowrap' : 'initial',
  cursor: 'pointer',
}))

const LineClampWithTooltip: React.FC<LineClampWithTooltipProps> = ({ lineNumber = 1, children }) => {
  return (
    <CustomTooltip
      title={children}
      placement="top-start"
      sx={{
        '& .MuiTooltip-tooltip': {
          maxWidth: '600px',
        },
      }}
      enterDelay={700}
    >
      <EllipsisTypography maxLines={lineNumber}>{children}</EllipsisTypography>
    </CustomTooltip>
  )
}

export default LineClampWithTooltip

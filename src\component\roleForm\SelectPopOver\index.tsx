import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './SelectPopOver.module.scss'
import Button from '../../shared/button'
import CloseCircleIcon from '../../svgImages/closeCircleIcon'

interface ModalProps {
  open: boolean
  onClose: () => void
  onSubmit: () => void
  title?: string
  children: React.ReactNode // Adjusted type for children
}

const ModelPopOver: React.FC<ModalProps> = ({ open, children, onClose, onSubmit, title }) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    width: '900px',
    maxHeight: '820px', // Fixed maximum height
    height: '100%',
    transform: 'translate(-50%, -50%)',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    px: '20px',
    zIndex: 1,
    // overflowY: 'hidden',
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.closeIcon}>
          <div className={styles.title}>{title}</div>
          <CloseCircleIcon className={styles.icon} onClick={onClose} />
        </div>
        <div className={styles.confirmContent}>
          <div>{children}</div>
        </div>
        <div className={styles.submitIcon}>
          <Button onClick={onSubmit}>Submit</Button>
        </div>
      </Box>
    </Modal>
  )
}

export default ModelPopOver

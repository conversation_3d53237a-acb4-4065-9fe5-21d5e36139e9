import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterProjectStageStatus,
  deleteMasterProjectStageStatus,
  getMasterProjectStageStatus,
  updateMasterProjectStageStatus,
} from '../services/projectStageStatus'
import { IGetProjectStageStatusResponse, IUpdateProjectStageStatus } from '../services/projectStageStatus/interface'

export const QUERY_PROJECT_STAGE_STATUS = 'project-stage-status'

/**
 * Hook to fetch Project Stage Statuses
 * @param enabled - Enables or disables the query
 */
export const useGetProjectStageStatuses = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetProjectStageStatusResponse>({
    queryKey: [QUERY_PROJECT_STAGE_STATUS],
    queryFn: getMasterProjectStageStatus,
    enabled,
  })

  return {
    projectStageStatuses: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Project Stage Statuses
 *  */
export const useCreateProjectStageStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterProjectStageStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STAGE_STATUS] })
    },
  })
}

/**
 * Hook to delete a Project Stage Statuses
 * */
export const useDeleteProjectStageStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterProjectStageStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STAGE_STATUS] })
    },
  })
}

/**
 * Hook to update a Project Stage Statuses
 * */
export const useUpdateProjectStageStatuses = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterProjectStageStatus,
    onMutate: async (updatedData: IUpdateProjectStageStatus) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PROJECT_STAGE_STATUS] })
      const previousData = queryClient.getQueryData<IGetProjectStageStatusResponse>([QUERY_PROJECT_STAGE_STATUS])
      // Optimistically update the cache
      queryClient.setQueryData<IGetProjectStageStatusResponse>([QUERY_PROJECT_STAGE_STATUS], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((project) =>
            project.id === updatedData.id
              ? { ...project, project_stage_status: updatedData.project_stage_status }
              : project,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PROJECT_STAGE_STATUS], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PROJECT_STAGE_STATUS] })
    },
  })
}

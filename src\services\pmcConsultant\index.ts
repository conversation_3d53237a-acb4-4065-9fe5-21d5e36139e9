import { IPmcConsultantPayload, IUpdatePmcConsultant } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PMC_CONSULTANT_URL = `/master-pmc-consultant`

// Get Master Pmc Consultant data
export const getMasterPmcConsultant = async () => {
  const response = await api.get(`${MASTER_PMC_CONSULTANT_URL}`)
  return response?.data
}

export const deleteMasterPmcConsultant = async (data: number) => {
  const response = await api.delete(`${MASTER_PMC_CONSULTANT_URL}/${data}`)
  return response?.data
}

// Create Master PmcConsultant entry
export const createMasterPmcConsultant = async (data: IPmcConsultantPayload) => {
  const response = await api.post(`${MASTER_PMC_CONSULTANT_URL}`, data)
  return response
}

// Update Master PmcConsultant entry
export const updateMasterPmcConsultant = async (data: IUpdatePmcConsultant): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PMC_CONSULTANT_URL}/${data.id}`, { ...rest })
  return response
}

import React, { ReactElement, useState } from 'react'
import { InfoOutlined, VisibilityOffOutlined, VisibilityOutlined } from '@mui/icons-material'
import { InputAdornment } from '@mui/material'
import TextField, { OutlinedTextFieldProps } from '@mui/material/TextField'
import { TooltipProps } from '@mui/material/Tooltip'
import styles from './TextInputField.module.scss'

interface TextFieldProps extends OutlinedTextFieldProps {
  labelText?: string
  required?: boolean
  infoIcon?: boolean
  type?: string
  toolTipText?: string
  toolTipPlacement?: TooltipProps['placement']
  className?: string
  error?: boolean
  halperText?: string
  maxLength?: number
}

const TextInputField: React.FC<TextFieldProps> = ({
  labelText = '',
  required,
  infoIcon,
  toolTipText,
  toolTipPlacement,
  type = 'text',
  className = '',
  ...props
}): ReactElement => {
  const [passwordVisible, setPasswordVisible] = useState(false)

  const otherProps =
    type === 'password'
      ? {
          InputProps: {
            endAdornment: (
              <InputAdornment position="start" className={styles.endAdornment}>
                {passwordVisible ? (
                  <VisibilityOffOutlined className={styles.visibleIcon} onClick={() => setPasswordVisible(false)} />
                ) : (
                  <VisibilityOutlined className={styles.visibleIcon} onClick={() => setPasswordVisible(true)} />
                )}
              </InputAdornment>
            ),
          },
        }
      : {}

  return (
    <div className={className} style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <label className={labelText.length ? styles.label : ''}>{labelText}</label>
      <TextField
        {...props}
        autoComplete="off"
        type={type === 'password' ? (passwordVisible ? 'text' : 'password') : type}
        // {...otherProps}
      />
    </div>
  )
}

export default TextInputField

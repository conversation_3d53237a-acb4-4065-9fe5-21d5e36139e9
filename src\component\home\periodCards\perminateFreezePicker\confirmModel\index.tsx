import React from 'react'
import { Modal, Box } from '@mui/material'
import styles from './ConfirmModel.module.scss'
import Button from '@/src/component/shared/button'
import TypographyField from '@/src/component/shared/typography'
import AddRoundedIcon from '@/src/component/svgImages/addRounded'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { payloadDateFormate } from '@/src/utils/dateUtils'

interface ModalProps {
  open: boolean
  date?: string
  onClose: () => void
  onConfirmOpen: () => void
  onConfirmClose: () => void
}

const ConfirmModel: React.FC<ModalProps> = ({ open, date, onClose, onConfirmClose, onConfirmOpen }) => {
  const { addMasterPeriodsApi, getMasterPeriodsApi } = useMasterPeriod()

  const style = {
    position: 'relative' as 'relative',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '440px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    // boxShadow: 24,
    pt: '20px',
    px: '20px',
    pb: '10px',
    zIndex: '9999999990999',
    // position: "relative",
    // top: "341px",
  }

  const handleAddPeriod = async () => {
    if (!date) return
    const response: Record<string, any> = await addMasterPeriodsApi({
      period: payloadDateFormate(date) as any,
    })
    if (response.payload.success) {
      onConfirmOpen()
      onClose()
      getMasterPeriodsApi()
    }
  }

  return (
    <Modal
      className={styles.model}
      open={open}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box sx={style}>
        <div className={styles.confirmButtonContent}>
          <div className={styles.header}>
            <div>Add New Period</div>
            <AddRoundedIcon fill="#444444" />
          </div>
          <div className={styles.confirmContent}>
            Are you sure you want to add a new period? Please note the action is irreversible.
          </div>
          <TypographyField variant="bodyBold" text={date ? date : ''} />

          <div className={styles.reassignButtons}>
            <Button onClick={() => handleAddPeriod()}>Yes</Button>
            <Button
              color="secondary"
              onClick={() => {
                onConfirmClose
                onClose()
              }}
            >
              No
            </Button>
          </div>
        </div>
      </Box>
    </Modal>
  )
}

export default ConfirmModel

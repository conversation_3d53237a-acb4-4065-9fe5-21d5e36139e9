import React, { useMemo, useEffect, useState } from 'react'
import Image from 'next/image'
import { io, Socket } from 'socket.io-client'
import { toast } from 'sonner'
import ConfirmClonePopOver from './ConfirmClonePopOver'
import styles from './HomeHeader.module.scss'
import Button from '../../shared/button'
import Typography<PERSON>ield from '../../shared/typography'
import CalendarIcon from '../../svgImages/calenderIcon'
import { BaseURL } from '@/src/api'
import { DESKTOP, LAPTOP, MOBILE, TABLET, WIDE_SCREEN } from '@/src/constant/breakpoint'
import { whiteListedUserList } from '@/src/constant/enum'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'
import { canEditUser } from '@/src/helpers/helpers'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useDatabaseManagement from '@/src/redux/dataManagement/useDataManagement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { formatDateString } from '@/src/utils/dateUtils'
import { successToast, errorToast } from '@/src/utils/toastUtils'
import { isSuperAdmin } from '@/src/utils/userUtils'

// let socket: Socket
// let prodToastId: string | number | null = null
// let stagingToastId: string | number | null = null

// const PROD_SYNC_KEY = 'prod_sync_in_progress'
// const STAGING_SYNC_KEY = 'staging_sync_in_progress'

const HomeHeader: React.FC = () => {
  const { currentPeriod, mainPeriod, freezeType } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { productionCloneApi, stagingCloneApi } = useDatabaseManagement()
  const [popoverOpen, setPopoverOpen] = useState(false)
  const [cloneType, setCloneType] = useState<'production' | 'staging' | null>(null)
  // const [prodSyncDisabled, setProdSyncDisabled] = useState(false)
  // const [stagingSyncDisabled, setStagingSyncDisabled] = useState(false)
  // const socketURL = BaseURL?.replace(/\/api\/?$/, '')
  const breakpoint = useBreakpoint()

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleOpenPopover = (type: 'production' | 'staging') => {
    setCloneType(type)
    setPopoverOpen(true)
  }

  const handleClosePopover = () => {
    setPopoverOpen(false)
    setCloneType(null)
  }

  // const showSyncToast = (type: 'production' | 'staging') => {
  //   const toastId = type === 'production' ? prodToastId : stagingToastId
  //   const setToastId = (id: string | number | null) => {
  //     if (type === 'production') prodToastId = id
  //     else stagingToastId = id
  //   }

  //   if (!toastId) {
  //     const id = toast(`${type === 'production' ? 'Production' : 'Staging'} sync in progress...`, {
  //       id: `${type}-sync-toast`,
  //       position: 'top-center',
  //       dismissible: true,
  //       duration: Infinity,
  //       description: 'This will close automatically after sync is done.',
  //       action: {
  //         label: '✕',
  //         onClick: () => {
  //           toast.dismiss(`${type}-sync-toast`)
  //           setToastId(null)
  //         },
  //       },
  //     })
  //     setToastId(id)
  //   }
  // }

  // const dismissSyncToast = (type: 'production' | 'staging') => {
  //   const toastId = type === 'production' ? prodToastId : stagingToastId
  //   if (toastId) {
  //     toast.dismiss(`${type}-sync-toast`)
  //     if (type === 'production') prodToastId = null
  //     else stagingToastId = null
  //   }
  // }

  // Load persisted state from localStorage on mount
  // useEffect(() => {
  //   const prod = localStorage.getItem(PROD_SYNC_KEY)
  //   const staging = localStorage.getItem(STAGING_SYNC_KEY)

  //   if (prod === 'true') setProdSyncDisabled(true)
  //   if (staging === 'true') setStagingSyncDisabled(true)
  // }, [])

  // Show/hide toasts based on state
  // useEffect(() => {
  //   prodSyncDisabled ? showSyncToast('production') : dismissSyncToast('production')
  //   stagingSyncDisabled ? showSyncToast('staging') : dismissSyncToast('staging')
  // }, [prodSyncDisabled, stagingSyncDisabled])

  // Socket connection
  // useEffect(() => {
  //   try {
  //     socket = io(socketURL, {
  //       transports: ['websocket'],
  //       reconnection: true,
  //       auth: { email: currentUser?.email },
  //     })

  //     socket.on('connect', () => console.log('✅ Socket connected:', socket.id))

  //     socket.on('cloneDb_completed', (message) => {
  //       successToast(message?.data?.message)
  //       setProdSyncDisabled(false)
  //       setStagingSyncDisabled(false)
  //       localStorage.removeItem(PROD_SYNC_KEY)
  //       localStorage.removeItem(STAGING_SYNC_KEY)
  //     })

  //     socket.on('cloneDb_error', (message) => {
  //       errorToast(message?.data?.message)
  //       setProdSyncDisabled(false)
  //       setStagingSyncDisabled(false)
  //       localStorage.removeItem(PROD_SYNC_KEY)
  //       localStorage.removeItem(STAGING_SYNC_KEY)
  //     })
  //   } catch (e) {
  //     console.error('❗ Socket setup failed:', e)
  //   }

  //   return () => {
  //     if (socket?.connected) socket.disconnect()
  //   }
  // }, [])

  const handleConfirmClone = async () => {
    if (cloneType === 'production') {
      const res = await productionCloneApi()
      if (res?.payload.success) {
        // setProdSyncDisabled(true)
        // localStorage.setItem(PROD_SYNC_KEY, 'true')
        successToast(res?.payload?.message)
      }
    } else if (cloneType === 'staging') {
      const res = await stagingCloneApi()
      if (res?.payload.success) {
        // setStagingSyncDisabled(true)
        // localStorage.setItem(STAGING_SYNC_KEY, 'true')
        successToast(res?.payload?.message)
      }
    }
    handleClosePopover()
  }

  return (
    <div className={styles.header}>
      <div className={styles.titleContainer}>
        <div>
          <div className={styles.heyText}>
            <TypographyField variant={'h7'} text={'Hello,'} />
            <Image src="/svg/waveHand.svg" alt="wave hand" height={12} width={12} />
          </div>
          <TypographyField variant={'h4'} text={currentUser.name} />
        </div>

        {(breakpoint === DESKTOP || breakpoint === LAPTOP || breakpoint === WIDE_SCREEN) && isEditForUser && (
          <div className={styles.cloneButtons}>
            {(isSuperAdmin(currentUser.user_type) || whiteListedUserList.includes(currentUser.email)) && (
              <>
                {!window.location.origin.includes('staging') && (
                  <Button
                    onClick={() => handleOpenPopover('production')}
                    // disabled={prodSyncDisabled}
                  >
                    Prod Sync
                  </Button>
                )}
                <Button
                  onClick={() => handleOpenPopover('staging')}
                  // disabled={stagingSyncDisabled}
                >
                  Staging Sync
                </Button>
              </>
            )}
          </div>
        )}
      </div>
      <div className={styles.buttons}>
        {(breakpoint === MOBILE || breakpoint === TABLET) && isEditForUser && (
          <>
            {(isSuperAdmin(currentUser.user_type) || whiteListedUserList.includes(currentUser.email)) && (
              <>
                {!window.location.origin.includes('staging') && (
                  <Button
                    onClick={() => handleOpenPopover('production')}
                    // disabled={prodSyncDisabled}
                  >
                    Prod Sync
                  </Button>
                )}
                <Button
                  onClick={() => handleOpenPopover('staging')}
                  // disabled={stagingSyncDisabled}
                >
                  Staging Sync
                </Button>
              </>
            )}
          </>
        )}
        <div className={styles.timerIcon}>
          <CalendarIcon />
          <span> Current Period :</span>
          <span className={styles.date}>{currentPeriod?.length ? formatDateString(currentPeriod) : ''}</span>
        </div>
      </div>
      <ConfirmClonePopOver open={popoverOpen} onClose={handleClosePopover} handleConfirm={handleConfirmClone} />
    </div>
  )
}

export default HomeHeader

import React, { useMemo } from 'react'
import { useRouter } from 'next/router'
import styles from './AddPhaseModel.module.scss'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import { convertValuesToCommaSeparated, getUniquePhases } from '@/src/helpers/helpers'
import useStatus from '@/src/redux/status/useStatus'
import {
  getUniqueValuesById,
  getUniqueValuesFromArray,
  getValue,
  populateDropdownOptions,
} from '@/src/utils/arrayUtils'

interface AddPhaseModelProps {
  lookup_project_to_phase_id: number
  handleSubmit: (args: number) => void
  onClose: () => void
}

const AddPhaseModel: React.FC<AddPhaseModelProps> = ({ lookup_project_to_phase_id, handleSubmit, onClose }) => {
  const router = useRouter()
  const { statuses } = useStatus()
  const [selectedPhase, setSelectedPhase] = React.useState<number>(lookup_project_to_phase_id)

  const phaseOptionsData = useMemo(() => {
    // TODO: Comment out old data
    // const options: any = populateDropdownOptions(
    //   statuses.filter((item: any) => item?.project_name === router.query.slug),
    //   'phase',
    // )
    // return getUniqueValuesFromArray(options)

    return populateDropdownOptions(
      statuses.filter((item: any) => item?.project_name === router.query.slug),
      'LookupProjectToPhase',
    )
  }, [statuses, router.query.slug])

  const phaseOptions = useMemo(() => {
    // TODO: Comment out old data
    // const uniquePhases = getUniquePhases(phaseOptionsData)
    // const filteredPhases = uniquePhases.filter((phase) => phase !== 'Overall')
    // return filteredPhases?.map((item: any) => {
    //   return {
    //     label: convertValuesToCommaSeparated(item),
    //     value: item,
    //   }
    // })
    const uniquePhases = getUniqueValuesById(phaseOptionsData.flat())
    return uniquePhases
      ?.map((item: any) => {
        return {
          label: item?.phase,
          value: item?.id,
        }
      })
      .filter((item: any) => !!item?.label && item?.label !== 'Overall')
  }, [phaseOptionsData])

  return (
    <div>
      <div className={styles.title}>
        {!!lookup_project_to_phase_id && lookup_project_to_phase_id !== null ? 'Edit Phase' : 'Add Phase'}
      </div>
      <div className={styles.selectFieldWrapper}>
        <ComboBox
          className={styles.selectField}
          focusCustomClass={styles.focusClass}
          options={phaseOptions}
          labelText="Phase/Package *"
          placeholder="Select phase..."
          value={selectedPhase ? getValue(phaseOptions, selectedPhase) : null}
          clearIcon={true}
          onChange={(val) => {
            setSelectedPhase(val?.value || null)
          }}
        />
        {/* <MultiAutoSelect
          isSx={false}
          labelText="Phase/Package"
          placeholder={selectedPhase ? '' : 'Select phase...'}
          clearIcon={true}
          options={convertMultiSelectOption(phaseOptionsData).filter(
            (item: any) => item.id !== null && item.name !== null,
          )}
          value={selectedPhase?.split('$#')}
          handleSelectedOption={(selectedOptions) => {
            const values = selectedOptions.filter((item) => item !== '').join('$#') || ''
            setSelectedPhase(values)
          }}
          isMultiLine={true}
          className={styles.multiSelect}
        /> */}
      </div>
      <div className={styles.actionButtons}>
        <Button
          disabled={lookup_project_to_phase_id ? selectedPhase === lookup_project_to_phase_id : !selectedPhase}
          color="secondary"
          onClick={() => handleSubmit(selectedPhase)}
        >
          Submit
        </Button>
        <Button color="secondary" onClick={onClose}>
          Cancel
        </Button>
      </div>
    </div>
  )
}

export default AddPhaseModel

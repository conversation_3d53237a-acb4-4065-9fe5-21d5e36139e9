import styles from './AvatarCard.module.scss'
import BannerAvatarPicker from './bannerAvatarPicker'
import TypographyField from '@/src/component/shared/typography'

export interface IAvatar {
  avatar: string | null
  avatar_url: string | null
  [key: string]: any
  email: string | null
  id: number
  location: string | null
  phone_number: string | null
}
export interface IAvatarCard {
  role: string
  occupation: string
  avatars: IAvatar[]
}

const width: any = {
  0: 40, // 40px
  1: 40, // 40px
  2: 72, // 40px * 2 - 8px
  3: 94, // 40px * 3 - 16px
}

const AvatarCard = ({ role, occupation, avatars }: IAvatarCard) => {
  const urls = avatars?.filter((res) => res?.avatar_url)?.filter(Boolean)

  return (
    <div className={styles.avatarCard}>
      {/* <div style={{ width: `${width[Math.min(avatars?.length || 0, 3)]}px` }}> */}
      <BannerAvatarPicker avatars={avatars} occupation={occupation} />
      {/* <AvatarPicker
          url={avatars}
          multiSelect={true}
          disabled={true}
          handleSelectAvatar={() => null}
          isManager={true}
        /> */}
      {/* </div> */}
      {/* <div className={styles.avatarDetails}>
        <TypographyField
          style={{ color: '#ffffff' }}
          variant="captionSemiBold"
          className={styles.avatarRole}
          text={`${role}`}
        />
        <TypographyField
          style={{ color: '#ffffff' }}
          variant="thin"
          className={styles.avatarOccupation}
          text={`${occupation}`}
        />
      </div> */}
    </div>
  )
}

export default AvatarCard

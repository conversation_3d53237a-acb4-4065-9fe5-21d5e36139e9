import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterConsultantsResponse, IAuthorityApprovalState, IAuthorityApproval } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IAuthorityApprovalState = {
  getAuthorityApprovalStatus: StatusEnum.Idle,
  addAuthorityApprovalStatus: StatusEnum.Idle,
  deleteAuthorityApprovalStatus: StatusEnum.Idle,
  updateAuthorityApprovalStatus: StatusEnum.Idle,
  authorities: [],
  authority: {},
}

export const getAuthorityApproval = createAsyncThunk(
  '/get-authority-approvals-tracking',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period

    try {
      const response = await ApiGetNoAuth(
        `/authority-approvals-tracking?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addAuthorityApproval = createAsyncThunk(
  '/add-authority-approvals-tracking',
  async (data: IAuthorityApproval, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/authority-approvals-tracking`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateAuthorityApproval = createAsyncThunk(
  '/update-authority-approvals-tracking',
  async (data: IAuthorityApproval, thunkAPI) => {
    const { id, ...rest } = data
    try {
      const response = await ApiPutNoAuth(`/authority-approvals-tracking/${id}`, rest)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteAuthorityApproval = createAsyncThunk(
  '/delete-authority-approvals-tracking',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/authority-approvals-tracking/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const authorityApprovalSlice = createSlice({
  name: 'authorityApproval',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getAuthorityApproval
    builder.addCase(getAuthorityApproval.pending, (state) => {
      state.getAuthorityApprovalStatus = StatusEnum.Pending
    })
    builder.addCase(getAuthorityApproval.fulfilled, (state, action) => {
      state.getAuthorityApprovalStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterConsultantsResponse
      state.authorities = actionPayload.data
    })
    builder.addCase(getAuthorityApproval.rejected, (state) => {
      state.getAuthorityApprovalStatus = StatusEnum.Failed
    })
    //addAuthorityApproval
    builder.addCase(addAuthorityApproval.pending, (state) => {
      state.addAuthorityApprovalStatus = StatusEnum.Pending
    })
    builder.addCase(addAuthorityApproval.fulfilled, (state, action) => {
      state.addAuthorityApprovalStatus = StatusEnum.Success
    })
    builder.addCase(addAuthorityApproval.rejected, (state) => {
      state.addAuthorityApprovalStatus = StatusEnum.Failed
    })
    //updateAuthorityApproval
    builder.addCase(updateAuthorityApproval.pending, (state) => {
      state.updateAuthorityApprovalStatus = StatusEnum.Pending
    })
    builder.addCase(updateAuthorityApproval.fulfilled, (state, action) => {
      state.updateAuthorityApprovalStatus = StatusEnum.Success
    })
    builder.addCase(updateAuthorityApproval.rejected, (state) => {
      state.updateAuthorityApprovalStatus = StatusEnum.Failed
    })
    //deleteAuthorityApproval
    builder.addCase(deleteAuthorityApproval.pending, (state) => {
      state.deleteAuthorityApprovalStatus = StatusEnum.Pending
    })
    builder.addCase(deleteAuthorityApproval.fulfilled, (state, action) => {
      state.deleteAuthorityApprovalStatus = StatusEnum.Success
    })
    builder.addCase(deleteAuthorityApproval.rejected, (state) => {
      state.deleteAuthorityApprovalStatus = StatusEnum.Failed
    })
  },
})

export const {} = authorityApprovalSlice.actions

// Export the reducer
export default authorityApprovalSlice.reducer

import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './NonRecoverableDelayJustification.module.scss'
import { DrawerStates } from '../../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateNonRecoverableDelayJustifications,
  useDeleteNonRecoverableDelayJustifications,
  useGetNonRecoverableDelayJustifications,
  useUpdateNonRecoverableDelayJustifications,
} from '@/src/hooks/useNonRecoverableDelayJustifications'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const NonRecoverableDelayJustification: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({
  drawerStates,
  onClose,
}) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { nonRecoverableDelayJustification } = drawerStates
  //REACT-QUERY
  const { nonRecoverableDelayJustifications } = useGetNonRecoverableDelayJustifications(
    nonRecoverableDelayJustification,
  )
  // MUTATIONS
  const { mutate: addNonRecoverableDelayJustifications } = useCreateNonRecoverableDelayJustifications()
  const { mutate: deleteNonRecoverableDelayJustifications } = useDeleteNonRecoverableDelayJustifications()
  const { mutate: updateNonRecoverableDelayJustifications } = useUpdateNonRecoverableDelayJustifications()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: {
      nonRecoverableDelayJustification: '',
      nonRecoverableDelayJustificationCode: '',
    },
    onSubmit: async (values) => {
      const payload = {
        non_recoverable_delay_justification: values.nonRecoverableDelayJustification,
        non_recoverable_delay_justification_code: values.nonRecoverableDelayJustificationCode,
      }
      if (editIndex !== null) {
        updateNonRecoverableDelayJustifications(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addNonRecoverableDelayJustifications(
          payload,
          handleMutationCallbacks('Record successfully added', 'Failed to add record'),
        )
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteNonRecoverableDelayJustifications(
      id,
      handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'),
    )
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editJustification = nonRecoverableDelayJustifications.find((justification) => justification.id === id)
    formik.setValues({
      nonRecoverableDelayJustification: editJustification?.non_recoverable_delay_justification || '',
      nonRecoverableDelayJustificationCode: editJustification?.non_recoverable_delay_justification_code || '',
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    { accessorKey: 'non_recoverable_delay_justification_code', header: 'Code', flex: 1 },
    { accessorKey: 'non_recoverable_delay_justification', header: 'Non-Recoverable Delay Justification', flex: 1 },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.row?.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text="Add Non-Recoverable Delay Justification"
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div className={styles.formGroup}>
            <TextInputField
              className={styles.field}
              name="nonRecoverableDelayJustification"
              labelText="Non-Recoverable Delay Justification"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.nonRecoverableDelayJustification}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.field}
              name="nonRecoverableDelayJustificationCode"
              labelText="Non-Recoverable Delay Justification Code"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.nonRecoverableDelayJustificationCode}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <Button
            className={styles.submitButton}
            type="submit"
            disabled={
              (!formik.values.nonRecoverableDelayJustification &&
                !formik.values.nonRecoverableDelayJustificationCode) ||
              formik.isSubmitting
            }
          >
            {editIndex !== null
              ? '+ Update Non-Recoverable Delay Justification'
              : '+ Add Non-Recoverable Delay Justification'}
          </Button>
        </form>

        {nonRecoverableDelayJustifications?.length > 0 && (
          <TanStackTable
            rows={sortData(nonRecoverableDelayJustifications, 'non_recoverable_delay_justification_code') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default NonRecoverableDelayJustification

import React, { ReactElement, forwardRef } from 'react'

interface ProjectManageIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: string
}

const ProjectManageIcon: React.FC<ProjectManageIconProps> = forwardRef<SVGSVGElement, ProjectManageIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7 1.25C7.41421 1.25 7.75 1.58579 7.75 2V3.75H8C8.01496 3.75 8.02987 3.75 8.04475 3.75C8.47757 3.74995 8.8744 3.7499 9.19721 3.7933C9.55269 3.84109 9.92841 3.95354 10.2374 4.26256C10.5465 4.57159 10.6589 4.94731 10.7067 5.30279C10.7501 5.62561 10.7501 6.02244 10.75 6.45526C10.75 6.47013 10.75 6.48504 10.75 6.5V7.88206C10.8169 7.93503 10.8818 7.99268 10.9445 8.05546C11.4 8.51093 11.5857 9.07773 11.6701 9.70552C11.7284 10.139 11.7442 10.6545 11.7484 11.25H12.25L12.25 7.71C12.25 6.45165 12.2499 5.42299 12.3656 4.6381C12.4856 3.82422 12.7528 3.09753 13.4336 2.62571C14.1145 2.15388 14.8887 2.15884 15.6929 2.33231C16.4684 2.49959 17.4316 2.8608 18.6098 3.30267L18.7057 3.33862C19.3012 3.56191 19.8051 3.75085 20.2009 3.95182C20.6219 4.16555 20.9859 4.42361 21.2603 4.81961C21.5347 5.21562 21.6486 5.647 21.7009 6.11624C21.75 6.55746 21.75 7.0956 21.75 7.73158V21.25H22C22.4142 21.25 22.75 21.5858 22.75 22C22.75 22.4142 22.4142 22.75 22 22.75H2C1.58579 22.75 1.25 22.4142 1.25 22C1.25 21.5858 1.58579 21.25 2 21.25H2.25L2.25 11.948C2.24997 11.0495 2.24994 10.3003 2.32991 9.70552C2.41432 9.07773 2.59999 8.51093 3.05546 8.05546C3.11823 7.99268 3.18313 7.93503 3.25 7.88206V6.5C3.25 6.48504 3.25 6.47013 3.25 6.45525C3.24995 6.02243 3.2499 5.6256 3.2933 5.30279C3.34109 4.94731 3.45354 4.57159 3.76256 4.26256C4.07159 3.95354 4.44731 3.84109 4.80279 3.7933C5.1256 3.7499 5.52243 3.74995 5.95525 3.75C5.97012 3.75 5.98504 3.75 6 3.75H6.25V2C6.25 1.58579 6.58579 1.25 7 1.25ZM4.75 7.32412C5.33751 7.24995 6.07178 7.24997 6.94801 7.25H7.05199C7.92822 7.24997 8.66249 7.24995 9.25 7.32412V6.5C9.25 6.00739 9.24841 5.71339 9.22008 5.50266C9.20709 5.40611 9.1918 5.35774 9.18284 5.33596C9.18077 5.33092 9.17915 5.3276 9.17814 5.32567L9.17676 5.32324L9.17433 5.32186C9.1724 5.32085 9.16908 5.31923 9.16404 5.31716C9.14226 5.3082 9.09389 5.29291 8.99734 5.27992C8.78661 5.25159 8.49261 5.25 8 5.25H6C5.50739 5.25 5.21339 5.25159 5.00266 5.27992C4.90611 5.29291 4.85774 5.3082 4.83596 5.31716C4.83092 5.31923 4.8276 5.32085 4.82567 5.32186L4.82324 5.32324L4.82186 5.32567C4.82085 5.3276 4.81923 5.33092 4.81716 5.33596C4.8082 5.35774 4.79291 5.40611 4.77992 5.50266C4.75159 5.71339 4.75 6.00739 4.75 6.5V7.32412ZM3.75 21.25H6.25L6.25 15.948C6.24997 15.0495 6.24995 14.3003 6.32991 13.7055C6.41432 13.0777 6.59999 12.5109 7.05546 12.0555C7.51093 11.6 8.07773 11.4143 8.70552 11.3299C9.13855 11.2717 9.65344 11.2559 10.2482 11.2516C10.244 10.6814 10.23 10.2512 10.1835 9.90539C10.1214 9.44393 10.0142 9.24643 9.88388 9.11612C9.75357 8.9858 9.55607 8.87858 9.09461 8.81654C8.61157 8.75159 7.96401 8.75 7 8.75C6.03599 8.75 5.38843 8.75159 4.90539 8.81654C4.44393 8.87858 4.24643 8.9858 4.11612 9.11612C3.9858 9.24643 3.87858 9.44393 3.81654 9.90539C3.75159 10.3884 3.75 11.036 3.75 12V21.25ZM7.75 21.25H16.25V16C16.25 15.036 16.2484 14.3884 16.1835 13.9054C16.1214 13.4439 16.0142 13.2464 15.8839 13.1161C15.7536 12.9858 15.5561 12.8786 15.0946 12.8165C14.6116 12.7516 13.964 12.75 13 12.75H11C10.036 12.75 9.38843 12.7516 8.90539 12.8165C8.44393 12.8786 8.24643 12.9858 8.11612 13.1161C7.9858 13.2464 7.87858 13.4439 7.81654 13.9054C7.75159 14.3884 7.75 15.036 7.75 16V21.25ZM17.75 21.25H20.25V7.772C20.25 7.08479 20.2489 6.63075 20.2101 6.28238C20.1734 5.95272 20.1091 5.79193 20.0274 5.67401C19.9457 5.55609 19.8177 5.43949 19.5219 5.28934C19.2094 5.13066 18.7846 4.97023 18.1412 4.72893C16.8906 4.25997 16.0312 3.93978 15.3766 3.79858C14.7379 3.66082 14.468 3.73388 14.288 3.85859C14.108 3.98331 13.9448 4.21044 13.8496 4.8568C13.752 5.5193 13.75 6.43639 13.75 7.772V11.2516C14.3455 11.2558 14.861 11.2716 15.2945 11.3299C15.9223 11.4143 16.4891 11.6 16.9445 12.0555C17.4 12.5109 17.5857 13.0777 17.6701 13.7055C17.7501 14.3003 17.75 15.0495 17.75 15.948L17.75 21.25ZM4.82324 5.32324C4.82357 5.32297 4.82364 5.32283 4.82324 5.32324C4.82278 5.32369 4.82296 5.32358 4.82324 5.32324ZM9.25 15C9.25 14.5858 9.58579 14.25 10 14.25H14C14.4142 14.25 14.75 14.5858 14.75 15C14.75 15.4142 14.4142 15.75 14 15.75H10C9.58579 15.75 9.25 15.4142 9.25 15ZM9.25 18C9.25 17.5858 9.58579 17.25 10 17.25H14C14.4142 17.25 14.75 17.5858 14.75 18C14.75 18.4142 14.4142 18.75 14 18.75H10C9.58579 18.75 9.25 18.4142 9.25 18Z"
          fill={props.color ? props.color : '#808080'}
        />
      </svg>
    )
  },
)
ProjectManageIcon.displayName = 'ProjectManageIcon'

export default ProjectManageIcon

import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIG<PERSON>,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'

// Function to get allowed statuses based on permissions
export const getStageStatusByPermission = (role: any, extraFilter?: (status: string) => boolean) => {
  //TODO:- Create Const for view Permissions and use
  const stageStatusMapping: Record<string, string> = {
    'Status-Initiation': INITIATION,
    'Status-LDC Procurement': LDC_PROCUREMENT,
    'Status-Design': DESIGN,
    'Status-Contractor Procurement': CONTRACTOR_PROCUREMENT,
    'Status-Construction': CONSTRUCTION,
    'Status-DLP and Project Closeout': DLP_PROJECT_CLOSEOUT,
  }
  let allowedStatuses = role?.view_permissions.map((permission: string) => stageStatusMapping[permission])

  // Apply extra filtering if provided
  if (extraFilter) {
    allowedStatuses = allowedStatuses.filter(extraFilter)
  }

  return allowedStatuses
}

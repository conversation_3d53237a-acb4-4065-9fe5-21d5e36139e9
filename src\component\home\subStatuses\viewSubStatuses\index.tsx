import React, { useEffect, useState } from 'react'
import styles from './ViewSubStatuses.module.scss'
import { IIViewSubStatusesProps } from '../interface'
import Button from '@/src/component/shared/button'
import Textarea from '@/src/component/shared/textArea'
import CustomTooltip from '@/src/component/shared/tooltip'
import TypographyField from '@/src/component/shared/typography'
import ViewMasterTable from '@/src/component/shared/viewMasterTable'
import { IViewMasterColumn } from '@/src/component/shared/viewMasterTable/interface'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { GREEN, WHITE } from '@/src/constant/color'
import useSubStatuses from '@/src/redux/subStatuses/useSubStatues'

const ViewSubStatuses: React.FC<IIViewSubStatusesProps> = ({ onClose, drawerStates }) => {
  const [editId, setEditId] = useState<number | null>(null)
  const [updatedValue, setUpdatedValue] = useState<string>('')
  const [updateCount, setUpdateCount] = useState<number>(0)
  const [deleteRow, setDeleteRow] = useState<number | null>(null)
  const [model, setModel] = useState<boolean>(false)
  const [loader, setLoader] = useState<boolean>(false)
  const {
    subStatuses,
    getMasterSubStatuesApi,
    addMasterSubStatuesApi,
    updateMasterSubStatuesApi,
    deleteMasterSubStatuesApi,
  } = useSubStatuses()
  const { viewSubStatuses } = drawerStates

  const [data, setData] = useState<any>([])

  useEffect(() => {
    if (!viewSubStatuses) {
      setDeleteRow(null)
    }
  }, [viewSubStatuses])

  useEffect(() => {
    if (viewSubStatuses) {
      try {
        const response: Record<string, any> = getMasterSubStatuesApi()
      } catch (error) {
        console.error('Error fetching owning entities:', error)
      }
    }

    setUpdateCount(0)
  }, [viewSubStatuses])

  const handleEditRow = (id: number) => {
    setEditId(id)
    const editItem: any = subStatuses.find((subStatus) => {
      // TODO
      return subStatus.id === id
    })
    setUpdatedValue(editItem.sub_status)
  }

  const handleUpdate = async () => {
    if (editId !== null) {
      try {
        const response: Record<string, any> = await updateMasterSubStatuesApi({
          id: editId,
          sub_status: updatedValue,
        })
        if (response.payload.success === true) {
          setUpdateCount((prevCount) => prevCount + 1)
          setEditId(null)
          getMasterSubStatuesApi()
        }
      } catch (error) {
        console.error('Error updating owning entity:', error)
      }
    }
  }

  const handleDelete = async (id: number) => {
    try {
      const response: Record<string, any> = await deleteMasterSubStatuesApi(id)
      if (response.payload.success === true) {
        getMasterSubStatuesApi()
        setDeleteRow(null)
      }
    } catch (error) {
      console.error('Error deleting owning entity:', error)
    }
  }

  const columns: IViewMasterColumn[] = [
    {
      key: 'sub_status',
      label: 'Sub status',
      renderCell: (value: unknown, row: any, rowIndex: number, isHover?: boolean) => (
        <td className={`${styles.tableData} ${editId === row.id ? styles.editCell : ''}`}>
          {editId === row.id ? (
            <div className={styles.editRow}>
              <Textarea
                autoFocus={editId === row.id}
                className={styles.updateField}
                value={updatedValue}
                onChange={(e) => setUpdatedValue(e.target.value)}
              />
              <button className={styles.updateButton} onClick={handleUpdate}>
                <TypographyField
                  variant={'caption'}
                  style={{
                    color: WHITE,
                    fontWeight: '400',
                    fontSize: '10px',
                    lineHeight: '20px',
                    whiteSpace: 'noWrap',
                  }}
                  text={`Update`}
                />
              </button>
            </div>
          ) : (
            <div className={styles.cellContent}>
              <TypographyField
                variant={'caption'}
                className={styles.rowValue}
                style={{ wordBreak: 'break-all' }}
                text={`${row.sub_status}`}
              />
              <div className={styles.badge}>
                {row.entityCount && (
                  <TypographyField variant={'caption'} className={styles.badgeValue} text={`${row?.entityCount}`} />
                )}
              </div>
            </div>
          )}
        </td>
      ),
    },
    {
      key: 'action',
      label: 'Action',
      renderCell: (value: unknown, row: any, rowIndex: number, isHover?: boolean) => (
        <td className={styles.tableData}>
          <div className={styles.actionCell}>
            {editId !== row.id ? (
              <EditIcon className={styles.cursorPointer} onClick={() => handleEditRow(row?.id)} />
            ) : (
              <CloseCircleIcon
                fill="#444444"
                height="16"
                width="16"
                className={styles.cursorPointer}
                onClick={() => setEditId(null)}
              />
            )}
            <CustomTooltip
              open={deleteRow === rowIndex}
              arrow
              margin="10px 5px 0px 0px"
              padding="12px"
              borderRadius="4px"
              boxShadow="0px 4px 200px 0px #00000026"
              backgroundColor="white"
              className={styles.tooltip}
              title={
                <div className={styles.deleteCard}>
                  <div className={styles.deleteHeader}>
                    Are you sure want to delete entity <span className={styles.boldHead}>{row.entityName} ?</span>
                  </div>
                  <div className={styles.deleteContent}>
                    Entity <span className={styles.boldContent}>{row.entityName}</span> is assigned to{' '}
                    <span className={styles.boldContent}>{row.entityCount}</span> projects. You will have to reassign
                    all projects to a different entity before deleting!
                  </div>
                  <div className={styles.actionButtons}>
                    <Button
                      className={styles.confirmButton}
                      onClick={(e) => {
                        e.stopPropagation()
                        setModel(true)
                      }}
                    >
                      Confirm Delete
                    </Button>
                    <Button color="secondary" onClick={() => setDeleteRow(null)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              }
              placement="bottom"
            >
              <DeleteIcon
                className={styles.cursorPointer}
                fill="#444444"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDelete(row?.id)
                }}
              />
            </CustomTooltip>
          </div>
        </td>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant={'caption'} text={'View Sub Statues'} />
      </div>
      <div className={styles.content}>
        {subStatuses?.length > 0 && <ViewMasterTable data={subStatuses} columns={columns} />}
        {updateCount ? (
          <TypographyField
            variant={'caption'}
            style={{
              color: GREEN,
              fontWeight: '400',
              fontSize: '10px',
              lineHeight: '20px',
            }}
            text={`Updated ${updateCount} Row`}
          />
        ) : null}
        {/* <ConfirmModel
          open={model}
          onClose={() => {
            setDeleteRow(null);
            setModel(false);
          }}
          onConfirmOpen={() => {
            setLoader(true);
          }}
        />
        <DeleteLoader open={loader} onClose={() => setLoader(false)} /> */}
      </div>
    </div>
  )
}

export default ViewSubStatuses

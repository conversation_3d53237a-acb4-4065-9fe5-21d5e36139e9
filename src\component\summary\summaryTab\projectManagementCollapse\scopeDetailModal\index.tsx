import { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { Box, IconButton, Modal } from '@mui/material'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './ScopeDetailModal.module.scss'
import { generateTempId, getTableData } from '../helper'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { canEditUser } from '@/src/helpers/helpers'
import {
  useCreatePotentialBudgetUpliftScope,
  useDeletePotentialBudgetUpliftScope,
  useGetPotentialBudgetUpliftScope,
  useUpdatePotentialBudgetUpliftScope,
} from '@/src/hooks/usePotentialBudgetUpliftScope'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'

interface ISelectedRowData {
  id: number | string
  current_scope: string
  amended_scope: string
  cost_impact: string
  isNew: boolean
}

const ScopeDetailModal: React.FC<{
  edit: boolean
  open: boolean
  onClose: () => void
}> = ({ edit, open, onClose }) => {
  const router = useRouter()
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [tableData, setTableData] = useState<any[]>([])
  const { currentUser } = useAuthorization()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const [selectedRowData, setSelectedRowData] = useState<ISelectedRowData | null>(null)
  const [originalRowData, setOriginalRowData] = useState<ISelectedRowData | null>(null)
  const { potentialBudgetUpliftScopes, isLoading } = useGetPotentialBudgetUpliftScope(
    open,
    currentPeriod,
    router.query.slug as string,
  )
  const { mutateAsync: addPotentialBudgetUpliftScope } = useCreatePotentialBudgetUpliftScope()
  const { mutateAsync: updatePotentialBudgetUpliftScope } = useUpdatePotentialBudgetUpliftScope()
  const { mutateAsync: deletePotentialBudgetUpliftScope } = useDeletePotentialBudgetUpliftScope()
  const [loader, setLoader] = useState(false) //Fot Add and Delete and edit

  // Memoized Edit Permissions
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  useEffect(() => {
    const data = getTableData(potentialBudgetUpliftScopes)
    setTableData([...data])
  }, [potentialBudgetUpliftScopes])

  const handleAdd = () => {
    if (!edit) {
      toast.error('Please switch to edit mode before adding a new scope')
      return
    }
    const newObj = {
      id: generateTempId(), // Temporary ID for UI handling
      current_scope: '',
      amended_scope: '',
      cost_impact: '',
      isNew: true,
    }
    setSelectedRowData({ ...newObj })
    setTableData((prev) => [...prev, newObj])
  }

  const handleEdit = (data: any) => {
    if (!edit) {
      toast.error('Please switch to edit mode before update scope')
      return
    }
    // If another row is being edited and not saved, revert its changes
    if (selectedRowData && originalRowData && selectedRowData.id !== data.id) {
      setTableData((prev) => prev.map((r) => (r.id === selectedRowData.id ? { ...originalRowData } : r)))
    }
    setSelectedRowData(data)
    setOriginalRowData({ ...data })
  }

  const handleDelete = async (id: any) => {
    await deletePotentialBudgetUpliftScope(id)
    setDeleteModel(null)
    setSelectedRowData(null)
  }

  const handleSubmit = async () => {
    setLoader(true)
    try {
      if (!selectedRowData) return
      const payload = {
        period: currentPeriod,
        project_name: router?.query?.slug as string,
        current_scope: selectedRowData?.current_scope,
        amended_scope: selectedRowData?.amended_scope,
        cost_impact: selectedRowData?.cost_impact,
      }
      if (!selectedRowData?.isNew) {
        await updatePotentialBudgetUpliftScope({
          ...payload,
          id: selectedRowData?.id as number,
        })
      } else {
        await addPotentialBudgetUpliftScope({ ...payload })
      }
      setSelectedRowData(null)
      setOriginalRowData(null)
    } catch (error) {
      console.log('error: ', error)
    }
    setLoader(false)
  }

  const onCellUpdate = async (cell: any, newValue: string, row: any) => {
    if (selectedRowData) {
      // Only allow editing the selected row
      if (row.id !== selectedRowData.id) return
      const field = cell.columnId
      const updatedObj = { ...selectedRowData, [field]: newValue }
      setSelectedRowData({ ...updatedObj })
      // Update table data
      const updatedData = tableData?.map((row) => {
        if (row.id === cell.rowId) {
          return {
            ...row,
            [cell.columnId]: newValue,
          }
        }
        return row
      })
      setTableData([...updatedData])
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'current_scope',
      header: 'Current Scope',
      editableType: 'text',
      size: 250,
      filterType: 'text',
      isEditableCell: (cell: any) => {
        const rowId = cell?.row?.original?.id
        return rowId === selectedRowData?.id && edit
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'amended_scope',
      header: 'Amended Scope',
      editableType: 'text',
      size: 250,
      filterType: 'text',
      isEditableCell: (cell: any) => {
        const rowId = cell?.row?.original?.id
        return rowId === selectedRowData?.id && edit
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
    {
      accessorKey: 'cost_impact',
      header: 'Cost Impact',
      editableType: 'text',
      size: 200,
      filterType: 'text',
      isEditableCell: (cell: any) => {
        const rowId = cell?.row?.original?.id
        return rowId === selectedRowData?.id && edit
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
    },
  ]

  if (edit) {
    columns.push({
      accessorKey: 'actions',
      header: 'Actions',
      size: 100,
      cell: ({ row }) => (
        <div>
          {!row?.original?.isNew && (
            <>
              <IconButton
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    handleEdit(row.original)
                  }
                }}
              >
                <EditIcon />
              </IconButton>
              <IconButton
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else if (!edit) {
                    toast.error('Please switch to edit mode before Delete scope')
                    return
                  }
                  setDeleteModel(row.original.id)
                }}
              >
                <DeleteIcon />
              </IconButton>
            </>
          )}
        </div>
      ),
    })
  }

  const handleClose = () => {
    onClose()
    setSelectedRowData(null)
  }

  const handleCancel = () => {
    // Revert changes if editing
    // if (selectedRowData && originalRowData) {
    //   setTableData((prev) => prev.map((r) => (r.id === selectedRowData.id ? { ...originalRowData } : r)))
    // }
    if (selectedRowData) {
      if (selectedRowData.isNew) {
        setTableData((prev) => prev.filter((row) => row.id !== selectedRowData.id))
      } else if (originalRowData) {
        setTableData((prev) => prev.map((row) => (row.id === originalRowData.id ? { ...originalRowData } : row)))
      }
    }
    setSelectedRowData(null)
    setOriginalRowData(null)
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          minWidth: '700px',
          bgcolor: 'background.paper',
          borderRadius: '12px',
          pt: '20px',
          px: '20px',
          pb: '20px',
          zIndex: 1,
          minHeight: '200px',
        }}
      >
        <>
          <div className={styles.header}>
            <TypographyField variant={'bodySemiBold'} text={'Scope Details'} />
            {selectedRowData && edit ? (
              <div className={styles.editBtnContainer}>
                <Button onClick={handleSubmit} disabled={loader || isLoading}>
                  Save
                </Button>
                <Button onClick={handleCancel}>Cancel</Button>
              </div>
            ) : (
              <>{edit && <Button onClick={handleAdd}>Add</Button>}</>
            )}
          </div>
          <div className={styles.tableContainer}>
            {/* <div className={styles.addBtn}>
            <Button onClick={handleAdd}>Add</Button>
          </div> */}
            <TanStackTable rows={tableData as any} columns={columns} isLoading={isLoading} />
          </div>
          <ConfirmDeleteModal
            open={Boolean(deleteModel)}
            onClose={() => setDeleteModel(null)}
            handleConfirm={() => handleDelete(deleteModel as number)}
            loading={loader}
          />
        </>
      </Box>
    </Modal>
  )
}

export default ScopeDetailModal

import React, { useMemo } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { toast } from 'sonner'
import styles from './ContractVosHeader.module.scss'
import Button from '@/src/component/shared/button'
import ContractsAndVOs from '@/src/component/svgImages/contractsAndVOs'
import PlusIcon from '@/src/component/svgImages/plusIcon'
import { IContractorAndVos } from '@/src/redux/contractorAndVos/interface'

const newContract: Record<string, string | number> = {
  phase: '',
  approved_vo_value: 0,
  contract_type: '',
  contractor_name: '',
  description: '',
  forecasted_contract_value: '',
  original_contract_value: '',
  pvr_value: 0,
  revised_contract_value: '',
  contract_signature: '',
  bonds: '',
  insurance: '',
  advanced_payment: '',
}

interface ContractVosHeaderProps {
  isEditForUser: boolean
  contracts: IContractorAndVos[]
  setAddSetTime: (value: boolean) => void
  setContracts: (contracts: IContractorAndVos[]) => void
  expanded: boolean
  contractVosAccordion: boolean
  handleToggleAll: () => void
  handleReplaceOverallPhase: () => void
}

const ContractVosHeader = ({
  isEditForUser,
  contracts,
  setAddSetTime,
  setContracts,
  expanded,
  contractVosAccordion,
  handleToggleAll,
  handleReplaceOverallPhase,
}: any) => {
  //USE_FOR:  Handles the toggle logic for the "Expand All" button label.
  const isAllExpand = useMemo(() => {
    if (!contractVosAccordion || contractVosAccordion.length === 0) return true
    return contractVosAccordion.some((item: any) => item?.isAccordion === false)
  }, [contractVosAccordion])

  const handleAddContract = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }
    setAddSetTime(true)
    // Check if the new contract already exists in the contracts array
    const isDuplicate = contracts.some((contract: any) =>
      Object.keys(newContract).every((key) => contract[key] === newContract[key]),
    )
    if (!isDuplicate) {
      setContracts([...contracts, newContract])
    }
  }

  const isExistOverall = contracts.some((contract: any) =>
    contract.phase
      ?.split('$@')
      .map((p: any) => p.toLowerCase())
      .includes('overall'),
  )

  return (
    <>
      <div className={styles.summaryTitle}>
        <div className={styles.header}>
          <ContractsAndVOs />
          <div>Contracts & VOs</div>
          <div className={styles.buttonPlus} onClick={handleAddContract}>
            <PlusIcon color="#ffffff" />
          </div>
        </div>
      </div>
      {expanded && (
        <>
          {isExistOverall && (
            <div className={styles.overallPhasereplacebutton}>
              <Button onClick={handleReplaceOverallPhase}>Replace Overall Phase</Button>
            </div>
          )}
          <div className={styles.expandCollapseGroup}>
            <Button className={styles.expandedCollapse} onClick={handleToggleAll}>
              {!isAllExpand ? 'Collapse All' : 'Expand All'}
            </Button>
          </div>
        </>
      )}
    </>
  )
}

export default ContractVosHeader

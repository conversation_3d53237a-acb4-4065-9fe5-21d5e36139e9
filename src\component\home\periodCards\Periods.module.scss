@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.cardContainer {
  padding: 12px 5px 12px 10px;
  background-color: rgba(104, 178, 226, 0.1);
  border-radius: 10px;
  @include respond-to('laptop') {
    padding: 12px 25px;
  }

  @include respond-to('desktop') {
    padding: 12px 10px;
  }
}

.cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;

  .card {
    background-color: white;
    padding: 5px 10px;
    border-radius: 10px;

    position: relative;
  }
}

.dateContainer {
  display: flex;
  padding-top: 0px;
}
.date {
  white-space: nowrap;
}
.currentLabel {
  padding-top: 0px;
}
.unFreeze {
  margin-top: 10px;
}

.statusCardBody {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 7px;
}

.statusIcon {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  box-shadow: 0px 4px 200px 0px rgba(0, 0, 0, 0.15);
  background: #ffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statusTextBtn {
  width: 100% !important;
  font-size: 13px;
}

.statusActions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .statusActionBtn {
    margin-top: 5px;
    width: 100%;
  }
}

.statusIconStyle {
  fill: #000;
}
.freezeContainer {
  width: 200px;
  .buttons {
    padding-top: 10px;
  }
}
.freezeType {
  display: flex;
  gap: 6px;
  .boldText {
    font-weight: 600;
  }
}

@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.accordionWrapper {
  // cursor: pointer;
  border-radius: 8px;
  background-color: $WHITE;
  padding: 16px 20px 16px 0px;
  box-shadow: 4px 10px 15px -3px rgba(0, 0, 0, 0.1);
  // box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
  //   0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  @include respond-to('mobile') {
    padding: 16px 15px 16px 15px;
  }
  @include respond-to('tablet') {
    padding: 16px 20px 16px 0px;
  }
}
.collapseContainer {
  cursor: pointer;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  .collapseName,
  .lastUpdatedText {
    color: $DARK;
    white-space: nowrap;
    flex: 1;
  }
  .collapseName {
    font-size: 16px;
    font-weight: 700;
    line-height: 18px;
  }
  .lastUpdatedText {
    text-align: end;
  }
  .divider {
    width: 100%;
    height: 1px;
    background: transparent;
  }
}

.cursorPointer {
  cursor: pointer;
}
.accordionTitle {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 8px 20px;
  background: #eeeeec;
  border-radius: 0 8px 8px 0;
  min-width: 200px;
  svg {
    width: 24px;
    height: 24px;
  }
  @include respond-to('mobile') {
    padding: 8px 10px;
  }
  @include respond-to('tablet') {
    padding: 8px 20px;
  }
}
.updateDetail {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  .error {
    color: red;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }
}

.governance {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.actionHeader {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  position: sticky;
  top: 250px;
}
.container {
  overflow: auto;
  scrollbar-width: none;
  display: flex;
  gap: 15px;
  flex-direction: column;
}
.expandedHeader {
  top: 132px !important;
}
.checkbox {
  height: 20px;
  width: 20px;
}

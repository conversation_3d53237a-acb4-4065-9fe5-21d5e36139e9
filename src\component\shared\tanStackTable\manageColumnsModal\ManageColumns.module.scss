@import '/styles/color.scss';

.menuRoot {
  .menuPaper {
    min-width: 200px;
    max-width: 200px;
    max-height: 300px;
    margin-top: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }
}
.dragItem {
  cursor: grab;
  display: flex;
  align-items: center;
  padding: 5px;
  gap: 10px;
  border: 1px solid $DARK;
  border-radius: 10px;
}
.items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

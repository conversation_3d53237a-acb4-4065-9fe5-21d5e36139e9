@import '/styles/color.scss';

.container {
  .label {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: $GRAY_500;
    gap: 2px;
    > div {
      display: flex;
    }
  }
  &.vertical textarea {
    resize: none;
  }
  .textArea {
    font-family: Poppins;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    line-height: 16px;
    font-weight: 400;
    background-color: $LIGHT;
    color: $DARK;
    padding: 6px 0px 0px 12px;
    min-height: 71px;
    width: -webkit-fill-available;
    &::placeholder {
      color: $GRAY_500;
      opacity: 50%;
    }
    &:focus {
      border-radius: 4px;
    }
    &:hover {
      border-radius: 4px;
    }
    &:focus-visible {
      outline: none;
    }
    &.errorField {
      border: 1px solid $ERROR;
      border-radius: 4px;
    }
  }
  .text {
    font-size: 12px;
    line-height: 16px;
    height: 16px;
    color: $GRAY_500;

    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    &.error {
      color: $ERROR;
    }
  }
}

import { AvatarItem } from '@/src/redux/avatars/interface'

export interface ISvpPayload {
  delivery_project_manager: string
  avatar?: string
  email: string
  phone_number: string
  location: string
}

export interface IUpdateSvp extends ISvpPayload {
  id: number
}

export interface ISvp {
  id: number
  delivery_project_manager: string
  avatar: AvatarItem
  email: string
  phone_number: string
  location: string
}

export interface ISvpResponse {
  data: ISvp[]
  message: string
  success: true
}

// filtersSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface FiltersState {
  searchQuery: string
  cpds: string[]
  project_id: string[]
  categoryValue: string[]
  entityValue: string[]
  classificationValue: string[]
  projectType: string[]
  projectStatus: string[]
  locationValue: string[]
  contractTypeValue: string[]
  portFolio: string[]
  designManager: string[]
  executive: string[]
  deliveryDirector: string[]
  control: string[]
  procure: string[]
  designProjectOwner: string[]
  startYear: string[]
  delivery_project_manager: string[]
  labelValue: string[]
  svp: string[]
  deviationStatusValue: string[]
  overall_forecasted_finish_date: string[]
  userFilterFields: string[]
}

const initialState: FiltersState = {
  searchQuery: '',
  cpds: [],
  project_id: [],
  categoryValue: [],
  entityValue: [],
  classificationValue: [],
  projectType: [],
  projectStatus: [],
  locationValue: [],
  contractTypeValue: [],
  portFolio: [],
  designManager: [],
  executive: [],
  deliveryDirector: [],
  control: [],
  procure: [],
  designProjectOwner: [],
  startYear: [],
  delivery_project_manager: [],
  labelValue: [],
  svp: [],
  deviationStatusValue: [],
  overall_forecasted_finish_date: [],
  userFilterFields: [],
}

const filtersSlice = createSlice({
  name: 'filters',
  initialState,
  reducers: {
    setSearchQuery(state, action: PayloadAction<string>) {
      state.searchQuery = action.payload
    },
    setStartYear(state, action: PayloadAction<string[]>) {
      state.startYear = action.payload
    },
    setCategoryValue(state, action: PayloadAction<string[]>) {
      state.categoryValue = action.payload
    },
    setEntityValue(state, action: PayloadAction<string[]>) {
      state.entityValue = action.payload
    },

    setCpdsValue(state, action: PayloadAction<string[]>) {
      state.cpds = action.payload
    },
    setProjectId(state, action: PayloadAction<string[]>) {
      state.project_id = action.payload
    },
    setClassificationValue(state, action: PayloadAction<string[]>) {
      state.classificationValue = action.payload
    },
    setProjectTypeValue(state, action: PayloadAction<string[]>) {
      state.projectType = action.payload
    },
    setSvp(state, action: PayloadAction<string[]>) {
      state.delivery_project_manager = action.payload
    },
    setProjectStatus(state, action: PayloadAction<string[]>) {
      state.projectStatus = action.payload
    },
    setLocationValue(state, action: PayloadAction<string[]>) {
      state.locationValue = action.payload
    },
    setContractTypeValue(state, action: PayloadAction<string[]>) {
      state.contractTypeValue = action.payload
    },
    setPortFolio(state, action: PayloadAction<string[]>) {
      state.portFolio = action.payload
    },
    setDesignManager(state, action: PayloadAction<string[]>) {
      state.designManager = action.payload
    },
    setExecutive(state, action: PayloadAction<string[]>) {
      state.executive = action.payload
    },
    setDeliveryDirector(state, action: PayloadAction<string[]>) {
      state.deliveryDirector = action.payload
    },
    setControl(state, action: PayloadAction<string[]>) {
      state.control = action.payload
    },
    setProcure(state, action: PayloadAction<string[]>) {
      state.procure = action.payload
    },
    setLabelValue(state, action: PayloadAction<string[]>) {
      state.labelValue = action.payload
    },
    setDesignProjectOwner(state, action: PayloadAction<string[]>) {
      state.designProjectOwner = action.payload
    },
    setDeviationStatus(state, action: PayloadAction<string[]>) {
      state.deviationStatusValue = action.payload
    },
    setUserFilterFields(state, action: PayloadAction<string[]>) {
      state.userFilterFields = action.payload
    },
    setOverallForecastedFinishDate(state, action: PayloadAction<string[]>) {
      state.overall_forecasted_finish_date = action.payload
    },
    resetFilters(state) {
      return initialState
    },
  },
})

export const {
  setDesignProjectOwner,
  setCpdsValue,
  setProjectId,
  setSearchQuery,
  setEntityValue,
  setCategoryValue,
  setClassificationValue,
  setProjectTypeValue,
  setProjectStatus,
  setLocationValue,
  setContractTypeValue,
  setPortFolio,
  setDesignManager,
  setExecutive,
  setDeliveryDirector,
  setControl,
  setProcure,
  setLabelValue,
  resetFilters,
  setSvp,
  setStartYear,
  setDeviationStatus,
  setOverallForecastedFinishDate,
  setUserFilterFields,
} = filtersSlice.actions

export default filtersSlice.reducer

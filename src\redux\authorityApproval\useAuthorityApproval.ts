import { useDispatch, useSelector } from 'react-redux'
import { getAuthorityApproval, addAuthorityApproval, updateAuthorityApproval, deleteAuthorityApproval } from '.'
import { IAuthorityApproval } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useAuthorityApproval = () => {
  const dispatch: AppDispatch = useDispatch()

  const getAuthorityApprovalStatus = useSelector(
    (state: RootState) => state.authorityApproval.getAuthorityApprovalStatus,
  )
  const authorities = useSelector((state: RootState) => state.authorityApproval.authorities)

  const getAuthorityApprovalApi = (data: { period: string; project_name?: string }) =>
    dispatch(getAuthorityApproval(data))
  const addAuthorityApprovalApi = (data: IAuthorityApproval) => dispatch(addAuthorityApproval(data))
  const updateAuthorityApprovalApi = (data: IAuthorityApproval) => dispatch(updateAuthorityApproval(data))
  const deleteAuthorityApprovalApi = (data: number) => dispatch(deleteAuthorityApproval(data))

  return {
    getAuthorityApprovalStatus,
    authorities,
    getAuthorityApprovalApi,
    addAuthorityApprovalApi,
    updateAuthorityApprovalApi,
    deleteAuthorityApprovalApi,
  }
}
export default useAuthorityApproval

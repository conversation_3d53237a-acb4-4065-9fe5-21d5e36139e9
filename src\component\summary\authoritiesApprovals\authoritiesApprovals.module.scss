@import '/styles/color.scss';

.accordionContainer {
  display: flex;
  gap: 10px;
  flex-direction: column;
  margin-top: 20px;
  // padding: 0 24px;

  .buttons {
    display: flex;
    justify-content: flex-end;
  }
}
.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.commercialSection {
  margin: 25px;
}

.accordion {
  background-color: #f9f9f9 !important;
  box-shadow: none;
  border-radius: 8px !important;
  margin-bottom: 28px;
  &::before {
    content: none;
  }
  .summary {
    // padding: 0 20px;
    padding-left: 0 !important;
    min-height: 56px;
    .content {
      margin: 16px 0 16px 0;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // &::after {
      //   content: "";
      //   position: absolute;
      //   left: 50px;
      //   right: 51px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   border: 1px solid $LIGHT_200;
      // }
      .header {
        display: flex;
        gap: 10px;
        align-items: center;
        padding: 8px 20px;
        background: #eeeeec;
        border-radius: 0 8px 8px 0;
        font-size: 16px;
        font-weight: 700;
        line-height: 18px;
        color: $DARK;
        white-space: nowrap;
        position: relative;
        z-index: 1;
        padding-right: 8px;
      }
      .editIcon {
        color: black !important;
        margin: 0 8px 0 auto;
      }
    }
  }
  .details {
    padding: 0;
    margin: 0 16px 25px;
  }
}
.container {
  padding: 0px 0px;
}

.accordionSection {
  margin: 16px 20px 24px 0;
}
.buttonPlus {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  border: 1px solid #444444;
  background: #444444;
  width: 20px;
  height: 20px;
}
.crudIcon {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  gap: 16px;
  margin-right: 8px;
}

.mainContainer {
  // padding-top: 10px;
  height: 100%;
  width: 100%;
}

.infoContainer {
  display: flex;
  align-items: center;
  gap: 5px;

  .infoIcon {
    cursor: pointer;
    margin-top: 5px;
  }
}

import React, { useState } from 'react'
import { useFormik } from 'formik'
import styles from './ProjectSubstage.module.scss'
import { IAddSubStatusesProps } from '../../subStatuses/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateProjectSubStage,
  useDeleteProjectSubStage,
  useGetProjectSubStage,
  useUpdateProjectSubStage,
} from '@/src/hooks/useProjectSubStage'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddSubStatuses: React.FC<IAddSubStatusesProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { addSubStatuses } = drawerStates
  //REACT-QUERY
  const { projectSubStages } = useGetProjectSubStage(addSubStatuses)
  // MUTATIONS
  const { mutate: addProjectSubStage } = useCreateProjectSubStage()
  const { mutate: deleteProjectSubStage } = useDeleteProjectSubStage()
  const { mutate: updateProjectSubStage } = useUpdateProjectSubStage()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { project_sub_stage: '' },
    onSubmit: async (values) => {
      const payload = { project_sub_stage: values.project_sub_stage }
      if (editIndex !== null) {
        updateProjectSubStage(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addProjectSubStage(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteProjectSubStage(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editPortfolioManagers: any = projectSubStages?.find((subStatus) => {
      //TODO
      return subStatus.id === id
    })

    formik.setValues({
      project_sub_stage: editPortfolioManagers?.project_sub_stage,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_sub_stage',
      header: 'project sub stage',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Sub Stage'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="project_sub_stage"
              labelText={'Project Sub Stage'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.project_sub_stage}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.project_sub_stage?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.project_sub_stage?.trim()}
              >
                {'+ Add Sub Stage'}
              </Button>
            )}
          </div>
        </form>
        {projectSubStages?.length >= 1 && (
          // <Table data={projectSubStages} columns={columns} />
          <TanStackTable
            rows={sortData(projectSubStages, 'project_sub_stage') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddSubStatuses

import React, { useEffect, useMemo, useState } from 'react'
import { Box, Tooltip } from '@mui/material'
import { parse } from 'date-fns'
import { FormikContext } from 'formik'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import { getMinDateForApprovalDate } from './helper'
import styles from './ProjectManagementCollapse.module.scss'
import ScopeDetailTable from './scopeDetailTable'
import { CollapseField } from '../../collapseField'
import PulseModel from '@/src/component/shared/pulseModel'
import UploadFileField from '@/src/component/shared/UploadFileField'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import { userType, whiteListedUserList } from '@/src/constant/enum'
import { getUrlOfUploadedFileByProjectManagement } from '@/src/helpers/helpers'
import { useGetMitigationRecoveryPlan } from '@/src/hooks/useMitigationRecoveryPlan'
import { useGetNstAdvancedProgresses } from '@/src/hooks/useNextStepsToAdvancedProgress'
import { useGetNonRecoverableDelayJustifications } from '@/src/hooks/useNonRecoverableDelayJustifications'
import { useGetOwningEntity } from '@/src/hooks/useOwningEntity'
import { useGetReasonForDelay } from '@/src/hooks/useReasonForDelay'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useCommercial from '@/src/redux/commercial/useCommercial'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IAttachments } from '@/src/redux/projectManagement/interface'
import useProjectManagement from '@/src/redux/projectManagement/useProjectManagement'
import useFile from '@/src/redux/uploadPicture/useFile'
import {
  IMitigationRecoveryPlans,
  INextStepsToAdvanceProgress,
  INonRecoverableDelayJustifications,
  IReasonForDelay,
} from '@/src/services/projects/interface'
import { getValue, getValues, populateDropdownOptions, prepareDropdownOptions } from '@/src/utils/arrayUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const ProjectManagementCollapse = ({ project, summaryEdit, formik, refetchProject, isProjectLoading }: any) => {
  //react-state
  const [openDatePickers, setOpenDatePickers] = useState({
    decree_date: false,
    commencement_date: false,
    decree_end_date: false,
    overall_planned_finish_date: false,
    overall_forecasted_finish_date: false,
    budget_uplift_submission_date: false,
    budget_uplift_last_submission_date: false,
    budget_uplift_approval_date: false,
    EOT_approval_date: false,
    EOT_submission_date: false,
    EOT_last_submission_date: false,
    submitted_eot_date: false,
    TIP_approval_date: false,
    TIP_submission_date: false,
    TIP_last_submission_date: false,
    scope_change_approval_date: false,
    scope_change_submission_date: false,
    scope_change_last_submission_date: false,
    bond_expiry_date: false,
  })
  //api-hooks
  const { commercials } = useCommercial()
  const { owningEntities } = useGetOwningEntity()
  const { currentUser } = useAuthorization()
  const { reasonForDelays } = useGetReasonForDelay()
  const { nonRecoverableDelayJustifications } = useGetNonRecoverableDelayJustifications()
  const { mitigationRecoveryPlans } = useGetMitigationRecoveryPlan()
  const { nextStepsToAdvancedProgresses } = useGetNstAdvancedProgresses()
  const { uploadDocProjectManagementsApi, deleteDocProjectManagementsApi, getDocumentApi } = useProjectManagement()
  const router = useRouter()
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false)
  const { currentPeriod } = useMasterPeriod()
  const { deleteFileApi } = useFile()

  useEffect(() => {
    if (
      (formik.values?.budget_uplift_status === 'Cancelled' ||
        formik.values?.budget_uplift_status === 'Approved by ADPIC') &&
      formik.values.budget_uplift_submitted === true
    ) {
      formik.setFieldValue('budget_uplift_submitted', false)
    }
  }, [formik.values.budget_uplift_status])

  const handleDatePickerOpen = (fieldName: string) => {
    setOpenDatePickers({
      decree_date: false,
      commencement_date: false,
      decree_end_date: false,
      overall_planned_finish_date: false,
      overall_forecasted_finish_date: false,
      budget_uplift_submission_date: false,
      budget_uplift_last_submission_date: false,
      EOT_last_submission_date: false,
      submitted_eot_date: false,
      budget_uplift_approval_date: false,
      EOT_approval_date: false,
      EOT_submission_date: false,
      TIP_approval_date: false,
      TIP_submission_date: false,
      TIP_last_submission_date: false,
      scope_change_approval_date: false,
      scope_change_submission_date: false,
      scope_change_last_submission_date: false,
      bond_expiry_date: false,
      [fieldName]: true, // Open the selected date picker
    })
  }

  const handleDatePickerClose = (fieldName: string) => {
    setOpenDatePickers((prevState) => ({
      ...prevState,
      [fieldName]: false,
    }))
  }

  const owningEntitiesOptions = useMemo(
    () => populateDropdownOptions(owningEntities, 'owning_entity'),
    [owningEntities],
  )

  const delayReasonsOption = useMemo(
    () => prepareDropdownOptions(reasonForDelays, 'reason_for_delay'),
    [reasonForDelays],
  )

  // TODO: Remove this commented code because now not needed, manage with 'delayReasonsOption'.
  // const delayReasonPairs = reasonForDelays?.map((item) => {
  //   return item.reason_for_delay_code
  //     ? { label: `${item.reason_for_delay_code} - ${item.reason_for_delay}`, value: item?.id }
  //     : { label: item.reason_for_delay, value: item?.id }
  // })

  // const filterDelayReasonPairs = useMemo(() => {
  //   if (!delayReasonPairs) return []
  //   if (!formik.values?.other_reason_for_delay) return delayReasonPairs
  //   return delayReasonPairs?.filter((item) => !formik.values?.other_reason_for_delay?.includes(item))
  // }, [delayReasonPairs, formik.values?.other_reason_for_delay])

  const mitigationRecoveryPlansOption = useMemo(
    () => prepareDropdownOptions(mitigationRecoveryPlans, 'mitigation_recovery_plan'),
    [mitigationRecoveryPlans],
  )

  const nextStepsToAdvancedProgressesOption = useMemo(
    () => prepareDropdownOptions(nextStepsToAdvancedProgresses, 'next_steps_to_advance_progress'),
    [mitigationRecoveryPlans, summaryEdit],
  )

  const nonRecoverableDelayJustificationsOption = useMemo(
    () => prepareDropdownOptions(nonRecoverableDelayJustifications, 'non_recoverable_delay_justification'),
    //() => populateDropdownOptions(nonRecoverableDelayJustifications, 'non_recoverable_delay_justification'),
    [mitigationRecoveryPlans, summaryEdit],
  )

  const isAdminOrSuperAdminOrWhiteListed =
    currentUser.user_type === userType.SUPER_USER ||
    currentUser.user_type === userType.SUPER_ADMIN ||
    whiteListedUserList.includes(currentUser.email)

  // const budgetUpliftSubmission = useMemo(() => {
  //   const todayDate = format(new Date(), 'yyyy-MM-dd')
  //   const date = formik.values.budget_uplift_last_submission_date
  //     ? (payloadDateFormate(formik.values.budget_uplift_last_submission_date) as string) ||
  //       format(formik.values.budget_uplift_last_submission_date, 'yyyy-MM-dd')
  //     : formik.values.budget_uplift_submission_date
  //       ? (payloadDateFormate(formik.values.budget_uplift_submission_date) as string) ||
  //         format(formik.values.budget_uplift_submission_date, 'yyyy-MM-dd')
  //       : null
  //   return date ? getDateDifference(date, todayDate) : 0
  // }, [formik.values.budget_uplift_submission_date, formik.values.budget_uplift_last_submission_date])

  const statusOptions = [
    'Pending With Entity',
    'Submitted to ADPIC',
    'Returned To AlDar',
    'Approved by ADPIC',
    'Cancelled',
  ]

  const upliftedBudgetValue = useMemo(() => {
    const currentBudget = commercials.find((item: any) => item.project_name === router.query.slug)?.budget

    const upliftedBudget = formik?.values?.uplifted_budget
    if (!upliftedBudget) return ''
    const formattedUplifted = formatNumberWithCommas(upliftedBudget.toLocaleString()) as string
    // const percentage = currentBudget
    //   ? Math.ceil(((Number(upliftedBudget) - Number(currentBudget)) * 100) / Number(currentBudget))
    //   : ''
    const percentage = currentBudget
      ? (((Number(upliftedBudget) - Number(currentBudget)) * 100) / Number(currentBudget)).toFixed(2)
      : ''

    return `${formattedUplifted}${percentage ? ` (${percentage}%)` : ''}`
    // return `${formattedUplifted}${percentage ? ` (${Math.abs(percentage)}%)` : ''}`
  }, [formik.values.uplifted_budget, commercials])

  const potentialBudgetUpliftValue = useMemo(() => {
    const currentBudget = commercials.find((item: any) => item.project_name === router.query.slug)?.budget

    const potentialBudgetUplift = formik?.values?.potential_budget_uplift_value
    if (!potentialBudgetUplift) return ''
    const formattedUplifted = formatNumberWithCommas(potentialBudgetUplift.toLocaleString()) as string
    const percentage = currentBudget
      ? (Math.abs((Number(potentialBudgetUplift) - Number(currentBudget)) * 100) / Number(currentBudget))?.toFixed(2)
      : ''

    return `${formattedUplifted}${percentage ? ` (${percentage}%)` : ''}`
  }, [formik.values.potential_budget_uplift_value, commercials])

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldName: string,
    onStart?: () => void,
    onFinish?: () => void,
  ) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      onStart?.() // Start loader

      const formData = new FormData()
      const { period, project_name, id } = formik.values || {}

      formData.append('period', period)
      formData.append('field_name', fieldName)
      // formData.append('field_type', 'Project Management')
      formData.append('project_name', project_name)
      formData.append('files', file)

      const response: Record<string, any> = await uploadDocProjectManagementsApi({ id, data: formData })

      if (!response?.payload?.success) {
        console.error('Upload failed:', response)
        errorToast(response?.payload?.response?.data?.message || 'Upload failed')
        return true
      }

      // ✅ Submit the rest of the form (trigger formik's onSubmit)
      await formik.submitForm()

      await refetchProject()

      successToast(`${fieldName} Attachment Successfully Uploaded`)
    } catch (error) {
      console.error('Error uploading file:', error)
      errorToast('An unexpected error occurred')
      return true
    } finally {
      onFinish?.() // Stop loader
    }

    return true
  }

  const handleDownload = async (
    upload_doc_attachment: {
      field_name: string
      file_path: string
    },
    formik: any,
    onStart: any,
    onFinish: any,
  ) => {
    onStart?.()
    try {
      const { period, project_name } = formik.values || {}
      const params = {
        name: upload_doc_attachment?.file_path,
        projectName: project_name,
        period: period,
      }
      const fetchImage: Record<string, any> = await getDocumentApi(params)
      let data = fetchImage.payload.success ? fetchImage.payload.data : []

      if (!data || data.length === 0) {
        errorToast('No attachment found')
        return
      }

      if (!upload_doc_attachment || !upload_doc_attachment?.file_path) {
        errorToast('No attachment found')
        return
      }

      // Open PDF directly in a new tab
      window.open(`https://docs.google.com/viewer?url=${encodeURIComponent(data.url)}`, '_blank')
      // window.open(data.url, '_blank')
    } catch (error) {
      console.error('Error uploading file:', error)
      errorToast('An unexpected error occurred')
      return true
    } finally {
      onFinish?.() // Stop loader
    }
  }

  const handleDelete = async (formik: any, attachments: IAttachments, onStart?: () => void, onFinish?: () => void) => {
    try {
      onStart?.() // Start loader

      const { period, project_name } = formik.values || {}
      const { id, media_type } = attachments || {}

      if (!id) {
        errorToast('Invalid attachment data')
        return true
      }

      //TODO: Old code commented
      /* const payload = {
        project_name,
        period,
        // field_type: 'Project Management',
        media_type: media_type,
        file_path: file_path,
      }

      const response: Record<string, any> = await deleteDocProjectManagementsApi(payload) */
      const response: Record<string, any> = await deleteFileApi(id)

      if (!response?.payload?.success) {
        console.error('Remove failed:', response)
        errorToast(response?.payload?.response?.data?.message || 'Remove failed')
        return true
      }

      refetchProject()

      successToast(`${media_type} Remove Attachment Successfully`)
    } catch (error) {
      console.error('Error uploading file:', error)
      errorToast('An unexpected error occurred')
      return true
    } finally {
      onFinish?.() // Stop loader
    }

    return true
  }

  return (
    <>
      <div onClick={(e) => e.stopPropagation()}>
        <div className={styles.fields}>
          <div className={`${styles.leftContent} ${summaryEdit ? styles.leftContentBorder : ''}`}>
            <section id="first-left-section">
              <CollapseField
                isOpen={openDatePickers.decree_date}
                handleDatePickerOpen={() => handleDatePickerOpen('decree_date')}
                handleDatePickerClose={() => handleDatePickerClose('decree_date')}
                label={`${formik.values.owning_entity === 'Aldar Development' ? 'Start Date' : 'Decree Date'}`}
                value={formik.values?.decree_date}
                summaryEdit={summaryEdit}
                formik={formik}
                fieldName="decree_date"
                isDate={true}
                labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
              />
              <CollapseField
                isOpen={openDatePickers.commencement_date}
                handleDatePickerOpen={() => handleDatePickerOpen('commencement_date')}
                handleDatePickerClose={() => handleDatePickerClose('commencement_date')}
                label={`Commencement Date`}
                value={formik.values?.commencement_date}
                summaryEdit={summaryEdit}
                formik={formik}
                fieldName="commencement_date"
                isDate={true}
                labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
              />
              <CollapseField
                isOpen={openDatePickers.decree_end_date}
                handleDatePickerOpen={() => handleDatePickerOpen('decree_end_date')}
                handleDatePickerClose={() => handleDatePickerClose('decree_end_date')}
                label={`${formik.values.owning_entity === 'Aldar Development' ? 'SPA Finish Date' : 'Decree End Date'}`}
                value={formik.values?.decree_end_date}
                summaryEdit={summaryEdit}
                formik={formik}
                fieldName="decree_end_date"
                isDate={true}
                labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
              />
              <Tooltip title={'Displays the highest Baseline Plan Finish date from the In Progress table.'} arrow>
                <div>
                  <CollapseField
                    isOpen={openDatePickers.overall_planned_finish_date}
                    handleDatePickerOpen={() => handleDatePickerOpen('overall_planned_finish_date')}
                    handleDatePickerClose={() => handleDatePickerClose('overall_planned_finish_date')}
                    label={`${formik.values.owning_entity === 'Aldar Development' ? 'Contract Finish Date' : 'Overall Planned Finish'}`}
                    value={formik.values?.overall_planned_finish_date}
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="overall_planned_finish_date"
                    isDate={true}
                    disabled={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    isShowBottomBorder={summaryEdit ? true : false}
                  />
                </div>
              </Tooltip>
              <Tooltip title={'Last updated value in the Forecast Finish field of the In Progress table.'} arrow>
                <div>
                  <CollapseField
                    isOpen={openDatePickers.overall_forecasted_finish_date}
                    handleDatePickerOpen={() => handleDatePickerOpen('overall_forecasted_finish_date')}
                    handleDatePickerClose={() => handleDatePickerClose('overall_forecasted_finish_date')}
                    label="Overall Forecasted Finish"
                    value={formik.values?.Overall_Forecasted_Finish_}
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="Overall_Forecasted_Finish_"
                    isDate={true}
                    disabled={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    isShowBottomBorder={summaryEdit ? true : false}
                  />
                </div>
              </Tooltip>
            </section>

            <Box
              sx={{
                height: '100%',
                '& .project-key-editor': {
                  maxHeight: `${
                    (document.getElementById('first-section')?.getBoundingClientRect?.().height ?? 0) -
                    (document.getElementById('first-left-section')?.getBoundingClientRect?.().height ?? 0) -
                    10
                  }px`,
                },
              }}
              component={'section'}
            >
              <CollapseField
                label="Project Key Highlights"
                value={formik.values.notes}
                summaryEdit={summaryEdit}
                labelStyle={styles.labelStyle}
                formik={formik}
                fieldName="notes"
                isTextEditor={true}
                dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                className={styles.richTextEditor}
                rootClassName={`${summaryEdit ? styles.edit_rich_root_project_key : styles.rich_root_project_key} project-key-editor`}
              />
            </Box>
          </div>
          <div className={styles.rightContent}>
            <section id="first-section">
              {/* <CollapseField
          label="LDC"
          options={consultantsOptions}
          isComboBox={true}
          value={formik.values?.ldc}
          summaryEdit={summaryEdit}
          formik={formik}
          fieldName="ldc"
        /> */}
              {/* <CollapseField
          label="Portfolio Manager"
          value={formik.values?.portfolio_manager}
          summaryEdit={summaryEdit}
          formik={formik}
          fieldName="portfolio_manager"
        /> */}
              {isAdminOrSuperAdminOrWhiteListed && (
                <>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox}>
                      <CollapseField
                        label="Subject to Rebaseline"
                        value={formik.values?.is_subject_to_rebaseline}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        fieldName="is_subject_to_rebaseline"
                        isCheckBox={true}
                        options={owningEntitiesOptions}
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle}  ${styles.borderBottomNone}`}
                      />
                    </div>
                    <div className={styles.mainContainer}>
                      <div className={` ${styles.singleElementContainer} ${styles.borderBottomNone}`}>
                        <CollapseField
                          label="Rebaseline Reason"
                          value={formik.values?.reason_rebaseline}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="reason_rebaseline"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle}  ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox} style={{ marginTop: `${summaryEdit ? '6px' : '0px'}` }}>
                      <CollapseField
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                        label="Transfer In Progress"
                        value={formik.values?.tip_submitted}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        isCheckBox={true}
                        fieldName="tip_submitted"
                      />
                      {formik.values?.tip_submitted && (
                        <UploadFileField
                          value={formik.values?.upload_doc}
                          attachments={getUrlOfUploadedFileByProjectManagement(
                            formik.values?.MediaFiles,
                            'tip_submission',
                          )}
                          onUpload={(event: any, onStart: any, onFinish: any) =>
                            handleFileUpload(event, 'tip_submission', onStart, onFinish)
                          }
                          onDownload={(attachments: any, onStart: any, onFinish: any) =>
                            handleDownload(attachments, formik, onStart, onFinish)
                          }
                          onDelete={(attachments: IAttachments, onStart: any, onFinish: any) => {
                            handleDelete(formik, attachments, onStart, onFinish)
                          }}
                          isProjectLoading={isProjectLoading}
                        />
                      )}
                    </div>
                    <div className={styles.mainContainer}>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer} ${summaryEdit && !formik.values?.tip_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.TIP_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('TIP_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('TIP_submission_date')}
                          label="First Submission Date"
                          value={formik.values?.tip_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="tip_submission_date"
                          isDate={true}
                          disabled={formik.values?.tip_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.TIP_approval_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('TIP_approval_date')}
                          handleDatePickerClose={() => handleDatePickerClose('TIP_approval_date')}
                          label="Approval Date"
                          value={formik.values?.tip_approval_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="tip_approval_date"
                          isDate={true}
                          disabled={
                            formik.values?.tip_submitted && project?.tip_submission_date !== null ? false : true
                          }
                          minDate={getMinDateForApprovalDate(
                            formik.values.tip_submission_date,
                            formik.values.tip_last_submission_date,
                          )}
                          disableWeekends={true}
                        />
                      </div>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${styles.borderBottomNone} ${summaryEdit && !formik.values?.tip_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          isOpen={openDatePickers.TIP_last_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('TIP_last_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('TIP_last_submission_date')}
                          label="Last Submission Date"
                          value={formik.values?.tip_last_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="tip_last_submission_date"
                          isDate={true}
                          minDate={
                            typeof formik.values.tip_submission_date === 'string'
                              ? parse(formik.values.tip_submission_date, 'dd-MM-yyyy', new Date())
                              : new Date(formik.values.tip_submission_date)
                          }
                          disabled={formik.values?.tip_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          label="Notes"
                          value={formik.values?.tip_notes}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="tip_notes"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          disabled={formik.values?.tip_submitted ? false : true}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox} style={{ marginTop: `${summaryEdit ? '6px' : '0px'}` }}>
                      <CollapseField
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                        label="EOT Submission"
                        value={formik.values?.eot_submitted}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        isCheckBox={true}
                        fieldName="eot_submitted"
                      />
                      {formik.values?.eot_submitted && (
                        <UploadFileField
                          value={formik.values?.upload_doc}
                          attachments={getUrlOfUploadedFileByProjectManagement(
                            formik.values?.MediaFiles,
                            'eot_submission',
                          )}
                          onUpload={(event: any, onStart: any, onFinish: any) =>
                            handleFileUpload(event, 'eot_submission', onStart, onFinish)
                          }
                          onDownload={(attachments: any, onStart: any, onFinish: any) =>
                            handleDownload(attachments, formik, onStart, onFinish)
                          }
                          onDelete={(attachments: IAttachments, onStart: any, onFinish: any) =>
                            handleDelete(formik, attachments, onStart, onFinish)
                          }
                          isProjectLoading={isProjectLoading}
                        />
                      )}
                    </div>
                    <div className={styles.mainContainer}>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${summaryEdit && !formik.values?.eot_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.EOT_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('EOT_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('EOT_submission_date')}
                          label="First Submission Date"
                          value={formik.values?.eot_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="eot_submission_date"
                          isDate={true}
                          disabled={formik.values?.eot_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.EOT_approval_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('EOT_approval_date')}
                          handleDatePickerClose={() => handleDatePickerClose('EOT_approval_date')}
                          label="Approval Date"
                          value={formik.values?.eot_approval_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="eot_approval_date"
                          isDate={true}
                          disabled={
                            formik.values?.eot_submitted && project?.eot_submission_date !== null ? false : true
                          }
                          minDate={getMinDateForApprovalDate(
                            formik.values.eot_submission_date,
                            formik.values.eot_last_submission_date,
                          )}
                          disableWeekends={true}
                        />
                      </div>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${styles.borderBottomNone} ${summaryEdit && !formik.values?.tip_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          isOpen={openDatePickers.EOT_last_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('EOT_last_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('EOT_last_submission_date')}
                          label="Last Submission Date"
                          value={formik.values?.eot_last_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="eot_last_submission_date"
                          isDate={true}
                          minDate={
                            typeof formik.values.eot_submission_date === 'string'
                              ? parse(formik.values.eot_submission_date, 'dd-MM-yyyy', new Date())
                              : new Date(formik.values.eot_submission_date)
                          }
                          disabled={formik.values?.eot_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.submitted_eot_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('submitted_eot_date')}
                          handleDatePickerClose={() => handleDatePickerClose('submitted_eot_date')}
                          label="Submitted EOT date"
                          value={formik.values?.submitted_eot_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="submitted_eot_date"
                          isDate={true}
                          disabled={formik.values?.eot_submitted ? false : true}
                          disableWeekends={true}
                        />
                      </div>
                      <div
                        className={`${styles.singleElementContainer} ${summaryEdit && !formik.values?.potential_budget_uplift_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          label="Notes"
                          value={formik.values?.eot_notes}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="eot_notes"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          disabled={formik.values?.eot_submitted ? false : true}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox} style={{ marginTop: `${summaryEdit ? '6px' : '0px'}` }}>
                      <CollapseField
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                        label="Scope Change"
                        value={formik.values?.scope_change}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        isCheckBox={true}
                        fieldName="scope_change"
                      />
                      {formik.values?.scope_change && (
                        <UploadFileField
                          value={formik.values?.upload_doc}
                          attachments={getUrlOfUploadedFileByProjectManagement(
                            formik.values?.MediaFiles,
                            'scope_change',
                          )}
                          onUpload={(event: any, onStart: any, onFinish: any) =>
                            handleFileUpload(event, 'scope_change', onStart, onFinish)
                          }
                          onDownload={(attachments: any, onStart: any, onFinish: any) =>
                            handleDownload(attachments, formik, onStart, onFinish)
                          }
                          onDelete={(attachments: IAttachments, onStart: any, onFinish: any) =>
                            handleDelete(formik, attachments, onStart, onFinish)
                          }
                          isProjectLoading={isProjectLoading}
                        />
                      )}
                    </div>
                    <div className={styles.mainContainer}>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${summaryEdit && !formik.values?.scope_change ? styles.containerBorderBottom : ''} `}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.scope_change_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('scope_change_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('scope_change_submission_date')}
                          label="First Submission Date"
                          value={formik.values?.scope_change_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="scope_change_submission_date"
                          isDate={true}
                          disabled={formik.values?.scope_change ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.scope_change_approval_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('scope_change_approval_date')}
                          handleDatePickerClose={() => handleDatePickerClose('scope_change_approval_date')}
                          label="Approval Date"
                          value={formik.values?.scope_change_approval_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="scope_change_approval_date"
                          isDate={true}
                          disabled={
                            formik.values?.scope_change && project?.scope_change_submission_date !== null ? false : true
                          }
                          minDate={getMinDateForApprovalDate(
                            formik.values.scope_change_submission_date,
                            formik.values.scope_change_last_submission_date,
                          )}
                          disableWeekends={true}
                        />
                      </div>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${styles.borderBottomNone} ${summaryEdit && !formik.values?.tip_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          isOpen={openDatePickers.scope_change_last_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('scope_change_last_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('scope_change_last_submission_date')}
                          label="Last Submission Date"
                          value={formik.values?.scope_change_last_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="scope_change_last_submission_date"
                          isDate={true}
                          minDate={
                            typeof formik.values.scope_change_submission_date === 'string'
                              ? parse(formik.values.scope_change_submission_date, 'dd-MM-yyyy', new Date())
                              : new Date(formik.values.scope_change_submission_date)
                          }
                          disabled={formik.values?.scope_change ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          label="Notes"
                          value={formik.values?.scope_change_notes}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="scope_change_notes"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          disabled={formik.values?.scope_change ? false : true}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox} style={{ marginTop: `${summaryEdit ? '6px' : '0px'}` }}>
                      <CollapseField
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                        label="Budget Uplift Submission"
                        value={formik.values?.budget_uplift_submitted}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        isCheckBox={true}
                        fieldName="budget_uplift_submitted"
                        onCheckBoxChange={(val: boolean) => {
                          if (
                            (formik.values?.budget_uplift_status === 'Cancelled' ||
                              formik.values?.budget_uplift_status === 'Approved by ADPIC') &&
                            val === true
                          ) {
                            setShowConfirmationPopup(true)
                            formik.setFieldValue('budget_uplift_submitted', false)
                          } else {
                            formik.setFieldValue('budget_uplift_submitted', val)
                          }
                        }}
                      />
                      {formik.values?.budget_uplift_submitted && (
                        <UploadFileField
                          value={formik.values?.upload_doc}
                          attachments={getUrlOfUploadedFileByProjectManagement(
                            formik.values?.MediaFiles,
                            'budget_uplift_submission',
                          )}
                          onUpload={(event: any, onStart: any, onFinish: any) =>
                            handleFileUpload(event, 'budget_uplift_submission', onStart, onFinish)
                          }
                          onDownload={(attachments: any, onStart: any, onFinish: any) =>
                            handleDownload(attachments, formik, onStart, onFinish)
                          }
                          onDelete={(attachments: IAttachments, onStart: any, onFinish: any) =>
                            handleDelete(formik, attachments, onStart, onFinish)
                          }
                          isProjectLoading={isProjectLoading}
                        />
                      )}
                    </div>
                    <div className={styles.mainContainer}>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer}  ${summaryEdit && !formik.values?.budget_uplift_submitted ? styles.containerBorderBottom : ''}`}
                        style={{ paddingTop: '2px' }}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle} ${summaryEdit && styles.borderBottomNone}`}
                          isOpen={openDatePickers.budget_uplift_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('budget_uplift_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('budget_uplift_submission_date')}
                          label="First Submission Date"
                          value={formik.values?.budget_uplift_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="budget_uplift_submission_date"
                          isDate={true}
                          disabled={formik.values?.budget_uplift_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.budget_uplift_approval_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('budget_uplift_approval_date')}
                          handleDatePickerClose={() => handleDatePickerClose('budget_uplift_approval_date')}
                          label="Approval Date"
                          value={formik.values?.budget_uplift_approval_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="budget_uplift_approval_date"
                          isDate={true}
                          disabled={
                            formik.values?.budget_uplift_submitted && project?.budget_uplift_submission_date !== null
                              ? false
                              : true
                          }
                          minDate={getMinDateForApprovalDate(
                            formik.values.budget_uplift_submission_date,
                            formik.values.budget_uplift_last_submission_date,
                          )}
                          disableWeekends={true}
                        />
                      </div>
                      <div
                        className={`${summaryEdit ? styles.editTwoElementContainer : styles.twoElementContainer} ${summaryEdit && !formik.values?.budget_uplift_submitted ? styles.containerBorderBottom : ''} `}
                        style={{ paddingTop: '5px' }}
                      >
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          isOpen={openDatePickers.budget_uplift_last_submission_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('budget_uplift_last_submission_date')}
                          handleDatePickerClose={() => handleDatePickerClose('budget_uplift_last_submission_date')}
                          label="Last Submission Date"
                          value={formik.values?.budget_uplift_last_submission_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="budget_uplift_last_submission_date"
                          isDate={true}
                          minDate={
                            typeof formik.values.budget_uplift_submission_date === 'string'
                              ? parse(formik.values.budget_uplift_submission_date, 'dd-MM-yyyy', new Date())
                              : new Date(formik.values.budget_uplift_submission_date)
                          }
                          disabled={formik.values?.budget_uplift_submitted ? false : true}
                          disableWeekends={true}
                        />
                        {/* //TODO : Change key type */}
                        <CollapseField
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          dateWidth={'140px'}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          isOpen={openDatePickers.bond_expiry_date}
                          handleDatePickerOpen={() => handleDatePickerOpen('bond_expiry_date')}
                          handleDatePickerClose={() => handleDatePickerClose('bond_expiry_date')}
                          label="Bond Expiry Date"
                          value={formik.values?.bond_expiry_date}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="bond_expiry_date"
                          isDate={true}
                          disabled={formik.values?.budget_uplift_submitted ? false : true}
                          disableWeekends={true}
                        />
                        <CollapseField
                          label="Status"
                          isComboBox={true}
                          labelStyle={`${styles.labelStyle}  ${summaryEdit && styles.borderBottomNone}`}
                          value={formik.values.budget_uplift_status}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          options={statusOptions}
                          fieldName="budget_uplift_status"
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                        />
                      </div>
                      <div
                        className={`${styles.twoElementContainer} ${styles.borderBottomNone}`}
                        style={{ paddingTop: '5px' }}
                      >
                        <CollapseField
                          label="New Budget"
                          value={upliftedBudgetValue}
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          isNumberField={true}
                          fieldName="uplifted_budget"
                          maxLength={13}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          disabled={formik.values?.budget_uplift_submitted ? false : true}
                          className={styles.numberField}
                        />
                        <CollapseField
                          label="Notes/Remark"
                          value={formik.values?.budget_uplift_submission_notes}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="budget_uplift_submission_notes"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${styles.borderBottomNone}`}
                          disabled={formik.values?.budget_uplift_submitted ? false : true}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.budgetUplift}>
                    <div className={styles.groupCheckbox}>
                      <CollapseField
                        valueStyle={styles.budgetValue}
                        labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                        dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                        label="Potential Budget Uplift"
                        value={formik.values?.potential_budget_uplift_submitted}
                        summaryEdit={summaryEdit}
                        formik={formik}
                        isCheckBox={true}
                        fieldName="potential_budget_uplift_submitted"
                      />
                      <ScopeDetailTable edit={summaryEdit} />
                    </div>
                    <div className={styles.mainContainer}>
                      <div
                        className={`${styles.singleElementContainer} ${summaryEdit && !formik.values?.potential_budget_uplift_submitted ? styles.containerBorderBottom : ''}`}
                      >
                        <CollapseField
                          label="Potential Budget"
                          value={potentialBudgetUpliftValue}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          isNumberField={true}
                          maxLength={13}
                          fieldName="potential_budget_uplift_value"
                          labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                          dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle} ${summaryEdit && styles.borderBottomNone}`}
                          disabled={formik.values?.potential_budget_uplift_submitted ? false : true}
                          className={styles.numberField}
                        />
                      </div>
                      <div className={styles.singleElementContainer} style={{ paddingTop: '5px' }}>
                        <CollapseField
                          label="Potential Uplift Reason"
                          value={formik.values?.potential_budget_uplift_reason}
                          summaryEdit={summaryEdit}
                          formik={formik}
                          fieldName="potential_budget_uplift_reason"
                          labelStyle={`${styles.labelStyle} ${styles.borderBottomNone}`}
                          dotStyle={`${styles.dotStyle} ${styles.borderBottomNone}`}
                          valueStyle={`${styles.valueStyle}  ${styles.borderBottomNone}`}
                          disabled={formik.values?.potential_budget_uplift_submitted ? false : true}
                          isTextArea={summaryEdit ? true : false}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </section>
          </div>
        </div>
        {isAdminOrSuperAdminOrWhiteListed && (
          <>
            <div className={styles.fields}>
              <Box
                className={`${styles.leftContent} ${summaryEdit ? styles.leftContentBorder : ''}`}
                sx={(theme) => ({
                  maxHeight: `${document.getElementById('third-section')?.getBoundingClientRect()?.height}px`,
                  '& .overlay-editor': {
                    paddingTop: 0,
                  },
                  [theme.breakpoints.up('md')]: {
                    '& .overlay-editor': {
                      paddingBottom: 0,
                    },
                  },
                })}
              >
                <CollapseField
                  label="Overall Delay Status"
                  value={formik.values?.delay_reason}
                  summaryEdit={summaryEdit}
                  labelStyle={styles.labelStyle}
                  formik={formik}
                  fieldName="delay_reason"
                  // isTextArea={true}
                  isTextEditor={true}
                  dotStyle={`${styles.dotStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                  className={`${styles.richTextEditor}`}
                  rootClassName={`${summaryEdit ? styles.edit_rich_root : styles.rich_root} overlay-editor`}
                />
              </Box>
              <div className={`${styles.leftContent} ${summaryEdit && styles.lowerRightContent}`}>
                <section id="third-section">
                  <CollapseField
                    label="Reason Category"
                    value={
                      summaryEdit
                        ? formik.values?.primary_reason_for_delay_ids
                        : formik.values?.PrimaryReasonsForDelay?.map(
                            (item: IReasonForDelay) => item?.reason_for_delay,
                          )?.join(', ')
                    }
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="primary_reason_for_delay_ids"
                    options={delayReasonsOption}
                    isComboBox={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                    clearIcon={true}
                  />
                  <CollapseField
                    label="Minor Reason(s)"
                    // value={formik.values?.other_reason_for_delay?.replace(/,/g, ',  ')}
                    value={
                      summaryEdit
                        ? formik.values?.reason_for_delay_ids
                        : formik.values?.ReasonsForDelay?.map((item: IReasonForDelay) => item?.reason_for_delay)?.join(
                            ', ',
                          )
                    }
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="reason_for_delay_ids"
                    options={delayReasonsOption?.filter(
                      (res) => res !== formik.values?.ReasonsForDelay?.map((item: any) => item?.reason_for_delay),
                    )}
                    isMultiSelect={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                    className={styles.multiselect}
                  />
                  <CollapseField
                    label="Mitigation Recovery Plan"
                    value={
                      summaryEdit
                        ? formik.values?.mitigation_recovery_plan_ids
                        : formik.values?.MitigationRecoveryPlans?.map(
                            (item: IMitigationRecoveryPlans) => item?.mitigation_recovery_plan,
                          )?.join(', ')
                    }
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="mitigation_recovery_plan_ids"
                    options={mitigationRecoveryPlansOption}
                    isMultiSelect={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                    className={styles.multiselect}
                  />
                  <CollapseField
                    label="Non-Recoverable Delay Justification"
                    // value={formik.values?.non_recoverable_delay_justification?.replace(/,/g, ',  ')}
                    value={
                      summaryEdit
                        ? formik.values?.non_recoverable_delay_justification_ids
                        : formik.values?.NonRecoverableDelayJustifications?.map(
                            (item: INonRecoverableDelayJustifications) => item?.non_recoverable_delay_justification,
                          )?.join(', ')
                    }
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="non_recoverable_delay_justification_ids"
                    options={nonRecoverableDelayJustificationsOption}
                    isMultiSelect={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                    className={styles.multiselect}
                  />
                  <CollapseField
                    label="Next Steps To Advance Progress"
                    // value={formik.values?.next_steps_to_advance_progress?.replace(/,/g, ',  ')}
                    value={
                      summaryEdit
                        ? formik.values?.next_steps_to_advance_progress_ids
                        : formik.values?.NextStepsToAdvanceProgress?.map(
                            (item: INextStepsToAdvanceProgress) => item?.next_steps_to_advance_progress,
                          ).join(', ')
                    }
                    summaryEdit={summaryEdit}
                    formik={formik}
                    fieldName="next_steps_to_advance_progress_ids"
                    options={nextStepsToAdvancedProgressesOption}
                    isMultiSelect={true}
                    labelStyle={`${styles.labelStyle} ${summaryEdit ? styles.borderBottomNone : ''}`}
                    dotStyle={`${styles.dotStyle} ${summaryEdit && styles.borderBottomNone}`}
                    className={styles.multiselect}
                  />
                </section>
              </div>
            </div>
          </>
        )}
      </div>
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={showConfirmationPopup}
        onClose={() => setShowConfirmationPopup(false)}
        content={
          <ValidationModel
            messages={[
              `You cannot change the value of 'Budget Uplift Submission' when the status is either 'Cancelled' or 'Approved by ADPIC'.`,
            ]}
            onClose={() => setShowConfirmationPopup(false)}
          />
        }
      />
    </>
  )
}

export default ProjectManagementCollapse

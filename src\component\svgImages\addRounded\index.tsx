import React, { ReactElement, forwardRef } from 'react'

interface AddRoundedIconProps {
  className?: string
  fill?: string
  onClick?: (e: any) => void
}

const AddRoundedIcon: React.FC<AddRoundedIconProps> = forwardRef<SVGSVGElement, AddRoundedIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <g clipPath="url(#clip0_1445_3947)">
          <path
            d="M8.50065 6.00065C8.50065 5.72451 8.27679 5.50065 8.00065 5.50065C7.72451 5.50065 7.50065 5.72451 7.50065 6.00065L7.50065 7.50067H6.00065C5.72451 7.50067 5.50065 7.72453 5.50065 8.00067C5.50065 8.27681 5.72451 8.50067 6.00065 8.50067H7.50065V10.0007C7.50065 10.2768 7.72451 10.5007 8.00065 10.5007C8.27679 10.5007 8.50065 10.2768 8.50065 10.0007L8.50065 8.50067H10.0007C10.2768 8.50067 10.5007 8.27681 10.5007 8.00067C10.5007 7.72452 10.2768 7.50067 10.0007 7.50067H8.50065V6.00065Z"
            fill={props.fill || '#EAEAEA'}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.00065 0.833984C4.04261 0.833984 0.833984 4.04261 0.833984 8.00065C0.833984 11.9587 4.04261 15.1673 8.00065 15.1673C11.9587 15.1673 15.1673 11.9587 15.1673 8.00065C15.1673 4.04261 11.9587 0.833984 8.00065 0.833984ZM1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065Z"
            fill={props.fill || '#EAEAEA'}
          />
        </g>
        <defs>
          <clipPath id="clip0_1445_3947">
            <rect width="16" height="16" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  },
)
AddRoundedIcon.displayName = 'AddRoundedIcon'

export default AddRoundedIcon

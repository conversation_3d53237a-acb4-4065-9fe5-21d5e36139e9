import { FormikProps } from 'formik'

export interface ISummaryTab {
  expanded: any
}

export interface IDiscardModel {
  onClose: () => void
  onDiscard: () => void
}

export interface ISustainabilityCollapse {
  summaryEdit: boolean
  formik: FormikProps<any>
}

export interface IProjectManagementCollapse {
  summaryEdit: boolean
  formik: FormikProps<any>
}

export interface IProjectDetailsCollapse {
  summaryEdit: boolean
  formik: FormikProps<any>
}

export interface IHealthSafetyCollapse {
  summaryEdit: boolean
  formik: FormikProps<any>
}

export interface IValidationState {
  open: boolean
  message: string[]
}

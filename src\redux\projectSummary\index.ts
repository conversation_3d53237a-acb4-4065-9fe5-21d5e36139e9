import { createSlice } from '@reduxjs/toolkit'
import { IProjectStates } from './interface'
import { StatusEnum } from '../types'

const initialState: IProjectStates = {
  localFilteredData: [],
  filterData: [],
  recentProject: null,
  projectFilter: [
    { field: 'project', value: '' },
    { field: 'owningEntity', value: [] },
    { field: 'dof_number', value: '' },
    { field: 'project_type', value: [] },
    { field: 'project_status', value: [] },
    { field: 'project_classification', value: [] },
    { field: 'location', value: [] },
  ],
  projectFilterValue: [],
  columnVisibilities: {},
  columnsWidth: {},
}

export const projectSummarySlice = createSlice({
  name: 'projectSummary',
  initialState,
  reducers: {
    setFilterData(state, action) {
      state.filterData = action.payload
    },
    setRecentProjectData(state, action) {
      state.recentProject = {
        ...state.recentProject,
        ...action.payload,
      }
    },
    setProjectFilter(state, action) {
      state.projectFilter = action.payload
    },
    setProjectFilterValue(state, action) {
      state.projectFilterValue = action.payload
    },
    setLocalFilteredData(state, action) {
      state.localFilteredData = action.payload
    },
    setColumnVisibilities(state, action) {
      state.columnVisibilities = action.payload
    },
    setColumnsWidth(state, action) {
      state.columnsWidth = action.payload
    },
  },
})

export const {
  setFilterData,
  setRecentProjectData,
  setProjectFilter,
  setProjectFilterValue,
  setLocalFilteredData,
  setColumnVisibilities,
  setColumnsWidth,
} = projectSummarySlice.actions

export default projectSummarySlice.reducer

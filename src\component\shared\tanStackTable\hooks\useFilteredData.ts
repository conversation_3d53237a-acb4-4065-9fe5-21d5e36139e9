import { useMemo } from 'react'
import { applyFilters } from '../helper'
import { IRowData } from '../interface'
import { FilterValue } from '@/src/component/customCells/headerCell/interface'

export const useFilteredData = (
  rows: IRowData[],
  filterValue: FilterValue[],
  visibleColumns: any[],
  setFilterRows?: (rows: IRowData[]) => void,
) => {
  const tableRows = rows ?? []
  const filteredData = useMemo(() => applyFilters(tableRows, filterValue, visibleColumns), [tableRows, filterValue])

  if (setFilterRows) {
    setFilterRows(filteredData)
  }

  return filteredData
}

import { ReactElement, useMemo } from 'react'
import { SxProps, Theme } from '@mui/material/styles'
import styles from './NumberField.module.scss'
import KeyBoardArrowDown from '../../svgImages/keyBoardArrowDown'
import KeyBoardArrowUp from '../../svgImages/keyBoardArrowUP'
import TextInputField from '../textInputField/index'

interface NumberFieldProps {
  value: number | string | null
  onChange: (value: number | string | null) => void
  labelText?: string
  placeholder?: string
  className?: string
  name?: string
  required?: boolean
  infoIcon?: boolean
  disabled?: boolean
  error?: boolean
  helperText?: string
  onBlur?: any
  min?: number | null
  max?: number | null
  endAdornment?: any
  format?: string
  sx?: SxProps<Theme>
  isUpAndDowns?: boolean
  minusValue?: boolean
  maxLength?: number
}

const NumberInputField: React.FC<NumberFieldProps> = ({
  value,
  onChange,
  labelText = '',
  placeholder = '',
  className = '',
  name = '',
  required,
  infoIcon,
  disabled = false,
  error = false,
  helperText,
  onBlur,
  min,
  max,
  endAdornment,
  format = '',
  sx,
  isUpAndDowns = true,
  minusValue = false,
  maxLength,
  ...props
}): ReactElement => {
  const inputProps = useMemo(() => {
    const props: any = {}

    if (format && format !== 'comma-separated') {
      props.min = 0
      props.pattern = '[0-9.]*'
    }

    if (typeof maxLength === 'number') {
      props.maxLength = maxLength
    }

    return props
  }, [format, maxLength])

  const REGEX = useMemo(() => {
    return format === 'comma-separated'
      ? /^[0-9,-]*(\.[0-9]{0,2})?$/
      : minusValue
        ? /^-?[0-9]*(\.[0-9]{0,2})?$/ // Allow negative numbers
        : /^[0-9]*(\.[0-9]{0,2})?$/
  }, [format, minusValue])

  const checkMinValue = (value: number) => {
    const minValue = min ? min : 0
    return Number(value) < Number(minValue)
  }

  const checkMaxValue = (value: number) => {
    const maxValue = max ? max : 0.9
    return Number(value) > Number(maxValue)
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value
    const isValidInput = REGEX.test(inputValue)
    if (isValidInput) {
      let updatedValue: any = inputValue
      let newValue: any = inputValue
      if (format && format === 'comma-separated') {
        // Remove commas before converting to number
        newValue = inputValue.replace(/,/g, '')
      }
      const shouldRoundUp = newValue % 1 > 0.5
      const value = shouldRoundUp ? Math.ceil(newValue) : Math.floor(newValue)
      updatedValue = newValue
      if (max && checkMaxValue(updatedValue)) return onChange(value)
      onChange(updatedValue)
    }
  }

  const handleIncrease = (e: any) => {
    if (disabled) return
    e.stopPropagation()
    const newValue = format && format === 'comma-separated' ? value?.toString().replace(/,/g, '') : value
    const val = value ? Number(newValue) + 1 : min ? min : 1
    if (!checkMinValue(val) && max && checkMaxValue(val)) return
    return onChange(val)
  }

  const handleDecrease = () => {
    if (disabled) return
    const minVal = min ? min : 0
    const newValue = format && format === 'comma-separated' ? value?.toString().replace(/,/g, '') : value
    const val =
      value && Number(newValue) > 0 ? (Number(newValue) > 0 && Number(newValue) < 0.9 ? 0 : Number(newValue) - 1) : 0
    if (!checkMinValue(val) && max && checkMaxValue(val)) return
    return onChange(val > minVal ? Number(val?.toFixed(2)) : minVal)
  }

  const onFocusOut = (event: any) => {
    const newValue = format && format === 'comma-separated' ? value?.toString().replace(/,/g, '') : value
    if (newValue && min && checkMinValue(newValue as number)) return onChange(min as number)
    onBlur && onBlur(event)
  }

  return (
    <TextInputField
      {...props}
      maxLength={maxLength}
      autoComplete="off"
      variant="outlined"
      type="text"
      name={name}
      className={`${styles.numberField} ${className}`}
      labelText={labelText}
      placeholder={placeholder}
      value={value ?? ''}
      onChange={handleInputChange}
      onBlur={onFocusOut}
      error={error}
      helperText={helperText}
      disabled={disabled}
      required={required}
      infoIcon={infoIcon}
      onKeyDown={(e) => {
        if (e.key === 'ArrowUp') {
          handleIncrease(e)
        } else if (e.key === 'ArrowDown') {
          handleDecrease()
        }
      }}
      sx={sx}
      InputProps={{
        inputProps: inputProps,
        endAdornment: (
          <>
            {endAdornment ? <div className={styles.endAdornment}>{endAdornment}</div> : ''}
            {isUpAndDowns && (
              <div className={styles.iconContainer}>
                <KeyBoardArrowUp onClick={handleIncrease} />
                <KeyBoardArrowDown onClick={handleDecrease} />
              </div>
            )}
          </>
        ),
      }}
    />
  )
}

export default NumberInputField

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterPortfolioManager,
  deletePortFolioManager,
  getMasterPortfolioManager,
  updateMasterPortfolioManager,
} from '../services/portfolioManager'
import { IGetMasterPortfolioManagersResponse, IUpdatePortfolioManager } from '../services/portfolioManager/interface'

export const QUERY_PORTFOLIO_MANAGER = 'portfolio-manager'

/**
 * Hook to fetch portfolio managers
 * @param enabled - Enables or disables the query
 */
export const useGetPortfolioManagers = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterPortfolioManagersResponse>({
    queryKey: [QUERY_PORTFOLIO_MANAGER],
    queryFn: getMasterPortfolioManager,
    enabled,
  })

  return {
    portfolioManagers: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a portfolio manager
 */
export const useCreatePortfolioManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterPortfolioManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PORTFOLIO_MANAGER] })
    },
  })
}

/**
 * Hook to delete a portfolio manager
 */
export const useDeletePortfolioManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deletePortFolioManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PORTFOLIO_MANAGER] })
    },
  })
}

/**
 * Hook to update a portfolio manager
 */
export const useUpdatePortfolioManager = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterPortfolioManager,
    onMutate: async (updatedData: IUpdatePortfolioManager) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_PORTFOLIO_MANAGER] })
      const previousData = queryClient.getQueryData<IGetMasterPortfolioManagersResponse>([QUERY_PORTFOLIO_MANAGER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterPortfolioManagersResponse>([QUERY_PORTFOLIO_MANAGER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, portfolio_manager: updatedData.portfolio_manager } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_PORTFOLIO_MANAGER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_PORTFOLIO_MANAGER] })
    },
  })
}

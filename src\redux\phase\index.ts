import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterPhaseCategoryResponse, IGetMasterPhaseResponse, IPhaseState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IPhaseState = {
  getMasterPhaseStatus: StatusEnum.Idle,
  getMasterPhaseCategoryStatus: StatusEnum.Idle,
  addMasterPhaseStatus: StatusEnum.Idle,
  addMasterPhaseCategoryStatus: StatusEnum.Idle,
  deleteMasterPhaseStatus: StatusEnum.Idle,
  updateMasterPhaseStatus: StatusEnum.Idle,
  phases: [],
  uniquePhaseCategories: [],
  phase: {
    id: 0,
    project_phase: '',
  },
  localPhase: [],
  localSubStage: [],
}

export const getMasterPhase = createAsyncThunk('/get-master-project-phase', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-project-phase`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterPhase = createAsyncThunk(
  '/add-master-project-phase',
  async (data: { project_phase: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-project-phase`, {
        project_phase: data.project_phase,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateMasterPhase = createAsyncThunk(
  '/update-master-project-phase',
  async (data: { id: number; project_phase: string }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/master-project-phase/${data.id}`, {
        project_phase: data.project_phase,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteMasterPhase = createAsyncThunk('/delete-master-project-phase', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/master-project-phase/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const getMasterPhaseCategory = createAsyncThunk(
  '/get-master-project-to-project-phase',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    try {
      const response = await ApiGetNoAuth(
        `/master-project-to-project-phase?period=${data.period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addMasterPhaseCategory = createAsyncThunk(
  '/add-master-project-to-project-phase',
  async (data: { period: string; project_name?: string; phase: string | null; category: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-project-to-project-phase`, {
        ...data,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const phaseSlice = createSlice({
  name: 'phase',
  initialState,
  reducers: {
    setLocalPhase(state, action) {
      state.localPhase = action.payload
    },
    setLocalSubStage(state, action) {
      state.localSubStage = action.payload
    },
  },
  extraReducers: (builder) => {
    //getMasterPhase
    builder.addCase(getMasterPhase.pending, (state) => {
      state.getMasterPhaseStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterPhase.fulfilled, (state, action) => {
      state.getMasterPhaseStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterPhaseResponse
      state.phases = actionPayload.data
    })
    builder.addCase(getMasterPhase.rejected, (state) => {
      state.getMasterPhaseStatus = StatusEnum.Failed
    })
    //getMasterPhaseCategory
    builder.addCase(getMasterPhaseCategory.pending, (state) => {
      state.getMasterPhaseCategoryStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterPhaseCategory.fulfilled, (state, action) => {
      state.getMasterPhaseCategoryStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterPhaseCategoryResponse
      state.uniquePhaseCategories = actionPayload.data
    })
    builder.addCase(getMasterPhaseCategory.rejected, (state) => {
      state.getMasterPhaseCategoryStatus = StatusEnum.Failed
    })
    //addMasterPhase
    builder.addCase(addMasterPhase.pending, (state) => {
      state.addMasterPhaseStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterPhase.fulfilled, (state, action) => {
      state.addMasterPhaseStatus = StatusEnum.Success
    })
    builder.addCase(addMasterPhase.rejected, (state) => {
      state.addMasterPhaseStatus = StatusEnum.Failed
    })
    //addMasterPhaseCategory
    builder.addCase(addMasterPhaseCategory.pending, (state) => {
      state.addMasterPhaseCategoryStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterPhaseCategory.fulfilled, (state, action) => {
      state.addMasterPhaseCategoryStatus = StatusEnum.Success
    })
    builder.addCase(addMasterPhaseCategory.rejected, (state) => {
      state.addMasterPhaseCategoryStatus = StatusEnum.Failed
    })
    //updateMasterPhase
    builder.addCase(updateMasterPhase.pending, (state) => {
      state.updateMasterPhaseStatus = StatusEnum.Pending
    })
    builder.addCase(updateMasterPhase.fulfilled, (state, action) => {
      state.updateMasterPhaseStatus = StatusEnum.Success
    })
    builder.addCase(updateMasterPhase.rejected, (state) => {
      state.updateMasterPhaseStatus = StatusEnum.Failed
    })
    //deleteMasterPhase
    builder.addCase(deleteMasterPhase.pending, (state) => {
      state.deleteMasterPhaseStatus = StatusEnum.Pending
    })
    builder.addCase(deleteMasterPhase.fulfilled, (state, action) => {
      state.deleteMasterPhaseStatus = StatusEnum.Success
    })
    builder.addCase(deleteMasterPhase.rejected, (state) => {
      state.deleteMasterPhaseStatus = StatusEnum.Failed
    })
  },
})

export const { setLocalPhase, setLocalSubStage } = phaseSlice.actions

// Export the reducer
export default phaseSlice.reducer

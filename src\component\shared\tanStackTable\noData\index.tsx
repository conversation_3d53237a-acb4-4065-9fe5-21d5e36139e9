import React from 'react'
import ContentPasteOffIcon from '@mui/icons-material/ContentPasteOff'
import styles from './NoData.module.scss'

interface Props {
  isLoading?: boolean
}

const NoData: React.FC<Props> = ({ isLoading }) => {
  return (
    <div className={styles.noDataContainer}>
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <>
          <ContentPasteOffIcon className={styles.noDataIcon} />
          <p>No Data Available</p>
        </>
      )}
    </div>
  )
}

export default NoData

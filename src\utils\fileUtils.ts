// Utility functions for file operations

export const FILE_SIZE_ERROR_MESSAGE = `File size should be less than 10MB.`

/**
 * Returns the size of the file in bytes.
 * @param file File object
 * @returns number (size in bytes)
 */
export function getFileSize(file: File): number {
  return file.size
}

/**
 * Validates if the file size is less than the specified max size (default 10MB).
 * @param file File object
 * @param maxSizeMB Maximum size in MB (default 10)
 * @returns boolean (true if valid, false if too large)
 */
export function isFileSizeValid(file: File, maxSizeMB = 10): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return file.size < maxSizeBytes
}

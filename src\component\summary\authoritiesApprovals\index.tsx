import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './authoritiesApprovals.module.scss'
import AuthorityTable from './authorityTable'
import Infrastructure from './infrastructure'
import Button from '../../shared/button'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import CommonPopoverForDisplayMultiSelect from '../../shared/popover/commonPopoverForDisplayMultiSelect'
import { isWildCardValue } from '../../shared/tanStackTable/helper'
import { showCustomToast } from '../../toast/ToastManager'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { convertValuesToCommaSeparated } from '@/src/helpers/helpers'
import useAuthorityApproval from '@/src/redux/authorityApproval/useAuthorityApproval'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILookupProjectToPhase } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import {
  getUniqueValuesById,
  getUniqueValuesFromArray,
  populateDropdownOptions,
  prepareDropdownOptions,
} from '@/src/utils/arrayUtils'
import { naturalSort } from '@/src/utils/sortingUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

interface Status {
  id: string
  project_name: string
  phase: string
  // Add more fields as needed
}

const AuthoritiesApproval: React.FC = () => {
  const router = useRouter()
  const [expandedBasicDetails, setExpandedBasicDetails] = useState<string | false>(false)
  const [phase, setPhase] = useState<ILookupProjectToPhase | null>(null)
  const [editId, setEditId] = useState<any>()
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false)
  const [loader, setLoader] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(true)
  const [drawerContent, setDrawerContent] = useState<string>('')
  const { statuses, getStatusApi } = useStatus()
  const [categoryAnchorEl, setCategoryAnchorEl] = useState(null)
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { getAuthorityApprovalApi, addAuthorityApprovalApi, authorities, deleteAuthorityApprovalApi } =
    useAuthorityApproval()

  const fetchData = async () => {
    try {
      await getStatusApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      await getAuthorityApprovalApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      setLoading(false)
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPeriod, drawerContent, isOpenDrawer, router?.query?.slug])

  const status: any = useMemo(() => {
    return populateDropdownOptions(
      statuses.filter((item: any) => {
        return item.project_name === router.query.slug
      }),
      'LookupProjectToPhase',
    )
    // TODO: Check changes in this logic if needed then use otherwise remove to code.
    // const uniqueArray = getUniqueValuesFromArray(stringArray)
    // const sortedMultiPhases = getSortedUniquePhases(uniqueArray)
    // return sortedMultiPhases
  }, [statuses, router.query.slug])

  //TODO: Check this sortingcode it's found in conflicts.
  // const getSortedUniquePhases = (array: string[]) => {
  //   return array?.sort((a, b) => {
  //     const rowAString = isWildCardValue(a as string) ? 'Multi Phases' : a
  //     const rowBString = isWildCardValue(b as string) ? 'Multi Phases' : b
  //     if (typeof rowAString === 'string' && typeof rowBString === 'string') {
  //       return rowAString.localeCompare(rowBString) // String comparison
  //     }
  //     return 0
  //   })
  // }

  const phaseArray = useMemo(() => {
    return status?.map((res: any) => {
      const phases = res
        ?.map((data: any) => data?.phase)
        ?.filter((res: any) => res)
        .join(', ')
      return phases
    })
  }, [status])

  const seen = new Set()
  const phases = status?.filter((arr: ILookupProjectToPhase[]) => {
    const hasNull = arr.some(({ phase, MasterProjectPhaseCategory }) => !phase || !MasterProjectPhaseCategory)
    const key = JSON.stringify(
      arr
        .map(({ id, phase, MasterProjectPhaseCategory }) => ({
          id,
          phase,
          category: MasterProjectPhaseCategory
            ? { id: MasterProjectPhaseCategory.id, name: MasterProjectPhaseCategory.project_phase_category }
            : null,
        }))
        .sort((a: any, b: any) => a.id - b.id),
    )
    if (hasNull || !seen.has(key)) {
      if (!hasNull) seen.add(key)
      return true
    }
    return false
  })

  const handleOpenDrawer = (content: string) => {
    setDrawerContent(content.toString())
    setIsOpenDrawer(true)
  }

  const handleCloseDrawer = () => {
    setIsOpenDrawer(false)
    setEditId(null)
  }

  const handleEdit = (id: string, type: string) => {
    setEditId(id as any)
    handleOpenDrawer(type)
    setDrawerContent(type)
  }

  const handleDelete = async (id: any, type: string) => {
    // setEditId(id as any);
    // handleOpenDrawer(type);
    // setDrawerContent(type);
    const res: Record<string, any> = await deleteAuthorityApprovalApi(id as number)
    if (res.payload.success) {
      getAuthorityApprovalApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
    } else {
      showCustomToast('Error', 'error')
    }
  }

  const findLookupProjectToPhaseId = (phase: any) => {
    const phaseArray = status?.flat()?.map((res: any) => {
      return { label: res?.phase, value: res?.id }
    })
    // TODO
    const findPhase = phaseArray?.find((res: any) => phase?.includes(res?.label))
    return findPhase?.value || null
  }

  const handleEnter = async (phase: any, drawerContent: any) => {
    const infrastructureComponents = [
      'Earthwork',
      'Road Works',
      'Sewer Network',
      'Storm water network',
      'Subsurface Drainage',
      'Potable Water',
      'Irrigation',
      'Recycled Water',
      'LV Network',
      'MV Network',
      'FOC Network',
      'Telecom Network',
      'Street light Network',
      'Gas Network',
      'MCC Network',
    ]

    const items = [
      'Soil Investigation',
      'Site clearance',
      'Estidama',
      'Geotechnical Report',
      'ITC',
      'Arch',
      'Structure',
      'Mechanical',
      'Plumbing',
      'MCC',
      'EAD',
      'ADCD',
      'ADDC',
      'Etisalat',
      'Building Permit',
    ]

    try {
      setLoader(true)

      const data = drawerContent === 'Infrastructure' ? infrastructureComponents : items
      const phaseId = findLookupProjectToPhaseId(phase)
      for (const item of data) {
        const payload: any = {
          period: currentPeriod,
          project_name: router.query.slug,
          // phase: phase,
          lookup_project_to_phase_id: phaseId,
          type: drawerContent,
          description: item,
          design_enoc_forecast_date: null,
          design_enoc_status: null,
          tpd_forecast_date: null,
          tpd_status: null,
          enoc_status: null,
          enoc_forecast_date: null,
        }

        try {
          const res: Record<string, any> = await addAuthorityApprovalApi(payload)
          if (res?.payload.success) {
            // successToast(res?.payload?.message)
          } else {
            // errorToast(res?.payload?.response?.data?.message || 'Failed')
          }
        } catch (error) {
          console.error(`Error processing ${item}:`, error)
        }
      }

      const res: any = await getAuthorityApprovalApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
      if (res.payload.success) setLoader(false)
    } catch (error) {
      console.error('Error in handleEnter:', error)
    }
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleClickCategoryIcon = (event: any) => {
    setCategoryAnchorEl(event.currentTarget)
  }

  return (
    <div className={styles.mainContainer}>
      {loading ? (
        <Loader />
      ) : (
        <>
          {phases?.map((res: ILookupProjectToPhase[]) => {
            const title = res
              // ?.filter((res: any) => res?.phase)
              ?.map(
                (data: ILookupProjectToPhase) =>
                  `${data?.phase || 'No Phase'} ${data?.MasterProjectPhaseCategory && data?.MasterProjectPhaseCategory?.project_phase_category && `(${data?.MasterProjectPhaseCategory?.project_phase_category})`}`,
              )
              ?.join(', ')
            const filteredInfrastructureData = authorities.filter((authority: any) => {
              const isExist =
                res?.length === 1 && res?.find((data: any) => data?.id === authority.LookupProjectToPhase?.id)
              return isExist && authority.project_name === router.query.slug && authority.type === 'Infrastructure'
            })

            const filteredBuildingData = authorities.filter((authority: any) => {
              const isExist =
                res?.length === 1 && res?.find((data: any) => data?.id === authority.LookupProjectToPhase?.id)
              return isExist && authority.project_name === router.query.slug && authority.type === 'Buildings'
            })

            const key = title + res[0]?.id

            const phaseNameArrayForMultiPhase = res?.map((data) => {
              const phaseName = `${data?.phase || 'No Phase'} ${data?.MasterProjectPhaseCategory && data?.MasterProjectPhaseCategory?.project_phase_category && `(${data?.MasterProjectPhaseCategory?.project_phase_category})`}`
              return { label: phaseName, value: data?.id }
            })

            return (
              <div className={styles.accordionSection} key={key}>
                {res?.length > 0 && (
                  <Accordion
                    expanded={expandedBasicDetails === key}
                    onChange={() => setExpandedBasicDetails(expandedBasicDetails === key ? false : key)}
                    classes={{ root: styles.accordion }}
                  >
                    <AccordionSummary className={styles.summary} classes={{ content: styles.content }}>
                      <div className={styles.header}>
                        {/* <div>{convertValuesToCommaSeparated(title)}</div> */}
                        <div>
                          {res?.length > 1 ? (
                            <div>
                              <span className={styles.infoContainer}>
                                <span>Multi phases</span>
                                <span className={styles.infoIcon} onClick={handleClickCategoryIcon}>
                                  <CommonPopoverForDisplayMultiSelect
                                    // title="Multi categories"
                                    content={phaseNameArrayForMultiPhase}
                                    maxWidth={350}
                                    placement="right"
                                  />
                                </span>
                              </span>
                            </div>
                          ) : (
                            <div>{title || 'No Phase'}</div>
                          )}
                        </div>
                      </div>
                      {filteredBuildingData.length > 0 || filteredInfrastructureData.length > 0 ? (
                        ''
                      ) : (
                        <div className={styles.crudIcon}>
                          <Button
                            onClick={(e) => {
                              if (!isEditForUser) {
                                toast(`The current reporting period is locked`, {
                                  icon: <WarningAmberOutlined />,
                                })
                              } else {
                                e.stopPropagation()
                                // handleOpenDrawer("Infrastructure");
                                setPhase(res[0])
                                // TODO : what to pass instead of title?
                                handleEnter(title, 'Infrastructure')
                              }
                            }}
                            disabled={true}
                          >
                            Infrastructure
                          </Button>
                          <Button
                            onClick={(e) => {
                              if (!isEditForUser) {
                                toast(`The current reporting period is locked`, {
                                  icon: <WarningAmberOutlined />,
                                })
                              } else {
                                e.stopPropagation()
                                // handleOpenDrawer("Buildings");
                                setPhase(res[0])
                                // TODO : what to pass instead of title?
                                handleEnter(title, 'Buildings')
                              }
                            }}
                            disabled={true}
                          >
                            Buildings
                          </Button>
                        </div>
                      )}
                    </AccordionSummary>
                    <AccordionDetails className={styles.details}>
                      <div className={styles.container}>
                        <div className={styles.contentWrapper}>
                          <div className={styles.header}></div>
                          {loader ? (
                            <Loader />
                          ) : (
                            <AuthorityTable
                              phase={res[0]}
                              setPhase={setPhase}
                              handleEdit={handleEdit}
                              handleDelete={handleDelete}
                            />
                          )}
                        </div>
                      </div>
                    </AccordionDetails>
                  </Accordion>
                )}
              </div>
            )
          })}

          <Drawer anchor="right" open={isOpenDrawer} onClose={handleCloseDrawer}>
            <div>
              <Infrastructure
                onClose={handleCloseDrawer}
                editId={editId}
                phase={phase?.phase || ''}
                drawerContent={drawerContent}
              />
            </div>
          </Drawer>
        </>
      )}
    </div>
  )
}

export default AuthoritiesApproval

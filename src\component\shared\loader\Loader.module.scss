@import '/styles/color.scss';

.loader {
  border: 3px solid $LIGHT;
  border-radius: 50%;
  border-top: 3px solid $DARK;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  position: relative;
}
.smallLoader {
  width: 25px !important;
  height: 25px !important;
  border: 3px solid $LIGHT;
  border-radius: 50%;
  border-top: 3px solid $DARK;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  position: relative;
}

/* Safari */
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.container {
  cursor: progress;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

import { useDispatch, useSelector } from 'react-redux'
import { allSelectedRows, emptySelectedRows, setSelectedRows } from './index'
import { AppDispatch, RootState } from '@/src/redux/store'

const useTable = () => {
  const dispatch: AppDispatch = useDispatch()
  const selectedRows = useSelector((state: RootState) => state.table.selectedRows)
  const addSelectedRows = (data: number) => dispatch(setSelectedRows(data))
  const emptyRowsSelected = () => dispatch(emptySelectedRows())
  const allRowsSelected = (data: number) => dispatch(allSelectedRows(data))

  return {
    selectedRows,
    addSelectedRows,
    emptyRowsSelected,
    allRowsSelected,
  }
}
export default useTable

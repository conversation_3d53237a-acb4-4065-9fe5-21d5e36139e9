import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IDesignProjectManagerPayload {
  design_manager: string
  avatar?: string
  phone_number: string
  email: string
  location: string
}

export interface IUpdateDesignProjectManager extends IDesignProjectManagerPayload {
  id: number
}

export interface IDesignProjectManager {
  id: number
  design_manager: string
  avatar: AvatarItem
  phone_number: string
  email: string
  location: string
}

export interface IGetMasterDesignProjectManagersResponse {
  data: IDesignProjectManager[]
  message: string
  success: true
}

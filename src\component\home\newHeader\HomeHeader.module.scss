@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.header {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  @include respond-to('tablet') {
    flex-direction: row;
  }
  .heyText {
    display: flex;
    align-items: center;
    gap: 2px;
  }
  .headerTitle {
    font-family: Poppins;
    font-size: 18px;
    font-weight: 600;
    line-height: 23px;
    text-align: left;
    padding: 13px 0 14px 24px;
  }
}

.buttons {
  display: flex;
  gap: 12px;
  flex-direction: row-reverse;

  @include respond-to('mobile') {
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }
  @include respond-to('tablet') {
    flex-direction: column;
    align-items: flex-end;
  }

  @include respond-to('laptop') {
    display: flex;
    gap: 12px;
    flex-direction: row-reverse;
  }
  .timerIcon {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;

    .date {
      font-family: Poppins;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
      color: $BLACK;
    }
  }
}
.titleContainer {
  display: flex;
  gap: 10px;
}

.cloneButtons {
  margin-left: 30px;
  padding: 10px;
  display: flex;
  gap: 10px;
  @include respond-to('mobile') {
    padding: 10px 0 0 0;
    margin-left: 0px;
  }
  @include respond-to('tablet') {
    padding: 0;
    margin-left: 30px;
  }
  @include respond-to('laptop') {
    padding: 10px;
    margin-left: 30px;
  }

  button {
    min-width: 150px;
    flex: 1;
    background-color: black !important;
  }
}

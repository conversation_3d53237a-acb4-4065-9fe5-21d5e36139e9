import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './ReasonForDelay.module.scss'
import { DrawerStates } from '../../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateReasonForDelay,
  useDeleteReasonForDelay,
  useGetReasonForDelay,
  useUpdateReasonForDelay,
} from '@/src/hooks/useReasonForDelay'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const ReasonForDelay: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { reasonForDelay } = drawerStates
  //REACT-QUERY
  const { reasonForDelays } = useGetReasonForDelay(reasonForDelay)
  // MUTATIONS
  const { mutate: addReasonForDelay } = useCreateReasonForDelay()
  const { mutate: deleteReasonForDelay } = useDeleteReasonForDelay()
  const { mutate: updateReasonForDelay } = useUpdateReasonForDelay()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: {
      reasonForDelay: '',
      reasonForDelayCode: '',
    },
    onSubmit: async (values) => {
      const payload = { reason_for_delay: values.reasonForDelay, reason_for_delay_code: values.reasonForDelayCode }
      if (editIndex !== null) {
        updateReasonForDelay(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addReasonForDelay(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteReasonForDelay(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editDelayReason = reasonForDelays.find((reason) => reason.id === id)
    formik.setValues({
      reasonForDelay: editDelayReason?.reason_for_delay || '',
      reasonForDelayCode: editDelayReason?.reason_for_delay_code || '',
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    { accessorKey: 'reason_for_delay_code', header: 'Code', flex: 1 },
    { accessorKey: 'reason_for_delay', header: 'Reason for Delay', flex: 1 },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.row?.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text="Add Reason for Delay" />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div className={styles.formGroup}>
            <TextInputField
              className={styles.field}
              name="reasonForDelay"
              labelText="Reason for Delay"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.reasonForDelay}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={styles.field}
              name="reasonForDelayCode"
              labelText="Reason for Delay Code"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.reasonForDelayCode}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <Button
            className={styles.submitButton}
            type="submit"
            disabled={(!formik.values.reasonForDelay && !formik.values.reasonForDelayCode) || formik.isSubmitting}
          >
            {editIndex !== null ? '+ Update Reason for Delay' : '+ Add Reason for Delay'}
          </Button>
        </form>

        {reasonForDelays?.length > 0 && (
          <TanStackTable
            rows={sortData(reasonForDelays, 'reason_for_delay_code') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default ReasonForDelay

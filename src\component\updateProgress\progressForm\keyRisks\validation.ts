import { isAfter, parse } from 'date-fns'
import { RATING } from './constant'

export class KeyRiskValidation {
  private static parseDate(dateStr: string) {
    const yyyyMMddRegex = /^\d{4}-\d{2}-\d{2}$/

    // If date format is 'yyyy-MM-dd', parse accordingly
    if (yyyyMMddRegex.test(dateStr)) {
      return parse(dateStr, 'yyyy-MM-dd', new Date())
    }

    // Default to 'dd-MM-yyyy' format
    return parse(dateStr.toString(), 'dd-MM-yyyy', new Date())
  }
  static async ForecastedClosureDate(values: any, isCell: boolean = false): Promise<boolean> {
    if (values?.rating !== RATING.CLOSED) {
      const field = isCell
        ? this.parseDate(values.forecasted_closure_date)
        : this.parseDate(values.forecastedClosureDate)

      if (!field || isNaN(field.getTime())) return false // Handle invalid dates

      const today = new Date()
      return !isAfter(field, today) // Ensures forecasted date is greater than today
    }
    return false
  }
}

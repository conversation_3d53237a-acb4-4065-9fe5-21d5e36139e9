import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IDesignManagerStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IDesignManagerStates = {
  stagingCloneStatus: StatusEnum.Idle,
  productionCloneStatus: StatusEnum.Idle,
}

export const stagingClone = createAsyncThunk('/staging', async (_, thunkAPI) => {
  try {
    const response: any = await ApiGetNoAuth(`/database-management/clone/staging`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const productionClone = createAsyncThunk('/production', async (_, thunkAPI) => {
  try {
    const response: any = await ApiG<PERSON><PERSON><PERSON><PERSON><PERSON>(`/database-management/clone/production`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const databaseManagement = createSlice({
  name: 'databaseManagement',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //staging
    builder.addCase(stagingClone.pending, (state) => {
      state.stagingCloneStatus = StatusEnum.Pending
    })
    builder.addCase(stagingClone.fulfilled, (state, action) => {
      state.stagingCloneStatus = StatusEnum.Success
    })
    builder.addCase(stagingClone.rejected, (state) => {
      state.stagingCloneStatus = StatusEnum.Failed
    })
    //production
    builder.addCase(productionClone.pending, (state) => {
      state.productionCloneStatus = StatusEnum.Pending
    })
    builder.addCase(productionClone.fulfilled, (state, action) => {
      state.productionCloneStatus = StatusEnum.Success
    })
    builder.addCase(productionClone.rejected, (state) => {
      state.productionCloneStatus = StatusEnum.Failed
    })
  },
})

export const {} = databaseManagement.actions

// Export the reducer
export default databaseManagement.reducer

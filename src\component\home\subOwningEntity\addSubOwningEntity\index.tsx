import React, { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import styles from './AddSubOwningEntity.module.scss'
import { IAddSubOwningEntityProps } from '../interface'
import Button from '@/src/component/shared/button'
import Table from '@/src/component/shared/table'
import { ITableColumn } from '@/src/component/shared/table/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import useSubOwningEntity from '@/src/redux/subOwningEntity/useSubOwningEntity'

const AddSubOwningEntity: React.FC<IAddSubOwningEntityProps> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const {
    getMasterSubOwningEntityStatus,
    subOwningEntities,
    getMasterSubOwningEntityApi,
    addMasterSubOwningEntityApi,
    updateMasterSubOwningEntityApi,
    deleteMasterSubOwningEntityApi,
  } = useSubOwningEntity()
  const { addSubOwningEntity } = drawerStates
  useEffect(() => {
    addSubOwningEntity && getMasterSubOwningEntityApi()
  }, [addSubOwningEntity])

  const formik = useFormik({
    initialValues: {
      subOwningEntity: '',
    },
    onSubmit: async (values) => {
      if (editIndex !== null) {
        const response: Record<string, any> = await updateMasterSubOwningEntityApi({
          id: editIndex,
          sub_owning_entity: values.subOwningEntity,
        })
        if (response.payload.success === true) getMasterSubOwningEntityApi()
        setEditIndex(null)
      } else {
        const response: Record<string, any> = await addMasterSubOwningEntityApi({
          sub_owning_entity: values.subOwningEntity,
        })
        if (response.payload.success === true) getMasterSubOwningEntityApi()
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    const response: Record<string, any> = await deleteMasterSubOwningEntityApi(id)
    if (response.payload.success === true) getMasterSubOwningEntityApi()
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = subOwningEntities.find((subOwningEntity) => {
      //TODO
      return subOwningEntity.id === id
    })

    formik.setValues({
      subOwningEntity: editControlManagers?.sub_owning_entity,
    })
    setEditIndex(id)
  }

  const columns: ITableColumn[] = [
    {
      key: 'sub_owning_entity',
      label: 'Sub Owning Entity',
    },
    {
      key: 'action',
      label: 'Action',
      cellStyle: {
        width: '40px',
      },
      renderCell: (value: unknown, row: Record<string, any>, rowIndex: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.id)} />
            <DeleteIcon onClick={() => handleDeleteEntity(row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Sub Owning Entity'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} color="secondary" onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {subOwningEntities?.length >= 1 && <Table data={subOwningEntities} columns={columns} />}
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.entityField}
              name="subOwningEntity"
              labelText={'Sub OwningEntity'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.subOwningEntity}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.subOwningEntity?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.subOwningEntity?.trim()}
              >
                {'+ Add Sub Owning Entity'}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddSubOwningEntity

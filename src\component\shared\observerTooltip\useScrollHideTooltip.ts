import { useCallback, useEffect, useState, useRef } from 'react'

interface UseScrollHideTooltipOptions {
  movementThreshold?: number // Minimum pixels moved to hide tooltip
  trackTargetElement?: boolean // Track the tooltip target element position
  trackScrollableParents?: boolean // Track scrollable parent containers
}

interface ElementPosition {
  x: number
  y: number
}

// Custom hook to detect scroll events and manage tooltip visibility
const useScrollHideTooltip = (options: UseScrollHideTooltipOptions = {}) => {
  const {
    movementThreshold = 0, // Default: hide on any movement
    trackTargetElement = true, // Track tooltip target element
    trackScrollableParents = true, // Track scrollable parents
  } = options

  const [isOpen, setIsOpen] = useState(false)
  const [shouldHide, setShouldHide] = useState(false)
  const [isHovering, setIsHovering] = useState(false) // Track hover state

  // Refs to track positions
  const targetElementRef = useRef<HTMLElement | null>(null)
  const initialTargetPosition = useRef<ElementPosition | null>(null)
  const scrollableParentsRef = useRef<HTMLElement[]>([])
  const initialParentPositions = useRef<ElementPosition[]>([])

  // Function to get element position
  const getElementPosition = useCallback((element: HTMLElement): ElementPosition => {
    const rect = element.getBoundingClientRect()
    return {
      x: rect.left + window.scrollX,
      y: rect.top + window.scrollY,
    }
  }, [])

  // Function to find all scrollable parents
  const getScrollableParents = useCallback((element: HTMLElement): HTMLElement[] => {
    const parents: HTMLElement[] = []
    let parent = element.parentElement

    while (parent && parent !== document.body) {
      const style = window.getComputedStyle(parent)
      const isScrollable =
        style.overflow === 'scroll' ||
        style.overflow === 'auto' ||
        style.overflowX === 'scroll' ||
        style.overflowX === 'auto' ||
        style.overflowY === 'scroll' ||
        style.overflowY === 'auto'

      if (isScrollable) {
        parents.push(parent)
      }
      parent = parent.parentElement
    }

    // Always include window/document
    parents.push(document.documentElement)
    return parents
  }, [])

  // Function to check if element has moved beyond threshold
  const hasElementMoved = useCallback(
    (currentPos: ElementPosition, initialPos: ElementPosition): boolean => {
      const deltaX = Math.abs(currentPos.x - initialPos.x)
      const deltaY = Math.abs(currentPos.y - initialPos.y)
      return deltaX > movementThreshold || deltaY > movementThreshold
    },
    [movementThreshold],
  )

  // Enhanced scroll handler that checks actual movement
  const handleScroll = useCallback(() => {
    if (!isOpen || !targetElementRef.current) return

    let shouldHideTooltip = false

    // Check target element movement (if enabled)
    if (trackTargetElement && initialTargetPosition.current) {
      const currentTargetPos = getElementPosition(targetElementRef.current)
      if (hasElementMoved(currentTargetPos, initialTargetPosition.current)) {
        shouldHideTooltip = true
        console.log('🎯 Target element moved - hiding tooltip')
      }
    }

    // Check scrollable parents movement (if enabled)
    if (trackScrollableParents && !shouldHideTooltip) {
      scrollableParentsRef.current.forEach((parent, index) => {
        const initialPos = initialParentPositions.current[index]
        if (initialPos) {
          const currentPos = getElementPosition(parent)
          if (hasElementMoved(currentPos, initialPos)) {
            shouldHideTooltip = true
            console.log('📜 Scrollable parent moved - hiding tooltip')
          }
        }
      })
    }

    if (shouldHideTooltip) {
      setShouldHide(true)
      setIsOpen(false)
    }
  }, [isOpen, trackTargetElement, trackScrollableParents, getElementPosition, hasElementMoved])

  useEffect(() => {
    if (!isOpen) return

    const scrollHandler = () => {
      handleScroll()
    }

    // Listen to scroll events on document (captures all scroll events)
    document.addEventListener('scroll', scrollHandler, { passive: true, capture: true })

    // Also listen to wheel events for additional scroll detection
    document.addEventListener('wheel', scrollHandler, { passive: true, capture: true })

    return () => {
      document.removeEventListener('scroll', scrollHandler, { capture: true })
      document.removeEventListener('wheel', scrollHandler, { capture: true })
    }
  }, [handleScroll, isOpen])

  // Reset shouldHide flag after a brief delay when hovering state changes
  useEffect(() => {
    if (shouldHide && isHovering) {
      const timer = setTimeout(() => {
        setShouldHide(false)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [shouldHide, isHovering])

  // Function to set target element and capture initial positions
  const setTargetElement = useCallback(
    (element: HTMLElement | null) => {
      targetElementRef.current = element

      if (element && isOpen) {
        // Capture initial position of target element
        if (trackTargetElement) {
          initialTargetPosition.current = getElementPosition(element)
        }

        // Capture initial positions of scrollable parents
        if (trackScrollableParents) {
          const parents = getScrollableParents(element)
          scrollableParentsRef.current = parents
          initialParentPositions.current = parents.map((parent) => getElementPosition(parent))
        }
      }
    },
    [isOpen, trackTargetElement, trackScrollableParents, getElementPosition, getScrollableParents],
  )

  const handleOpen = useCallback(() => {
    if (!shouldHide) {
      setIsOpen(true)
    }
  }, [shouldHide])

  const handleClose = useCallback(() => {
    setIsOpen(false)
    setIsHovering(false)
    // Clear position tracking
    initialTargetPosition.current = null
    scrollableParentsRef.current = []
    initialParentPositions.current = []
  }, [])

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true)
    if (!shouldHide) {
      setIsOpen(true)
    }
  }, [shouldHide])

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false)
    setIsOpen(false)
    // Clear position tracking
    initialTargetPosition.current = null
    scrollableParentsRef.current = []
    initialParentPositions.current = []
  }, [])

  return {
    isOpen,
    handleOpen,
    handleClose,
    handleMouseEnter,
    handleMouseLeave,
    setTargetElement,
  }
}

export { useScrollHideTooltip }
export type { UseScrollHideTooltipOptions }

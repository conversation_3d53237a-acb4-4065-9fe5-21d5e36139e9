import React, { useState } from 'react'
import { useFormik } from 'formik'
import { Toaster } from 'sonner'
import styles from './ProjectPhaseCategory.module.scss'
import { DrawerStates } from '../../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { ERROR_MESSAGE } from '@/src/constant/enum'
import {
  useCreateProjectPhaseCategories,
  useDeleteProjectPhaseCategories,
  useGetProjectPhaseCategories,
  useUpdateProjectPhaseCategories,
} from '@/src/hooks/useProjectPhaseCategory'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const ProjectPhaseCategory: React.FC<{ drawerStates: DrawerStates; onClose: () => void }> = ({
  drawerStates,
  onClose,
}) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const { projectPhaseCategory } = drawerStates
  //REACT-QUERY
  const { projectPhaseCategories } = useGetProjectPhaseCategories(projectPhaseCategory)
  // MUTATIONS
  const { mutate: addProjectPhaseCategories } = useCreateProjectPhaseCategories()
  const { mutate: deleteProjectPhaseCategories } = useDeleteProjectPhaseCategories()
  const { mutate: updateProjectPhaseCategories } = useUpdateProjectPhaseCategories()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
    },
  })

  const formik = useFormik({
    initialValues: { phaseCategoryName: '' },
    onSubmit: async (values) => {
      const payload = { project_phase_category: values.phaseCategoryName }
      if (editIndex !== null) {
        updateProjectPhaseCategories(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addProjectPhaseCategories(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteProjectPhaseCategories(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editCategory = projectPhaseCategories.find((category) => category.id === id)
    formik.setValues({ phaseCategoryName: editCategory?.project_phase_category || '' })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    { accessorKey: 'project_phase_category', header: 'Project Phase Category', flex: 1 },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>) => (
        <div className={styles.actionButtons}>
          <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
          <DeleteIcon onClick={() => setDeleteModel(row?.row?.id)} className={styles.deleteRowIcon} />
        </div>
      ),
    },
  ]
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text="Add Project Phase Category"
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div className={styles.formGroup}>
            <TextInputField
              className={styles.field}
              name="phaseCategoryName"
              labelText="Project Phase Category Name"
              placeholder="Type something..."
              variant="outlined"
              value={formik.values.phaseCategoryName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <Button
            className={styles.submitButton}
            type="submit"
            disabled={!formik.values.phaseCategoryName || formik.isSubmitting}
          >
            {editIndex !== null ? '+ Update Project Phase Category' : '+ Add Project Phase Category'}
          </Button>
        </form>

        {projectPhaseCategories?.length > 0 && (
          <TanStackTable
            rows={sortData(projectPhaseCategories, 'project_phase_category') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default ProjectPhaseCategory

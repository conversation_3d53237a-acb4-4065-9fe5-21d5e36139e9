import { StatusEnum } from '../types'

export interface IMasterPeriodsState {
  getMasterPeriodStates: StatusEnum
  getAllPeriodStatus: StatusEnum
  addMasterPeriodStates: StatusEnum
  finalFreezePeriodStatus: StatusEnum
  unFreezePeriodStates: StatusEnum
  userFreezePeriodStates: StatusEnum
  periods: IPeriod[]
  mainPeriod: string
  period: IPeriod
  allPeriods: any
  // currentPeriod: any
  // isPeriodChangeFromProjectList: boolean
}

export interface IPeriod {
  id: number
  period: string
  freeze_type: string
}

export interface IGetMasterPeriodResponse {
  data: IPeriod
  message: string
  success: true
}

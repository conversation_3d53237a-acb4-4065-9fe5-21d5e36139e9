@import '/styles/color.scss';

.root {
  width: 100%;

  .tagInput {
    // padding: 10px !important;
  }

  .popupIndicator {
    padding: 0;
    top: 2px;
    .downArrow {
      height: 18px;
      width: 18px;
      fill: $GRAY_500;
    }
  }

  // .chip {
  //   border: 0;
  //   margin: 0 5px;
  // }
  .chipsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 4px; /* Adjust the gap between chips if needed */
    max-width: 100%; /* Ensure container doesn't exceed input width */
    overflow: hidden; /* Hide overflow */
  }

  .chip {
    white-space: nowrap; /* Prevent text from wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis;
    margin: 5px; /* Add ellipsis */
  }
  .clearIcon {
    width: 13px;
    color: #585858;
    height: 13px;
    // margin-right: 5px;
  }

  .isMultiLine {
    -webkit-line-clamp: unset !important;
  }

  .inputText {
    font-size: 12px;
    line-height: 18px;
    font-family: Poppins;
    font-weight: 400;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: $DARK;
  }

  .labelText {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #808080;
  }
}

.popper {
  .paper {
    width: 100%;
    border-radius: 4px;
    border: 1px solid $WHITE;
    box-shadow: 0px 4px 30px 0px #0000000d;
    margin-top: 4px;

    .listBox {
      margin: 4px 4px 4px 0;
      overflow: auto;
      padding: 0;
      cursor: pointer;

      li {
        padding: 11px 16px;
        font-family: Poppins;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: $BLACK;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: transparent;
      }
    }

    .formGroupRoot {
      margin-left: 0;
      width: 100%;

      > span {
        padding: 0;
        min-width: 16px;
      }

      .checkboxLabel {
        padding-left: 4px;
        font-size: 14px;
        line-height: 16px;
        color: $GRAY_500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.checkBox {
  padding: 0px !important;
}

.optionLi {
  display: flex;
  border-bottom: 1px solid $LIGHT;
  flex-direction: column;
}

.options {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-wrap: wrap;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  gap: 1rem;
}

.mainOptions {
  display: flex;
  align-items: center;

  .optionName {
    display: flex;
    flex-direction: row;
  }
}

.subOptions {
  margin-left: 20px;
  padding-left: 16px;
}

import React, { ReactElement } from 'react'
import { Tooltip as TooltipMui } from '@mui/material'
import { styled } from '@mui/material/styles'
import { TooltipProps, tooltipClasses } from '@mui/material/Tooltip'
import styles from './Tooltip.module.scss'

const BootstrapTooltip = styled(
  ({ className, margin, bgColor, padding, borderRadius, boxShadow, ref, ...props }: any) => (
    <TooltipMui
      {...props}
      ref={ref}
      classes={{ popper: className, arrow: styles.arrow }}
      slotProps={{
        popper: {},
      }}
    />
  ),
)(({ bgColor, padding, borderRadius, margin, boxShadow }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: bgColor || '#FAFAFA !important',
    boxShadow: boxShadow || '0px 4px 200px 0px #000000A1 !important',
    padding: padding || '7px 12px',
    fontFamily: 'Poppins !important',
    fontSize: '12px !important',
    lineHeight: '14px',
    fontWeight: '400',
    color: '#444444',
    borderRadius: borderRadius || '0px 10px 10px 10px',
    margin: margin,
  },
}))

const CustomTooltip: React.FC<any> = ({
  children,
  placement,
  title,
  backgroundColor,
  padding,
  borderRadius,
  arrow = false,
  boxShadow,
  margin = 0,
  ref,
  ...rest
}): ReactElement => {
  return (
    <BootstrapTooltip
      ref={ref}
      arrow={arrow}
      title={title}
      margin={margin}
      bgColor={backgroundColor}
      padding={padding}
      boxShadow={boxShadow}
      borderRadius={borderRadius}
      {...rest}
      placement={placement}
    >
      {children}
    </BootstrapTooltip>
  )
}

export default CustomTooltip

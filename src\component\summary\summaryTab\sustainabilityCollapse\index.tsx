import React, { useMemo } from 'react'
import { useRouter } from 'next/router'
import { getValue } from './helper'
import styles from './SustainabilityCollapse.module.scss'
import ImageCard from '../imageCard'
import { ISustainabilityCollapse } from '../interface'
import useSustainability from '@/src/redux/sustainability/useSustainability'
import { formatNumberWithCommas, removeCommasAndConvert } from '@/src/utils/numberUtils'

const SustainabilityCollapse = ({ summaryEdit, formik }: ISustainabilityCollapse) => {
  const router = useRouter()
  const { sustainabilities } = useSustainability()

  const sustainability: any = useMemo(() => {
    const found = sustainabilities.find(
      (item: { project_name: string | string[] | undefined }) => item.project_name === router.query.slug,
    )
    return found
      ? {
          ...found,
          waste_recycled_percentage: found.waste_recycled_percentage ? found.waste_recycled_percentage : '0',
          reinvented_economy_percentage: found.reinvented_economy_percentage
            ? found.reinvented_economy_percentage
            : '0',
          reinvented_economy_value: found.reinvented_economy_value ? found.reinvented_economy_value : '0',
          pearl_rating_percentage: found.pearl_rating_percentage ? found.pearl_rating_percentage : '0',
          recycled_material_percentage: found.recycled_material_percentage ? found.recycled_material_percentage : '0',
          workers_welfare_compliance_percentage: found.workers_welfare_compliance_percentage
            ? found.workers_welfare_compliance_percentage
            : '0',
        }
      : undefined
  }, [sustainabilities, router.query.slug])

  const handleFieldChange = (field: string, value: any, max: number) => {
    // Check if the input is empty or has a valid number with at most 2 decimal places
    const decimalRegEx = /^(\d+(\.\d{0,2})?)?$/
    // Allow clearing the field by setting an empty value
    if (value === '' || value === null) {
      formik.setFieldValue(field, '')
      return
    }
    // Convert the value to a number
    const numericValue = parseFloat(value)
    // Validate if the value is between 0 and max, and matches the decimal pattern
    if (!isNaN(numericValue) && decimalRegEx.test(value) && numericValue >= 0 && numericValue <= max) {
      formik.setFieldValue(field, value)
    }
  }

  const handleEconomyValue = (value: string, fieldName: string) => {
    if (value === '') {
      formik.setFieldValue(fieldName, 0)
      return
    }
    if (value?.length > 15) return
    const validValue = removeCommasAndConvert(value)
    const numValue = Number(validValue?.toString())
    if (!isNaN(numValue) && numValue >= 0) {
      formik.setFieldValue(fieldName, value === '' ? 0 : numValue)
    }
  }

  return (
    <div className={styles.cards} onClick={(e) => e.stopPropagation()}>
      <ImageCard
        label="Waste Recycled %"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'waste_recycled_percentage', !summaryEdit, true)}
        onChange={(value) => handleFieldChange('waste_recycled_percentage', value, 100)}
        imageSrc="/svg/recycleSymbol.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="waste_recycled_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        formik={formik}
        label="Recycled Material %"
        value={
          getValue(formik, summaryEdit, sustainability, 'recycled_material_percentage', !summaryEdit, true)
          // (!sustainability && !summaryEdit) || !sustainability
          //   ? ''
          // :
          // !summaryEdit
          //   ? formik?.values?.recycled_material_percentage
          //     ? `${formatNumberWithCommas(formik?.values?.recycled_material_percentage)}%` || ''
          //     : ''
          //   : formik?.values?.recycled_material_percentage.toString()
          //     ? formik?.values?.recycled_material_percentage
          //     : ''
        }
        onChange={(value) => handleFieldChange('recycled_material_percentage', value ? value : 0, 100)}
        imageSrc="/svg/recycleMaterial.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName={'recycled_material_percentage'}
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Pearl Rating"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'pearl_rating_percentage')}
        onChange={(value) => handleFieldChange('pearl_rating_percentage', value, 5)}
        imageSrc="/svg/pearlRating.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="pearl_rating_percentage"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Reinvested In The Economy %"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'reinvented_economy_percentage', !summaryEdit, true)}
        onChange={(value) => handleFieldChange('reinvented_economy_percentage', value, 100)}
        imageSrc="/svg/reinvestInPercentage.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="reinvented_economy_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Reinvested In The Economy(AED)"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'reinvented_economy_value', false, false, true)}
        onChange={(value) => handleEconomyValue(value, 'reinvented_economy_value')}
        imageSrc="/svg/reinvestInAED.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="reinvented_economy_value"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Workers Welfare Compliance %"
        formik={formik}
        value={getValue(
          formik,
          summaryEdit,
          sustainability,
          'workers_welfare_compliance_percentage',
          !summaryEdit,
          true,
        )}
        onChange={(value) => handleFieldChange('workers_welfare_compliance_percentage', value, 100)}
        imageSrc="/svg/manhour.svg"
        imageWidth={65}
        imageHeight={50}
        fieldName="workers_welfare_compliance_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />
    </div>
  )
}

export default SustainabilityCollapse
